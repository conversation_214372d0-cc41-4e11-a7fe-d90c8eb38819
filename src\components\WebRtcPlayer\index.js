import React, { useRef, useEffect } from "react";
import Srs from "./srs.sdk";
import { getGuid } from "@/utils/helper";
import { RtmpUrl } from "@/utils/utils";
import { getRtmpWebrtcUrl } from "@/utils/config";

const VideoViewer = ({ sn3, ctl, height, width,isDrc }) => {
  //const url='webrtc://112.44.103.230/live/'+sn3
  const RtmpUrl=getRtmpWebrtcUrl();
  const url = RtmpUrl + sn3;
  //const url2 = url + getGuid();
  //const guid=getGuid();
  const guid="fj_player";
  useEffect(() => {

      const player = document.getElementById(guid);
      const rtcPlayer = new Srs.SrsRtcPlayerAsync();
      rtcPlayer.play(url);
      // video标签
      player.srcObject = rtcPlayer.stream;
    
      return () => {
        rtcPlayer.close();
      }
    
  }, [url]);
  return (
    <div style={{ background: "black" }}>
      <video
        // key={url2}
        id={guid}
        autoPlay
        style={{ objectFit: "cover", background: "black"}}
        width={width? width : "100%"}
        height={height}
      ></video>
    </div>
  );
};

export default VideoViewer;
