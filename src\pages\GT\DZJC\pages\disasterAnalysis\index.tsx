import React, { useEffect, lazy } from "react";
import { useLocation, useModel } from "umi";
import useContentStore from "@/pages/GT/DZJC/store";
import ButtonBox from "@/pages/GT/DZJC/pages/disasterAnalysis/Panels/ButtonBox";
import LeaflatMap from "../Maps/LeaflatMap/LeaflatMap";
import { Get2 } from "@/services/general";
import CesiumMap from "../Maps/CesiumMap/CesiumMap";
import useConfigStore from "@/stores/configStore";
import { queryPage } from "@/utils/MyRoute";
import styles2 from '@/pages/SI/ControlCenter/index.module.less';
const FlyDispatch = lazy(() => import('@/pages/DJI/OrgPage/JiaoTongHome'));

const DataAcquisition: React.FC = () => {
  const { page, setPage, pageData } = useModel("pageModel");
  const { MenuShow, setMenuShow } = useConfigStore();
  const location = useLocation();
  const { region } = useContentStore(); // 获取区域状态
  const currentPath = location.pathname;
  const [deviceList, setDeviceList] = React.useState([]);
  const [status, setStatus] = React.useState("时序选择");
  const [render, setRender] = React.useState(
    <LeaflatMap data={deviceList}></LeaflatMap>
  );
  // 模拟根据 region 加载数据的方法
  const fetchDataByRegion = (regionId: string) => {
    console.log(`正在根据区域 ID "${regionId}" 请求数据...`);
    // 这里可以调用 API 接口获取数据
    // 示例：
    // api.fetchData(regionId).then(data => setData(data));
  };
  useEffect(() => {
    const fetchData = async () => {
      const res = await Get2("/api/open/Device/GetAllList");
      setDeviceList(res);
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (region) {
      fetchDataByRegion(region);
    }
  }, [region]);
  useEffect(() => {
    function MenuShowFc() {
      if (status === "模型对比") {
        setRender(<CesiumMap></CesiumMap>);
        setMenuShow(false);
      } else {
        // setRender(<LeaflatMap data={deviceList}></LeaflatMap>);
        setRender(<FlyDispatch className={''} />)
        setMenuShow(true);
      }
    }
    MenuShowFc();
  }, [status]);

  return (
    <div style={{ position: "relative", height: "100%" }}>
      {render}
      <ButtonBox setStatus={setStatus}></ButtonBox>
    </div>
  );
};

export default DataAcquisition;
