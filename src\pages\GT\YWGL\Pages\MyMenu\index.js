import { useState, useEffect } from "react";
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
  HddOutlined,
} from "@ant-design/icons";
import { But<PERSON>, Menu } from "antd";
import { useModel } from "umi";
import useConfigStore from "@/stores/configStore";

const items = [
  {
    key: "设备管理",
    icon: <PieChartOutlined />,
    label: "设备管理",
    children: [
      {
        key: "设备列表",
        label: "设备列表",
      },
      {
        key: "视频九宫格",
        label: "视频九宫格",
      },
      {
        key: "设备日志",
        label: "设备日志",
      },
      {
        key: "解禁文件",
        label: "解禁文件",
      },
    ],
  },
  {
    key: "地图管理",
    icon: <ContainerOutlined />,
    label: "地图管理",
    children: [
      {
        key: "地图基本设置",
        label: "地图基本设置",
      },
      {
        key: "地图底图配置",
        label: "地图底图配置",
      },
      {
        key: "地图数据管理",
        label: "地图数据管理",
      },
    ]
  },
  {
    key: "飞行记录",
    icon: <HddOutlined />,
    label: "飞行记录",
    children: [
      {
        key: "航行记录",
        label: "航行记录",
      },
      {
        key: "任务记录",
        label: "任务记录",
      },
      {
        key: "飞行统计",
        label: "飞行统计",
      },
    ]
  },
  {
    key: "成果管理",
    icon: <HddOutlined />,
    label: "成果管理",
    children: [
      {
        key: "航拍照片",
        label: "航拍照片",
      },
      {
        key: "航拍视频",
        label: "航拍视频",
      },
      {
        key: "正射影像",
        label: "正射影像",
      },
      {
        key: "正射对比",
        label: "正射对比",
      },
      {
        key: "三维模型",
        label: "三维模型",
      },
      {
        key: "三维对比",
        label: "三维对比",
      },
      {
        key: "地图管理",
        label: "地图管理",
      },
      {
        key: "建模任务",
        label: "建模任务",
      },
      {
        key: "航行记录",
        label: "飞行记录",
      },
    ]
  },
  {
    key: "系统管理",
    icon: <DesktopOutlined />,
    label: "系统管理",
    children: [
      {
        key: "用户管理",
        label: "用户管理",
      },
      {
        key: "子系统管理",
        label: "子系统管理",
      },
      {
        key: "系统日志",
        label: "系统日志",
      },
    ]
  },
];
const App = ({ handlePageChange, setCollapsed, collapsed }) => {
  const { headerHeight } = useConfigStore()
  const { pageData, setPageData } = useModel("pageModel");
  const [openKeys, setOpenKeys] = useState(['设备管理']);
  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  // 处理菜单点击
  function handleMenu(e) {
    console.log("click ", e);
    handlePageChange(e.key);
  };
  // 挂载时 默认打开第一个菜单
  useEffect(() => {
    handlePageChange(items[0].key);
  }, []);
  return (
    <>
      {
        <div
          style={{
            position: 'fixed',
            left: 0,
            top: headerHeight,
            bottom: 0,
            width: collapsed ? 80 : 160,
            background: '#001529',
            zIndex: 1000,
            transition: 'width 0.2s',
          }}
        >
          <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{
              marginBottom: 16,
            }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <Menu
            //defaultSelectedKeys和selectedKeys同时存在的时候 只有selectedKeys生效 而且defaultSelectedKeys不会触发点击事件 只会让菜单的第一个被选中而已
            openKeys={openKeys}
            onOpenChange={keys => setOpenKeys(keys)}
            defaultSelectedKeys={[items[0].children[0].key]}
            // selectedKeys={[pageData?.title]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 56px)', // 100%可视高度减去按钮区域高度
              overflowY: 'auto'
            }}
          />
        </div>
      }
    </>
  );
};
export default App;
