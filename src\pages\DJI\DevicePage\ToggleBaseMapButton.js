
import { useModel } from "umi";
import { useState } from 'react';
import styles from './ToggleBaseMapButton.module.less';
import mapImg from "@/assets/img/地图.png"

const ToggleBaseMapButton = (props) => {
  const { mapUrl, setMapUrl, baseMapLayers } = useModel("mapModel");
  const [isExpanded, setIsExpanded] = useState(false);

  const setDiTu = (i) => {
    const selectedLayer = baseMapLayers[i];
    if (selectedLayer) {
      localStorage.setItem("baseMap", selectedLayer.tileUrl);
      setMapUrl(selectedLayer.tileUrl);
      setIsExpanded(false);
    }
  };

  return (
    <div className={styles['toggle-button-container']}>
      <button
        className={`${styles['toggle-button']} ${isExpanded ? styles.expanded : ''}`}
        onClick={() => setIsExpanded(!isExpanded)}
        title="切换底图"
      >
        <img src={mapImg} alt="切换底图" />
      </button>
      {isExpanded && (
        <div className={styles['map-list']}>
          {baseMapLayers.map((layer, index) => (
            <div
              key={index}
              className={`${styles['map-item']} ${mapUrl === layer.tileUrl ? styles.active : ''}`}
              onClick={() => setDiTu(index)}
              title={layer.name}
            >
              <img src={mapImg} alt={layer.name} />
              <span className={styles['map-name']}>{layer.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ToggleBaseMapButton;