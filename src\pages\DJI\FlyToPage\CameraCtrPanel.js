import { getGuid } from "@/utils/helper";
import { HGet2, HPost2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import dayjs from "dayjs";
import { useState } from "react";
import KeyboardEventHandler from 'react-keyboard-event-handler';
import { useModel } from "umi";
import ChangeHeightPanel from './Panels/ChangeHeightPanel';


const CameraCtrPanel = (props) => {
    const {sn,fj}=props;
    const [fdjj,setFdjj]=useState(2)
    const {DoCMD,DoCMD2}=useModel('cmdModel')
    const {setOpen,setModal}=useModel('pageModel')


    const device= JSON.parse( localStorage.getItem('device'));
    
    const SendCMD=(m1,data)=>{
        if(isEmpty(m1)) return;
        console.log('SendCMD',device.SN,m1,data)
        DoCMD(device.SN,m1,data);
    }



    const CameraDrag = (sn, x, y) => {
        const data = {
            "locked": true,
            "payload_index": "53-0-0",
            "pitch_speed": x,
            "yaw_speed": y
        }
        SendCMD("camera_screen_drag",data);
       // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_screen_drag", data)
    }


    const CameraZoom = (sn, z) => {

        if(z<2)z=2;
        if(z>66)z=66;

        if(z==fdjj){
            return;
        }  
        setFdjj(z);
        const data = {
            "camera_type": "zoom",
            "payload_index": device.Camera2,
            "zoom_factor": z
        }
        SendCMD("camera_focal_length_set", data)
        //goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_focal_length_set", data)
    }

    const CameraIRZoom = (sn, z) => {
        if(z<2)z=2;
        if(z>20)z=20;
        const data = {
            "camera_type": "ir",
            "payload_index": device.Camera2,
            "zoom_factor": z
        }
        SendCMD("camera_focal_length_set", data)
       // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_focal_length_set", data)
    }

    const CameraSave=()=>{
        const data = {
            "payload_index": device.Camera2,
            "photo_storage_settings": [
               'current',
            ]
        }
        SendCMD("photo_storage_set", data)
    }

    const VideoSave=()=>{
        const data = {
            "payload_index": device.Camera2,
            "video_storage_settings": [
               'current',
            ]
        }
        SendCMD("video_storage_set", data)
    }

    const CameraIRType=()=> {
   
        let data={}
        data[device.Camera2]={"thermal_current_palette_style": 6}
        let s = {}
        s.bid = getGuid();
        s.tid = getGuid();
       // s.gateway = sn
        s.data = data
        s.timestamp = dayjs().valueOf();
        DoCMD2(`thing/product/${device.SN}/property/set`, s)  
    }

    const CameraJT = (sn, z) => {
        const data = {
            "video_id": `${device.SN2}/${device.Camera2}/normal-0`,
            "video_type": z
        }
        SendCMD("live_lens_change", data)

        setTimeout(CameraIRType, 1000);

       // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
    }

    const CameraPai=(sn)=>{
        // console.log('CameraPai',fj.cameras[0])
        // if(!fj.cameras[0].photo_storage_settings.includes("current")){
        //     //console.log('CameraPai',fj.cameras[0])
        //     CameraSave();
        // }
        const data={"payload_index": device.Camera2}
        SendCMD("camera_photo_take", data)
    }


    const CameraRefrush=(sn,z)=>{
       
        const data={"payload_index": device.Camera2,"reset_mode": z}
        SendCMD("gimbal_reset", data)
    }

    const FlyToPoint = (sn, lat,lng,h1) => {
        HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1)
      //  setIfCMDPanel(false);
    }





    const goPost = async (url, data) => {
        HPost2(url, data)
    }


    // if(fj["home_distance"]>50){
    //     HGet2("/api/v1/WayLine/ToHome?sn="+sn)
    // }   

   // CameraSave();
    return <div><KeyboardEventHandler
        handleKeys={['w', 's', 'a', 'd','f','g','h','q','e', 'r','t','j','l','i','k','z','x','c','v','enter']}
        onKeyEvent={(key,e) => {
            console.log('SendCMD',key);
            if (key == 'w') {
                CameraDrag(sn, 5, 0)
            }
            if (key == 's') {
                CameraDrag(sn, -5, 0)
            }

            // if (key == 'j') {
            //     CameraDrag2(sn, 3)
            // }
            // if (key == 'l') {
            //     CameraDrag2(sn, -3)
            // }
            if (key == 'i') {
               // FlyToPoint(sn, fj.latitude,fj.longitude,fj.height+20)  
               // ChangeHeightPanel(fj.height,(h1)=>FlyToPoint(sn,fj.latitude,fj.longitude,device.Height+h1))
               // setCMDPanel(<ChangeHeightPanel h1={fj.elevation} flyTo={(h1)=>FlyToPoint(sn,fj.latitude,fj.longitude,device.Height+h1)}/>);
               setModal(<ChangeHeightPanel h1={50} flyTo={(h1)=>FlyToPoint(sn,32,124,50+h1)}/>);
               setOpen(true)
            }
            if (key == 'k') {
                FlyToPoint(sn, fj.latitude,fj.longitude,fj.height-10)
            }


            if (key == 'd') {
                CameraDrag(sn, 0, 5)
            }
            if (key == 'a') {
                CameraDrag(sn, 0, -5)
            }
            if (key == 'q') {
                if(isEmpty(fj)){
                    return;
                }
                CameraZoom(sn, fj.cameras[0].zoom_factor*1.5)

            
            }
            if (key == 'e') {

                if(isEmpty(fj)){
                    return;
                }
                CameraZoom(sn, fj.cameras[0].zoom_factor*0.75)

            }

            if (key == 'r') {
                CameraPai(sn);
             //   CameraSave(sn);
               // CameraIRZoom(sn, fj.cameras[0].ir_zoom_factor+1)
                
            }
            if (key == 't') {
                HGet2("/api/v1/WayLine/ToHome?sn="+sn)
            }

            if (key == 'f') {
                CameraJT(sn, "zoom")
            }
            if (key == 'g') {
                CameraJT(sn, "wide")
            }
            if (key == 'h') {
                CameraJT(sn, "ir")
            }
            // if (key == 'z') {
            //     CameraRefrush(sn, 0)
            // }
            // if (key == 'x') {
            //     CameraRefrush(sn, 1)
            // }
            if (key == 'c') {
                CameraRefrush(sn, 0)
            }
            if (key == 'v') {
                CameraRefrush(sn, 1)
            }

            console.log('KeyboardEventHandler', key,fj)
        }} />

       
    </div>
}

export default CameraCtrPanel;