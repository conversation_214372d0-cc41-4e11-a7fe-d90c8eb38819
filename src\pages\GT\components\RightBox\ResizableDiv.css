.resizable {
    position: absolute;
    right: 0;
    height:calc(100vh - 50px);
    /* overflow: hidden; */
    z-index: 999;
    -webkit-user-select: none;
    /* Safari */
    -ms-user-select: none;
    /* IE 10+ 和 Edge */
    user-select: none;
    transition: width 0.4s;
}

.resizeHandle {
    position: absolute;
    left: 0;
    top: 0;
    width: 10px;
    height: 100%;
    cursor: ew-resize;
    /* 水平拖动指针 */
    display: flex;
    justify-content: center;
    align-items: center;
    transform: rotate(180deg);
}

.resizeContent {
    padding: 0 25px 20px 15px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.3);
}

.leftButton {
    position: absolute;
    right: 0;
    /* top: 50vh; */
    margin-right: 15px;
    height: 70vh;
    width: 1px;
    background: #29f2ff;
    z-index: 999;
    cursor: pointer;
    top: calc(50vh - 35vh); 
}

.triangle {
    position: absolute;
    top:50%;
    left:0px;
    width: 0;
    height: 0;
    border-top: 14px solid transparent;
    border-bottom: 14px solid transparent;
    border-left: 14px solid #29f2ff;
}