.default-container {
  padding: 24px;
  color: #fff;
  
  h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #fff;
  }
  
  p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.85);
  }
}

.menuContainer{
  // :global(.ant-menu-item){
  //   flex-direction: column;
  //   height: 55px;
  //   span{
  //     padding: 0;
  //     margin: 0 !important;
  //   }
  // }
}
.mapForSI{
  .border-box-content{
    
  }
}