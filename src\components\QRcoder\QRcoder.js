import QRCode from "qrcodejs2";
import { useEffect, useRef } from "react";
import {useConfigStore} from "@/stores/configStore"

const QRcoder = (props) => {
  let { SN} = props;
  let qrcodeRef = useRef();
  let {config} = useConfigStore.getState();
  let wsUrl = new URL(config.mqtt.wsUrl);
  let host = wsUrl.hostname;  
  let rtmpShareUrl = `http://www.bdzl.unibeidou.com/#/rtmpShare?sn=`;
  useEffect(() => {
    if(host && host == "***************"){
      rtmpShareUrl = `http://www.mzyj.unibeidou.com:11501/#/rtmpShare?sn=`;
    }else{
      rtmpShareUrl = `http://www.bdzl.unibeidou.com:11501/#/rtmpShare?sn=`;
    }
    let text = `${rtmpShareUrl}${SN}`
    console.log("QRCode-text:",text);
    
    qrcodeRef.current.innerHTML = "";
    var qrcode = new QRCode(qrcodeRef.current, {
      text: text,
      width: 100,
      height: 100,
      colorDark: "#264392",
      colorLight: "#fff",
      correctLevel: QRCode.CorrectLevel.H
    });
    return () => {
      qrcode.clear();
    };
  }, [SN]);
  return (
    <>
      <div key={SN?SN:Math.random()} id="qrcode" ref={qrcodeRef}></div>
    </>
  );
};

export default QRcoder;
