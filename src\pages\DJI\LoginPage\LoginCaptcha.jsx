import { LockOutlined, UserOutlined, SafetyOutlined } from "@ant-design/icons";
import { Form, Input, Button, message } from "antd";
import React, { useEffect, useState } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { history, useModel, useSearchParams, useParams } from "umi";
import configStore from "@/stores/configStore";
import styles from "./index.less";
import { HGet2, HPost2 } from "@/utils/request";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Post2, Post3 } from "@/services/general";
import { SM4Util } from "sm4util";

const LoginPage = (props) => {
  const { userLogin = {}, submitting } = props;
  const [load, setIsload] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const params = new URLSearchParams(window.location.search);
  const [captchaValue, setCaptchaValue] = useState(""); //验证码
  let { systemInfos } = configStore();

  //SM4加密
  const GetSM4 = (data) => {
    const sm4 = new SM4Util();
    const miStr1 = sm4.encryptDefault_ECB(data);
    return miStr1;
  };

  const submitData = (data) => {
    if (isEmpty(data)) return;
    if (!isEmpty(data.err)) {
      message.info(data.err);
      if (data.err.includes("睡眠")) {
        return;
      }
      if (data.err.includes("修改")) {
        localStorage.setItem("ID", data.ID);
        history.push("/ChangePwd");
        return;
      }
      return;
    }

    localStorage.setItem("token", data.token);
    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("orgId", data.user.OrgCode);
    localStorage.setItem("orgName", data.user.OrgName);

    if (window.location.href.indexOf("token") > 0) {
      let url = new URL(window.location.href);
      url.searchParams.delete("token");
      url.searchParams.delete("json");
      url.searchParams.delete("system");
      window.location.href = url.href.split("#")[0] + "#/DJ/login";
    } else {
      history.push("/DJ/index");
    }
    console.log(data);
  };
  const handleSubmit = async (e) => {
    const data = await HPost2("/api/v2/User/Login?", {
      Id: e.userName,
      Password: GetSM4(e.password),
      CaptchaID: String(captchaValue.id),
      CaptchaCode: String(e.Captcha),
    });
    console.log("login", data);
    submitData(data);
    GetCaptcha();
  };

  //获取验证码
  const GetCaptcha = async () => {
    const data = await HPost2("/api/v2/User/GetCaptcha");
    setCaptchaValue(data);
  };

  useEffect(() => {
    const getByToken = async () => {
      const params = new URLSearchParams(window.location.search);
      const p1 = params.get("api_token");

      if (isEmpty(p1)) {
        setIsload(true);
        return;
      }
      const data = await HGet2("/api/v2/User/LoginByTokenDFM?token=" + p1);
      submitData(data);
      setIsload(true);
    };

    getByToken();
    GetCaptcha();
    localStorage.removeItem("PageIndexTitle");
  }, []);

  const fff = (
    <div className={styles.appLogin}>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "flex-start",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div
          className={styles.appLoginForm}
          style={{ width: 380, height: 420 }}
        >
          <div
            style={{ width: "100%", textAlign: "center", marginBottom: 12.0 }}
          >
            <h2
              style={{
                fontFamily: "'Roboto', sans-serif", // 使用 Google Fonts 的 Roboto 字体
                fontSize: "24px", // 上面一行字体大小
                color: "#2C3E50", // 深色字体
                lineHeight: "1.5", // 增加行间距
                textShadow: "1px 1px 2px rgba(0, 0, 0, 0.2)", // 添加阴影效果
                margin: 0, // 去掉默认的 margin
                letterSpacing: "2px", // 添加字符间距
              }}
              className="title"
            >
              {systemInfos?.title ? systemInfos?.title : "智慧机巢控制平台"}
            </h2>
          </div>

          <div className="body">
            <Form
              name="basic-login"
              // form={loginForm}
              initialValues={{ remember: true, agreement: true }}
              onFinish={handleSubmit}
              autoComplete="off"
            >
              <Form.Item
                name="userName"
                rules={[{ required: true, message: "请输入用户名" }]}
              >
                <Input
                  size="large"
                  prefix={<UserOutlined className="site-form-item-icon" />}
                  placeholder="账户"
                />
              </Form.Item>
              <Form.Item
                name="password"
                rules={[{ required: true, message: "请输入密码" }]}
              >
                <Input.Password
                  size="large"
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="密码"
                />
              </Form.Item>
              <Form.Item
                name="Captcha"
                rules={[{ required: true, message: "请输入验证码" }]}
                style={{ marginBottom: 16 }} // 添加底部间距以确保布局一致
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <Input
                    size="large"
                    prefix={<SafetyOutlined className="site-form-item-icon" />}
                    placeholder="验证码"
                    style={{ flex: 1 }} // 使输入框充满可用空间
                  />
                  <img
                    style={{ width: 150, height: 75 }}
                    onClick={GetCaptcha}
                    src={captchaValue.image}
                  />
                </div>
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" block size="large">
                  登录
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
        <div
          className={styles.GlyLogo}
          style={{
            backgroundImage: `url(${systemInfos?.backgroundImage})`,
          }}
        ></div>
      </div>
    </div>
  );

  if (!load) {
    return <LoadPanel></LoadPanel>;
  }

  return fff;
};

export default LoginPage;
