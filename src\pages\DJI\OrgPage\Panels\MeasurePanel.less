.tipsBar {
  color: aqua
}

.camerasBar {
  display: flex;
  position: absolute;
  top:20px;
  // left: auto;
  // left: 360px;
  z-index: 1010;
  gap: 1px;
  
}

.camerasBarItem {
  padding: 4px 20px;
  color: #fff;
  font-size: 85%;
	// height: 28px;
  // line-height: 19px;
//   border: #5883d9 solid 1px;
  // background: #091A1B;
  background-image: url('../../../SI/assets/image/btn_primary.png');
  background-size: 100% 100%;
  // border: 1px solid #08E7CB;
  position: relative;
  cursor: pointer;
  // text-shadow: #150062 2px 0 6px;
  z-index: 1001;
}

.camerasBarItem::after,
.camerasBarItem ::before {
  content: "";
  position: absolute;

}

// .camerasBarItem::after {
//   left: 0px;
//   top: 3px;
//   width:0;
// 	height:0;
// 	border-right:4px solid transparent;
// 	border-left:4px solid transparent;
// 	border-bottom:4px solid #5883d9;
// 	transform: rotate(-45deg);
// }
.camerasBarItem:hover{filter: brightness(1.5);} 