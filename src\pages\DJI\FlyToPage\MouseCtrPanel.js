import { useRef, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, CameraZoom } from "./helper";
import { isEmpty, getBodyH } from "@/utils/utils";
import { useModel } from "umi";
import configStore from "@/stores/configStore";
const MouseCtrPanel = ({ child, sn }) => {
  const ref = useRef(null);
  const { DoCMD } = useModel("cmdModel");

  const [sz, setSz] = useState(0);
  const [p1, setP1] = useState({});
  const [p2, setP2] = useState({});
  const [r1, setR1] = useState(0);
  const [r2, setR2] = useState(0);

  const { fjData } = useModel("droneModel");
  const { stopMove } = configStore();

  const device = JSON.parse(localStorage.getItem("device"));
  if (isEmpty(device)) return child;
  if (isEmpty(sn)) return child;

  if (fjData.current.Height < device.Height + 20) {
    return child;
  }


  const SendCMD = (m1, data) => {
    if (isEmpty(m1)) return;
    console.log("CameraCtrPanel", m1, data);
    DoCMD(sn, m1, data);
  };

  const hc = (e) => console.log("MouseCtrPanel", e);

  const onMouseDown = (e) => {
    console.log("xxdasfs", e);
    setP1({ x: e.clientX, y: e.clientY });
  };

  const getDistance = (touches) => {
    if (touches.length != 2) return 0;
    const r2 = Math.pow(
      Math.pow(touches[1].clientX - touches[0].clientX, 2) +
        Math.pow(touches[1].clientY - touches[0].clientY, 2),
      0.5
    );
    return r2;
  };
  const handleTouchStart = (e) => {
    setP1({ x: e.touches[0].clientX, y: e.touches[0].clientY });
    setSz(e.touches.length);
    const r3 = getDistance(e.touches);
    if (r3 > 0) {
      setR1(r3);
    }
  };

  const handleTouchMove = (e) => {
    setP2({ x: e.touches[0].clientX, y: e.touches[0].clientY });
    const r3 = getDistance(e.touches);
    if (r3 > 0) {
      setR2(r3);
    }
  };

  const handleTouchEnd = (e) => {
    if (sz == 1) {
      const x = p2.x - p1.x;
      const y = p2.y - p1.y;
      if (Math.abs(x) > 20 || Math.abs(y) > 20) {
        CameraDrag(SendCMD, y / 5, -x / 5);
      }
    }

    if (sz == 2) {
      const z1 = fjData.current.cameras[0].zoom_factor;
      CameraZoom(SendCMD, (r2 / r1) * z1);
    }
  };

  function debounce(fn, delay) {
    // 设置定时器标识（写在return函数外面，方便下面内部函数获取）
    let timer;
    // 返回函数，不然调用debounce会立即执行此函数
    return function () {
      // fn指定this
      let context = this;
      // fn参数
      let args = arguments;
      // 先清除定时器
      clearTimeout(timer);
      //设置定时器
      timer = setTimeout(() => {
        // 调用函数
        fn.apply(context, args);
      }, delay);
    };
  }

  const changeZoom = (y2) => {
    //
    let xs = 1.5;
    if (y2 < 0) {
      xs = 0.75;
    }
    if (isEmpty(fjData.current?.cameras)) return;
    const z1 = fjData.current?.cameras[0]?.zoom_factor;
    CameraZoom(SendCMD, z1 * xs);
  };

  const HandleWheel = (e) => {
    const y2 = (e.deltaY / 40) * -1;
    console.log("wheel", y2);
    debounce(changeZoom(y2), 100);
    // const z1=fj.data.cameras[0].zoom_factor
    // CameraZoom(SendCMD,z1+y2);
  };

  const onMouseUp = (e) => {
    //拖动摄像头画面
    if (stopMove) return;//条件为真，不会拖动摄像头画面
    const x = e.clientX - p1.x;
    const y = e.clientY - p1.y;
    console.log("MouseCtrPanel", x, y);
    if (Math.abs(x) > 20 || Math.abs(y) > 20)
      CameraDrag(SendCMD, y / 5, -x / 5);
  };

  const onDoubleClick = (e) => {
    //双击摄像头画面
    const x = e.nativeEvent.layerX / ref.current.offsetWidth;
    const y = e.nativeEvent.layerY / ref.current.offsetHeight;
    CamerAIM(SendCMD, x, y);
  };

  return (
    <div
      ref={ref}
      onDoubleClick={onDoubleClick}
      onWheel={HandleWheel}
      onTouchMove={handleTouchMove}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={onMouseDown}
      onMouseUp={onMouseUp}
      onDragEnter={hc}
      onDragLeave={hc}
    >
      {child}
    </div>
  );
};

export default MouseCtrPanel;
