import whiteHotImg from '@/assets/images/white_hot.png'
import blackHotImg from '@/assets/images/black_hot.png'
import redHotImg from '@/assets/images/red_hot.png'
import iceFire from '@/assets/images/ice_fire.png'
import color1 from '@/assets/images/color_1.png'
import color2 from '@/assets/images/color_2.png'
import greenHot from '@/assets/images/green_hot.png'
import ironBow from '@/assets/images/iron_bow_1.png'
import rainBow from '@/assets/images/rain_bow.png'
import rain from '@/assets/images/rain.png'


export function getHexColor(r1,g1,b1,r2,g2,b2,v){
    const v1=v.toFixed(1);
    let r=0;
    let g=0;
    let b=0;

    r=(r2-r1)*v1+r1
    g=(g2-g1)*v1+g1
    b=(b2-b1)*v1+b1
    return 'rgb('+r +','+g+','+b+')'
        //   colors.push( 'rgb('+red +','+green+','+blue+')'); 
        //   red += parseInt(maxRed/colorLevel); 
        //   green += parseInt(maxGreen/colorLevel); 
        //   blue += parseInt(maxBlue/colorLevel); 
      
}

 //export const  MainColor='#1890ff'
 
export const  MainColor='rgba(10,60,185,0.8)'
//export const  MainColor='rgba(0,60,20,0.8)'
  
//export const  MainColor='rgba(0,0,139,0.6)';

export const irColorType =   [
    {value:0,name:"白热",img:whiteHotImg},
    {value:1,name:"黑热",img:blackHotImg},
    {value:2,name:"描红",img:redHotImg},
    {value:3,name:"医疗",img:greenHot},
    {value:5,name:"彩虹1",img:rainBow},
    {value:6,name:"铁红",img:ironBow},
    {value:8,name:"北极",img:iceFire},
    {value:11,name:"熔岩",img:color1},
    {value:12,name:"热铁",img:color2},
    {value:13,name:"彩虹2",img:rain},
]

export function gradientColor(startColor, endColor, proportion = 1.0) {

    // 此处调用了 自定义的 16 进制转换 10 进制的函数
    const startColorDecimalisArray = transfeRgbHex(startColor);
    const endColorDecimalisArray = transfeRgbHex(endColor);

    // 两个颜色的差值数组

    const rgbDifferenceArray = [];

    //endColor 的 rgb 值 分别减掉 startColor 的 rgb 值并分别记录

    for (let index = 0; index < startColorDecimalisArray.length; index++) {

        rgbDifferenceArray.push(endColorDecimalisArray[index] - startColorDecimalisArray[index]);

    }



    // startColor 的 rgb 值 分别加上对应比例的 rgb 差值 得到 结果色值的 rgb 数组

    const resultRgbHexArray = rgbDifferenceArray.map((item, index) => {

        let resultVal;

        resultVal = Math.round(startColorDecimalisArray[index] + item * proportion);

        // 将 10 进制的 值转换为 16 进制

        return resultVal.toString(16);

    });

    // 将 16 进制的 rgb 数组转换为 16进制表示法 hexColor 字符串

    const resultHexColor = `#${resultRgbHexArray.join('')}`;

    return resultHexColor;

}


/**
 
* @description 从 hex值中获取 rgb 颜色信息 并转换为 10 进制
 
* @param {String} hexcolor hex颜色值
 
* @return {Array} 一个包含 用十进制表示 rgba 值的数组[red, green, blue]
 
*/

function transfeRgbHex(hexcolor) {

    //10进制的 rgb 值数组

    const rgbDecimalismArray = [];

    // 两位一组转换为 10 进制

    for (let index = 1; index < 7; index += 2) {

        const hexVal = hexcolor.slice(index, index + 2)

        const decimalismVal = parseInt(hexVal, 16);



        if (index < 7) {

            rgbDecimalismArray.push(decimalismVal);

        }

    }

    return rgbDecimalismArray;

}




