.toggle-map-type-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
}

.toggle-map-type-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  position: relative;

  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
  }

  .map-type-text {
    position: center;
    font-size: 18px;
    font-weight: bold;
    color: #666;
  }
}