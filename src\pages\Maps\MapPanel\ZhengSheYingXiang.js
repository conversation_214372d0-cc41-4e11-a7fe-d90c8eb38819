import React, { useState } from "react";
import { LayersControl } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { isEmpty, getBodyH } from "@/utils/utils";
import L from "leaflet";
import "leaflet-side-by-side";
import { useRef } from "react";
import { useEffect } from "react";
import { Row, Col, Button, List, message, Card } from "antd";
import { Get2, Post2 } from "@/services/general";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import VirtualList from "rc-virtual-list";

const ZhengSheYingXiang = () => {
  const mapRef = useRef({});
  const [layer1, setLayer1] = useState({});
  const layers = useRef({});
  const [mapList, setMapList] = useState([]);
  const [Lat, setLat] = useState();
  const [Lng, setLng] = useState();
  const [ifLoad, setIfLoad] = useState(true);
  const ContainerHeight = getBodyH(166);
  const getMList = async () => {
    let pst = await Get2("/api/v1/MapData/GetAllList");
    if (pst && pst.length > 0) {
      setIfLoad(true);
      setMapList(pst);
    }
  };
  useEffect(() => {
    getMList();
  }, []);
  const onScroll = (e) => {
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          ContainerHeight
      ) <= 1
    ) {
      getMList();
    }
  };
  useEffect(() => {
    mapRef.current = L.map("mapDiv", { attributionControl: false }).setView(
      [Lat ? Lat : 30.06346956642108, Lng ? Lng : 105.31706203454723],
      16
    );
    mapRef.current.on("click", onMapClickTip);
    mapRef.current.createPane("labels");
    mapRef.current.getPane("labels").style.zIndex = 650;
    mapRef.current.getPane("labels").style.pointerEvents = "none";
    // L.control.mousePosition({ position: "bottomleft" }).addTo(mapRef.current);
    mapRef.current.on("dblclick", onMapClickTip);
    function onMapClickTip(e) {
      L.popup()
        .setLatLng(e.latlng)
        .setContent(`此地的经纬度:${e.latlng.lat + "," + e.latlng.lng}`)
        .openOn(mapRef.current);
    }
  }, []);

  const getType = (x1) => {
    if (layer1.name == x1) {
      return "primary";
    } else {
      return "text";
    }
  };

  const data = [];
  const getList = () => {
    mapList?.map((e) => {
      if (e.MapType == 0 || e.MapType == 1) {
        data.push({
          name: e.MapName,
          type: e.MapType,
          layer: L.tileLayer(e.Url, {
            tms: e.MapType === 1 ? true : false,
            maxZoom: e.MaxZoom,
            minZoom: e.MinZoom,
            opacity: 1.0,
            attribution: "",
          }),
          Lat: e.Lat,
          Lng: e.Lng,
        });
      }
    });

    useEffect(() => {
      if (data?.length > 0) {
        // 组件加载后自动选择第一个地图
        const firstMap = data[0];
        onClick(firstMap);
      }
    }, [mapList]);

    const onClick = (item, i) => {
      let center = L.latLng(item.Lat, item.Lng);
      mapRef.current.setView(center);
      setLayer1(item);
      if (!isEmpty(layers.current)) {
        mapRef.current.removeLayer(layers.current);
      }
      item.layer.addTo(mapRef.current);
      layers.current = item.layer;
    };

    const showPage = () => {
      if (!ifLoad) return <LoadPanel></LoadPanel>;
      return (
        <List>
          <VirtualList
            data={data}
            height={ContainerHeight}
            itemHeight={47}
            itemKey="i"
            onScroll={onScroll}
          >
            {(item, i) => (
              <List.Item key={item.ID}>
                <Button
                  type={getType(item.name)}
                  onClick={() => onClick(item, i)}
                  style={{
                    width: "100%",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    borderColor:'#ccc'
                  }}
                >
                  {item.name}
                </Button>
              </List.Item>
            )}
          </VirtualList>
        </List>
      );
    };

    return (
      <>
        <Card title="正射影像列表">{showPage()}</Card>
      </>
    );
  };

  return (
    <div style={{ background: "url(../../../../../assets/img/cbg.png)" }}>
      <Row>
        <Col span={4}>{getList()}</Col>
        <Col span={20}>
          <div
            id="mapDiv"
            style={{ width: "100%", height: "calc(100vh - 47px)" }}
          ></div>
        </Col>
      </Row>
    </div>
  );
};

export default ZhengSheYingXiang;
