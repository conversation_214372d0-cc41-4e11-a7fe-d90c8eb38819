
import {  isEmpty } from "@/utils/utils";
import { useState, useRef, useCallback } from "react";
import { GetMqttClient } from "@/utils/websocket";

export default function eventModel() {

    const mqttC=useRef({})
    const [hms,setHms]=useState({})
    const drcStatus=useRef({})
    const [cloudAuth,setCloudAuth]=useState({})
    const joyReason=useRef('正常飞行')
    const [otaData,setOtaData]=useState({})//固件升级
    
    // 全景拍照事件回调
    const panoramaCallbacks = useRef(new Set());
    const EventMqttConn = useCallback(async(sn) => {
        if (!isEmpty(mqttC.current)) {
            mqttC.current.end();
            setHms({});
            drcStatus.current = {}
            setCloudAuth({})
        }
      
        mqttC.current = await GetMqttClient()
        mqttC.current.on("message", (topic, message) => {
            updateVal(topic, message);
        });
        mqttC.current.unsubscribe('#')
        mqttC.current.subscribe("thing/product/" + sn + "/events");
      
    }, [updateVal, setHms, setCloudAuth]); // 明确声明依赖 updateVal 和状态设置函数
    const joyList=["遥控器失联","低电量返航","低电量降落","靠近限飞区","遥控器夺权（例如：触发了返航，B控夺权）"]

    const updateVal = useCallback((t1, m1) => {
        const xx = JSON.parse(m1);
        console.log('hms', xx);
        if (xx.method == 'hms') {
            setHms(xx.data)
        }
        if (xx.method == 'drc_status_notify') {
            drcStatus.current = xx.data;
        }
        if (xx.method == 'drc_status_notify') {
            console.log('drc', 'mode_code_reason_drc_status_notify', joyList[xx.data.reason]);
                
            joyReason.current = joyList[xx.data.reason];
        }
        if (xx.method == 'joystick_invalid_notify') {
            console.log('drc', 'mode_code_reason_joystick_invalid_notify', joyList[xx.data.reason]);
                
            joyReason.current = joyList[xx.data.reason];
        }
        if (xx.method == "cloud_control_auth_notify") {
            setCloudAuth(xx.data)
            // console.log('hms-cloudAuth赋值',cloudAuth)
        }
        if (xx.method == "ota_progress") {
            //固件升级进度
            console.log('固件升级进度', xx.data)
            setOtaData(xx.data)
        }
        if (xx.method == "camera_photo_take_progress") {
            //全景拍照进度 - 通知所有注册的回调函数
            console.log('全景拍照进度事件', xx.data)
            // 通知所有全景拍照回调
            panoramaCallbacks.current.forEach(callback => {
                try {
                    callback(xx.data);
                } catch (error) {
                    console.error('全景拍照回调执行失败:', error);
                }
            });
        }
    }, [setHms, setCloudAuth, setOtaData]); // 明确声明依赖的状态设置函数

    // 注册全景拍照事件回调
    const registerPanoramaCallback = useCallback((callback) => {
        panoramaCallbacks.current.add(callback);
        return () => {
            // 返回注销函数
            panoramaCallbacks.current.delete(callback);
        };
    }, []);

    // 注销全景拍照事件回调
    const unregisterPanoramaCallback = useCallback((callback) => {
        panoramaCallbacks.current.delete(callback);
    }, []);
    
    return {
        hms,
        drcStatus,
        joyReason, 
        EventMqttConn,
        cloudAuth,
        otaData,
        registerPanoramaCallback,
        unregisterPanoramaCallback
    };
};