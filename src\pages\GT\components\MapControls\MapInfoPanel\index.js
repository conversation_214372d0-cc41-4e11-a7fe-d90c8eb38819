import React from 'react';
import styles from './index.module.less';

const MapInfoPanel = ({ mousePosition = { lat: 0, lng: 0 }, mapType, scaleInfo }) => {
  return (
    <div className={styles['map-info-panel']}>
      <div className={styles['info-item']}>
        <span className={styles.label}>经度:</span>
        <span className={styles.value}>{mousePosition.lng}</span>
      </div>
      <div className={styles['info-item']}>
        <span className={styles.label}>纬度:</span>
        <span className={styles.value}>{mousePosition.lat}</span>
      </div>
      <div className={styles['info-item']}>
        <span className={styles.label}>{mapType === '2D' ? '缩放级别:' : '相机高度:'}</span>
        <span className={styles.value}>
          {mapType === '2D' ? 
            `${scaleInfo}` : 
            `${Math.round(scaleInfo)}米`
          }
        </span>
      </div>
    </div>
  );
};

export default MapInfoPanel;