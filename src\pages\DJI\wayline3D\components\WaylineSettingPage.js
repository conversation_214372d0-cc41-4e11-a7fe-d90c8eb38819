import { useState, useEffect, useRef } from 'react';
import { Input, InputNumber, Checkbox, Radio, Select, Tooltip, Slider } from 'antd';
import { axiosApi } from '@/services/general';
import styles from './index.less';

function WaylineSetting({
    wayline,
    waylineName,
    aircraftSN,
    setAircraftSN,
    aircraftSNList,
    droneSubEnumValueOptions,
    setWaylineName,
    authorChange,
    droneEnumValueChange,
    droneSubEnumValueChange,
    imageFormatChange,
    heightModeChange,
    flyToWaylineModeChange,
    globalHeightChange,
    autoFlightSpeedChange,
    globalTransitionalSpeedChange,
    globalWaypointTurnModeChange,
    globalWaypointHeadingParamChange,
    gimbalPitchModeChange,
    finishActionChange,
    addModel3Dtiles,
    cameraFlyTo
}) {
    // let pst = await Get2("/api/v1/MapData/GetAllList")
    useEffect(() => {
        axiosApi("/api/v1/MapData/GetAllList", "GET",).then((res) => {
            setModelOptions([...res])
        })
    }, []);
    const [model, setModel] = useState(null);
    const [modelOptions, setModelOptions] = useState([]);
    let inputRef = useRef(null)

    function switchingModels(modelUrl) {
        console.log(modelUrl);
        setModel(modelUrl)
        addModel3Dtiles(modelUrl)
    }

    return <div className={styles.waylineSetting} style={{ position: 'absolute', left: -160, top: '120%', borderRadius: 10, zIndex: 3, width: 420, height: 1000, backgroundColor: '#001c1a', padding: '20px', overflowY: 'auto', }}>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>选择模型</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%', }}
                    placeholder={'请选择模型'}
                    value={model}
                    fieldNames={{ label: 'MapName', value: 'Url' }}
                    onChange={(value, option) => {
                        switchingModels(option.Url)
                    }}
                    options={modelOptions}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>航线名称</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Input placeholder="请输入航线名称" value={waylineName} onChange={(e) => { setWaylineName(e.target.value) }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>选择机库</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%', }}
                    placeholder={'请选择机库'}
                    value={aircraftSN}
                    onChange={(value, option) => {
                        console.log(option);
                        cameraFlyTo(option.Lng, option.Lat)
                        setAircraftSN(value)
                    }}
                    options={aircraftSNList}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>选择飞行器</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    value={wayline.missionConfig.droneInfo.droneEnumValue}
                    onChange={(e) => { droneEnumValueChange(e) }}
                    options={[
                        {
                            value: 77,
                            label: 'Mavic 3 行业系列',
                        },
                        {
                            value: 91,
                            label: 'Matrice 3D系列',
                        },
                        {
                            value: 99,
                            label: 'Matrice 4 行业系列',
                        },
                        {
                            value: 100,
                            label: 'Matrice 4D 系列',
                        },
                    ]}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>选择型号</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select style={{ width: '100%' }} value={wayline.missionConfig.droneInfo.droneSubEnumValue} optionLabelProp="label" onChange={(e) => { droneSubEnumValueChange(e) }}>
                    {droneSubEnumValueOptions.map((item, index) => { return <Select.Option value={item.value} label={item.label} key={item.value}>{item.label}</Select.Option> })}
                </Select>
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>拍照设置</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Checkbox.Group value={wayline.Folder.payloadParam.imageFormat} onChange={(e) => { imageFormatChange(e) }}>
                    <Checkbox value={'visable'} style={{ fontSize: '14px', marginRight: '10px' }}>可见光</Checkbox>
                    <Checkbox value={'ir'} style={{ fontSize: '14px', }}>红外光</Checkbox>
                </Checkbox.Group>
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>飞向首航点模式</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Radio.Group value={wayline.missionConfig.flyToWaylineMode} onChange={(e) => { flyToWaylineModeChange(e.target.value) }}>
                    <Radio value={'safely'} style={{ fontSize: '14px', }}>垂直爬升</Radio>
                    <Radio value={'pointToPoint'} style={{ fontSize: '14px', }}>倾斜爬升</Radio>
                </Radio.Group>
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>航线高度模式</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Radio.Group value={wayline.Folder.waylineCoordinateSysParam.heightMode} onChange={(e) => { heightModeChange(e.target.value) }}>
                    {/* <Radio disabled value={'WGS84'} style={{ fontSize: '14px', }}>绝对高度</Radio> */}
                    <Radio value={'relativeToStartPoint'} style={{ fontSize: '14px', }}>相对起点高度</Radio>
                    <Radio value={'aboveGroundLevel'} style={{   fontSize: '14px', }}>相对地面高度</Radio>
                </Radio.Group>
            </div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                    <InputNumber ref={inputRef} min={1} max={1500} value={wayline.Folder.globalHeight} onPressEnter={(e) => { globalHeightChange(Number(e.target.value)); inputRef.current.blur() }} style={{ width: '100%' }} />
                </Tooltip>
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>全局航线速度</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Slider min={1} max={15} value={wayline.Folder.autoFlightSpeed} onChange={(e) => { autoFlightSpeedChange(e) }} style={{ width: '100%' }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>起飞速度</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Slider min={1} max={15} value={wayline.missionConfig.globalTransitionalSpeed} onChange={(e) => { globalTransitionalSpeedChange(e) }} style={{ width: '100%' }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>航点类型</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    value={wayline.Folder.globalWaypointTurnMode}
                    onChange={(e) => { globalWaypointTurnModeChange(e) }}
                    options={[
                        {
                            value: 'coordinateTurn',
                            label: '协调转弯，不过点，提前转弯',
                        },
                        {
                            value: 'toPointAndStopWithDiscontinuityCurvature',
                            label: '直线飞行，飞行器到点停',
                        },
                        {
                            value: 'toPointAndStopWithContinuityCurvature',
                            label: '曲线飞行，飞行器到点停',
                        },
                        {
                            value: 'toPointAndPassWithContinuityCurvature',
                            label: '曲线飞行，飞行器过点不停',
                        },
                    ]}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>飞行器偏航角模式</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    value={wayline.Folder.globalWaypointHeadingParam.waypointHeadingMode}
                    onChange={(e) => { globalWaypointHeadingParamChange(e) }}
                    options={[
                        {
                            value: 'followWayline',
                            label: '沿航线方向',
                        },
                        {
                            value: 'manually',
                            label: '手动控制',
                        },
                        {
                            value: 'fixed',
                            label: '锁定当前偏航角',
                        },
                        {
                            value: 'smoothTransition',
                            label: '自定义',
                        },
                    ]}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>航点间云台俯仰角控制模式</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    value={wayline.Folder.gimbalPitchMode}
                    onChange={(e) => { gimbalPitchModeChange(e) }}
                    options={[
                        {
                            value: 'manual',
                            label: '手动控制',
                        },
                        {
                            value: 'fixed',
                            label: '依照每个航点设置',
                        },
                    ]}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '20px', backgroundColor: '#232323', borderRadius: '10px', marginTop: '20px' }}>
            <div style={{ fontSize: '14px', }}>完成动作</div>
            <div style={{ fontSize: '14px', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    value={wayline.missionConfig.finishAction}
                    onChange={(e) => { finishActionChange(e) }}
                    options={[
                        {
                            value: 'goHome',
                            label: '自动返航',
                        },
                        {
                            value: 'noAction',
                            label: '退出航线模式',
                        },
                        {
                            value: 'autoLand',
                            label: '原地降落',
                        },
                        {
                            value: 'gotoFirstWaypoint',
                            label: '返回航线起始点悬停',
                        },
                    ]}
                />
            </div>
        </div>
    </div>
}
export default WaylineSetting;