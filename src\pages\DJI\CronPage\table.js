import { Space, Tag, message,Modal,Switch } from 'antd';
import { getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { getGuid,  timeFormat22 } from '@/utils/helper';
import { HGet2, HPost2 } from '@/utils/request';
import { Post2 } from '@/services/general';
const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}
const { confirm } = Modal;
const onChange=(e)=>{
  console.log('ddd',e);
  if(e){
    updateCron()
  }
}
const updateCron=async(record,refrush)=>{
  if(isEmpty(record)) return;
  const xx=await HGet2("/api/v1/CronJob/ChangeState?id="+record.ID);
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("更新成功！")
    refrush();
  }
}

const deleteCronJob=async(record,refrush)=>{
  const xx=await Post2("/api/v1/CronJob/Delete",record);
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除航线',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该航线吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteCronJob(record,refrush);
    },
    onCancel() {
     // console.log('Cancel');
    },
  });
};

const TableCols =(refrush,showMap)=>{return [

  {
    title: getTableTitle('所属机场'),
    dataIndex: 'SN',
    key: 'SN',
    align:'center',
    render: (record) => (
      getDeviceName(record)
     )
   // width:200,
  },

          {
            title: getTableTitle('任务名称'),
            dataIndex: 'CName',
            key: 'CName',
            align:'center',
           // width:200,
          },
          // {
          //   title: getTableTitle('所属机场'),
          //   dataIndex: 'WayLineName',
          //   key: 'WayLineName',
          //   align:'center',
          // //  width:300,
          // //  className: 'table-header-cell'
          // },
          {
            title: getTableTitle('航线名称'),
            dataIndex: 'FLightName',
            key: 'FLightName',
            align:'center',
          //  width:300,
          //  className: 'table-header-cell'
          },
          {
            title: getTableTitle('执行时间'),
            dataIndex: 'JobTM',
            key: 'JobTM',
            align:'center',
            render: (record) => (
              timeFormat22(record)
             )
          //  width:200,
          },
          {
             title: getTableTitle('执行频率'),
            dataIndex: 'DT',
            key: 'DTD',
            align:'center',
            render: (record) => (
              (record/1000/60/60)+" 小时"
             )
         //   width:200,
          },
          {
            title: getTableTitle('任务状态'),
           align:'center',
           
           render: (record) => (
               <Switch size='small' key={getGuid()} defaultChecked={record.State>0} onChange={()=>{updateCron(record,refrush)}} />
            )
        //   width:200,
         },
        {
             title: getTableTitle('操作'),
            align:'center',
            render: (record) => (
              <Space size="middle">
                <Tag><a onClick={()=>showMap(record)}>航线点位</a></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除</a></Tag>
              </Space>)
          }];
        }



export default TableCols;
