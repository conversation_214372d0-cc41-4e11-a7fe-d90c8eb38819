import { useEffect, useRef, useState, useCallback } from 'react'
import L from 'leaflet'
import 'leaflet-side-by-side'
import './LeafletSplit.less'

/**
 * Leaflet卷帘对比组件
 * 实现左右分屏对比不同图层的功能
 */
const LeafletSplit = ({
  map,
  enabled = false,
  onSplitPositionChange,
  leftLayers = [],
  rightLayers = [],
  splitPosition = 0.5
}) => {
  const sideBySideRef = useRef(null)
  const previewLineRef = useRef(null)
  const [currentPosition, setCurrentPosition] = useState(splitPosition)
  const [showPreviewLine, setShowPreviewLine] = useState(false)

  // 创建预览分割线
  const createPreviewLine = useCallback(() => {
    if (!map || previewLineRef.current) return

    const mapContainer = map.getContainer()
    const previewLine = document.createElement('div')
    previewLine.className = 'leaflet-split-preview-line'

    mapContainer.appendChild(previewLine)
    previewLineRef.current = previewLine
  }, [map, currentPosition])

  // 移除预览分割线
  const removePreviewLine = useCallback(() => {
    if (previewLineRef.current) {
      previewLineRef.current.remove()
      previewLineRef.current = null
    }
  }, [])

  // 更新预览分割线位置
  const updatePreviewLinePosition = useCallback(() => {
    if (previewLineRef.current) {
      previewLineRef.current.style.left = `${currentPosition * 100}%`
    }
  }, [currentPosition])

  // 初始化卷帘功能
  useEffect(() => {
    if (!map || !enabled) {
      // 清理现有的side-by-side控件和预览线
      resetAllLayersSplitDirection()
      removePreviewLine()
      setShowPreviewLine(false)
      return
    }

    // 检查是否有图层需要显示
    const hasLayers = leftLayers.length > 0 || rightLayers.length > 0

    if (!hasLayers) {
      // 没有图层时显示预览分割线
      setShowPreviewLine(true)
      createPreviewLine()
    } else {
      // 有图层时隐藏预览分割线，更新图层分割方向
      setShowPreviewLine(false)
      removePreviewLine()
      updateLayersSplitDirection()
    }

    return () => {
      // 清理函数
      resetAllLayersSplitDirection()
      removePreviewLine()
    }
  }, [map, enabled, leftLayers, rightLayers, resetAllLayersSplitDirection, updateLayersSplitDirection, createPreviewLine, removePreviewLine])

  // 创建side-by-side控件
const createSideBySideControl = useCallback((leftInstances, rightInstances) => {
  if (!map) return

  // 清理现有控件
  if (sideBySideRef.current) {
    sideBySideRef.current.remove()
    sideBySideRef.current = null
  }

  // 确保有图层可以对比
  if (leftInstances.length === 0 && rightInstances.length === 0) {
    console.warn('没有图层可以进行卷帘对比')
    return
  }

  // 隐藏预览分割线（因为即将创建真正的side-by-side控件）
  removePreviewLine()
  setShowPreviewLine(false)

  // 确保图层已添加到地图（side-by-side需要图层在地图上）
  leftInstances.forEach(layer => {
    if (!map.hasLayer(layer)) {
      map.addLayer(layer)
    }
  })
  rightInstances.forEach(layer => {
    if (!map.hasLayer(layer)) {
      map.addLayer(layer)
    }
  })

  // 创建side-by-side控件
  try {
    let leftLayerParam = null
    let rightLayerParam = null

    // 处理左侧图层参数
    if (leftInstances.length === 1) {
      leftLayerParam = leftInstances[0]
    } else if (leftInstances.length > 1) {
      leftLayerParam = leftInstances // 直接传递数组
    }

    // 处理右侧图层参数
    if (rightInstances.length === 1) {
      rightLayerParam = rightInstances[0]
    } else if (rightInstances.length > 1) {
      rightLayerParam = rightInstances // 直接传递数组
    }

    // 如果只有一侧有图层，另一侧传null或空数组
    if (!leftLayerParam && rightLayerParam) {
      leftLayerParam = []
    } else if (leftLayerParam && !rightLayerParam) {
      rightLayerParam = []
    }

    if (leftLayerParam !== null || rightLayerParam !== null) {
      sideBySideRef.current = L.control.sideBySide(leftLayerParam, rightLayerParam, {
        thumbSize: 42,
        padding: 0
      }).addTo(map)

      // 设置初始位置
      if (sideBySideRef.current.setPosition) {
        sideBySideRef.current.setPosition(currentPosition)
      }

    } else {
      console.warn('无法创建side-by-side控件：缺少有效图层')
    }
  } catch (error) {
    console.error('创建side-by-side控件失败:', error)
  }

}, [map, currentPosition, removePreviewLine, setShowPreviewLine])

  // 根据ID查找图层
  const findLayerById = useCallback((layerId) => {
    if (!map) return null

    let foundLayer = null
    let allLayers = []

    // 遍历地图上的所有图层
    map.eachLayer((layer) => {
      // 收集所有图层信息用于调试
      allLayers.push({
        _layerId: layer._layerId,
        id: layer.id,
        optionsLayerId: layer.options?.layerId,
        optionsId: layer.options?.id,
        optionsLayers: layer.options?.layers,
        constructor: layer.constructor.name
      })

      // 检查图层是否有我们设置的ID标识
      if (layer._layerId === layerId ||
          layer.id === layerId ||
          layer.options?.layerId === layerId ||
          layer.options?.id === layerId ||
          layer.options?.layers === layerId) { // 支持WMS图层的layers参数
        foundLayer = layer
        return false // 停止遍历
      }
    })

    if (!foundLayer) {
      console.warn(`LeafletSplit: 未找到图层 ${layerId}`)
    } else {
      // console.log(`LeafletSplit: 找到图层 ${layerId}`, foundLayer)
    }

    return foundLayer
  }, [map])

  // 重置所有图层的分割状态（Leaflet中主要是移除side-by-side控件）
  const resetAllLayersSplitDirection = useCallback(() => {
    if (sideBySideRef.current) {
      sideBySideRef.current.remove()
      sideBySideRef.current = null
    }

    // 恢复所有图层到地图上
    if (map) {
      const allLayerIds = [...leftLayers, ...rightLayers]
      allLayerIds.forEach(layerId => {
        const layer = findLayerById(layerId)
        if (layer && !map.hasLayer(layer)) {
          map.addLayer(layer)
          console.log(`恢复图层到地图: ${layerId}`)
        }
      })
    }
  }, [leftLayers, rightLayers, findLayerById, map])

  // 更新图层的分割方向（在Leaflet中通过重新创建side-by-side控件实现）
  const updateLayersSplitDirection = useCallback(() => {
    if (!map || !enabled) return

    const updateWithRetry = (layerId, side, retryCount = 0) => {
      const layer = findLayerById(layerId)
      if (layer) {
        return layer
      } else if (retryCount < 3) {
        // 如果没找到图层，可能是异步加载中，延迟重试
        console.log(`${side}侧图层 ${layerId} 未找到，${500 * (retryCount + 1)}ms后重试 (${retryCount + 1}/3)`)
        setTimeout(() => {
          updateWithRetry(layerId, side, retryCount + 1)
        }, 500 * (retryCount + 1))
      } else {
        console.warn(`${side}侧图层 ${layerId} 重试3次后仍未找到`)
      }
      return null
    }

    // 获取左右侧图层
    const leftInstances = leftLayers.map(layerId => updateWithRetry(layerId, '左')).filter(Boolean)
    const rightInstances = rightLayers.map(layerId => updateWithRetry(layerId, '右')).filter(Boolean)

    // 重新创建side-by-side控件
    if (leftInstances.length > 0 || rightInstances.length > 0) {
      createSideBySideControl(leftInstances, rightInstances)
    }
  }, [map, enabled, leftLayers, rightLayers, findLayerById, createSideBySideControl])

  // 更新分割位置
  useEffect(() => {
    setCurrentPosition(splitPosition)
    if (sideBySideRef.current && sideBySideRef.current.setPosition) {
      sideBySideRef.current.setPosition(splitPosition)
    }
    // 同时更新预览分割线位置
    updatePreviewLinePosition()
  }, [splitPosition, updatePreviewLinePosition])

  // 监听side-by-side控件的位置变化（如果插件支持）
  useEffect(() => {
    if (sideBySideRef.current && onSplitPositionChange) {
      // 尝试监听插件的位置变化事件
      const handlePositionChange = (e) => {
        if (e && e.position !== undefined) {
          onSplitPositionChange(e.position)
        }
      }

      // 如果插件支持事件监听，添加监听器
      if (sideBySideRef.current.on) {
        sideBySideRef.current.on('positionchange', handlePositionChange)

        return () => {
          if (sideBySideRef.current && sideBySideRef.current.off) {
            sideBySideRef.current.off('positionchange', handlePositionChange)
          }
        }
      }
    }
  }, [sideBySideRef.current, onSplitPositionChange])

  return null
}

export default LeafletSplit