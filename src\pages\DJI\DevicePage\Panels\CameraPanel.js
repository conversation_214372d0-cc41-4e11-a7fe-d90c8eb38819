import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import camera4 from "@/assets/drcImgs/camera4.png";
import camera4svg from "@/assets/drcImgs/camera4.svg";
import camera3svg from "@/assets/drcImgs/camera3.svg";
import cameraPanorama from "@/assets/drcImgs/cameraPanorama.svg";
import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import video4 from "@/assets/drcImgs/video4.png";
import { getBodyH, isEmpty } from "@/utils/utils";
import { useEffect, useState, useMemo } from "react";
import { useModel } from "umi";
import { getMinstr } from "../../DRCPage/Panels/helper";
import HanHuaPage from "@/pages/DJI/DevicePage/Panels/HanHuaQi";
import styles from "./CameraPanel.less";
import { checkIfFlyer } from "@/utils/utils";
import { Tooltip, Modal, Slider, Radio, Row, Col, Space, Button, message } from "antd";
import light1 from "@/assets/drcImgs/light1.png"
import light1svg from "@/assets/drcImgs/light1.svg"
import { getMqttTcpAddress } from "@/utils/config";
import DRCXinTiao from '@/pages/DJI/DRCPage/Panels/DrcXinTiao';
import PanoramaProgressModal from "@/components/PanoramaProgressModal";

const CameraPanel = () => {
  const w1 = 60;
  const h1 = 240;
  const { fj, fjData } = useModel("droneModel");
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  
  // 使用 useMemo 缓存 device 对象
  const device = useMemo(() => {
    const deviceStr = localStorage.getItem("device");
    return deviceStr ? JSON.parse(deviceStr) : null;
  }, []);
  const [ifVideo, setIfVideo] = useState(false);
  // 添加本地Modal状态
  const [localModalOpen, setLocalModalOpen] = useState(false);
  const { setModal, setOpen } = useModel("pageModel");
  const { lightPSDKIndex,lightData} = useModel("drcModel");
  const [isLightModalOpen, setIsLightModalOpen] = useState(false);
  const [lightBright, setlightBright] = useState(50);
  const [lightMode, setLightMode] = useState(2);
  
  // 全景拍照相关
  const {
    panoramaState,
    startPanorama,
    stopPanorama,
    setCurrentDevice,
    PanoramaMqttConn
  } = useModel("panoramaModel");

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);

    // if (v == 1) {
    //   VideoSave();
    // } else {
    //   CameraSave();
    // }
  };

  const photoCamera = (v) => {
    const data = { payload_index: device.Camera2 };
    if (fj.data.cameras[0].camera_mode == 0) {
      DoCMD(device.SN, "camera_photo_take", data);
    } else {
      changeCamera(0);
      setTimeout(() => {
        DoCMD(device.SN, "camera_photo_take", data);
      }, 1000);
    }
  };

  const VideoStart = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_start", data);
  };

  const VideoStop = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_stop", data);
  };

  const VideoClick = () => {
    if (fj.data.cameras[0].recording_state == 1) {
      VideoStop();
      setIfVideo(false);
    } else {
      changeCamera(1);
      setTimeout(() => {
        VideoStart();
        setIfVideo(true);
      }, 1000);
    }
  };

  const CameraSave = (z) => {
    const data = {
      payload_index: device.Camera2,
      photo_storage_settings: [z],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const VideoSave = () => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["current"],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const getVideoImg = () => {
    if (ifVideo) return video3;
    return video4;
  };

  // 喊话器功能 - 使用本地Modal状态
  const onHanHuaQi = () => {
    setLocalModalOpen(true);
  };

  const onPanorama = () => {
    console.log("开始全景拍照");
    
    // 检查设备信息
    if (!device || !device.SN || !device.Camera2) {
      message.error('设备信息不完整，无法开始全景拍照');
      return;
    }
    
    // 检查是否已经在进行全景拍照
    if (panoramaState.isActive) {
      message.warning('全景拍照正在进行中，请等待完成');
      return;
    }
    
    // 设置当前设备并开始全景拍照
    setCurrentDevice(device);
    startPanorama(device);
  };

  useEffect(() => {
    //
    if (isEmpty(fjData.current)) return;
    if (isEmpty(fjData.current.cameras)) return;
    if (fjData.current.cameras[0].recording_state == 1) {
      setIfVideo(false);
    } else {
      setIfVideo(true);
    }
  }, [fjData.current]);

  // 初始化全景拍照功能
  useEffect(() => {
    if (device && device.SN) {
      // 设置当前设备
      setCurrentDevice(device);
      // 连接MQTT监听全景拍照事件
      PanoramaMqttConn(device.SN);
    }
  }, [device, setCurrentDevice, PanoramaMqttConn]);


  // useEffect(()=>{
  //   beginX()
  // },[])


  const beginX = () => {
    const data = {
      hsi_frequency: 1,
      mqtt_broker: {
        address: getMqttTcpAddress(),
        client_id: device.SN2,
        enable_tls: false,
        expire_time: 1872744922,
        password: "",
        username: "",
      },
      osd_frequency: 10,
    };
    DoCMD(device.SN, "drc_mode_enter", data);
    console.log('状态上报---beginX222',data)
  };

  const onLight = ()=>{

    // 赋初始值
    setLightMode(lightData?.work_mode==0?2:lightData.work_mode+1)
    setlightBright(lightData.brightness||50)
     console.log("状态上报-打开探照灯111",lightData)

    if(lightData.work_mode == 0){
      // 第一次打开探照灯
      beginX()
      setLight()
      console.log("状态上报-打开探照灯222")
    }
    setIsLightModalOpen(true)
  }

  const handleLightCancel = () => {
    setIsLightModalOpen(false);
  };

  const setLight = ()=>{
    console.log('探照灯',lightPSDKIndex)
    if(lightPSDKIndex==0){
      message.info("未获取到探照灯psdk_index")
      return
    }
    onLightModeChange(2)

    onLightBrightChange(50)


  }

   const onLightBrightChange = (newValue) => {
    setlightBright(newValue);
    let s1 ={}
    s1.method="drc_light_brightness_set"
    s1.data = {
      psdk_index: lightPSDKIndex,
      group: 0,
      brightness: newValue
    }
    DoCMD2(`thing/product/${device.SN}/drc/down`,s1)
  };

  const onLightModeChange = (e ) => {
    // console.log('状态上报---onLightModeChange111111',e)
    setLightMode( typeof e =='number'?e: e?.target?.value);
    let s ={}
    let mode = typeof e =='number'?e: e?.target?.value
    s.method="drc_light_mode_set"
    s.data = {
      psdk_index: lightPSDKIndex,
      group: 0,
      mode: mode-1
    }
    console.log('状态上报---onLightModeChange',s)
    DoCMD2(`thing/product/${device.SN}/drc/down`,s)
  };

  const onLightTurnSet =(which,action)=>{
    let s ={}
    s.method="drc_light_fine_tuning_set"
    s.data = {
      psdk_index: lightPSDKIndex,
      position: which=="right"?1:0,
      saved:false,
      value: action=="add"?1:-1
    }
    DoCMD2(`thing/product/${device.SN}/drc/down`,s)
  }

  //fj.data.cameras[0].camera_mode=0
  if (isEmpty(fj)) return;
  if (isEmpty(fj.data)) return;
  if (isEmpty(fj.data.cameras)) return;
  if (isEmpty(fj.data.cameras[0])) return;
  

  return (
    <>
      {/* 全景拍照进度弹窗 */}
      <PanoramaProgressModal
        visible={panoramaState.visible}
        progress={panoramaState.progress.percent}
        currentStep={panoramaState.progress.current_step}
        status={panoramaState.progress.status}
        onCancel={stopPanorama}
      />
      
      <div className={styles.camerasTool} style={{ top: null, bottom: 160 }}>
        <DRCXinTiao device={device} DoCMD2={DoCMD2}></DRCXinTiao>
        <div className={styles.items}>
          <Tooltip placement="left" title="探照灯">
                <img
                  draggable={false}
                  src={light1svg}
                  onClick={() => {
                    if (checkIfFlyer()) {
                      onLight();
                    }
                  }}
                ></img>
          </Tooltip>
        </div>
        <div className={styles.items}>
          <Tooltip placement="left" title="喊话器">
            <img
              draggable={false}
              src={camera4svg}
              onClick={() => {
                if (checkIfFlyer()) {
                  onHanHuaQi();
                }
              }}
            ></img>
          </Tooltip>
        </div>
        <div className={styles.items}>
          <Tooltip placement="left" title="全景拍照">
            <img
              draggable={false}
              src={cameraPanorama}
              onClick={() => {
                if (checkIfFlyer()) {
                  onPanorama();
                }
              }}
            ></img>
          </Tooltip>
        </div>
        <div className={styles.items}>
          <Tooltip placement="left" title="拍照">
            <img
              draggable={false}
              src={camera3svg}
              onClick={() => {
                if (checkIfFlyer()) {
                  photoCamera();
                }
              }}
            ></img>
          </Tooltip>
        </div>
      </div>
      <div className={styles.camerasTool} style={{ top: null, bottom: 10 }}>
        <div className={styles.items}>
          <Tooltip
            placement="left"
            title={
              fj.data.cameras[0].recording_state == 0 ? "开始录像" : "结束录像"
            }
          >
            <img
              draggable={false}
              src={getVideoImg()}
              onClick={() => {
                if (checkIfFlyer()) {
                  VideoClick();
                }
              }}
            ></img>
          </Tooltip>
        </div>
        <div className={styles.camerasNums}>
          {getMinstr(fj.data.cameras[0].record_time)}
        </div>
      </div>
      
      {/* 本地Modal */}
      <Modal
        title="喊话器"
        open={localModalOpen}
        onCancel={() => {
          setLocalModalOpen(false);
        }}
        footer={null}
        width={800}
        style={{ top: 150 }}
      >
        <HanHuaPage 
          device={device} 
          setOpen={setLocalModalOpen}
        />
        </Modal>


      <Modal
        open={isLightModalOpen}
        onCancel={handleLightCancel}
        destroyOnClose={true}
        footer={null}
      >
          <div>
          <h3>亮度设置:</h3>
            <Slider
                min={1}
                max={100}
                onChange={onLightBrightChange}
                value={typeof lightBright === 'number' ? lightBright : 0}
              />
          <h3>模式设置:</h3>
          <Radio.Group
            onChange={onLightModeChange}
            value={lightMode}
            options={[
              {
                value: 1,
                label: "关闭",
              },
              {
                value: 2,
                label: "常量",
              },
              {
                value: 3,
                label: "爆闪",
              },
              {
                value: 4,
                label: "快速爆闪",
              },
              {
                value: 5,
                label: "交替爆闪",
              },
            ]}
          />
          <h3 style={{marginTop:"10px"}}>角度微调:</h3>
          <Row>
            <Col span={12}>
              <Space>
                <Button type="primary" onClick={()=>onLightTurnSet('left','add')}>左灯向右</Button>
                <Button type="primary" onClick={()=>onLightTurnSet('left','reduce')}>左灯向左</Button>
              </Space>
            </Col>
            <Col span={12}>
                <Space>
                <Button type="primary" onClick={()=>onLightTurnSet('right','add')}>右灯向右</Button>
                <Button type="primary" onClick={()=>onLightTurnSet('right','reduce')}>右灯向左</Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Modal>
    </>
  );
};

export default CameraPanel;
