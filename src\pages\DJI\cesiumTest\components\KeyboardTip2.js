import { useState, useEffect, useRef } from 'react';
import { Tooltip } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

function KeyboardTip2({  }) {

    return <div style={{ position: 'absolute', bottom: 50, left: 150, width: 50, height: 150, zIndex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.6)', borderRadius: '10px' }}>
        <Tooltip placement="top" title='按C键飞机向上平移'>
            <div style={{ height: '45%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center',  borderRadius: '10px', color: 'white', fontSize: 18, fontWeight: 600 }}>
                <div style={{ height: '80%', width: '65%', cursor: 'pointer' }}>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><ArrowUpOutlined /></div>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>C</div>
                </div>
            </div>
        </Tooltip>
        <Tooltip placement="bottom" title='按Z键飞机向下平移'>
            <div style={{ height: '45%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', borderRadius: '10px', color: 'white', fontSize: 18, fontWeight: 600 }}>
                <div style={{ height: '80%', width: '65%', cursor: 'pointer' }}>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>Z</div>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><ArrowDownOutlined /></div>
                </div>
            </div>
        </Tooltip>
    </div>
}
export default KeyboardTip2;