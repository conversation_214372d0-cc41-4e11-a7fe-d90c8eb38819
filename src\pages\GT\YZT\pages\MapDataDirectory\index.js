import React, { useState, useEffect, useMemo } from 'react';
import { Layout, Tree, Button, Form, Input, Select, Table, Modal, message, Tabs, Space, Popconfirm, Dropdown, Menu, Checkbox, Upload, Row, Col, Tooltip, TreeSelect } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined, GlobalOutlined, DownOutlined, QuestionCircleOutlined, UploadOutlined, ReloadOutlined } from '@ant-design/icons';
import './index.less';
import { serviceTypeList } from '@/pages/GT/components/Map/mapServiceConfig';
import api from '@/pages/GT/utils/api';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { TabPane } = Tabs;

// 服务类型名称映射
const getServiceTypeName = (type) => {
  if (type === 'folder') return '文件夹';
  const serviceType = serviceTypeList.find(item => item.value.toLowerCase() === type.toLowerCase());
  return serviceType ? serviceType.label : type;
};

// ArcGIS动态地图服务才能保存子图层
const arcgisTypes = ['ArcGISDynamicMapServiceLayer', 'ArcGISTiledMapServiceLayer', 'ArcGISFeatureMapServiceLayer'];

export default function MapDataDirectory() {
  const [serviceForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [catalogName, setCatalogName] = useState('');
  const [selectedParentId, setSelectedParentId] = useState(null);
  const [mapServices, setMapServices] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [currentService, setCurrentService] = useState(null);
  const [modalType, setModalType] = useState('view'); // add, edit, view

  // 初始化数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('fetchData');
        const data = await api.queryDataDirectory();
        console.log('data', data);
        setMapServices(data);
        // 默认展开第一级目录
        if(data.length > 0) {
          setExpandedKeys(data.map(item => item.Rid));
        }
      } catch (error) {
        message.error('获取数据目录失败: ' + error.message);
      }
    };
    fetchData();
  }, []);

  // 初始化表单值
  useEffect(() => {
    if (currentService) {
      serviceForm.setFieldsValue({
        name: currentService.name,
        expandedNode: true,
        internalType: '否',
        serviceTypeName: currentService.serviceTypeName,
        servicePath: currentService.servicePath || '',
        secureAccess: false,
        useConfig: false,
        status: ['运行状态未知'],
        autoStart: false,
        mapUseRange: ['2D'],
        isUseExpandParam: ['配置', '启用配置'],
        editUrl: '', 
        bootLoad: currentService.bootLoad, 
        imageSrc: currentService.thumbnail || '',
        dataType: currentService.dataType,
        parentId: currentService.parentId,
      });
    }
  }, [currentService]);

  // 处理树节点展开/收起
  const onExpand = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 处理树节点选择
  const onSelect = (selectedKeysValue, info) => {
    setSelectedKeys(selectedKeysValue);
    if (selectedKeysValue.length > 0) {
      const node = findNodeById(mapServices, selectedKeysValue[0]);
      if (node) {
        setCurrentService({
          id: node.Rid,
          name: node.Name,
          type: node.Attribute.dataType === "" || node.Attribute.dataType === "1" ? 'folder' : node.Attribute?.serviceTypeName,
          ...node.Attribute
        });
      }
    } else {
      setCurrentService(null);
    }
  };

  // 根据ID查找节点
  const findNodeById = (data, id) => {
    for (let item of data) {
      if (item.Rid === id) return item;
      if (item.Children) {
        const found = findNodeById(item.Children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 删除目录或服务
  const handleDelete = async (Rid) => {
    try {
      await api.directoryDelete(Rid);

      const newData = await api.queryDataDirectory();

      setMapServices(newData);

      if (currentService?.id === Rid) {
        setCurrentService(null);
        setSelectedKeys([]);
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败: ' + error.message);
    }
  };

  // 保存服务
  const handleSaveService = (type) => {
    serviceForm.validateFields().then(async values => {
      const { 
        name, 
        expandedNode, 
        internalType, 
        serviceTypeName, 
        servicePath, 
        editUrl, 
        bootLoad, 
        mapUseRange, 
        isUseExpandParam, 
        imageSrc, 
      } = values;
      
      const data = {
        name,
        expandedNode,
        internalType,
        serviceTypeName,
        servicePath,
        editUrl,
        bootLoad: bootLoad,
        mapUseRange,
        isUseExpandParam,
        thumbnail: imageSrc,
        dataType: currentService.dataType,
        parentId: currentService.parentId, 
      };

      try {
        if (type === 'add') {
          // 调用API新增数据
          await api.addAndUpdate(data);
          
          if (data.dataType === 2 && data.internalType === 2 && arcgisTypes.includes(data.serviceTypeName)) {
            // 如果是ArcGIS动态地图服务，调用添加子图层接口
            await api.addMapChildLayer({
              ...data,
            });
          }
          
          // 刷新数据目录
          const newData = await api.queryDataDirectory();
          setMapServices(newData);
          
          message.success('新增成功');
        } else if (type === 'edit') {
          console.log('保存服务2', data);
          // 更新数据
          await api.addAndUpdate({
            ...data,
            rid: currentService.id
          });
          
          // 刷新数据目录
          const newData = await api.queryDataDirectory();
          setMapServices(newData);
          
          message.success('更新成功');
        }
        
        setModalType('view');
      } catch (error) {
        message.error('操作失败: ' + error.message);
      }
    });
  };

  // 渲染树节点
  const renderTreeNodes = (data) => {
    return data.map(item => {
      if (item.Attribute.dataType === "" || item.Attribute.dataType === "1") {
        return (
          <Tree.TreeNode 
            key={item.Rid} 
            title={
              <div className="tree-node-title">
                <FolderOutlined /> {item.Name}
                <div className="tree-node-actions">
                  <Popconfirm
                    title="确定要删除此文件夹及其所有服务吗？"
                    onConfirm={() => {
                      console.log('触发删除文件夹确认', item.Rid);
                      handleDelete(item.Rid);
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<DeleteOutlined />} 
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </div>
              </div>
            }
          >
            {item.Children && renderTreeNodes(item.Children)}
          </Tree.TreeNode>
        );
      }
      
      return (
        <Tree.TreeNode 
          key={item.Rid} 
          title={
            <div className="tree-node-title">
              <GlobalOutlined /> {item.Name}
              <div className="tree-node-actions">
                <Popconfirm
                  title="确定要删除此服务吗？"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    console.log('触发删除服务确认', item.Rid);
                    handleDelete(item.Rid);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Popconfirm>
              </div>
            </div>
          }
        />
      );
    });
  };

  // 渲染新增下拉菜单
  const addMenu = (
    <Menu
      items={[
        {
          key: 'folder',
          label: '新增目录',
          onClick: () => setIsModalVisible(true)
        },
        {
          key: 'service',
          label: '新增图层服务',
          onClick: () => {
            if (!selectedKeys.length || !findNodeById(mapServices, selectedKeys[0])?.Attribute?.dataType?.match(/^[1]?$/)) {
              message.error('请先选择一个目录节点');
              return;
            }
            setModalType('add');
            serviceForm.resetFields();
            setCurrentService({
              type: 'service',
              name: '',
              dataType: '2',
              parentId: currentService.rid,
              mapUseRange: ['2D'],
              isUseExpandParam: ['配置', '启用配置'],
              bootLoad: 0,
              imageSrc: '',
            });
          }
        }
      ]}
    />
  );

  // 渲染新增目录Modal
  const renderAddCatalogModal = () => {
    return (
      <Modal
        title="新增目录"
        visible={isModalVisible}
        onOk={() => {
          if (!catalogName) {
            message.error('请输入目录名称');
            return;
          }
          handleAddService(selectedParentId, 'folder');
        }}
        onCancel={() => setIsModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form layout="vertical">
          <Form.Item
            label="目录名称"
            required
            rules={[{ required: true, message: '请输入目录名称' }]}
          >
            <Input 
              placeholder="请输入目录名称"
              value={catalogName}
              onChange={(e) => setCatalogName(e.target.value)}
            />
          </Form.Item>
          <Form.Item label="目录分类">
            <TreeSelect
              placeholder="请选择父目录"
              value={selectedParentId}
              onChange={(value) => setSelectedParentId(value)}
              allowClear
              style={{ width: '100%' }}
              treeData={parentOptions}
              treeDefaultExpandAll
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  // 获取可选的父目录列表
  const parentOptions = useMemo(() => {
    const findFolderNodesRecursive = (data) => {
        const folderNodes = [];
        if (!Array.isArray(data)) {
            return folderNodes;
        }

        data.forEach(item => {
            if (item.Attribute.dataType === '' || item.Attribute.dataType === '1') {
                const childFolders = item.Children ? findFolderNodesRecursive(item.Children) : [];

                const node = {
                    title: item.Name,
                    value: item.Rid,
                    key: item.Rid,
                    children: childFolders
                };
                folderNodes.push(node);
            }
        });
        return folderNodes;
    };

    const topLevelFolders = findFolderNodesRecursive(mapServices);

    const rootNode = {
        title: '顶级目录',
        value: '#', 
        key: '#',
        children: topLevelFolders
    };

    console.log('Final parentOptions structure:', [rootNode]);
    
    return [rootNode];

}, [mapServices]);

  //新增服务函数，增加类型参数
  const handleAddService = async (parentId = null, type = null) => {
    if (type === 'folder') {
      if (!catalogName) {
        message.error('请输入目录名称');
        return;
      }
      
      // 调用addAndUpdate接口创建目录
      try {
        const data = {
          name: catalogName,
          dataType: '1',
          expandedNode: true,
          parentId: parentId
        };
        console.log('新增目录addAndUpdate', data);
        await api.addAndUpdate(data);
        message.success('新增目录成功');
        const newData = await api.queryDataDirectory();
        setMapServices(newData);
        setIsModalVisible(false);
        setCatalogName('');
        setSelectedParentId(null);
      } catch (error) {
        message.error('新增目录失败: ' + error.message);
      }
      return;
    }

    setModalType('add');
    serviceForm.resetFields();
      
    serviceForm.setFieldsValue({
        type: type === 'folder' ? 'folder' : 'ArcGISTiledMapServiceLayer',
        dataType: type === 'folder' ? '1' : '2',
        name: '',
        expandedNode: true
    });
    setSelectedKeys([]);
    };

  // 测试服务连接
  const handleTestService = () => {
    message.success('服务连接测试成功');
  };

  // 选择缩略图
  const handleSelectThumbnail = (file) => {
    // 处理缩略图选择逻辑
    return false; // 阻止自动上传
  };

  // 渲染配置表单
  const renderConfigForm = () => {
    if (!currentService) return <div className="empty-content">请选择一个服务查看配置</div>;
    
    // 根据服务类型显示不同的表单
    if (currentService.type === 'folder') {
      return (
        <Form
          form={serviceForm}
          layout="horizontal"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <h2 style={{ marginBottom: '20px' }}>
            {modalType === 'add' ? '新增目录' : '目录信息'}
          </h2>
          
          <Form.Item
            name="name"
            label="目录名称"
            rules={[{ required: true, message: '请输入目录名称' }]}
          >
            <Input placeholder="请输入目录名称" />
          </Form.Item>
          
          <Form.Item
            name="expandedNode"
            label="默认展开节点"
            valuePropName="checked"
          >
            <Checkbox />
          </Form.Item>
          
          <Form.Item
            name="mapUseRange"
            label="目录使用范围"
          >
            <Checkbox.Group defaultValue={['2D']}>
              <Checkbox value="2D">二维地图</Checkbox>
              <Checkbox value="3D">三维地图</Checkbox>
            </Checkbox.Group>
          </Form.Item>
          
          <Form.Item
            wrapperCol={{ offset: 4, span: 20 }}
          >
            <Space>
              <Button type="primary" onClick={() => {
                const formValues = serviceForm.getFieldsValue();
                console.log('formValues', formValues);
                console.log('currentService', currentService);
                const hasChanges = Object.keys(formValues).some(key => {
                  if (key in currentService) {
                    return formValues[key] !== currentService[key];
                  }
                });
                if (hasChanges) {
                  if (modalType !== 'add') {
                    setModalType('edit');
                    handleSaveService('edit');
                  }
                  else {
                    handleSaveService('add');
                  }
                } else {
                  message.info('没有修改内容');
                }
              }}>
                保存
              </Button>
              {modalType === 'add' && (
                <Button onClick={() => {
                  setModalType('view');
                  setCurrentService(null);
                  setSelectedKeys([]);
                }}>
                  取消
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      );
    }
    
    // 服务类型表单
    return (
      <Form
        form={serviceForm}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <h2 style={{ marginBottom: '20px' }}>
          {modalType === 'add' ? '新增图层服务' : '图层信息'}
        </h2>
        
        <Form.Item
          name="name"
          label="服务名称"
          rules={[{ required: true, message: '请输入服务名称' }]}
        >
          <Input placeholder="请输入服务名称" />
        </Form.Item>
        
        <Form.Item
          name="expandedNode"
          label="默认展开节点"
          valuePropName="checked"
        >
          <Checkbox />
        </Form.Item>
        
        <Form.Item
          name="internalType"
          label="内部服务转换"
        >
          <Select defaultValue="2" placeholder="请选择是否使用内部服务转换">
            <Option value="1">是</Option>
            <Option value="2">否</Option>
          </Select>
        </Form.Item>
        
        {currentService?.internalType === '1' && (
          <Form.Item
            name="servicePath"
            label="转换接口地址"
            rules={[{ required: true, message: '请输入转换接口地址' }]}
          >
            <Input placeholder="请输入转换接口地址" />
          </Form.Item>
        )}
        
        <Form.Item
          name="serviceTypeName"
          label="服务类型"
          rules={[{ required: true, message: '请选择服务类型' }]}
        >
          <Select placeholder="请选择服务类型">
            {serviceTypeList.map(service => (
              <Option key={service.value} value={service.value}>{service.label}</Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="servicePath"
          label="服务地址"
          rules={[{ required: true, message: '请输入服务URL' }]}
        >
          <Input placeholder="请输入服务URL" />
        </Form.Item>
        
        {currentService?.serviceTypeName === 'WfsServiceLayer' && (
          <Form.Item
            name="editUrl"
            label="编辑服务地址"
            rules={[{ required: true, message: '请输入编辑服务地址' }]}
          >
            <Input placeholder="请输入编辑服务地址" />
          </Form.Item>
        )}
        
        {currentService?.internalType === '2' && (
          <Form.Item
            name="accessInfo.isUseVerification"
            label="服务安全访问"
            valuePropName="checked"
          >
            <Checkbox>启用配置</Checkbox>
          </Form.Item>
        )}
        
        <Form.Item
          name="bootLoad"
          label="启动加载"
          valuePropName="checked"
        >
          <Checkbox defaultChecked={false} />
        </Form.Item>
        
        <Form.Item
          name="mapUseRange"
          label="服务使用范围"
        >
          <Checkbox.Group defaultValue={['2D']}>
            <Checkbox value="2D">二维地图</Checkbox>
            <Checkbox value="3D">三维地图</Checkbox>
          </Checkbox.Group>
        </Form.Item>
        
        <Form.Item
          name="isUseExpandParam"
          label="加载参数"
          valuePropName="checked"
        >
          <Checkbox>启用配置</Checkbox>
        </Form.Item>
        
        <Form.Item
          name="imageSrc"
          label="缩略图"
        >
          <Row>
            <Col span={6}>
              <div style={{ width: '100px', height: '100px', border: '1px dashed #d9d9d9', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {currentService.imageSrc ? (
                  <img src={currentService.imageSrc} alt="缩略图" style={{ maxWidth: '100%', maxHeight: '100%' }} />
                ) : (
                  <div>预览图</div>
                )}
              </div>
            </Col>
            <Col span={18}>
              <Button icon={<UploadOutlined />}>选择</Button>
            </Col>
          </Row>
        </Form.Item>
        
        <Form.Item
          wrapperCol={{ offset: 4, span: 20 }}
        >
          <Space>
              <Button type="primary" onClick={() => {
                const formValues = serviceForm.getFieldsValue();
                console.log('formValues', formValues);
                console.log('currentService', currentService);
                const hasChanges = Object.keys(formValues).some(key => {
                  if (key in currentService) {
                    return formValues[key] !== currentService[key];
                  }
                  if (key === 'imageSrc') {
                    return formValues[key] !== currentService['thumbnail'];
                  }
                });
                if (hasChanges) {
                  if (modalType !== 'add') {
                    setModalType('edit');
                    handleSaveService('edit');
                  }
                  else {
                    handleSaveService('add');
                  }
                } else {
                  message.info('没有修改内容');
                }
              }}>
                保存
              </Button>
            {modalType === 'add' && (
              <Button onClick={() => {
                setModalType('view');
                setCurrentService(null);
                setSelectedKeys([]);
              }}>
                取消
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    );
  };

  return (
    <Layout className="map-data-directory">
      <Sider width={300} className="directory-sider">
        <div className="sider-header">
          <h2>地图服务目录</h2>
          <Space>
            <Dropdown overlay={addMenu}>
              <Button type="primary">
                新增 <DownOutlined />
              </Button>
            </Dropdown>
            <Button 
              type="primary" 
              // icon={<ReloadOutlined />}
              onClick={() => {
                serviceForm.resetFields();
                setCurrentService(null);
                setSelectedKeys([]);
                setModalType('view');
                message.success('已刷新');
              }}
            >
              刷新 <ReloadOutlined />
            </Button>
          </Space>
        </div>
        {renderAddCatalogModal()}
        <div className="directory-tree">
          <Tree
            showLine
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={onExpand}
            onSelect={onSelect}
          >
            {renderTreeNodes(mapServices)}
          </Tree>
        </div>
      </Sider>
      <Layout className="directory-content">
        <Content>
          {renderConfigForm()}
          
          {/* 包含图层表格 */}
          {currentService && currentService.type === 'folder' && (
            <div style={{ marginTop: '20px' }}>
              <h3>包含图层</h3>
              <Table
                rowSelection={{ type: 'checkbox' }}
                dataSource={currentService.children || []}
                rowKey="id"
                columns={[
                  { title: 'ID', dataIndex: 'id', key: 'id' },
                  { title: '图层名称', dataIndex: 'name', key: 'name' },
                  { title: '图层类型', dataIndex: 'type', key: 'type', render: type => getServiceTypeName(type) },
                  { title: '几何类型', dataIndex: 'geometryType', key: 'geometryType', render: () => '暂无数据' },
                  { title: '操作', key: 'action', render: (_, record) => (
                    <Space>
                      <Button type="link">编辑</Button>
                    </Space>
                  )}
                ]}
                pagination={false}
                locale={{ emptyText: '暂无数据' }}
              />
            </div>
          )}
        </Content>
      </Layout>
    </Layout>
  );
};