
import { useState,useRef } from "react";
export default function gpsModel() {
    const [gps, setGps] = useState( []);
    const pList=useRef([])
    const count=useRef(0)
    const setGps2=(e)=>{
        setGps(e);
        pList.current.push([e[0],e[1],e[2]]);
        if(count.current>20){
            localStorage.setItem('gpsPoints',JSON.stringify(pList.current));
            count.current=0
        }
        count.current=count.current+1
    }

    const thePList=()=>{
        return pList.current;
    }
    const newPList=()=>{
        localStorage.removeItem('gpsPoints');
        pList.current=[]
    }
    return {gps, setGps,setGps2,pList,newPList};
};