import { HGet2, HPost2 } from "@/utils/request";
import {Dropdown,But<PERSON>} from "antd";
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import { Post2 } from "@/services/general";

const goUrl=async(url)=>{
    HGet2(url)
}


const goPost=async(url,data)=>{
  HPost2(url,data)
}

const FZKZQ=(sn)=>{
  const data={"payload_index": "80-0-0"}
  goPost("/api/v1/Camera/Control?sn="+sn+"&m1="+"payload_authority_grab",data)
}

// const PaiZhao=(sn)=>{
//   const data={"payload_index": "80-0-0"}
//   goPost("/api/v1/Camera/Control?sn="+sn+"&m1="+"camera_photo_take",data)
// }

// const VideoStart=(sn)=>{
//   const data={"payload_index": "80-0-0"}
//   goPost("/api/v1/Camera/Control?sn="+sn+"&m1="+"camera_recording_start",data)
// }

// const VideoEnd=(sn)=>{
//   const data={"payload_index": "80-0-0"}
//   goPost("/api/v1/Camera/Control?sn="+sn+"&m1="+"camera_recording_stop",data)
// }



const items =(sn)=>{return [
    {
      key: '1',
      label: (  <a onClick={()=>goUrl("/api/v1/WayLine/ToHome?sn="+sn)}>一键返航</a>),
    },
    {
      key: '2',
      label: (  <a onClick={()=>goUrl("/api/v1/WayLine/ToHomeCancel?sn="+sn)}>取消返航</a>),
    },
    {
      key: '3',
      label: (  <a onClick={()=>goUrl("/api/v1/WayLine/Pause?sn="+sn)}>航线暂停</a>),
    },
    {
      key: '4',
      label: (  <a onClick={()=>goUrl("/api/v1/WayLine/Recory?sn="+sn)}>恢复航线</a>),
    },
    // {
    //   key: '4',
    //   label: (  <a onClick={()=>FZKZQ(sn)}>负载控制权</a>),
    // },

  ]};

  const FJButton=(sn)=>{
    return   <Dropdown
       menu={{
        items:items(sn),
       }}
       placement="bottomRight"
       arrow
     //  theme="dark"
       style={{
          // zIndex: 1000000,
         }}
     >
       <Button  type="text" style={{color:'white',fontSize:13.0}}>  <DownOutlined style={{color:'white'}}/></Button>
     </Dropdown>
   }

export default FJButton;
