import { Modal, Form, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

const MyModal = ({
  visible,
  onOk,
  onCancel,
  title,
  confirmText = '确认',
  cancelText = '取消',
  width = 600,
  maxHeight = '80vh',
  form,
  children,
  ...rest
}) => {
  // 处理确定事件
  const handleOk = async () => {
    try {
      if (form) {
        await form.validateFields();
      }
      onOk?.();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消事件
  const handleCancel = () => {
    onCancel?.();
    // 重置表单
    form?.resetFields();
  };

  return (
    <Modal
      title={null} // 禁用默认标题 //因为
      footer={null} // 禁用默认footer
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText={confirmText}
      cancelText={cancelText}
      closable={false}
      destroyOnClose
      width={width}
      styles={{
        content: {
          padding: 0,
          maxHeight,
          overflow: 'hidden',
        },
      }}
      {...rest}
    >
      {/* 自定义头部 */}
      <div style={{
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0',
        background: '#fff',
        position: 'sticky',
        top: 0,
        zIndex: 1
      }}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
        >
          <div style={{ fontSize: 16, fontWeight: 500 }}>{title}</div>
          <CloseOutlined
            onClick={handleCancel}
            style={{ cursor: 'pointer' }}
          />
        </div>
      </div>

      {/* 滚动内容区域 */}
      <div style={{
        maxHeight: `calc(${maxHeight} - 64px)`,
        overflowY: 'auto',
        padding: 24
      }}
      >
        {form ? (
          <Form form={form} layout="vertical">{children}</Form>
        ) : children}
      </div>

      {/* 自定义底部 */}
      <div style={{
        padding: '10px 24px',
        borderTop: '1px solid #f0f0f0',
        background: '#fff',
        position: 'sticky',
        bottom: 0,
        textAlign: 'right'
      }}>
        <Button onClick={handleCancel} style={{ marginRight: 8 }}>
          {cancelText}
        </Button>
        <Button type="primary" onClick={handleOk}>
          {confirmText}
        </Button>
      </div>
    </Modal>
  );
};

export default MyModal;