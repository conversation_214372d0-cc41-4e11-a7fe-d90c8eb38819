import { useState,useEffect } from "react";
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
} from "@ant-design/icons";
import { Button, Menu } from "antd";
import { useModel } from "umi";
import { queryPage } from "@/utils/MyRoute";

const items = [
  {
    key: "项目管理",
    icon: <PieChartOutlined />,
    label: "项目管理",
  },
  {
    key: "子阵管理",
    icon: <DesktopOutlined />,
    label: "子阵管理",
  },
  {
    key: "组串管理",
    icon: <DesktopOutlined />,
    label: "组串管理",
  },
];
const App = ({handlePageChange,setCollapsed,collapsed}) => {
  const { setListPage, listPage, pageData } = useModel("pageModel");
  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  // 处理菜单点击
  function handleMenu(e) {
    console.log("click ", e);
    setListPage(e.key) // 点击时 同步listPage状态 告知系统要显示哪个表格
    handlePageChange(e.key);
  };

  useEffect(() => {
    // 页面加载时 点开菜单的第一个
    handleMenu(items[0])
  }, []);
    
  
  return (
    <>
      { 
        <div 
        style={{
          position: 'fixed',
          left: 0,
          top: 56,
          bottom: 0,
          width: collapsed ? 80 : 160,
          background: '#001529',
          zIndex: 1000,
          transition: 'width 0.2s',
        }}
        >
          <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{ 
              marginBottom: 16 ,
            }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <Menu
            defaultSelectedKeys={[items[0].key]} 
            selectedKeys={[pageData?.title]}
            defaultOpenKeys={["sub1"]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 56px)', // 100%可视高度减去按钮区域高度
              overflowY: 'auto'
            }}
          />
        </div>
      }
    </>
  );
  
};
export default App;
