// 飞机镜头 相关信息 RTK GPS 4G SDR 电池电量
import { useModel } from "umi";
import wxImg from "@/assets/drcImgs/weixing.png";
import BatteryButton from "./BatteryButton";
import BatteryButton2 from "./BatteryButton2";
import { isEmpty } from "@/utils/utils";
import "./AircraftLensPanel.less";

const getBatteryLevel = (v) => {
  // 电池电量
  if (v > 90) return 5;
  if (v > 75) return 4;
  if (v > 50) return 3;
  if (v > 20) return 2;
  return 1;
};

const Panel = (data, sdrData) => {
  return (
    <div className="aircraft-lens-panel">
      <div className="panel-content">
        <div className="info-item">
          RTK
          <img className="icon" src={wxImg} height={18} width={18} />
          {data.position_state.rtk_number}
        </div>

        <div className="info-item">
          GPS
          <img className="icon" src={wxImg} height={18} width={18} />
          {data.position_state.gps_number}
        </div>

        <div className="info-item">
          4G
          <div className="battery-item">
            <BatteryButton value={sdrData.wireless_link["4g_uav_quality"]} />
          </div>
        </div>

        <div className="info-item">
          SDR
          <div className="battery-item">
            <BatteryButton value={sdrData.wireless_link.sdr_quality} />
          </div>
        </div>

        <div className="info-item">
          <div className="battery-item">
            <BatteryButton2
              value={getBatteryLevel(data.battery["capacity_percent"])}
            />
          </div>
          {data.battery["capacity_percent"]}%
        </div>
      </div>
    </div>
  );
};

const AircraftLensPanel = (props) => {
  const { fj } = useModel("droneModel");
  const { sdrData } = useModel("dockModel");


  if (isEmpty(fj) || isEmpty(sdrData)) {
    return null;
  }

  return Panel(fj.data, sdrData);
};

export default AircraftLensPanel;
