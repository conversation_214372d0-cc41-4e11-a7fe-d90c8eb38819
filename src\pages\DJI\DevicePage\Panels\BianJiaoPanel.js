import rulerImg from "@/assets/drcImgs/ruler.png";
import youbiaoImg from "@/assets/drcImgs/youbiao.png";
import { isEmpty } from "@/utils/utils";
import { useEffect, useRef, useState } from "react";
import { greyCol } from "@/pages/DJI/DRCPage/Panels/helper";
import { message } from "antd";
import { useModel } from "umi";

const RulerPanel=({showSlidingBar})=>{
    const [t1,setT1]=useState(0)
    const [t2,setT2]=useState(0)

    // 预设了几个档位 按上下键切换 也可以通过滚轮切换
    const bL=[2,3,4,5,7,11,14];
    
    const [val,setVal]=useState(2)
    const pressed=useRef(0);
    const min=0
    const max=120
    const min2=14
    const max2=1
 
    const h1=160
    const w1=50

   const {fj}=useModel('droneModel');

   const {DoCMD}=useModel('cmdModel');
   const device = JSON.parse(localStorage.getItem('device'))

   
   const PToV=(p)=>{
        const v1= ((p-min)/(max-min))*(max2-min2)+min2
        return v1;
   }
   const VToP=(p)=>{
    const v1= ((p-min2)/(max2-min2))*(max-min)+min
    return v1;
   }

   useEffect(() => {
    //const canvas = canvasRef.current;
    const cc = document.getElementById('bianjiaoPanel2');

       if ("ontouchstart" in document.documentElement) {
           cc.addEventListener("touchstart", onTouchStart, false);
           document.addEventListener("touchmove", onTouchMove, false);
           document.addEventListener("touchend", onTouchEnd, false);
       }
       else {
           cc.addEventListener("mousedown", MouseDown, false);
           document.addEventListener("mousemove", MouseMove, false);
           document.addEventListener("mouseup", MouseUp, false);
       }

    const context = cc.getContext('2d');
    const drawLine=()=>{
      cc.width = w1;
      cc.height = h1;
      // 设置线条的颜色
      context.strokeStyle = 'white';
      // 设置线条的宽度
      context.lineWidth = 1;  
      // 绘制直线
      context.beginPath();

     for(let i=1;i<30;i++){
        //drawLine(4+(i-1)*4,1);
        let h2=8+(i-1)*8;
        context.moveTo(4, h2);
        context.lineTo(32, h2);
     }

      context.closePath();
      context.stroke();
    }
    drawLine();
  },[]);

// 同步飞机的实时焦距到滑块
  useEffect(()=>{
    if(!isEmpty(fj)){
        if (!fj?.data?.cameras?.[0]) {
            console.log("相机数据未就绪");
            return;
        }
        const v2 = VToP(fj?.data?.cameras[0]?.zoom_factor);
        // console.log('@@fj',fj);
        setT2(v2);
    }
  },[fj])


  // 对相机焦距进行设置 
  const CameraZoom = (z) => {
    const data = {
        "camera_type": "zoom",
        "payload_index": device.Camera2,
        "zoom_factor": z
    }
    DoCMD(device.SN, "camera_focal_length_set", data)
}


   const MouseDown=()=>{
      pressed.current=1
   }


   const MouseMove=(event)=>{
    if(pressed.current==1){
        const xy=getMovedXY(event.pageX,event.pageY);
         const v1=xy[1];
        setT1(v1);
        const v2=PToV(v1);
        setVal(v2.toFixed(0));
    }
   }

    const getMovedXY = (x0,y0) => {
        const canvas = document.getElementById('bianjiaoPanel2');
        let movedX = x0;
        let movedY = y0;
        // Manage offset
      //  
        if (canvas.offsetParent.tagName.toUpperCase() === "BODY") {
            movedX -= canvas.offsetLeft;
            movedY -= canvas.offsetTop;
        }
        else {
            // 
            movedX -= canvas.offsetParent.offsetLeft;
            movedY -= canvas.offsetParent.offsetTop;
        }
        console.log('fyj',movedY);
        if(movedY<min) movedY=min;
        if(movedY>max) movedY=max;
        return [movedX, movedY];
    }

    const MouseUp = (event) => {
        if (pressed.current == 1) {
            const xy=getMovedXY(event.pageX,event.pageY);
            const v1=xy[1];
            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
            pressed.current = 0
           // console.log('ruler', v1 / (max - min), v1, event.clientY);
           CameraZoom(v2);
        }
    }

    function onTouchStart(event)
    {
        pressed.current=1
    }

    function onTouchMove(event)
    {
        if(pressed.current === 1)
        {
           
            const xy=getMovedXY(event.changedTouches[0]?.pageX,event.changedTouches[0]?.pageY);
            const v1=xy[1];
            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
        }
    }

    function onTouchEnd(event)
    {
        if (pressed.current == 1) {
          //  console.log('fyj',event);
            const xy=getMovedXY(event.changedTouches[0]?.pageX,event.changedTouches[0]?.pageY);
            const v1=xy[1];
            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
            pressed.current = 0
           // console.log('ruler', v1 / (max - min), v1, event.clientY);
           CameraZoom(v2);
        }
        
    }

    const changX=(pb1)=>{
        pressed.current=0; 

        let x1=bL.findIndex(p=>(p>fj?.data?.cameras[0]?.zoom_factor-0.1));
       // if(x1)
        if(pb1){
            x1=x1+1
        }else{
            x1=x1-1
        }
        if(x1>bL.length-1) x1=bL.length-1
        if(x1<0) x1=x1=0;
        console.log('@@即将调整去的焦距',bL[x1]);
        CameraZoom(bL[x1]);
       // const v2=VToP(x2);
      //  setT2(v2);
    }
   

    const panel1=()=>{
        if(pressed.current==0){
            return  <div  draggable={false} onMouseDown={MouseDown} onTouchStart={onTouchStart} style={{background:'white',borderRadius:2.0, cursor:'pointer', position:'absolute',border:'1px 1px 1px 1px',borderStyle:'groove', top:t2,left:0,height:w1,width:w1}}>
            <div  draggable={false}  style={{userSelect:'none', width:w1, fontFamily:'MiSan',height:w1/2, textAlign:'center',fontSize:14.0}}>Zoom</div> 
            <div  draggable={false}  style={{userSelect:'none',width:w1, fontFamily:'MiSan',height:w1/2, textAlign:'center',fontSize:14.0}}>{PToV(t2).toFixed(0)}X</div> 
            </div>
        }
        return  <div  draggable={false} onMouseDown={MouseDown} onTouchStart={onTouchStart} style={{background:'white',borderRadius:2.0, cursor:'pointer', position:'absolute',border:'1px 1px 1px 1px',borderStyle:'groove', top:t1,left:0,height:w1,width:w1}}>
        <div  draggable={false}  style={{userSelect:'none', width:w1, fontFamily:'MiSan',height:w1/2, textAlign:'center',fontSize:14.0}}>Zoom</div> 
        <div  draggable={false}  style={{userSelect:'none',width:w1, fontFamily:'MiSan',height:w1/2, textAlign:'center',fontSize:14.0}}>{val}X</div> 
   </div> 
    }

   return <div style = {{position:"fixed",right:200,top:-90,width:w1*2,height:h1*2,zIndex:1010,display : showSlidingBar? "block" : "none"/* display : "block" */}}>
            <div style={{userSelect:'none',cursor:'pointer', background:greyCol,position:'absolute',top:180,right:120, height:40,width:w1,zIndex:1010}}>
                <div style={{textAlign:'center',color:'white',fontFamily:'MiSan',paddingTop:6.0,fontSize:24.0}} onClick={()=>changX(true)}>
                    +
                </div>
            </div>
            <div style={{background:greyCol,position:'absolute',top:222,right:120, height:h1,width:w1,zIndex:1010}}>
                <div  style={{ height:h1,width:w1,zIndex:1010,cursor:'pointer'}} >
                    <canvas id="bianjiaoPanel2"  style={{position:'absolute',top:6,left:6, height: h1-12, width: 48 }}>
                        您的浏览器不支持canvas元素，请升级您的浏览器
                    </canvas>
                    {panel1()}
                </div>
            </div>

            <div style={{userSelect:'none',cursor:'pointer', background:greyCol,position:'absolute',top:384,right:120, height:40,width:w1,zIndex:1010}}>
                    <div style={{textAlign:'center',color:'white',fontFamily:'MiSan',paddingTop:6.0,fontSize:24.0}}  onClick={()=>changX(false)}>
                        -
                    </div>
            </div>
        </div> 
}

export default RulerPanel;



