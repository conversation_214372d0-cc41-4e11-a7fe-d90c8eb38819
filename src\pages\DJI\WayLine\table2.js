import { Space, Tag, message, Modal } from "antd";
import { downloadFile, isEmpty } from "@/utils/utils";
import "./table.css";
import { timeFormat } from "@/utils/helper";
import { HGet2 } from "@/utils/request";
import { Post2 } from "@/services/general";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const toFly = async (record, setShowCanvas, setWanLineId) => {
  if (isEmpty(record)) return;
  setShowCanvas(true);// 打开canvas画布
  setWanLineId(record.WanLineId);// 当前航线id传给画布组件
  localStorage.setItem("WanLineId", record.WanLineId);
  /* 航线飞行业务逻辑 */
  localStorage.setItem("wayPoints", record.PointList);
  localStorage.removeItem("gpsPoints");
  await HGet2("/api/v1/WayLine/Fly?fID=" + record.WanLineId);
};

const TableCols = (setShowCanvas, setWanLineId) => {
  return [
    {
      title: getTableTitle("航线类型"),
      dataIndex: "WayLineType",
      key: "WayLineType",
      align: "center",
      // width:200,
    },
    {
      title: getTableTitle("航线名称"),
      dataIndex: "WayLineName",
      key: "WayLineName",
      align: "center",
      //  width:300,
      //  className: 'table-header-cell'
    },

    {
      title: getTableTitle("航线操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <Tag>
            <a onClick={() => toFly(record, setShowCanvas, setWanLineId)}>立即执行</a>
          </Tag>
          {/* <Tag><a onClick={()=>showMap(record)}>飞行点位</a></Tag> */}
        </Space>
      ),
    },
  ];
};

export default TableCols;
