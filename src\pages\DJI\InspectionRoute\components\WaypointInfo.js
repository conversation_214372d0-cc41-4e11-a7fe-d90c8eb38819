import React from 'react';
import { Select, Descriptions, InputNumber, Input, } from 'antd';
import { Cesium } from "umi";
import * as turf from '@turf/turf'

function WaypointInfo({ wayline, seleteIndex, item_height_change_function, PTZ_headingPitchRoll_Change, setWaylineCopy, computeHeading, computePitch }) {
    // 动作选项
    const items = [
        {
            label: '普通航点',
            value: '',
        },
        {
            label: '姿态调整',
            value: '0',
        },
        {
            label: '开始录像',
            value: '1',
        },
        {
            label: '结束录像',
            value: '2',
        },
        {
            label: '拍照',
            value: '3',
        },
        {
            label: '悬停',
            value: '4',
        },
        {
            label: '全景拍照',
            value: '6',
        },
    ];
    // 添加动作
    function menuClick({ key }) {
        wayline.PList[seleteIndex].PType = key
        if (key === '') {
            wayline.PList[seleteIndex].IfStop = true
            wayline.PList[seleteIndex].ActionList = []
        } else if (key === '0') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [
                {
                    ActionName: '7',
                    ActionParam: [`${computeHeading(seleteIndex)}`]
                }, {
                    ActionName: '0',
                    ActionParam: [`0`, `${computePitch(seleteIndex)}`, '0']//yaw,pitch,roll
                }]
            PTZ_headingPitchRoll_Change({
                heading: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[0]),
                pitch: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[1].ActionParam[1]),
                roll: 0
            })
        } else if (key === '1') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [{
                ActionName: '1',
                ActionParam: ['zoom']
            }]
        } else if (key === '2') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [{
                ActionName: '2',
                ActionParam: ['zoom']
            }]
        } else if (key === '3') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [
                {
                    ActionName: '3',
                    ActionParam: [`${computePitch(seleteIndex)}`, `${computeHeading(seleteIndex)}`, 'zoom', '1']
                }]
            PTZ_headingPitchRoll_Change({
                heading: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[1]),
                pitch: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[0]),
                roll: 0
            })
        } else if (key === '4') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [{
                ActionName: '4',
                ActionParam: ['10']
            }]
        } else if (key === '6') {
            wayline.PList[seleteIndex].IfStop = false
            wayline.PList[seleteIndex].ActionList = [{
                ActionName: '6',
                ActionParam: ['zoom']
            }]
        }
        setWaylineCopy({ ...wayline, })
    };
    return <div style={{  }}>
        <div style={{ padding: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ fontSize: 14, fontWeight: 'bold', color: '#1890ff', }} >{wayline.PList[seleteIndex].PName}</div>
            {/* {seleteIndex !== null && wayline.IsParallelLine && <Select
                style={{ width: '48%' }}
                value={wayline.PList[seleteIndex].PType}
                placeholder={'请选择航点类型'}
                onChange={(value, option) => { menuClick({ key: value }) }}
                options={items}
                size="small"
            />} */}
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between', rowGap: '8px', background: '#fff', borderRadius: '4px', padding: '8px' }}>
            <div style={{ width: '48%' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>高度</div>
                <InputNumber
                    style={{ width: '100%' }}
                    precision={2}
                    addonAfter={'米'}
                    size="small"
                    value={wayline.PList[seleteIndex].global_height}
                    onChange={(e) => {
                        wayline.PList[seleteIndex].global_height = e
                        if (e !== null) {
                            wayline.PList[seleteIndex].Height = wayline.PList[seleteIndex].ground_height + (e)
                            setWaylineCopy({ ...wayline })
                            item_height_change_function(wayline.PList[seleteIndex].Height, seleteIndex)
                        }
                    }}>
                </InputNumber>
            </div>
            <div style={{ width: '48%' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>速度</div>
                <InputNumber
                    size="small"
                    min={1}
                    max={15}
                    style={{ width: '100%' }}
                    value={wayline.PList[seleteIndex].Speed}
                    addonAfter={'米/秒'}
                    onChange={(e) => {
                        wayline.PList[seleteIndex].Speed = e
                        setWaylineCopy({ ...wayline })
                    }}>
                </InputNumber>
            </div>
            <div style={{ width: '48%' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>经度</div>
                <InputNumber
                    size="small"
                    style={{ width: '100%' }}
                    disabled
                    value={wayline.PList[seleteIndex].Lat}
                    onChange={(e) => {
                        wayline.PList[seleteIndex].Lat = e
                        setWaylineCopy({ ...wayline })
                    }}>
                </InputNumber>
            </div>
            <div style={{ width: '48%' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>纬度</div>
                <InputNumber
                    size="small"
                    style={{ width: '100%' }}
                    disabled
                    value={wayline.PList[seleteIndex].Lng}
                    onChange={(e) => {
                        wayline.PList[seleteIndex].Lng = e
                        setWaylineCopy({ ...wayline })
                    }}>
                </InputNumber>
            </div>
        </div>
    </div>
}
export default WaypointInfo;