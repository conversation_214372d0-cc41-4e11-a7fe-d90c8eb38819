import { getBodyH, isEmpty } from "@/utils/utils";
import { useEffect, useState } from "react"

const DatasetImgPanel = ({img1,data,data2,aList,DrawOK,ifDraw,labelID}) => {
    const [p1,setP1]=useState([])
    const [pL,setPL]=useState([])
    const [scale, setScale] = useState(1)
    const [offset, setOffset] = useState({ x: 0, y: 0 })
    const [isDragging, setIsDragging] = useState(false)
    const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
    const [crosshairPosition, setCrosshairPosition] = useState({ x: -1, y: -1 });
    const [drawing, setDrawing] = useState(false); // 是否在绘制状态
    const [rectangles, setRectangles] = useState([]); // 存储绘制的矩形
    useEffect(() => {
        if(img1==="/api/v2/AI/DownloadImg?id=") return;
        const cc = document.getElementById('mycanvas');
        const ctx = cc.getContext('2d');
        const w1=cc.clientWidth
        const h1=cc.clientHeight

        const img = new Image()
        img.src = img1

        img.onload = function () {
            if(cc.width !== w1 || cc.height !== h1) {
                cc.width = w1;
                cc.height = h1;
            }
            
            ctx.clearRect(0, 0, w1, h1);
            ctx.save();
            ctx.translate(offset.x, offset.y);
            ctx.scale(scale, scale);
            
            ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, w1, h1);
            
            let nn = 0;
            ctx.strokeStyle = "red";
            data.forEach(xx => {
                ctx.strokeText(aList[xx[0]]+"-"+nn, xx[1] * w1- xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2)
                ctx.strokeRect(xx[1] * w1 - xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2, xx[3] * w1, xx[4] * h1)
                nn++;
            });

            if(!isEmpty(data2)){
                const xx=data2;
                ctx.strokeStyle = "green";
                ctx.strokeRect(xx[1] * w1 - xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2, xx[3] * w1, xx[4] * h1)
            }
            if(!isEmpty(pL)){
                const xx=pL;
                ctx.strokeStyle = "yellow";
                ctx.strokeRect(xx[1] * w1 - xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2, xx[3] * w1, xx[4] * h1)
            }
               // 如果正在绘制，绘制当前的轨迹
               if (drawing) {
                ctx.strokeStyle = "#00ff00"; // 轨迹颜色
            
                // 计算当前的十字线位置
                const drawX = (crosshairPosition.x - offset.x) / scale; // 调整的 x 坐标
                const drawY = (crosshairPosition.y - offset.y) / scale; // 调整的 y 坐标
            
                const width = drawX - p1[0]; // 当前宽度
                const height = drawY - p1[1]; // 当前高度
            
                ctx.strokeRect(p1[0], p1[1], width, height);
            }
            
            
            ctx.restore();
        }
    }, [img1, data, data2, pL, scale, offset, drawing, crosshairPosition, rectangles]);

    const onMouseDown = (e) => {
        const cc = document.getElementById('mycanvas');
        const canvasInfo = cc.getBoundingClientRect();
        
        if(ifDraw) {
            const scaledX = (e.clientX - canvasInfo.left - offset.x) / scale;
            const scaledY = (e.clientY - canvasInfo.top - offset.y) / scale;
            setP1([scaledX, scaledY]);
            setCrosshairPosition({ x: scaledX, y: scaledY });
            setDrawing(true); // 开始绘制
        } else {
            setIsDragging(true);
            setLastPosition({
                x: e.clientX,
                y: e.clientY
            });
        }
    }

    const onMouseMove = (e) => {
        const cc = document.getElementById('mycanvas');
        const canvasInfo = cc.getBoundingClientRect();
        if (isDragging && !ifDraw) {
            const deltaX = e.clientX - lastPosition.x;
            const deltaY = e.clientY - lastPosition.y;
            
            setOffset(prev => ({
                x: prev.x + deltaX,
                y: prev.y + deltaY
            }));
            
            setLastPosition({
                x: e.clientX,
                y: e.clientY
            });
        }else if (ifDraw) {
            // 更新十字线位置
            const x = (e.clientX - canvasInfo.left - offset.x) / scale;
            const y = (e.clientY - canvasInfo.top - offset.y) / scale;
            setCrosshairPosition({ x: e.clientX - canvasInfo.left - 1, y: e.clientY - canvasInfo.top - 1 }); // 根据缩放调整十字线位置

        }
    }

    const onMouseUp = (e) => {
        if(ifDraw) {
            const cc = document.getElementById('mycanvas');
            const canvasInfo = cc.getBoundingClientRect();
            const w1 = cc.width;
            const h1 = cc.height;
            
            const scaledEndX = (e.clientX - canvasInfo.left - offset.x) / scale;
            const scaledEndY = (e.clientY - canvasInfo.top - offset.y) / scale;
            
            const width = scaledEndX - p1[0];
            const height = scaledEndY - p1[1];
            
            const newRectangle = [
                labelID,
                (p1[0] + width/2)/w1,
                (p1[1] + height/2)/h1,
                Math.abs(width)/w1,
                Math.abs(height)/h1
            ];

            setRectangles(prev => [...prev, newRectangle]); // 存储新的矩形
            DrawOK(newRectangle); // 调用回调
            setDrawing(false); // 停止绘制
            setCrosshairPosition({ x: -1, y: -1 }); // 隐藏十字线
        } else {
            setIsDragging(false);
        }
    }

    const handleWheel = (e) => {
        e.preventDefault();
        const cc = document.getElementById('mycanvas');
        const canvasInfo = cc.getBoundingClientRect();
        
        const mouseX = e.clientX - canvasInfo.left;
        const mouseY = e.clientY - canvasInfo.top;
        
        const delta = e.deltaY;
        const newScale = delta > 0 ? scale * 0.9 : scale * 1.1;
        const boundedScale = Math.min(Math.max(newScale, 0.1), 5);
        
        setScale(boundedScale);
    }
    const getBody = () => {
        return <canvas
            id="mycanvas"
            onMouseDown={onMouseDown}
            onMouseMove={onMouseMove}
            onMouseUp={onMouseUp}
            onMouseLeave={() => { setIsDragging(false); setDrawing(false); setCrosshairPosition({ x: -1, y: -1 }); }}
            onWheel={handleWheel}
            style={{
                height: getBodyH(320),
                width: '100%',
                cursor: ifDraw ? 'crosshair' : isDragging ? 'grabbing' : 'grab'
            }}
        >
            您的浏览器不支持canvas元素，请升级您的浏览器
        </canvas>
    }
    const Crosshair = ({ x, y }) => {
        return (
            <div className="crosshair" style={{
                position: 'absolute',
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                zIndex: 999 
            }}>
                <div className="crosshair-line-horizontal" style={{
                    position: 'absolute',
                    height: '2px', 
                    backgroundColor: '#00ff00',
                    top: y + 'px',
                    left: 0,
                    right: 0,
                }} />
                <div className="crosshair-line-vertical" style={{
                    position: 'absolute',
                    width: '2px', 
                    backgroundColor: '#00ff00',
                    left: x + 'px',
                    top: 0,
                    bottom: 0,
                }} />
            </div>
        );
    };

    return (
        <div style={{ position: 'relative' }}>
            {getBody()}
            {crosshairPosition.x !== -1 && crosshairPosition.y !== -1 && (
                <Crosshair x={crosshairPosition.x} y={crosshairPosition.y} />
            )}
        </div>
    );
}

export default DatasetImgPanel;