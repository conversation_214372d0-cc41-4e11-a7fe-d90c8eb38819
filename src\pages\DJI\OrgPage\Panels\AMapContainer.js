import React from 'react';
import { <PERSON>, Marker ,Pixel,Polyline} from 'react-amap';
import icon1 from '@/assets/icons/device2.png'
import icon2 from '@/assets/icons/device.png'
import AMap from '@/pages/Maps/AMap/MapContainer';
import { isEmpty } from '@/utils/utils';
import { useModel } from 'umi';
import DevicePage from '../../DevicePage';
//import DevicePage from '../../DRCPage';
import { ZTDT } from '@/pages/Maps/DiTuJson/ditu';
import {Wgs84ToGcj02} from '@/pages/Maps/gps_helper';
import { ZXLine } from '@/pages/Maps/gcline';
//import { Map as AMapMap } from 'AMap'; // 引入高德地图


const  OrgAMapPanel=({h1,data}) =>{
  const {setPage,lastPage}=useModel('pageModel')


  const onClick=(e)=>{
   // console.log('MyMap',e);
    localStorage.setItem('device',JSON.stringify( e))
    
    setPage(<DevicePage device={e}/>)
  }
  
  const getMarker=(e)=>{
    const e2=Wgs84ToGcj02(e.Lat,e.Lng);
   return <Marker
    visiable={true}
    title={e.DName}
    position={{latitude:e2[0],longitude:e2[1]}}
    events={{'click':()=>onClick(e)}}
    // style={{zIndex: 1000}}
  >
    <div style={{height:80,width:72}}>
        <img style={{marginLeft:12.0}} height={48.0} width={48.0} src={icon2}></img>
        <div style={{height:20.0,width:72.0,color:'white',fontSize:14.0, textAlign:'center',paddingTop:2.0, background:'rgba(0,0,0,0.5)'}}>{e.DName}</div>
</div>
  </Marker>
  }


  
  const getLayersGaoDe = () => {
    const list = []
    //
    // const renderMarker=(x)=>{
    //    return <div style={{background:'red', width:300,fontSize:12.0}}>{x}</div>
    // }
    if(isEmpty(ZTDT)){
      return list;
    }

    ZTDT.features.forEach(e => {
      if (isEmpty(e.geometry)) {
       // 
      } else {
        if (e.geometry.type == "GeometryCollection") {
          e.geometry.geometries.forEach(x => {
            if (x.type == "LineString") {
              let pL = []
              x.coordinates.forEach(y => {
                pL.push({ longitude: y[0], latitude: y[1] })
              });
              list.push(<Polyline
                path={pL}
                style={{ strokeColor: e.properties["stroke"], strokeWeight: e.properties["stroke-width"], strokeOpacity: 0.8 }}
              />);
            }
          });
        }
        if (e.geometry.type == "LineString") {
          let pL = []
          e.geometry.coordinates.forEach(y => {
            pL.push({ longitude: y[0], latitude: y[1] })
          });
         // list.push(<Polyline weight={e.properties["stroke-width"]} color={e.properties["stroke"]} positions={pL} />)
          list.push(<Polyline
            path={pL}
            style={{ strokeColor: e.properties["stroke"], strokeWeight: e.properties["stroke-width"], strokeOpacity: 0.8 }}
          />);
        }
        // if (e.geometry.type == "Point") {
        //   list.push(
        //     <Marker anchor='center'

        //     offset={[0,0]} position={{longitude: e.geometry.coordinates[0], latitude: e.geometry.coordinates[1] }}  >
        //     <div style={{textAlign:'center', width:200,fontSize:12.0,color:'yellow',opacity:0.4}}> {e.properties.name}</div>
        //   </Marker>);
        // }
      }
  
    });
    // 
    return list;
  }

  const getLine=()=>{
    const list=[]
    ZXLine.forEach(e => {
        list.push({ longitude: e[1], latitude: e[0] })
    });
   return <Polyline
    path={list}
    style={{strokeColor:"red",strokeWeight:4, strokeOpacity:0.8}}
    // 其他你需要的 Polyline 属性
  />
  }

  const getHomeIcon=()=>{
    const list=[]
    data.forEach(e => {
      //  list.push(getLine());
       // list.push(getLayersGaoDe());
        list.push(getMarker(e));
      //  list.push(<Marker data={e} events={{'click':()=>onClick(e)}} icon={icon1} position={{latitude:e.Lat+0.01,longitude:e.Lng+0.01}} />)
    });
    return  list
  }

  if(isEmpty(data)) return <div/>

  return (
    <AMap
      center={{latitude:data[0].Lat,longitude:data[0].Lng}}
    //  center={{latitude:30.0760379509356,longitude:105.299613318494}}
      child={getHomeIcon()} 
      eve={{}}
    />

  );
}
export default OrgAMapPanel;