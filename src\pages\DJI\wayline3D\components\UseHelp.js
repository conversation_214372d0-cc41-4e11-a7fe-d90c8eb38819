import { useState, useEffect, useRef } from 'react';
import { Tooltip, Popover } from 'antd';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';

function UseHelp({ }) {
    // 使用帮助
    let [popoverOpen, setPopoverOpen] = useState(false);
    return <div style={{ position: 'absolute', bottom: 50, left: 210, width: 150, height: 150, zIndex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tooltip placement="top" title={'点击查看使用帮助'}>
            <Popover content={
                <div>
                    <p>1:先选择机库再点击“绘制航线”</p>
                    <p>2:鼠标左键将飞机移动至该点</p>
                    <p>3:鼠标右键添加航点</p>
                    <p>4:点击选中航点</p>
                    <p>5:航点选中时可以调整参数</p>
                    <p>6:鼠标放在航点底部原点，变红后拖动改变航点位置</p>
                    <p>7:鼠标放在航点顶部图标，变红后拖动改变航点高度</p>
                </div>
            } title="使用帮助" trigger="click" placement="top" open={popoverOpen} zIndex={1}>
                <div style={{ color: '#ffffff', height: '45%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', borderRadius: '10px', color: 'white', fontSize: 20, fontWeight: 600 }} onClick={(e) => {
                    setPopoverOpen(!popoverOpen)
                }}>
                    <div style={{ backgroundColor: 'rgba(0, 0, 0, 0.6)', borderRadius: '50%', width: 40, height: 40, display: 'flex', justifyContent: 'center', alignItems: 'center', }}>
                        <QuestionCircleOutlined />
                    </div>

                </div>
            </Popover>
        </Tooltip>
        <Tooltip placement="bottom" title='按空格在飞机位置添加航点'>
            <div style={{ height: '45%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.6)', borderRadius: '10px', color: 'white', fontSize: 18, fontWeight: 600 }}>
                <div style={{ height: '80%', width: '85%', cursor: 'pointer' }}>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>space</div>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><PlusOutlined /></div>
                </div>
            </div>
        </Tooltip>
    </div>
}
export default UseHelp;