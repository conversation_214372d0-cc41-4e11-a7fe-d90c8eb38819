import { Al<PERSON>, Card, Descriptions } from "antd";
import { useModel } from "umi";
import DeBugButton from "../DebugButton";
import { getBaseColor, getGuid, getLocation } from "@/utils/helper";
import { useEffect, useState } from "react";
import useMqttStore from "@/stores/useMqttStore";
import styles from "../JXSSPanel.less";
import { axiosApi } from "@/services/general";
import styles2 from "./index.less";
const GasDetectorPanel = ({ sn, setIfY }) => {
  // sn = "7CTDM7100B5U7J";
  //气体检测仪组件
  const { connectMqtt, disconnectMqtt, messages, sendMqttMessage } =
    useMqttStore();
  const [data, setData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    connectMqtt();
    return () => {
      disconnectMqtt();
    };
  }, [connectMqtt, disconnectMqtt]);
  useEffect(() => {
    async function sendMessage() {
      if (!sn) return;
      let res = await axiosApi(
        `/api/v1/Device/Payload/GetList?device_sn=${sn}`,
        "GET"
      );
      if (res && res.code === 1) {
        let list = res.data;
        if (!list || list.length <= 0) return;
        list.forEach((item, index) => {
          let topic = JSON.parse(item.property);
          sendMqttMessage(topic.topic);
        });
      }
    }
    sendMessage();
  }, []);
  useEffect(() => {
    getData();
  }, [messages]);
  function getData() {
    if (messages && messages.length > 0) {
      setData((prevData) => {
        const newData = [...prevData];
        messages.forEach((message) => {
          const existingIndex = newData.findIndex(
            (item) => item.serial === message.serial
          );
          if (existingIndex > -1) {
            // 更新现有条目
            newData[existingIndex] = { ...newData[existingIndex], ...message };
          } else {
            // 添加新条目
            newData.push(message);
          }
        });
        return newData;
      });
      setIsModalOpen(true);
    } else {
      if (data[0]?.length <= 0) {
        setIsModalOpen(false);
      }
      setIsModalOpen(false);
    }
  }
  const getPanel = (list) => {
    return (
      <Descriptions style={{ fontSize: 12.0, color: "white" }} column={2}>
        {list}
      </Descriptions>
    );
  };
  const getItem = (label, val, dw) => {
    return (
      <Descriptions.Item
        key={getGuid()}
        labelStyle={{ fontSize: 12.0, color: "white" }}
        contentStyle={{ fontSize: 12.0, color: "white" }}
        label={label}
      >
        {val + dw}
      </Descriptions.Item>
    );
  };

  const elements = (data) => {
    if (!data || data.length <= 0 || !data.airData) return;
    const list = [];
    // list.push(getItem("序列号", data.serial,""));
    // list.push(getItem("仪器状态", data.missionOpenStatus? "在线" : "离线",""));
    // list.push(getItem("经纬度", data.longitude + "," + data.latitude, ""));
    list.push(getItem("温度", data.temperature, "℃"));
    list.push(getItem("湿度", data.humidity, "%RH"));
    list.push(getItem("气压", data.pressure, "Pa"));
    list.push(getItem("海拔", data.altitude, "m"));
    list.push(getItem("相对海拔", data.relativeAltitude, ""));
    list.push(getItem("NO₂", data.airData.NO2, ""));
    list.push(getItem("O₃", data.airData.O3, ""));
    list.push(getItem("SO₂", data.airData.SO2, ""));
    list.push(getItem("O₃+NO₂", data.airData["O3+NO2"], ""));
    list.push(getItem("PM₁₀", data.airData.PM10, ""));
    list.push(getItem("NO₂", data.airData.NO2, ""));
    list.push(getItem("O₃", data.airData.O3, ""));
    list.push(getItem("CH₄", data.airData.CH4, ""));
    list.push(getItem("CO", data.airData.CO, ""));
    list.push(getItem("CO₂", data.airData.CO2, ""));
    list.push(getItem("Cl₂", data.airData.Cl2, ""));
    list.push(getItem("CxHy", data.airData.CxHy, ""));
    list.push(getItem("H₂", data.airData.H2, ""));
    list.push(getItem("H₂S", data.airData.H2S, ""));
    list.push(getItem("HCN", data.airData.HCN, ""));
    list.push(getItem("HC₁", data.airData.HCl, ""));
    list.push(getItem("IR", data.airData.IR, ""));
    list.push(getItem("NH₃", data.airData.NH3, ""));
    list.push(getItem("NO", data.airData.NO, ""));
    list.push(getItem("O₂", data.airData.O2, ""));
    list.push(getItem("PH₃", data.airData.PH3, ""));
    return getPanel(list);
  };
  const titleDiv = (
    <div draggable={false} onClick={() => setIfY(false)}>
      气体检测仪
    </div>
  );

  return (
    <>
      {isModalOpen && (
        <div className={styles.cardCon}>
          <Card
            bordered={false}
            size="small"
            title={titleDiv}
            // extra={<DeBugButton sn={sn}></DeBugButton>}
            headStyle={{ color: "#fff" }}
          >
            <div
              className={styles2.scroll_container}
            >
              {elements(data[0])}
            </div>
          </Card>{" "}
        </div>
      )}
    </>
  );
};
export default GasDetectorPanel;
