import AddButton from "@/components/AddButton";
import { getGuid } from "@/utils/helper";
import { Row,Col,Input } from "antd";
import { useState } from "react";

export const ChangeDNamePanel = ({ nm1, flyTo }) => {
    const [nm, setNM] = useState(nm1)


    return <Row >
              <Col span={6} style={{paddingTop:4.0}}>机场名称：</Col>
              <Col span={10} style={{marginRight:12.0}}> <Input  onChange={(e) => setNM(e.target.value)} defaultValue={nm} /></Col>
              <Col span={4} ><AddButton  onClick={()=>flyTo(nm)}>修改</AddButton></Col>
       </Row>
  };

  export const ChangeDNamePanel2 = ({label, nm1, flyTo }) => {
    const [nm, setNM] = useState(nm1)

    return <div  style={{zIndex:1002}}><Row   >
              <Col span={6} style={{paddingTop:4.0}}>{label}：</Col>
              <Col span={10} style={{marginRight:12.0}}> <Input  onChange={(e) => setNM(e.target.value)} defaultValue={nm}/></Col>
              <Col span={4} ><AddButton  onClick={()=>flyTo(nm)}>修改</AddButton></Col>
       </Row></div>
  };

  export const ChangeDNamePanel3 = ({label, nm1,tooltip,flyTo }) => {
     const [nm, setNM] = useState(nm1)
 
     return<div><Row >
               <Col span={6} style={{paddingTop:4.0}}>{label}：</Col>
               <Col span={10} style={{marginRight:12.0}}> <Input   onChange={(e) => setNM(e.target.value)} defaultValue={nm} /></Col>
               <Col span={4} ><AddButton  onClick={()=>flyTo(nm)}>修改</AddButton></Col>
        </Row>
        {tooltip}
        </div> 
   };