import { Space, Tag, message, Modal } from "antd";
import { downloadFile, isEmpty } from "@/utils/utils";
//import "./table.css";
import { timeFormat } from "@/utils/helper";
import { HGet2 } from "@/utils/request";
import { Post2 } from "@/services/general";
import dayjs from "dayjs";

const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const toFly = async (record) => {
  //console.log('toFly')
  if (isEmpty(record)) return;
  localStorage.setItem("wayPoints", record.PointList);
  localStorage.removeItem("gpsPoints");
  // localStorage.setItem("ifWayline",1)
  await HGet2("/api/v1/WayLine/Fly?fID=" + record.WanLineId);
};

const TableCols = (refrush,<PERSON>Yong,showMap) => {
  const typeJson={"0":"授权区解禁","1":"自定义圆形区域解禁","2":"国家/地区解禁","3":"限高解禁","4":"自定义多边形区域解禁","5":"功率解禁","6":"RID 解禁"}
 
  
 
  return [
    {
      title: getTableTitle("解禁文件名称"),
      dataIndex: "common_fields",
      key: "common_fields",
      align: "center",
      render: (r) => (r.name),
      // width:200,
    },
    {
      title: getTableTitle("解禁文件类型"),
      dataIndex: "common_fields",
      key: "type",
      align: "center",
      render: (r) => (typeJson[r.type]),
      //  width:300,
      //  className: 'table-header-cell'
    },
    {
      title: getTableTitle("证书绑定的设备序列号"),
      dataIndex: "common_fields",
      key: "device_sn",
      align: "center",
      render: (r) => (r.device_sn),
    },
    {
      title: getTableTitle("有效期起始时间"),
      dataIndex: "common_fields",
      key: "begin_time",
      align: "center",
      render: (r) => (dayjs( r.begin_time*1000).format('YYYY-MM-DD')),
    },

    {
      title: getTableTitle("有效期终止时间"),
      dataIndex: "common_fields",
      key: "end_time",
      align: "center",
      render: (r) => (dayjs( r.end_time*1000).format('YYYY-MM-DD')),
    },

    {
      title: getTableTitle("是否启用"),
      dataIndex: "common_fields",
      key: "enabled",
      align: "center",
      render: (r) => (r.enabled?"是":"否"),
    },

    
    {
      title: getTableTitle("操作"),
      //dataIndex: "common_fields",
      key: "caozuo",
      align: "center",
      render: (r) => (
        
        <Space size="middle">
          <Tag>
            <a onClick={() => QiYong(true,r['common_fields']['id'])}>启用</a>
          </Tag>
          <Tag>
          <a onClick={() => QiYong(false,r['common_fields']['id'])}>禁用</a>
          </Tag>
          <Tag><a onClick={()=>showMap(r)}>解禁范围</a></Tag>
        </Space>
      ),
    },
  ];
};

export default TableCols;
