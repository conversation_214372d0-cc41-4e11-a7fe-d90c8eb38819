import { useState, useEffect, useRef } from 'react';
import { message, Button, Tooltip } from 'antd';
import { Cesium } from "umi";
import { axiosApi } from '@/services/general';
import { useModel } from 'umi';
import { queryPage2 } from '@/utils/MyRoute';
import MapControl from '@/hooks/mapControl';
import WaypointList from './components/WaypointList';
import UseHelp from './components/UseHelp';
import KeyboardTip from './components/KeyboardTip';
import KeyboardTip2 from './components/KeyboardTip2';
import WaylineSetting from './components/WaylineSettingPage';
import WayPointSetting from './components/WayPointSetting';
import WayPointAction from './components/WayPointAction';
import useWayline3DHooks from './hooks/wayline3DHooks';
import useWaylineFileHooks from './hooks/waylineFileHooks';
import JSZ<PERSON> from "jszip";
import OneMap from '@/pages/GT/YZT/pages/Map/components/OneMap/index';

const WayLine3DPage = ({ wayLineData }) => {
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    let [cesiumViewer, setCesiumViewer] = useState(null)
    let {
        waylineFileInfo,
        addPlacemark,
        deletePlacemark,
        authorChange,
        droneEnumValueChange,
        droneSubEnumValueChange,
        PlacemarkListItemHeightChange,
        PlacemarkListItemPositionChange,
        CreatFile,
        imageFormatChange,
        heightModeChange,
        flyToWaylineModeChange,
        globalHeightChange,
        autoFlightSpeedChange,
        globalTransitionalSpeedChange,
        globalWaypointTurnModeChange,
        globalWaypointHeadingParamChange,
        gimbalPitchModeChange,
        finishActionChange,
        waypointUseGlobalSetting,
        useGlobalHeightChange,
        waypointSpeedChange,
        waypointHeadingModeChange,
        waypointTurnModeChange,
        addWaypointAction,
        editWaypointAction,
        deleteWaypointAction,
        computeHeading,
        computePitch } = useWaylineFileHooks(
            waylineChange,
            droneSubEnumValueOptionsChange,
            item_height_change,
            item_position_change,
            SwitchWaypointAction,
            routeFeedbackFu,
            heightMode,
            wayLineData);
    let { routeFeedback,
        deleteWaypoint,
        SwitchWaypoint,
        item_height_change_function,
        item_position_change_function,
        setAirplaneModelheading,
        setAirplaneModelPitch,
        cameraFlyTo,
        addModel3Dtiles,
        switchHeightMode } = useWayline3DHooks(
            waylineFileInfo,
            addPlacemark,
            deletePlacemark,
            PlacemarkListItemHeightChange,
            PlacemarkListItemPositionChange,
            seleteIndexChange,
            computeHeading,
            computePitch,
            actionIndexChange,
            getMapInstance,
            setCesiumViewer);
    // 选中的航点索引
    const [seleteIndex, setSeleteIndex] = useState(null);
    // 航线名称
    const [waylineName, setWaylineName] = useState('新建航线1');
    // 选中的动作索引
    const [actionIndex, setActionIndex] = useState(null);
    // 航线信息
    const [wayline, setWayline] = useState(null);
    // 航线设置页面是否显示
    const [waylineSettingShow, setWaylineSettingShow] = useState(false);
    // 显示更多动作
    const [moreAction, setMoreAction] = useState(false);
    // 航点设置
    const [WaypointIndex, setWaypointIndex] = useState(null);
    // 无人机机型子类型
    const [droneSubEnumValueOptions, setDroneSubEnumValueOptions] = useState([
        {
            value: 0,
            label: 'M4E',
        },
        {
            value: 1,
            label: 'M4T',
        }
    ]);
    // 机库SN
    let [aircraftSN, setAircraftSN] = useState('')
    // 机库列表
    let [aircraftSNList, setAircraftSNList] = useState([])
    let mapRef = useRef(null);
    // 页面载入
    useEffect(() => {
        setWayline({ ...waylineFileInfo.current })
        axiosApi("/api/open/Device/GetAllList", "GET",).then((res) => {
            let newRes = []
            res.forEach((item, index) => {
                newRes.push({
                    value: item.SN, label: item.DName, Lat: item.Lat, Lng: item.Lng, Height: item.Height, key: item.ID
                })
            })
            // cameraFlyTo(newRes[0].Lng, newRes[0].Lat)
            setAircraftSN(newRes[0].value)
            setAircraftSNList([...newRes])
        })
        if (wayLineData) {
            setAircraftSN(wayLineData.SN)
        }

    }, []);
    useEffect(() => {
        if (aircraftSN !== '') {
            let aircraft = aircraftSNList.filter(item => item.value === aircraftSN)
            if (aircraft.length) {
                // cameraFlyTo(aircraft[0].Lng, aircraft[0].Lat)
            }
        }
    }, [aircraftSN]);
    function seleteIndexChange(index) {
        setSeleteIndex(index)
    }
    function waylineChange(wayline) {
        setWayline(wayline)
    }
    function droneSubEnumValueOptionsChange(val) {
        setDroneSubEnumValueOptions([...val])
    }
    function actionIndexChange(index) {
        setActionIndex(index)
    }
    function SwitchWaypointAction(index, inde) {
        SwitchWaypoint(index)
        setActionIndex(inde)
    }
    function editWaypointIndex(index) {
        SwitchWaypoint(index)
        if (index === WaypointIndex) {
            setWaypointIndex(null)
        } else {
            setWaypointIndex(index)
            setMoreAction(false)
            setWaylineSettingShow(false)
        }
    }
    function item_height_change(index, val) {
        item_height_change_function(index, val)
    }
    function item_position_change(index, longitude, latitude, globeHeight) {
        item_position_change_function(index, longitude, latitude, globeHeight)
    }
    async function save() {
        if (waylineName.trim().length === 0) {
            message.error("航线名不能为空");
            return
        }
        let { wpmlString, kmlString } = CreatFile(wayline)
        // 创建 JSZip 实例
        const zip = new JSZip();
        // 创建 wpmz 文件夹
        const wpmzFolder = zip.folder('wpmz');
        // 添加文件到文件夹
        wpmzFolder.file('waylines.wpml', wpmlString);
        wpmzFolder.file('template.kml', kmlString);
        // 生成 zip 文件
        zip.generateAsync({ type: 'blob' }).then(file => {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("wName", waylineName);
            formData.append("wType", 1);
            formData.append("sn", aircraftSN);
            axiosApi(
                "/api/v1/WayLine/Upload",
                "POST",
                formData,
            ).then((res) => {
                if (res === 'ok') {
                    message.success("保存成功");
                    lastPage()
                }
            })
        })

    }
    function routeFeedbackFu(wayline) {
        routeFeedback(wayline)
    }
    function getMapInstance() {
        if (!mapRef.current) {
            return
        }
        let MapInstance = mapRef.current.getMapInstance()
        if (MapInstance) {
            return MapInstance
        } else {
            return null
        }
    }
    function heightMode(val) {
        switchHeightMode(val)
    }
    return (
        <div style={{ height: '100%', padding: 12, background: '#001c1a', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
            <div style={{ width: '15%', height: '100%', position: 'relative' }} >
                {wayline && <WaypointList wayline={wayline} seleteIndex={seleteIndex} SwitchWaypoint={SwitchWaypoint} deleteWaypoint={deleteWaypoint} SwitchWaypointAction={SwitchWaypointAction} editWaypointIndex={editWaypointIndex} />}
                {WaypointIndex !== null && <WayPointSetting
                    wayline={wayline}
                    seleteIndex={seleteIndex}
                    setWaypointIndex={setWaypointIndex}
                    PlacemarkListItemPositionChange={PlacemarkListItemPositionChange}
                    useGlobalHeightChange={useGlobalHeightChange}
                    PlacemarkListItemHeightChange={PlacemarkListItemHeightChange}
                    waypointUseGlobalSetting={waypointUseGlobalSetting}
                    waypointSpeedChange={waypointSpeedChange}
                    waypointHeadingModeChange={waypointHeadingModeChange}
                    waypointTurnModeChange={waypointTurnModeChange}
                />}
            </div>
            <div style={{ width: '60%', height: '100%' }} >
                <div style={{ height: '100%', width: '100%', position: 'relative' }} id="cesisss">
                    {/* <OneMap
                        ref={mapRef}
                        deviceList={[]} // 传递设备数据
                        deviceLoading={false} // 传递加载状态
                        showTreePanel={true}
                        showZoomControls={true}
                        showMapInfoPanel={true}
                        showMapScale={true}
                        showMeasureBtn={false}
                        showMapCompass={true}
                        showSplitButton={false} // 隐藏原有的卷帘按钮
                        showCoordinateQuery={false} // 控制坐标查询组件显示
                        showNFZButton={true} // 控制禁飞区按钮显示/隐藏
                        isLeftSidebarCollapsed={true}
                        isRightSidebarCollapsed={true}
                        mapType={'2D'}
                        isCurtainMode={false} // 传递卷帘状态
                        theme={'light'}
                        customControlStyle={{ bottom: '50px' }} // 接收外部传入的控制面板自定义样式
                        customCompassStyle={{ bottom: '350px' }} // 接收外部传入的指南针自定义样式
                    /> */}
                    <KeyboardTip />
                    <KeyboardTip2 />
                    <UseHelp />
                    <div style={{ position: 'absolute', left: 120, top: 0, zIndex: 1, width: 100, height: 35 }}>
                        <Button type="primary" style={{ width: '100%', height: '100%', borderRadius: '5px' }} onClick={() => { save() }}>保存</Button>
                    </div>
                    <div style={{ position: 'absolute', top: 0, left: 0, zIndex: 2, width: 100, height: 35 }}>
                        <Button type="primary" style={{ width: '100%', height: '100%', borderRadius: '5px' }} onClick={() => {
                            setWaylineSettingShow(!waylineSettingShow)
                            setWaypointIndex(null)
                            setMoreAction(false)

                        }}>航线设置{waylineSettingShow ? '▲' : '▼'}</Button>
                        {wayline && waylineSettingShow && <WaylineSetting
                            wayline={wayline}
                            waylineName={waylineName}
                            aircraftSN={aircraftSN}
                            setAircraftSN={setAircraftSN}
                            aircraftSNList={aircraftSNList}
                            droneSubEnumValueOptions={droneSubEnumValueOptions}
                            setWaylineName={setWaylineName}
                            authorChange={authorChange}
                            droneEnumValueChange={droneEnumValueChange}
                            droneSubEnumValueChange={droneSubEnumValueChange}
                            imageFormatChange={imageFormatChange}
                            heightModeChange={heightModeChange}
                            flyToWaylineModeChange={flyToWaylineModeChange}
                            globalHeightChange={globalHeightChange}
                            autoFlightSpeedChange={autoFlightSpeedChange}
                            globalTransitionalSpeedChange={globalTransitionalSpeedChange}
                            globalWaypointTurnModeChange={globalWaypointTurnModeChange}
                            globalWaypointHeadingParamChange={globalWaypointHeadingParamChange}
                            gimbalPitchModeChange={gimbalPitchModeChange}
                            finishActionChange={finishActionChange}
                            addModel3Dtiles={addModel3Dtiles}
                            cameraFlyTo={cameraFlyTo}
                        />}
                    </div>
                    <div style={{ position: 'absolute', left: 30, top: '50%', zIndex: 1, width: 32, height: 32, backgroundColor: 'rgba(27, 48, 81, .7)' }}>
                        <div onClick={() => {
                            setMoreAction(!moreAction)
                            setWaylineSettingShow(false)
                            setWaypointIndex(null)
                        }} style={{ cursor: 'pointer', fontSize: 10, fontWeight: 800, color: '#ffffff', width: '100%', height: '100%', borderRadius: 3, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>•••</div>
                        {moreAction && <div style={{ width: '100%', position: 'absolute', right: -50, height: 356, top: -178, padding: 10 }}>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'takePhoto') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='拍照'>
                                    拍
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'startRecord') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='开始录像'>
                                    开
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'stopRecord') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='结束录像'>
                                    结
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'zoom') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='变焦'>
                                    变
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'rotateYaw', 0) }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='偏航角'>
                                    偏
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'gimbalRotate', 0) }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='转动云台'>
                                    转
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'hover') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='悬停'>
                                    悬
                                </Tooltip>
                            </div>
                            <div onClick={() => { addWaypointAction(seleteIndex, 'panoShot') }} style={{ cursor: 'pointer', width: 32, height: 32, borderRadius: 3, marginTop: 10, backgroundColor: 'rgba(27, 48, 81, .7)', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#ffffff' }}>
                                <Tooltip placement="right" title='全景拍照'>
                                    全
                                </Tooltip>
                            </div>
                        </div>}
                    </div>
                    <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
                        <MapControl viewerData={cesiumViewer} />
                    </div>
                </div>
            </div>
            <div style={{ width: '25%', height: '100%', }} >
                <div style={{ height: '100%', position: 'relative' }} >
                    <div style={{ height: '60%' }}>
                        <WayPointAction
                            wayline={wayline}
                            seleteIndex={seleteIndex}
                            actionIndex={actionIndex}
                            deleteWaypointAction={deleteWaypointAction}
                            editWaypointAction={editWaypointAction}
                            setAirplaneModelheading={setAirplaneModelheading}
                            setAirplaneModelPitch={setAirplaneModelPitch}
                        />
                    </div>
                    <div style={{ width: '100%', aspectRatio: (4 / 3), position: 'absolute', bottom: 0 }} id="cesisss2"></div>
                </div>
            </div>
        </div>
    )
};

export default WayLine3DPage;
