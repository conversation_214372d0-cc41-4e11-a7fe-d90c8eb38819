import React from 'react'
import { Progress } from 'antd'
import styles from './index.module.less'

const PanoramaProgressModal = ({
  visible = false,
  progress = 0,
  currentStep = 3000,
  status = 'idle',
  onCancel
}) => {
  // 根据当前步骤获取状态文本
  const getStepText = () => {
    switch (currentStep) {
      case 3002:
        return '正在拍摄全景图片...'
      case 3005:
        return '正在合成全景图片...'
      case 3000:
      default:
        return '正在准备全景拍照...'
    }
  }

  // 根据状态获取进度条颜色
  const getProgressColor = () => {
    switch (status) {
      case 'fail':
        return '#ff4d4f'
      case 'ok':
        return '#52c41a'
      case 'in_progress':
        return '#1890ff'
      default:
        return '#1890ff'
    }
  }

  // 获取主要提示文本
  const getMainText = () => {
    if (status === 'fail') {
      return '全景拍照失败，请重试'
    }
    if (status === 'ok') {
      return '全景拍照完成'
    }
    return '正在全景拍照中，请等待拍照完成，再执行其它操作...'
  }

  if (!visible) {
    return null
  }

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <div className={styles.progressContainer}>
          {/* 环状进度条 */}
          <Progress
            type="circle"
            percent={progress}
            size={120}
            strokeColor={getProgressColor()}
            strokeWidth={6}
            format={(percent) => (
              <div className={styles.progressText}>
                <div className={styles.percentText}>{percent}%</div>
                <div className={styles.stepText}>{getStepText()}</div>
              </div>
            )}
          />
        </div>

        {/* 主要提示文本 */}
        <div className={styles.mainText}>
          {getMainText()}
        </div>

        {/* 状态指示器 */}
        <div className={styles.statusIndicator}>
          <div className={`${styles.statusDot} ${styles[status]}`}></div>
          <span className={styles.statusText}>
            {status === 'in_progress' && '执行中'}
            {status === 'ok' && '已完成'}
            {status === 'fail' && '已失败'}
            {status === 'idle' && '准备中'}
          </span>
        </div>

        {/* 取消按钮（仅在拍照进行中显示） */}
        {status === 'in_progress' && onCancel && (
          <div className={styles.actionButtons}>
            <button 
              className={styles.cancelButton}
              onClick={onCancel}
            >
              停止拍照
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default PanoramaProgressModal