import { Get2 } from "@/services/general";
import { List, Card, Button, Tag, Image, Spin, Divider, Modal, Select } from "antd";
import { useState, useEffect, useRef } from "react";
import { useModel } from "umi";
import ModelAddPage from "./ModelAddPage";
import LastPageButton from "@/components/LastPageButton";
import commonStyles from "@/pages/common.less";
import ReactPlayer from 'react-player';
import ModelInfoPage from "./ModelInfoPage";
import { getImgUrl } from '@/utils/utils';
import { PlayCircleFilled } from "@ant-design/icons";
import './ModelListPage3.css'
import { color } from "echarts";


const ModelListPage3 = ({ isSipage, }) => {
  const [MList, setModelList] = useState([]);
  const [filtedMList, setFiltedMList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [playing, setPlaying] = useState(true);
  const [currentVideo, setCurrentVideo] = useState();
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]); // 选中的分类
  const [Fcatalogs, setFcatalogs] = useState([]); // 大类筛选
  const [selectedFcatalogs, setSelectedFcatalogs] = useState([]); // 选中的分类


  const playerRef = useRef(null);

  const { setPage, setModal, open, setOpen } = useModel("pageModel");
  const IsSIJump = localStorage.getItem("IsSIJump");

  // 获取模型数据
  const getModelData = async () => {
    const pst = await Get2("/api/v1/AI/GetModelList", {});
    const filteredData = pst.filter(item => item.Video !== '');
    setModelList(filteredData);
    setFiltedMList(filteredData); // 初始筛选条件
    if (pst && pst.length > 0) {
      const allCategories = [... new Set(
        pst.flatMap(item =>
          (item.UsrTips || '') //处理空值
            .split(',') // 按逗号分割
            .map(tag => tag.trim()) // 去除空格
            .filter(tag => tag) // 去除空值
        )
      )];
      const allFcatalogs = [... new Set(
        pst.flatMap(item =>
          (item.Fcatalog || '') //处理空值
            .split(',') // 按逗号分割
            .map(tag => tag.trim()) // 去除空格
            .filter(tag => tag) // 去除空值
        )
      )];
      setFcatalogs(allFcatalogs);
      setCategories(allCategories);
    }
  };

  // 获取默认数据
  useEffect(() => {
    getModelData();
  }, []);

  // 关闭页面的时候 重置 IsSIJump 值
  useEffect(() => {
    const handleUnload = () => {
      localStorage.setItem("IsSIJump", ""); // 重置为空值或你需要设置的值
    };

    window.addEventListener('beforeunload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, []);

  // 处理筛选
  const handleFillter = () => {
    let filtered = MList;

    if (selectedFcatalogs.length > 0) {
      filtered = filtered.filter(item => {
        const itemFcatalogs = (item.Fcatalog || '')
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag);
        return selectedFcatalogs.some(fcatalog => itemFcatalogs.includes(fcatalog));
      });
    }

    if (selectedCategories.length > 0) {
      filtered = filtered.filter(item => {
        const itemCategories = (item.UsrTips || '')
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag);
        return selectedCategories.every(category => itemCategories.includes(category));
      });
    }

    setFiltedMList(filtered);
  };

  // // 处理点击筛选按钮筛选
  // const handleCategoryClick = (category) => {
  //   setSelectedCategories(prev => {
  //     const newCategories = prev.includes(category)
  //       ? prev.filter(c => c !== category)
  //       : [...prev, category];
  //     handleFillter(newCategories); // 触发筛选
  //     return newCategories;
  //   });
  // };

  // 处理点击小类筛选按钮筛选(单选)
  const handleCategoryClick = (category) => {
    setSelectedCategories(prev =>
      prev.includes(category) ? [] : [category]
    );
  };

  // 处理点击大类筛选按钮筛选(单选)
  const handleFcatalogClick = (Fcatalog) => {
    setSelectedFcatalogs(prev =>
      prev.includes(Fcatalog) ? [] : [Fcatalog]
    );
  };

  // 自动触发筛选的useEffect
  useEffect(() => {
    handleFillter();
  }, [selectedFcatalogs, selectedCategories]);

  const filterSection = (
    <>
      <div style={{
        height: 70,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        padding: 16,
      }}>
        {/* 固定文字部分 */}
        <span style={{
          marginRight: 16,
          fontSize: 14,
          flexShrink: 0,
        }}>
          大类筛选：
        </span>

        {/* 可滚动按钮容器 */}
        <div style={{
          flex: 1,
          height: '100%',
          flexWrap: 'wrap',
          overflow: 'auto',
          display: 'flex',
          gap: 8,
          alignItems: 'center',
        }}>
          {Fcatalogs.map(Fcatalog => (
            <Button
              key={Fcatalog}
              type={selectedFcatalogs.includes(Fcatalog) ? 'primary' : 'default'}
              onClick={() => handleFcatalogClick(Fcatalog)}
              style={{
                borderRadius: 20,
                transition: 'all 0.3s',
                borderWidth: selectedCategories.includes(Fcatalog) ? 0 : 1,
                flexShrink: 0, // 保持按钮不收缩
              }}
            >
              {Fcatalog}
            </Button>
          ))}
        </div>
      </div>
      <div style={{
        height: 70,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        padding: 16,
      }}>
        {/* 固定文字部分 */}
        <span style={{
          marginRight: 16,
          fontSize: 14,
          flexShrink: 0,
        }}>
          小类筛选：
        </span>

        {/* 可滚动按钮容器 */}
        <div style={{
          flex: 1,
          height: '100%',
          flexWrap: 'wrap',
          overflow: 'auto',
          display: 'flex',
          gap: 8,
          alignItems: 'center',
        }}>
          {categories.map(category => (
            <Button
              key={category}
              type={selectedCategories.includes(category) ? 'primary' : 'default'}
              onClick={() => handleCategoryClick(category)}
              style={{
                borderRadius: 20,
                transition: 'all 0.3s',
                borderWidth: selectedCategories.includes(category) ? 0 : 1,
                flexShrink: 0, // 保持按钮不收缩
              }}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>
    </>

  );

  const getModelItem = (item) => {
    return (
      <div className="list-item-container" key={item.ID}>
        <div
          onClick={() =>
            setPage(<ModelInfoPage model={item}></ModelInfoPage>)
          }
          className="list-item-content">
          {/* 添加视频容器 */}
          <div
            onClick={e => {
              e.stopPropagation();
              handleVideoClick(item);
            }}
            style={{
              marginTop: 16,
              height: '50%',
              width: '100%',
              position: 'relative',
              marginBottom: 16,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              cursor: 'pointer',
              overflow: 'hidden',
              borderRadius: 8,
              transition: 'opacity 0.3s'
            }}
            // 鼠标悬停效果
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = 0.8;
              e.currentTarget.querySelector('.play-icon').style.opacity = 1;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = 1;
              e.currentTarget.querySelector('.play-icon').style.opacity = 0;
            }}
          >
            {/* 新增播放图标 */}
            <PlayCircleFilled
              className="play-icon"
              style={{
                position: 'absolute',
                fontSize: 48,
                color: 'white',
                opacity: 0, // 默认隐藏
                transition: 'opacity 0.3s',
                zIndex: 1
              }}
            />

            {item.Video ? (
              <ReactPlayer
                url={getImgUrl(item.Video)}
                controls={false}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  overflow: 'hidden',
                  width: '100%',
                  height: '100%',
                }}
                width="100%"
                height="100%"
              />
            ) : (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)'
              }}>
                <Spin tip="视频加载中..." size="large" />
              </div>
            )}
          </div>

          <div className="list-item-content-head">
            <lable>{item.AName}</lable>
            <Tag
              bordered={false}
              color={item.State === 1 ? 'green' : 'orange'}
              style={{
                position: 'absolute',
                right: 16
              }}
            >
              {item.State === 1 ? '已部署' : '未部署'}
            </Tag>
          </div>
          <div className="list-item-content-tag" style={{
            padding: '0 16px',
            display: 'flex',
            flexWrap: 'wrap',
            gap: 8,
            marginTop: 8
          }}>
            {(item.UsrTips || '')
              .split(',')
              .map(tag => tag.trim())
              .filter(tag => tag)
              .map((tag, index) => (
                <Tag
                  key={index}
                  color='default'
                  style={{
                    borderRadius: 20,
                    margin: 0,
                  }}
                >
                  {tag}
                </Tag>
              ))}
          </div>
        </div>
      </div>
    )
  }

  const refrush = async () => {
    setOpen(false);
    getModelData();
  };

  const addForm = () => {
    setModal(<ModelAddPage refrush={refrush} />);
    setOpen(true);
  };

  // 视频点击处理
  const handleVideoClick = (item) => {
    setCurrentVideo(item);
    setVisible(true);
    setPlaying(true); // 默认自动播放
  };

  // 关闭视频弹窗
  const handleCancel = () => {
    setVisible(false);
    setPlaying(false);
  };

  // 弹窗内容
  const modalContent = (
    <div style={{
      position: 'relative',
      paddingTop: '56.25%',  // 保持宽高比为16:9
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}>
        <ReactPlayer
          ref={playerRef}
          url={getImgUrl(currentVideo?.Video)}
          playing={playing}
          controls={true}
          width="100%"
          height="100%"
          style={{
            objectFit: 'contain',
          }}
          config={{
            file: {
              attributes: {
                style: {
                  objectFit: 'contain', // 保持视频比例不变
                  width: '100%',
                  height: '100%'
                }
              }
            }
          }}
        />
      </div>
    </div>
  );


  const exr = (
    <Button className={commonStyles.addButton} type="primary" onClick={addForm}>
      新建模型
    </Button>
  );

  return (
    <Card
      title={!isSipage && <LastPageButton title="AI模型" />}
    >
      {/* 筛选部分 */}
      {filterSection}
      <div className={!isSipage ? commonStyles.my_scroll_y2 : commonStyles.my_scroll_y1}>
        <div className="modle-list-body">
          <List
            dataSource={filtedMList}
            grid={{ column: 5 }}
            renderItem={(item) => (
              <List.Item>
                {getModelItem(item)}
              </List.Item>
            )}
          />
        </div>
      </div>
      <Modal
        title={currentVideo?.AName || '视频播放'}
        open={visible}
        onCancel={handleCancel}
        footer={null}
        width={'70vw'}
        centered
      >
        {modalContent}
      </Modal>
    </Card>
  );
};

export default ModelListPage3;
