import React, { useRef, useEffect } from "react";
import Srs from "./srs.sdk";
import { getGuid } from "@/utils/helper";
import { getRtmpWebrtcUrl } from "@/utils/config";

const VideoViewer = ({ sn3, ctl, height, width }) => {
  const RtmpUrl=getRtmpWebrtcUrl();
  const url = RtmpUrl + sn3;
  console.log("机场视频流地址", url);
  
  const url2 = url + getGuid();
  useEffect(() => {
   
      const player = document.getElementById("webrtc_player2");
      const rtcPlayer = new Srs.SrsRtcPlayerAsync();
      rtcPlayer.play(url);
      // video标签
      player.srcObject = rtcPlayer.stream;
      return () => {
        rtcPlayer.close();
      }
  }, [url]);

  return (
    <div id="container">
      <video
        id="webrtc_player2"
        autoPlay
        style={{ objectFit: "cover", cursor: "default" }}
        width={width?width:"100%"}
        height={height}
      ></video>
    </div>
  );
};

export default VideoViewer;
