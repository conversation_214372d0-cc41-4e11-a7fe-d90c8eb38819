import { useState, useEffect, useRef, useCallback } from 'react';
import { Button,Tabs, Card, Table, Form, Input, Select, Modal, DatePicker,message,Row, Col,Descriptions,Slider,InputNumber,Image,Spin } from 'antd';
import { axiosApi } from "@/services/general";
import { columnTranslation } from "./TableHelper";
import { timeFormat } from "@/utils/helper";
import { isEmpty,getImgSLTUrl,getImgUrl,getDeviceName } from "@/utils/utils";
import WaylineTable from './WaylineTable';
import FlightlineTable from './FlightlineTable';
import { PictureOutlined,LeftOutlined, RightOutlined } from '@ant-design/icons';
import { TransformWrapper, TransformComponent,} from 'react-zoom-pan-pinch';
import { useModel } from "umi";
import { queryPage2 } from "@/utils/MyRoute";

const RecordsDetailPage = () => {
  const { setPage, lastPage } = useModel("pageModel");
  const [record, setRecord] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState({
    wayline: [],
    flightline: [],
    filteredFlightline: null,
  });
  const [filterParams, setFilterParams] = useState({
    waylineId: null,
    selectedDate: null,
  });
  const [form] = Form.useForm();


  // const [currentTablePage, setCurrentTablePage] = useState(1);
  const [selectedRows, setSelectedRows] = useState([]);

  const [currentView, setCurrentView] = useState('basic');
  const [imageData1, setImageData1] = useState(null);
  const [imageData2, setImageData2] = useState(null);

  const [sliderValue, setSliderValue] = useState(1);

  const [imgUrl1 , setImgUrl1] = useState(null);
  const [imgUrl2 , setImgUrl2] = useState(null);
  const [SLTUrl1 , setSLTUrl1] = useState(null);
  const [SLTUrl2 , setSLTUrl2] = useState(null);

  const [modalVisible, setModalVisible] = useState(false);

  const [currentTable, setCurrentTable] = useState('wayline'); // 默认显示航线信息
  // 同步缩放和拖拽 两幅图片共用的状态
  const [transformState, setTransformState] = useState({
    scale: 1,
    positionX: 0,
    positionY: 0
  });

  const handlePageChange = (page) => {
    setPage(queryPage2(page));
  };

  // 保持表单实例稳定的引用
  const formRef = useRef(form);
  useEffect(() => {
    formRef.current = form;
  }, [form]);

  // 第一张图片url的获取
  useEffect(() => {
    if (imageData1 && imageData2) {
      sliderOnChange(sliderValue || 1);
    }
  }, [imageData1, imageData2]);


  // 挂载完成后 获取点击的记录 并且查询对应记录的详情信息
  useEffect(() => {
    const localStorageRecord = JSON.parse(localStorage.getItem('record'));
    setRecord(localStorageRecord);
    getDefaltData(localStorageRecord);
  }, []);

  // // 当数据变化时重置页码
  // useEffect(() => {
  //   setCurrentTablePage(1);
  // }, [dataSource]);


  // 处理滑块变化
  const sliderOnChange = (newValue) => {
    setSliderValue(newValue);

    if (!imageData1 || !imageData2) return;
    // 计算两个数组的长度
    const len1 = imageData1?.length || 0;
    const len2 = imageData2?.length || 0;
    const maxLength = Math.max(len1, len2);
    // 确保滑块值在有效范围内
    const safeValue = Math.max(1, Math.min(newValue, maxLength));

    // 处理第一组图片
    const newImg1 =
      len1 >= safeValue
        ? getImgUrl(imageData1[safeValue - 1]?.ObjectName)
        : null;
    const newSLT1 =
      len1 >= safeValue
        ? getImgSLTUrl(imageData1[safeValue - 1]?.SLTImg)
        : null;

    // 处理第二组图片
    const newImg2 =
      len2 >= safeValue
        ? getImgUrl(imageData2[safeValue - 1]?.ObjectName)
        : null;
    const newSLT2 =
      len2 >= safeValue
        ? getImgSLTUrl(imageData2[safeValue - 1]?.SLTImg)
        : null;

    setImgUrl1(newImg1);
    setSLTUrl1(newSLT1);
    setImgUrl2(newImg2);
    setSLTUrl2(newSLT2);
  };
  
  // 获取默认数据
  const getDefaltData = async (record) => {
    try {
      // console.log('@@@record',record);
      setLoading(true);
      const url = `/api/v1/Wayline/GetByTaskId?ID=${record.TaskID}`
      const res = await axiosApi(url,'GET',null);
      if(!res.data.wayline){
        message.error('该任务没有航线信息');
      }else{
        setDataSource(res.data);
        setLoading(false);
        console.log("@@@res", res);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const openBasicInfo = () => {
    setCurrentView("basic");
    setSelectedRows([]);
  };

  // 打开图像对比页面相关逻辑
  const openImageCompare = () => {
    if (selectedRows.length !== 2) {
      message.error("请勾选两个飞行记录进行对比");
      return;
    }
    setCurrentView('image');
    getImageData(selectedRows);
    
  };

  // 同步处理缩放、拖拽函数
  const handleTransform = useCallback((newState) => {
    setTransformState({
      scale: newState.scale,
      positionX: newState.positionX,
      positionY: newState.positionY
    });
  }, []);

  // 通过勾选的两个飞行记录获取对应的图像数据
  const getImageData = async (selectedRows) => {
    try {
      const imageRes1 = await axiosApi(
        `/api/v1/Media/GetListByTaskId?id=${selectedRows[0].TaskID}`,
        "GET",
        null
      );
      const imageRes2 = await axiosApi(
        `/api/v1/Media/GetListByTaskId?id=${selectedRows[1].TaskID}`,
        "GET",
        null
      );
      const filteredData1 = imageRes1.filter((item) => item.MediaType === 5);
      const filteredData2 = imageRes2.filter((item) => item.MediaType === 5);
      setImageData1(filteredData1);
      setImageData2(filteredData2);
      setSliderValue(1); // 重置滑块
    } catch (error) {
      console.log(error);
    }
  };

  // 处理筛选
  const handleFilter = (params) => {
    const filteredFlightline =
      dataSource.flightline?.filter((f) => {
        const waylineMatch =
          !params.waylineId || f.FlightLineId === params.waylineId;
        const dateMatch =
          !params.selectedDate ||
          f.CreateTime.split("T")[0] === params.selectedDate;
        return waylineMatch && dateMatch;
      }) || []; // 确保始终返回数组

    setDataSource((prev) => ({
      ...prev,
      filteredFlightline: filteredFlightline.length ? filteredFlightline : [], // 空数组表示无数据
    }));
  };

  // 选择的行
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log("selectedRows: ", selectedRows);
      setSelectedRows(selectedRows);
    },
  };

  const exr = (
    <div style={{ display: 'flex', gap: 10 }}>
      {/* 当处于图像对比页面时显示返回基本信息按钮 */}
      {currentView === 'image' && (
        <Button 
          onClick={() => openBasicInfo()}
        >
          基本信息
        </Button>
      )}
    </div>
  );
  return (
    <div 
      className='blackBackground'
      style={{
        height: "calc(100vh - 56px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Card
        title={"巡检详情"}
        extra={exr}
        style={{
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: "hidden",
        }}
        styles={{
          body: {
            flex: 1,
            display: "flex",
            flexDirection: "column",
            padding: 0,
            overflow: "hidden",
          },
        }}
      >
        {currentView === 'basic' ? (
          <>
            {/* 上方表单部分 */}
            <Form
              form={form}
              onFinish={console.log("onFinish")}
              layout="inline"
              style={{  
                marginBottom: 10,
                borderBottom: '1px solid #333',
                padding: '16px 24px',
                flexShrink: 0,
                display: "flex",
                flexWrap: "wrap",
                gap: 16, // 元素间距
                overflowY: 'auto',
                minWidth: 1200,
              }}
            >
              {record &&
                Object.entries(record)
                  // 筛选出不用的字段
                  .filter(([key, value]) => !['ID', 'TaskKeyValue','TableName','State','OrgCode','TaskContent','Unit','Cyclicplan'].includes(key))
                  .map(([key, value]) => (
                    <Form.Item
                      label={columnTranslation[key] || key}
                      key={key}
                      style={{
                        marginBottom: 0,
                        minWidth: 300,
                      }}
                    >
                      <Input
                        value={
                          key === "CreateTM"
                            ? isEmpty(value)
                              ? "-"
                              : timeFormat(value)
                            : String(value)
                        }
                        readOnly
                      />
                    </Form.Item>
                  ))}
            </Form>
            {/* 下方表格部分 */}
            <div style={{ 
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              padding: '0 24px 24px',
            }}>
              <Tabs
                destroyInactiveTabPane
                activeKey={currentTable}
                onChange={setCurrentTable}
                tabBarStyle={{
                  marginBottom: 16,
                  display: 'flex',
                  alignItems: 'center'
                }}
                tabBarExtraContent={
                  <div style={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: 16,
                  }}>
                    {currentTable === 'flightline' && (
                      <>
                        <Button 
                          style={{position:"fixed" ,left:"15%"}}
                          type={currentView === 'image' ? 'primary' : 'default'}
                          onClick={openImageCompare}
                        >
                          图像对比
                        </Button>
                        <Form layout="inline" style={{ position:"fixed" ,left:"20%" }}>
                          <Form.Item label="航线名称">
                            <Select 
                              placeholder="筛选航线" 
                              style={{ width: 200 }}
                              allowClear
                              options={dataSource.wayline?.map(item => ({
                                label: item.WayLineName,
                                value: item.WanLineId
                              }))}
                              onChange={value => {
                                const newParams = { ...filterParams, waylineId: value };
                                setFilterParams(newParams);
                                handleFilter(newParams);
                              }}
                            />
                          </Form.Item>
                          <Form.Item label="创建时间">
                            <DatePicker
                              placeholder="筛选时间"
                              allowClear 
                              onChange={(date, dateString) => {
                                const newParams = {
                                  ...filterParams,
                                  selectedDate: dateString || null
                                };
                                setFilterParams(newParams);
                                handleFilter(newParams);
                              }}
                            />
                          </Form.Item>
                        </Form>
                      </>
                    )}
                  </div>
                }
              >
                <Tabs.TabPane tab="航线信息" key="wayline">
                  <Table
                    style={{ height: '100%' }}
                    pagination={{ pageSize: 5 }}
                    rowKey={record => record.ID}
                    loading={loading}
                    bordered
                    dataSource={dataSource.wayline}
                    columns={WaylineTable()}
                    scroll={{ y: 'calc(100vh - 400px)', x: 'max-content' }}
                  />
                </Tabs.TabPane>
                
                <Tabs.TabPane tab="飞行记录" key="flightline">
                  <Table
                    rowSelection={{ type: "checkbox", ...rowSelection }}
                    style={{ height: '100%' }}
                    pagination={{ pageSize: 5 }}
                    rowKey={record => record.ID}
                    loading={loading}
                    bordered
                    dataSource={dataSource.filteredFlightline || dataSource.flightline}
                    columns={FlightlineTable(setPage)}
                    scroll={{ y: 'calc(100vh - 460px)', x: 'max-content' }}
                  />
                </Tabs.TabPane>
              </Tabs>
            </div>
          </>
        ) : (
          // 图像对比部分
          <div style={{ 
            flex: 1,
            padding: 24,
            color: 'white',
            fontSize: 18,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 20
          }}>
            <div style={{height: '10%',width: '100%',display: 'flex',gap: 16,flexDirection: 'column',}}> 
              <Row justify="center">
                <Col>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Button 
                      style={{ backgroundColor: "transparent",border:'none' }}
                      icon={<LeftOutlined />}
                      onClick={() => sliderOnChange(Math.max(1, sliderValue - 1))}
                    />
                    <InputNumber
                      controls={false}
                      min={1}
                      max={selectedRows[1]?.PhotoCount || 1}
                      style={{ 
                        width: 120,
                      }}
                      value={sliderValue}
                      onChange={value => sliderOnChange(value || 1)}
                      onBlur={(e) => {
                        if (!e.target.value) {
                          sliderOnChange(1)
                        }
                      }}
                      className="hide-number-arrows" // 添加自定义class
                    />
                    <Button 
                      style={{ backgroundColor: "transparent",border:'none' }}
                      icon={<RightOutlined />}
                      onClick={() => sliderOnChange(Math.min(selectedRows[1]?.PhotoCount || 1, sliderValue + 1))}
                    />
                    <Button 
                      type="primary" 
                      onClick={() => setModalVisible(true)}
                      style={{ position: 'fixed', right: 200 }}
                    >
                      查看大图对比
                    </Button>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Slider
                    min={1}
                    max={Math.max(
                      imageData1?.length || 0,
                      imageData2?.length || 0
                    )}
                    onChange={sliderOnChange}
                    value={typeof sliderValue === "number" ? sliderValue : 0}
                  />
                </Col>
              </Row>
            </div>
            <div style={{height: '90%',width: '100%',display: 'flex',flexDirection: 'column',}}> 
              {/* 详情部分 */}
              <div style={{height: '100%',width: '100%',display: 'flex',justifyContent: 'space-evenly'}}> 
                <div style={{width:'15%',display: 'flex',justifyContent: 'space-evenly'}}>  
                    <Card 
                      title = {selectedRows[0].FlightLineName}
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      <Descriptions style={{ padding: 12.0 }} column={1}>
                        <Descriptions.Item label="执行机场">
                          {getDeviceName(selectedRows[0].DeviceSN)}
                        </Descriptions.Item>
                        <Descriptions.Item label="开始时间">
                          {timeFormat(selectedRows[0].TaskBeginTime)}
                        </Descriptions.Item>
                        {/* <Descriptions.Item label="结束时间">{timeFormat(danger.TaskInvokeTime)}</Descriptions.Item> */}
                        <Descriptions.Item label="飞行时间">
                          {(selectedRows[0].FlyTM / 60).toFixed(0) + " 分钟"}
                        </Descriptions.Item>
                        <Descriptions.Item label="飞行距离">
                          {selectedRows[0].Distance.toFixed(1) + " 公里"}
                        </Descriptions.Item>
                        <Descriptions.Item label="拍摄文件">
                          {selectedRows[0].PhotoCount + " 个"}
                        </Descriptions.Item>
                        <Descriptions.Item label="上传文件">
                          {selectedRows[0].PhotoUpload + " 个"}
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                </div>
                
                <div width={'35%'} style={{marginRight:10}}>
                  <Image style={{objectFit: 'contain'}} height={'100%'} src={SLTUrl1} preview={{src: imgUrl1}}/>
                </div>
                <div width={'35%'} style={{marginLeft:10}}>
                  <Image style={{objectFit: 'contain'}} height={'100%'} src={SLTUrl2} preview={{src: imgUrl2}}/>
                </div>
                
                <div style={{width:'15%',display: 'flex',gap: 16,justifyContent: 'space-evenly'}}>  
                    <Card 
                      title = {selectedRows[1].FlightLineName}
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      <Descriptions style={{ padding: 12.0 }} column={1}>
                        <Descriptions.Item label="执行机场">
                          {getDeviceName(selectedRows[1].DeviceSN)}
                        </Descriptions.Item>
                        <Descriptions.Item label="开始时间">
                          {timeFormat(selectedRows[1].TaskBeginTime)}
                        </Descriptions.Item>
                        {/* <Descriptions.Item label="结束时间">{timeFormat(danger.TaskInvokeTime)}</Descriptions.Item> */}
                        <Descriptions.Item label="飞行时间">
                          {(selectedRows[1].FlyTM / 60).toFixed(0) + " 分钟"}
                        </Descriptions.Item>
                        <Descriptions.Item label="飞行距离">
                          {selectedRows[1].Distance.toFixed(1) + " 公里"}
                        </Descriptions.Item>
                        <Descriptions.Item label="拍摄文件">
                          {selectedRows[1].PhotoCount + " 个"}
                        </Descriptions.Item>
                        <Descriptions.Item label="上传文件">
                          {selectedRows[1].PhotoUpload + " 个"}
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                </div>
              </div>
            </div>
            {/* 大图对比弹窗 */}
            <Modal
              title="图像对比"
              open={modalVisible}
              onCancel={() => setModalVisible(false)}
              footer={null}
              width="90vw"
              styles={{
                body: {
                  height: "70vh",
                  display: "flex",
                  flexDirection: "column",
                  padding: 24,
                },
              }}
            >
              <div style={{
                flex: 1,
                display: 'flex',
                gap: 24,
                overflow: 'hidden',
                marginBottom: 24
              }}>
                {/* 左侧图片 */}
                <TransformWrapper
                  key={`left-${transformState.scale}-${transformState.positionX}`}
                  initialScale={transformState.scale}
                  initialPositionX={transformState.positionX}
                  initialPositionY={transformState.positionY}
                  onZoom={(e) => handleTransform(e.state)}
                  onPanningStop={(e) => handleTransform(e.state)}
                  minScale={0.5}
                  maxScale={8}
                  wheel={{ step: 0.08 }}
                  doubleClick={{ disabled: true }}
                  style={{ flex: 1 }}
                >
                  {({ zoomIn, zoomOut, resetTransform }) => (
                    <TransformComponent>
                      <div 
                        style={{ 
                          width: '100%',
                          height: '100%',
                          cursor: 'pointer',
                        }}
                        onDoubleClick={(e) => {
                          e.stopPropagation();
                          resetTransform();
                        }}
                      >
                        <Image
                          preview={false}
                          style={{ 
                            width: '100%',
                            height: '100%',
                          }}
                          src={imgUrl1}
                          placeholder={
                            <div style={{ 
                              background: '#fff',
                              height: '100%',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}>
                              <Spin size="large" />
                            </div>
                          }
                        />
                      </div>
                    </TransformComponent>
                  )}
                </TransformWrapper>

                {/* 右侧图片 */}
                <TransformWrapper
                  key={`right-${transformState.scale}-${transformState.positionX}`}
                  initialScale={transformState.scale}
                  initialPositionX={transformState.positionX}
                  initialPositionY={transformState.positionY}
                  onZoom={(e) => handleTransform(e.state)}
                  onPanningStop={(e) => handleTransform(e.state)}
                  minScale={0.5}
                  maxScale={8}
                  wheel={{ step: 0.08 }}
                  doubleClick={{ disabled: true }}
                  style={{ flex: 1 }}
                >
                  {({ zoomIn, zoomOut, resetTransform }) => (
                    <TransformComponent>
                      <div 
                        style={{ 
                          width: '100%',
                          height: '100%',
                          cursor: 'pointer'
                        }}
                        onDoubleClick={(e) => {
                          e.stopPropagation();
                          resetTransform();
                        }}
                      >
                        <Image
                          preview={false}
                          style={{ 
                            width: '100%',
                            height: '100%',
                          }}
                          src={imgUrl2}
                          placeholder={
                            <div style={{ 
                              background: '#fff',
                              height: '100%',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}>
                              <Spin size="large" />
                            </div>
                          }
                        />
                      </div>
                    </TransformComponent>
                  )}
                </TransformWrapper>
              </div>
              <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
                <PictureOutlined />
                <Slider
                  style={{ flex: 1 }}
                  min={1}
                  max={Math.max(
                    imageData1?.length || 0,
                    imageData2?.length || 0
                  )}
                  value={sliderValue}
                  onChange={sliderOnChange}
                />
                <InputNumber
                  min={1}
                  value={sliderValue}
                  onChange={sliderOnChange}
                  style={{ width: 100 }}
                />
              </div>
            </Modal>
          </div>
        )}
      </Card>
    </div>
  );
};

export default RecordsDetailPage;
