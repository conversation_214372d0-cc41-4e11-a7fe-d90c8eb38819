.Card {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.left_div {
  width: 100%;
  height: 64vh;
}

.splitterOne_Topdiv {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  align-items: center;
  padding: 12px;
}

.splitterOne_Topdiv .QieHaun_Button {
  /* margin-left: auto; */
  margin-left: 20px;
  color: #fff;
  // background-color: #0a3cb9cc;
}

.splitterTwo_Topdiv {
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
}

.splitterTwo_Topdiv .QieHaun_Button {
  margin-left: auto;
  color: #fff;
  // background-color: #0a3cb9cc;
}

.Tabs_Class {
  width: 42vw;
  height: 64vh;
}

.Tabs_Class .ant-tabs-tab {
  padding: 8px 15px !important;
}

.table {
  width: 100%;
  // height: 90%;
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.Label_Class {
  margin-bottom: 10px; /* 增加底部间距 */
  font-size: 15px;
  font-weight: bold;
  transition: color 0.3s;
  line-height: 1.5; /* 增加行高 */
}

.input_Ttile_Class {
  height: 35px;
  width: 13vw;
  padding: 10px;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input_content_Class {
  height: 250px; /* 增加高度 */
  width: 30vw; /* 增加宽度 */
  padding: 10px;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input_Ttile_Class:focus,
.input_content_Class:focus {
  outline: none;
}

.Radio_Class {
  margin-left: 10px;
  font-size: 15px;
  margin-right: 10px;
  margin-top: 10px;
  font-weight: bold;
  line-height: 1.5; /* 增加行高 */
}

.word_ellipsis{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
