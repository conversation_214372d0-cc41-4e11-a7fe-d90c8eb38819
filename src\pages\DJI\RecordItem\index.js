import React, {  useState, useEffect } from 'react';
import {  Card, Timeline, Input,Button,  message, Col ,Row} from 'antd';
import { getBodyH, isEmpty } from '@/utils/utils';
import { Get2, Post2 } from '@/services/general';
import { timeFormat2 } from '@/utils/helper';

//import { useModel } from 'umi';

const RecordItemListPage = ({ spaceID,ifExr }) => {

  const [RecordItemList, setRecordItemList] = useState({});
  //const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
  const [content, setContent] = useState('');


  useEffect(() => {

    const getRecordItemData = async () => {
      const pst = await Get2('/api/v1/RecordItem/GetList?id=' + spaceID, {});
      if (isEmpty(pst)) return;
      setRecordItemList(pst);
    };

    // getLineData();
    getRecordItemData();
    

  }, []);


  const refrush = async () => {
    const pst = await Get2('/api/v1/RecordItem/GetList?id=' + spaceID, {});
    if (isEmpty(pst)) return;
    setRecordItemList(pst);

  };

  const onSave = async (e) => {
    const user = JSON.parse(localStorage.getItem('user'));
    console.log('RecordItemAddForm', user);
    const data = {
      Title: content,
      SpaceID:spaceID,
    }
    const xx = await Post2('/api/v1/RecordItem/Add', data);
    if (isEmpty(xx.err)) {
      message.info("添加成功！")
    }
    refrush();
  };

  const getItems = (data) => {
    const list = []
    if(isEmpty(data)) return list;
    console.log('getItems',data)
    data.forEach(e => {
      list.push(
        {
          children: <div><div>{e.Title}</div>
          <div style={{color:'grey',fontSize:14.0}}>{timeFormat2(e.TM)}</div> 
          </div>,
        },
        
        );
    });
    return list;
  }

  const exr =()=>{
    if(!ifExr) return;
  <Row>
    <Col style={{width:240.0}}><Input
      onChange={(e) => setContent(e.target.value)}>
    </Input>
    </Col>
    <Col style={{marginLeft:24.0}}>
      <Button onClick={() => onSave()}>添加记录</Button>
    </Col>
  </Row>
 }


  return (

   

      <div  >
        <div>
          {isEmpty(RecordItemList) ? <div /> : <Timeline
            items={getItems(RecordItemList)}
          />}
        </div>
      </div>

  )
};

export default RecordItemListPage;
