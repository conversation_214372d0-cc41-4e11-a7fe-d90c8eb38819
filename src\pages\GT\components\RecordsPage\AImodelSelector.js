// AIModelSelector.jsx
import { useState, useEffect } from 'react';
import { Modal, List, Checkbox, Descriptions, Select, Spin, message, Radio, Table, Button, Tag, Input } from 'antd';
import { axiosApi, Get2 } from '@/services/general'; // 根据实际路径调整
import './AImodelSelector.less';
import { CloseOutlined } from '@ant-design/icons';


const AIModelSelector = ({
    selectedModels,
    onChange,
    type,
    isSingle,
}) => {
    const [modelList, setModelList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [tempSelectedModels, setTempSelectedModels] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [category, setCategory] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState('');
    const [classification, setClassification] = useState([]);
    const [selectedClassification, setSelectedClassification] = useState('');

    // 获取模型数据
    const fetchModels = async () => {
        try {
            setLoading(true);
            const pst = await axiosApi('/api/v1/AIAlgorithm/pageList', 'GET', {
                classification: type,
                page: 1,
                pageSize: 100,
            });
            if (pst.code == 1) {
                setModelList(pst.data.list || []);
                if (pst && pst.data.list.length > 0) {
                    const allCategory = [... new Set(
                        pst.data.list.flatMap(item =>
                            (item.category || '') //处理空值
                                .split(',') // 按逗号分割
                                .map(tag => tag.trim()) // 去除空格
                                .filter(tag => tag) // 去除空值
                        )
                    )];
                    const allClassification = [... new Set(
                        pst.data.list.flatMap(item =>
                            (item.classification || '')
                                .split(',')
                                .map(tag => tag.trim())
                                .filter(tag => tag)
                        )
                    )];
                    setCategory(allCategory);
                    setClassification(allClassification);
                }
            } else if (pst.code == 0) {
                message.error(pst.msg);
            }

        } catch (error) {
            message.error('算法列表加载失败');
            console.log('@@@', error);
        } finally {
            setLoading(false);
        }
    };

    // 当打开模态框时获取数据
    useEffect(() => {
        if (modalVisible) {
            fetchModels();
        }
    }, [modalVisible]);

    useEffect(() => {
        console.log('@@@tempSelectedModels', tempSelectedModels);
    }, [tempSelectedModels]);

    // 同步临时状态
    useEffect(() => {
        // 将传入的selectedModels转换为数组形式
        setTempSelectedModels(isSingle ?
            (selectedModels ? [selectedModels] : []) // 单选时强制转为数组
            : selectedModels
        );
    }, [selectedModels]);

    const handleConfirm = () => {
        // 如果是单选 返回数组第一项
        onChange({
            selectedModels: isSingle ?
                (tempSelectedModels.length > 0 ? tempSelectedModels[0] : null)
                : tempSelectedModels
        });
        setModalVisible(false);
    };

    // 生成识别内容选项
    // const getAList = (selectedModel) => {
    //     const labels = selectedModel?.AList?.split(',') || [];
    //     return (
    //         <Checkbox.Group
    //             value={cls[selectedModel.Guid] || []}
    //             onChange={values => onChange({
    //                 cls: { ...cls, [selectedModel.Guid]: values }
    //             })}
    //             options={labels.map((label, index) => ({
    //                 label,
    //                 value: index,
    //             }))}
    //         />
    //     );
    // };

    const handleDeleteModel = (model) => {
        if (isSingle) {
            // 单选模式直接置空
            onChange({ selectedModels: null });
        } else {
            // 多选模式过滤数组
            const newModels = selectedModels.filter(m => m.funcID !== model.funcID);
            onChange({ selectedModels: newModels });
        }
    };

    return (
        <Descriptions column={2} colon={false} bordered style={{ marginTop: 16 }} labelStyle={{
            width: '20%',
            paddingRight: 16
        }}
            contentStyle={{
                width: '80%'
            }}>
            <Descriptions.Item
                label="选择AI算法"
                span={24}
            >
                <Button
                    type="primary"
                    onClick={() => setModalVisible(true)}
                >
                    {isSingle
                        ? (selectedModels ? '已选择1个算法' : '选择AI算法')
                        : (selectedModels.length > 0
                            ? `已选择${selectedModels.length}个算法`
                            : '选择AI算法')
                    }
                </Button>
                <Modal
                    title="选择AI算法"
                    open={modalVisible}
                    width="50vw"
                    onCancel={() => setModalVisible(false)}
                    onOk={handleConfirm}
                >
                    <Spin spinning={loading}>
                        {/* 大类筛选区域 */}
                        <div className="filter-section">
                            <div className="filter-label">大类筛选：</div>
                            <Radio.Group
                                value={selectedCategory}
                                onChange={e => setSelectedCategory(e.target.value)}
                                optionType="button"
                                buttonStyle="solid"
                                style={{ flex: 1 }} // 占据剩余空间
                            >
                                <Radio.Button key="all-category" value="" style={{ margin: 4 }}>
                                    全部
                                </Radio.Button>
                                {category.map(item => (
                                    <Radio.Button
                                        key={item}
                                        value={item}
                                        style={{ margin: 4 }}
                                    >
                                        {item}
                                    </Radio.Button>
                                ))}
                            </Radio.Group>
                        </div>
                        {/* 小类筛选区域 */}
                        <div className="filter-section">
                            <div className="filter-label">小类筛选：</div>
                            <Radio.Group
                                value={selectedClassification}
                                onChange={e => setSelectedClassification(e.target.value)}
                                optionType="button"
                                buttonStyle="solid"
                                style={{ flex: 1 }} // 占据剩余空间
                            >
                                <Radio.Button key="all-classification" value="" style={{ margin: 4 }}>
                                    全部
                                </Radio.Button>
                                {classification.map(item => (
                                    <Radio.Button
                                        key={item}
                                        value={item}
                                        style={{ margin: 4 }}
                                    >
                                        {item}
                                    </Radio.Button>
                                ))}
                            </Radio.Group>
                        </div>
                        <div className="model-table">
                            <Table
                                rowKey="funcID"
                                dataSource={modelList.filter(model => {
                                    const modelCatalogs = (model.category || '').split(',').map(t => t.trim()).filter(t => t)
                                    const modelTypes = (model.classification || '').split(',').map(t => t.trim()).filter(t => t)

                                    const categoryMatch = selectedCategory ? modelCatalogs.includes(selectedCategory) : true
                                    const classificationMatch = selectedClassification ? modelTypes.includes(selectedClassification) : true

                                    return categoryMatch && classificationMatch
                                })}
                                pagination={false}
                                scroll={{ y: '30vh' }}
                                columns={[
                                    {
                                        title: '算法名称',
                                        dataIndex: 'sceneFunc',
                                        width: 200,
                                        render: (text, record) => (
                                            isSingle ? (
                                                <Radio
                                                    checked={tempSelectedModels.some(m => m.funcID === record.funcID)}
                                                    onChange={() => {
                                                        setTempSelectedModels([record]); // 单选时直接替换数组
                                                    }}
                                                >
                                                    {text}
                                                </Radio>
                                            ) : (
                                                <Checkbox
                                                    checked={tempSelectedModels.some(m => m.funcID === record.funcID)}
                                                    onChange={e => {
                                                        const checked = e.target.checked;
                                                        setTempSelectedModels(prev =>
                                                            checked
                                                                ? [...prev, record]
                                                                : prev.filter(m => m.funcID !== record.funcID)
                                                        );
                                                    }}
                                                >
                                                    {text}
                                                </Checkbox>
                                            )
                                        )
                                    },

                                    {
                                        title: '算法详情',
                                        render: (_, record) => (
                                            <div className="model-detail-container">
                                                <Tag
                                                    className="model-tag"
                                                    color={record.category === '视频' ? 'blue' : record.category === '图片' ? 'gold' : undefined}
                                                >
                                                    {record.category}
                                                </Tag>
                                                <span className="model-description">
                                                    {record.description}
                                                </span>
                                            </div>
                                        )
                                    },
                                    {
                                        title: '参数配置',
                                        width: 100,
                                        render: (_, record) => (
                                            <>
                                                {record.pyFunc === '' ? (
                                                    <span>--</span>
                                                ) : (
                                                    <Input
                                                        value={tempSelectedModels.find(m => m.funcID === record.funcID)?.pyFunc || ''}
                                                        onChange={(e) => {
                                                            const newValue = e.target.value;
                                                            setTempSelectedModels(prev =>
                                                                prev.map(model =>
                                                                    model.funcID === record.funcID
                                                                        ? { ...model, pyFunc: newValue }
                                                                        : model
                                                                )
                                                            );
                                                        }}
                                                    />
                                                )}
                                            </>
                                        )
                                    }


                                ]}
                                rowClassName={record =>
                                    tempSelectedModels.some(m => m.funcID === record.funcID)
                                        ? 'selected-row'
                                        : ''
                                }
                            />
                        </div>
                    </Spin>
                </Modal>

            </Descriptions.Item>

            {((isSingle && selectedModels) || (!isSingle && selectedModels?.length > 0)) && (
                <Descriptions.Item
                    label="选择内容"
                    span={24}
                    styles={{
                        label: { width: 150, whiteSpace: 'nowrap', paddingRight: 12 },
                        content: { width: 'calc(100% - 150px)' }
                    }}
                >
                    <Table
                        rowKey="funcID"
                        dataSource={isSingle ? [selectedModels] : selectedModels}
                        pagination={false}
                        scroll={{ y: 200 }}
                        columns={[
                            {
                                title: '算法名称',
                                render: (_, record) => (
                                    <div className="model-item">
                                        <span className="model-name">{record.sceneFunc}</span>
                                    </div>
                                )
                            },
                            {
                                title: '资源类型',
                                align: "center",
                                render: (_, record) => (
                                    <Tag
                                        color={record.category === '视频' ? 'blue' : record.category === '图片' ? 'gold' : undefined}
                                    >
                                        {record.category}
                                    </Tag>
                                )
                            },
                            {
                                title: "任务描述",
                                dataIndex: "description",
                                key: "description",
                                align: "center",
                            },
                            {
                                title: "配置参数",
                                align: "center",
                                render: (_, record) => (
                                    <>
                                        {record.pyFunc === '' ? (
                                            <span>--</span>
                                        ) : (
                                            <span>{record.pyFunc}</span>
                                        )
                                        }
                                    </>

                                )
                            },
                            {
                                title: '操作',
                                width: 80,
                                render: (_, record) => (
                                    <Button
                                        type="link"
                                        danger
                                        onClick={() => handleDeleteModel(record)}
                                        icon={<CloseOutlined />}
                                        style={{ padding: 0 }}
                                    />

                                )
                            }
                        ]}
                        rowClassName="selected-model-row"
                    />
                </Descriptions.Item>
            )}
        </Descriptions>
    );
};

export default AIModelSelector;
