import {<PERSON>,Radio,Row,Col,Slider} from 'antd';
import { getBaseColor } from '@/utils/helper';


const CameraPanel=()=>{
    return <div  style={{ background: 'rgba(255, 255, 255, 0.4)', height: '100%', width: '100%' }}>
    <Row justify="center">


    <Col span={4}>
    <div style={{color:'black',margin:12.0}}>高度</div>
    <div style={{height:100,color:'white',margin:12.0}}>
      <Slider style={{
          color: 'white',
        }}  vertical defaultValue={30} />
    </div>
 </Col>

    <Col span={16}>
    <Radio.Group  defaultValue="a" buttonStyle="solid" style={{marginTop:12.0}}>
      <Radio.Button   value="a" style={{marginLeft:12.0}}>红外</Radio.Button>
      <Radio.Button value="b" style={{marginLeft:12.0}}>广角</Radio.Button>
      <Radio.Button value="c" style={{marginLeft:12.0}}>变焦</Radio.Button>
    </Radio.Group>

    <Radio.Group  defaultValue="a" buttonStyle="solid" style={{marginTop:12.0}}>
      <Radio.Button   value="a" style={{marginLeft:12.0}}>拍照模式</Radio.Button>
      <Radio.Button value="b" style={{marginLeft:12.0}}>录像模式</Radio.Button>
    </Radio.Group>
</Col>

<Col span={4}>
    <div style={{color:'black',margin:12.0}}>焦距</div>
    <div style={{height:100,color:'white',margin:12.0}}>
      <Slider style={{
          color: 'white',
        }}  vertical defaultValue={30} />
    </div>
 </Col>



</Row>
    </div>
}

export default CameraPanel;
