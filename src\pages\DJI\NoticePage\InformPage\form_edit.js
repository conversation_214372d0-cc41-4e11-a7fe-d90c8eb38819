import {
  Input,
  Card,
  Descriptions,
  Modal,
  message,
} from "antd";
import { getGuid } from "@/utils/helper";
import { useState, useEffect } from "react";
import { isEmpty } from "@/utils/utils";
import { HPost2 } from "@/utils/request";
import { useModel } from "umi";
const { TextArea } = Input;

const EditForm = ({ data0, refrush, isTitle }) => {
  const [fdata, setFData] = useState(data0);
  const [canSee, setCanSee] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState();
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const onSave = async (e) => {
    fdata.State = parseFloat(fdata.State)
    const xx = await HPost2(
      `/api/v1/MessageData/${data0["ID"] > 0 ? "Update" : "Add"}`,
      fdata
    );
    if (isEmpty(xx.err)) {
      message.success(`${data0["ID"] > 0 ? "编辑" : "创建"}成功！`);
    }
    refrush();
  };
  let inputStyle = {
    margin: 0,
    width: "100%",
  };
  const getDataItem = (title, field, span) => {
    const data = fdata;
    if (isEmpty(span)) span = 1;
    return (
      <Descriptions.Item label={title} span={span}>
        <Input
        allowClear
          style={inputStyle}
          value={data[field]}
          onChange={(e) => {
            const d2 = { ...data };
            // if(field == 'State'){
            // d2[field] = Number(e.target.value)

            // }
            d2[field] = e.target.value;
            setFData(d2);
          }}
        ></Input>
      </Descriptions.Item>
    );
  };

  const getPanel = (data) => {
    const list = [];
    // list.push(getInputItem('单位','FDepartment'));
    list.push(getDataItem("状态", "State"));
    list.push(getDataItem("消息类型", "MType"));
    // list.push(getDataItem("通知内容", "MData"));
    // list.push(getDataItem("通知时间", "CreateTM"));
    return (
      <Descriptions labelStyle={{ width: 120 }} bordered column={1}>
        {list}
        <Descriptions.Item label={"消息内容"}>
          <TextArea
          allowClear
          value={data['MData']}
            showCount
            maxLength={300}
            onChange={(e) => {
              const d2 = { ...data };
              d2['MData'] = e.target.value;
              setFData(d2);
            }}
            placeholder="输入通知的消息内容"
            style={{
              height: 120,
              resize: "none",
            }}
          />
        </Descriptions.Item>
      </Descriptions>
    );
  };

  return (
    <div>
      <a onClick={() => setCanSee(true)}>编辑</a>
      <Modal
        title={null}
        footer={null}
        onOk={null}
        open={canSee}
        onCancel={() => setCanSee(false)}
      ></Modal>
      <div>
        <div>
          <Modal
            title={null}
            onOk={onSave}
            open={canSee}
            onCancel={() => setCanSee(false)}
            okText="提交"
            cancelText="取消"
            width={900}
          >
            <Card
              title={data0["ID"] > 0 ? "编辑通知" : "新建通知"}
              bordered={false}
            >
              {getPanel(fdata)}
            </Card>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default EditForm;
