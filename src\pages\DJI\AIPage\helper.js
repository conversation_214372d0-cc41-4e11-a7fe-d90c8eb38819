
export function UpProps(url,data,onOKHandle, ifMultiple) {
    const token=localStorage.getItem('token');
     return {
     name: 'file',
     action: url,
     data,
     showUploadList: false,
     //accept:'video/*',
     multiple: ifMultiple,
     headers: {
       authorization: 'authorization-text',
       'auth':token, 
     },
    //  beforeUpload: file => {
    //    //console.log('112',file.type)
    //   // setFName('');
    //   // setAData([]);
    //   setIfOk(false);
    //   //  const isPNG = (file.type.indexOf("vnd.google-earth.kmz") == -1);
    //   //  if (isPNG) {
    //   //    message.error(`${file.name} 不是kmz文件`);
    //   //  }
    //   // return !isPNG || Upload.LIST_IGNORE;
    //   return true;
    //  },
     onChange(info) {
       if (info.file.status === 'done') {
          console.log('ai',info.file.response);
          if(onOKHandle!=null)
            onOKHandle();

       } else if (info.file.status === 'error') {
         message.error(`${info.file.name} 上传失败！`);
       }
     },

   }};

