import { But<PERSON>, Card } from 'antd';
import { useModel } from 'umi';
import { getBodyH } from '@/utils/utils';
import { ArrowLeftOutlined } from '@ant-design/icons';

const LastPagePanel=({child})=>{
    const { lastPage } = useModel('pageModel')
    const ftn=<Button icon={<ArrowLeftOutlined />} onClick={lastPage}>返回</Button>
    return <Card style={{height:getBodyH(56),margin:0,width:'100%'}} bodyStyle={{margin:0}} title={ftn}>{child}</Card>
}

export default LastPagePanel;