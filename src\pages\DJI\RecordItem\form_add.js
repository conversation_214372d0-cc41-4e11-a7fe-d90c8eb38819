import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Form, DatePicker, Radio, Descriptions, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';

import React, { useState, useEffect } from 'react';

import { isEmpty } from '@/utils/utils';


import 'dayjs/locale/zh-cn';
import { HPost2 } from '@/utils/request';


const RecordItemAddForm = (props) => {
  const {sn,refrush } = props;
  const [type, setType] = useState({})
  const [name, setName] = useState("")
  const [content, setContent] = useState("")
  const [img,setImg]=useState("")
  const [level,setLevel]=useState("")

  const onSave =async (e) => {
    const user=JSON.parse( localStorage.getItem('user'));
    console.log('RecordItemAddForm', user);
    const data = {
      Level:level,
      Title:name,
      Content:content,
      ImgObjectName:img,
      DangerType:type,
    }
   const xx=await  HPost2('/api/v1/Danger/Add',data);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
      
    }
    refrush();
  };







  const token=localStorage.getItem('token');

  return <Form

    labelCol={{
      span: 4,
    }}

    wrapperCol={{
      span: 14,
    }}
    layout="horizontal"
    //disabled={componentDisabled}
    style={{
      maxWidth: 600,
    }}

  >



    <Form.Item label="记录内容">
      <Input
        onChange={(e)=>setName(e.target.value)}>
      </Input>
    </Form.Item>

    <Form.Item label={" "} colon={false}>
      <Button type="primary" onClick={onSave}>
        提交
      </Button>
    </Form.Item>
  </Form>

}



export default RecordItemAddForm;