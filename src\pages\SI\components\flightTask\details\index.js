import { Card,Row, Col } from 'antd'
import TaskDetailPage from "@/pages/DJI/TaskDetailPage";
import ContentTitle from '../contentTitle/ContentTile';
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";

const index = (props) => {
   const { setPage } = useModel("pageModel");
  //  const {state:{baseInfo}} =  useLocation()
  const {baseInfo} = props

  return (
    <Card style={{
      height: "100%",
    overflowY: "auto",
    }} className='noDataColor' title={<LastPageButton title="巡飞任务详情"/>}>
        <ContentTitle>基础信息</ContentTitle>
        <Row style={{padding:"10px 20px"}}>
            <Col span={6}>应用场景：{baseInfo.surveyTaskTypeName}</Col>
            <Col span={6}>航线名称：{baseInfo.waylineName}</Col>
            <Col span={6}>所属机场：{baseInfo.deviceName}</Col>
        </Row>
        <ContentTitle>影像数据</ContentTitle>
        <TaskDetailPage noShow  data2={{...baseInfo,FlightLineName:baseInfo.waylineName,TaskID:baseInfo.taskID,ID:baseInfo.flightTaskID,DeviceSN:baseInfo.deviceSN,CreateTime:null,FlyTM:0,Distance:0,PhotoCount:0,PhotoUpload:0}} />
    </Card>
  )
}

export default index