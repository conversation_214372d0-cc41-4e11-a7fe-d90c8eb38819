import HeadTitle from "@/pages/GT/components/HeadTitle/HeadTitle";
import styles from "./LeftPage.less";
import MyEcharts from "@/utils/chart2";
import alarmStatistics from "./Panels/EchartsData/alarmStatistics";
import alarmStatistics2 from "./Panels/EchartsData/alarmStatistics2";
import {
  WuRenJiIcon,
  ZuoBiaoIcon,
  ShanDianIcon,
} from "@/pages/GT/assets/svg.js";
export default function LeftPage2() {

  let arr = [
    { icon: WuRenJiIcon(), num: 100, type: "总装机容量(mw)" },
    { icon: ZuoBiaoIcon(), num: 5000, type: "今日发电量(kwh)" },
    { icon: ShanDianIcon(), num: 31231, type: "运行天数" },
    { icon: WuRenJiIcon(), num: 100, type: "无人机数量" },
    { icon: ZuoBiaoIcon(), num: 5000, type: "累计巡检架次" },
    { icon: ShanDianIcon(), num: 31231, type: "累计消缺数量" },
  ];
  let option = alarmStatistics;
  let option2 = alarmStatistics2;

  return (
    <div className={styles.LeftPage2}>
      <HeadTitle text="数据统计" />
        <div className={styles.inspection_statistics}>
          {arr.map((item, index) => {
            return (
              <div className={styles.inspection_statistics_item} key={index}>
                <div className={styles.type_num}>{item.num}</div>
                <div className={styles.type_type}>{item.type}</div>
              </div>
            );
          })}
          </div>
 
         <HeadTitle text="缺陷处理统计" style={{ marginTop: "-10px" }} />
          <div className={styles.myEcharts}>
            <MyEcharts height={150} option={option} />
          </div>
          <div className={styles.myEcharts}>
            <MyEcharts height={200} option={option2} />
          </div>

    </div>
  );
}
