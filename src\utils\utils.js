import { parse } from 'querystring';
import axios from 'axios'
import { getGuid } from './helper';
import React from 'react';
import { Post2 } from '@/services/general';
import { message } from 'antd';
import { getOssConfig } from './config';

/* eslint no-useless-escape:0 import/prefer-default-export:0 */


const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;
export const isUrl = (path) => reg.test(path);
export const isAntDesignPro = () => {
  // eslint-disable-next-line no-undef
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }

  return window.location.hostname === 'preview.pro.ant.design';
}; // 给官方演示站点用，用于关闭真实开发环境不需要使用的特性


export const getBodyH=(fh)=>{
  const  idoc = window.document;
 // const bodyH1=idoc.body.offsetHeight*1.0-fh;
  const bodyH1=window.innerHeight-fh
 // console.log(bodyH1)
  return bodyH1;
}

export const getBodyW=(fh)=>{
  const  idoc = window.document;
 // const bodyH1=idoc.body.offsetHeight*1.0-fh;
  const bodyH1=window.innerWidth/2-fh/2
 // console.log(bodyH1)
  return bodyH1;
}

export const getBodyW2=(fh)=>{
  const  idoc = window.document;
 // const bodyH1=idoc.body.offsetHeight*1.0-fh;
  const bodyH1=window.innerWidth-fh
 // console.log(bodyH1)
  return bodyH1;
}





export const isAntDesignProOrDev = () => {
  const { NODE_ENV } = process.env;

  if (NODE_ENV === 'development') {
    return true;
  }

  return isAntDesignPro();
};
export const getPageQuery = () => parse(window.location.href.split('?')[1]);

export function toFloat(val) {
    if(val>0) return val;
    return 0;
}
export function isEmpty(obj) {

  if (obj === '') {
    return true;
  }

  if (typeof obj === 'undefined' ) {
    return true;
  }

  if (!obj && obj !== 0 && obj !== '') {
      return true;
  }

  if (Array.prototype.isPrototypeOf(obj) && obj.length === 0) {
      return true;
  }

  if (Object.prototype.isPrototypeOf(obj) && Object.keys(obj).length === 0) {
      return true;
  }
  return false;
}





 export function downloadFile (url,fileName) {
  
  axios({
  url, //调用的接口，该接口返回文件流
  method: 'get',
  headers: {
    /* 一些公用的 header */
    //'auth':token,
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': '*',
  },
  params: {
    //接口的参数
  },
  responseType: 'blob', 
}).then((response) => {
  //
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName); //下载后的文件名myfile.log
  document.body.appendChild(link);
  link.click();
});
}


export function downloadFile2 (url){
       const eleLink= document.createElement('a',getGuid()); // 新建A标签
        eleLink.href = url; // 下载的路径
       // eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
        eleLink.style.display = "none";
        document.body.appendChild(eleLink);
        eleLink.click(); // 触发点击事件
        document.body.removeChild(eleLink);
 }


 export function downloadFile3 (url){
  let eleLink= React.createElement('a',getGuid()); // 新建A标签
  eleLink.setAttribute('href',url)
  eleLink.click();
  //  eleLink.href = url; // 下载的路径
  // // eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
  //  eleLink.style.display = "none";
  //  document.body.appendChild(eleLink);
  //  eleLink.click(); // 触发点击事件
  //  document.body.removeChild(eleLink);
}

export function RequestFullScreen() {
	let element = document.documentElement;
	let requestMethod = element.requestFullScreen || //W3C
		element.webkitRequestFullScreen || //Chrome
		element.mozRequestFullScreen || //FireFox
		element.msRequestFullScreen; //IE11
	if (requestMethod) {
		requestMethod.call(element);
	} else if (typeof window.ActiveXObject !== "undefined") { //for Internet Explorer
		let wscript = new ActiveXObject("WScript.Shell");
		if (wscript !== null) {
			wscript.SendKeys("{F11}");
		}
	}
}


// export const MqttUrl="ws://*************:8082/mqtt"
// export const MqttAddress="*************:1882"
// export const RtmpUrl="webrtc://************/live/"
// export const OssUrl="https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com"
// export const OssBucket="e48e14d9-068b-42e1-8d46-b9e0befd2e70"

// export const MqttUrl="ws://**************:8082/mqtt"
// //export const MqttUrl="ws://***********:11501/mqtt"
// export const MqttAddress="**************:1882"
// export const RtmpUrl='webrtc://**************/live/'
// export const OssUrl="https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com"
// export const OssBucket="e48e14d9-068b-42e1-8d46-b9e0befd2e70"


//export const MqttUrl="ws://***********:8083/mqtt"
//export const MqttAddress="***********:1883"
//export const RtmpUrl='webrtc://***********/live/'
//export const RtmpUrl2='http://***********:8080/live/'
export const rtmpShareUrl = `http://www.mzyj.unibeidou.com:11501/#/rtmpShare?sn=` //绵竹应急视频分享二维码地址
//export const OssUrl="http://***********:9000"
//export const OssBucket="300bdf2b-a150-406e-be63-d28bd29b409f"



// export let MqttUrl="ws://***************:8083/mqtt"
// //export const MqttUrl="ws://***********:11501/mqtt"
// export const MqttAddress="***************:1883"
// export const RtmpUrl='webrtc://***************/live/'
// export const RtmpUrl2='http://***************:1938/live/'

// //export const RtmpUrl='webrtc://**************/live/'

// export const OssUrl="http://***************:9001"
// export const OssBucket="6251daf8-4127-40e0-980d-c86f8a765b20"

export const getImgUrl=(obj)=>{
  const x1=getOssConfig();
 return `${x1.url}/${x1.bucket}/${obj}`
//   return `https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/${obj}`
}

export const getRedlineUrl=(sn)=>{
  const x1=getOssConfig();
 return `${x1.url}/${x1.bucket}/wayline/redlinePoint/${sn}.json`
}


export const getVideoUrl=(item)=>{
  // if(isEmpty(item.FilePath))
    return getImgUrl(item.ObjectName)
  // return  getImgUrl(item.FilePath)
 //   return `https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/${obj}`
 }

export const getImgSLTUrl=(obj)=>{
  //return `https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/${obj}`
  return getImgUrl(obj)
}

export const getVideoSLTUrl=(obj)=>{
  //return `https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/${obj}`
  return  getImgUrl(obj)
}


export const getImgUrl2=(obj)=>{
  return getImgUrl(obj)
  //return `https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/${obj}`
}


export const getImgUrlByAli=(obj)=>{
 return `https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/${obj}`
  //return `https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/${obj}`
}

export const getDeviceName=(sn)=>{
  const dL=localStorage.getItem('deviceList');
  if(isEmpty(dL)) return "";
  if(dL.length<5) return "";
  const dL2=JSON.parse(dL);
  const v2=dL2[sn];
  if(isEmpty(v2)) return "";
  return v2.DName ? v2.DName : "";
}

export const getDevice=(sn)=>{
  //  
    const dL=localStorage.getItem('deviceList');
    if(isEmpty(dL)) return {};
    if(dL.length<5) return {};
    const dL2=JSON.parse(dL);
    const v2=dL2[sn];
    return v2;
  }

 export function WayLineTypeToString(Type) {
    if (Type === "0") {
      return "其他航线";
    } else if (Type === "1") {
      return "航点航线";
    } else if (Type === "2") {
      return "垂直航线";
    } else if (Type === "3") {
      return "面状航线";
    } else if (Type === "4") {
      return "带状航线";
    }else if (Type === "5") {
      return "几何体航线";
    } else {
      return Type;
    }
  }


export function ChangeFullScreen() {
	const element = document.documentElement
  if (!document.fullscreenElement) {
    //进入页面全屏
    RequestFullScreen();
  } else {
    if (document.exitFullscreen) {
      //退出全屏
      document.exitFullscreen();
    }
}}


export  function  AddSystemLog (title,content) {
  Post2('/api/v1/SystemLog/Add',{'Title':title,'Content':content});
}

export function Clone(obj) {
  //判断是对象，就进行循环复制
  if (typeof obj === 'object' && obj!=null) {
      // 区分是数组还是对象，创建空的数组或对象
      var o = Object.prototype.toString.call(obj).slice(8, -1) === "Array" ? [] : {};
      for (var k in obj) {
          // 如果属性对应的值为对象，则递归复制
          if(typeof obj[k] === 'object' && obj[k]!=null){
              o[k] = Clone(obj[k])
          }else{
              o[k] = obj[k];
          }
      }
  }else{ //不为对象，直接把值返回
      return obj;
  }
  return o;
}




export function bytesToSize  (bytes)  {
  
  if (bytes < 1024) return bytes + 'B';
  const kb = bytes / 1024;
  if (kb < 1024) return kb.toFixed(1) + 'KB';
  const mb = kb / 1024;
  if (mb < 1024) return mb.toFixed(1) + 'MB';
  const gb = mb / 1024;
  return gb.toFixed(1) + 'GB';

}
 



const user=JSON.parse( localStorage.getItem('user'));
let Authority = user?.Authority;
export const checkIfFlyer=()=>{
  if(!ifFlyer()){
      message.info("无操作权限！");
      return false;
   }
   return true;
}

export const ifFlyer=(auth)=>{
  auth?auth:auth = Authority;
  if(auth=="无人机飞手"||auth=="系统管理员"||auth=="admin"){
      return true;
  }
  return false;
}

export const ifAdmin=(auth)=>{
  auth?auth:auth =Authority;
  if(auth=="系统管理员"||auth=="admin"){
      return user;
  }
  return false;
}

export const checkIfAdmin=()=>{
  if(!ifAdmin()){
      message.info("仅限系统管理员操作！");
      return false;
   }
   return true;
}

