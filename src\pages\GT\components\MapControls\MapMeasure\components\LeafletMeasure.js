import React, { useEffect, useState, useRef, useCallback, forwardRef, useImperativeHandle } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import * as turf from "@turf/turf";
import { Dropdown, Button, Select } from "antd";
import { AimOutlined, BorderOutlined, ClearOutlined, DownOutlined } from '@ant-design/icons';
import styles from "../index.module.less";
import { getGuid } from "@/utils/helper";

// 定义测量单位
const DISTANCE_UNITS = {
  METERS: { key: 'm', label: '米', factor: 1 },
  KILOMETERS: { key: 'km', label: '公里', factor: 0.001 },
};

const AREA_UNITS = {
  SQUARE_METERS: { key: 'm²', label: '平方米', factor: 1 },
  SQUARE_KILOMETERS: { key: 'km²', label: '平方公里', factor: 0.000001 },
  HECTARES: { key: 'ha', label: '公顷', factor: 0.0001 },
};

const LeafletMeasure = ({ map }) => {
  const [markersLayerGroup, setMarkersLayerGroup] = useState(() => L.layerGroup());
  const [NodeLayerGroup, setNodeLayerGroup] = useState(() => L.layerGroup());

  const [lineData, setLineData] = useState([]);
  const [tempLine, setTempLine] = useState(null);
  const [drawing, setDrawing] = useState(false);
  const [distanceGroupValues, setDistanceGroupValues] = useState([]); // 存储原始米数值

  const [polygonData, setPolygonData] = useState([]);
  const [tempPolygon, setTempPolygon] = useState(null);
  const [drawingPolygon, setDrawingPolygon] = useState(false);

  const [drawnItems, setDrawnItems] = useState([]); // 存储所有完成的测量项
  const [currentDrawingSegmentLayers, setCurrentDrawingSegmentLayers] = useState([]); // 存储当前正在绘制的线段图层

  const [distanceUnit, setDistanceUnit] = useState(DISTANCE_UNITS.KILOMETERS);
  const [areaUnit, setAreaUnit] = useState(AREA_UNITS.SQUARE_KILOMETERS);

  // 格式化距离显示
  const formatDistance = useCallback((distanceInMeters) => {
    if (typeof distanceInMeters !== 'number' || isNaN(distanceInMeters)) return '无效距离';
    const convertedDistance = distanceInMeters * distanceUnit.factor;
    return `${convertedDistance.toFixed(2)} ${distanceUnit.label}`;
  }, [distanceUnit]);

  // 格式化面积显示
  const formatArea = useCallback((areaInSquareMeters) => {
    if (typeof areaInSquareMeters !== 'number' || isNaN(areaInSquareMeters)) return '无效面积';
    const convertedArea = areaInSquareMeters * areaUnit.factor;
    return `${convertedArea.toFixed(2)} ${areaUnit.label}`;
  }, [areaUnit]);

  // 创建marker的HTML内容 (返回 HTMLElement)
  const createMarkerHtmlElement = useCallback((text, isTotal = false, onDeleteCallback) => {
    const container = L.DomUtil.create('div', styles.markerContainer);
    const textDiv = L.DomUtil.create('div', styles.markerText, container);
    textDiv.innerText = text;

    if (isTotal && onDeleteCallback) {
      const deleteBtn = L.DomUtil.create('div', styles.deleteButton, container);
      deleteBtn.innerHTML = '×'; // 使用简单的 '×'
      deleteBtn.title = "删除此测量";
      L.DomEvent.on(deleteBtn, 'click', (e) => {
        L.DomEvent.stop(e); // 阻止事件冒泡到地图，防止触发地图点击
        onDeleteCallback();
      });
    }
    return container;
  }, [styles.markerContainer, styles.markerText, styles.deleteButton]);

  // 创建Leaflet divIcon (使用 HTMLElement)
  const createMarkerIcon = useCallback((htmlElement) => {
    return L.divIcon({
      html: htmlElement,
      iconSize: [80, 30], // 可以根据内容动态调整或保持固定
      iconAnchor: [40, 35], // 根据iconSize调整
      className: "measure-marker" // 自定义类名，用于CSS控制
    });
  }, []);


  // 计算两点距离 (返回米)
  const calculateDistance = useCallback((point1LatLng, point2LatLng) => {
    const point1 = turf.point([point1LatLng.lng, point1LatLng.lat]);
    const point2 = turf.point([point2LatLng.lng, point2LatLng.lat]);
    const distanceInMeters = turf.distance(point1, point2, { units: "meters" });
    return distanceInMeters;
  }, []);

  // 点击位置添加marker (用于显示每段距离)
  const addSegmentMarker = useCallback((latlng, text) => {
    const markerElement = createMarkerHtmlElement(text, false); // 分段标记没有删除按钮
    const customIcon = createMarkerIcon(markerElement);
    const newMarker = L.marker(latlng, { icon: customIcon });
    markersLayerGroup.addLayer(newMarker);
  }, [markersLayerGroup, createMarkerHtmlElement, createMarkerIcon]);

  // 清除所有的Marker (通常用于开始新绘制前或全部清除时)
  const clearMarkers = useCallback(() => {
    markersLayerGroup.clearLayers();
  }, [markersLayerGroup]);

  // 清空所有红点 (通常用于开始新绘制前或全部清除时)
  const clearNodes = useCallback(() => {
    NodeLayerGroup.clearLayers();
  }, [NodeLayerGroup]);

  // 面积计算 (返回平方米)
  const calculateArea = useCallback((polygonCoords) => {
    if (!polygonCoords || polygonCoords.length < 3) {
      return 0;
    }
    const coordinates = polygonCoords.map((latlng) => [latlng.lng, latlng.lat]);
    // turf.polygon 需要一个包含线性环的数组，线性环的首尾点必须相同
    const turfPolygon = turf.polygon([[...coordinates, coordinates[0]]]);
    const areaInSquareMeters = turf.area(turfPolygon);
    return areaInSquareMeters;
  }, []);


  const handleMapClick = useCallback((e) => {
    if (!map || (!drawing && !drawingPolygon)) return;

    const latlng = e.latlng;

    if (drawing) {
      const newLineData = [...lineData, latlng];
      setLineData(newLineData);

      let segmentText = "起点";
      if (newLineData.length === 1) {
        const newTempLine = L.polyline([latlng, latlng], {
          color: "#1890ff", weight: 2, opacity: 0.8, dashArray: "5, 10",
        }).addTo(map);
        setTempLine(newTempLine);
      } else if (newLineData.length > 1) {
        const lastPoint = newLineData[newLineData.length - 2];
        const distanceInMeters = calculateDistance(lastPoint, latlng);
        segmentText = formatDistance(distanceInMeters);
        setDistanceGroupValues(prev => [...prev, distanceInMeters]);

        const segmentLine = L.polyline([lastPoint, latlng], {
          color: "#1890ff", weight: 3, opacity: 0.9,
        }).addTo(map);
        setCurrentDrawingSegmentLayers(prev => [...prev, segmentLine]); // 存储当前绘制的线段

        if (tempLine) map.removeLayer(tempLine);
        const newTempLine = L.polyline([latlng, latlng], {
          color: "#1890ff", weight: 2, opacity: 0.8, dashArray: "5, 10",
        }).addTo(map);
        setTempLine(newTempLine);
      }
      addSegmentMarker(latlng, segmentText);
    }

    if (drawingPolygon) {
      const newPolygonData = [...polygonData, latlng];
      setPolygonData(newPolygonData);

      if (newPolygonData.length > 1) {
        const lastPoint = newPolygonData[newPolygonData.length - 2];
        const segmentLine = L.polyline([lastPoint, latlng], {
          color: "#1890ff", weight: 3, opacity: 0.9,
        }).addTo(map);
        setCurrentDrawingSegmentLayers(prev => [...prev, segmentLine]); // 存储当前绘制的线段
      }

      if (tempPolygon) map.removeLayer(tempPolygon);
      const updatedPolygonPoints = [...newPolygonData];
      const newTempPolygon = L.polygon(updatedPolygonPoints, {
        color: "#1890ff", weight: 2, opacity: 0.8, fillOpacity: 0.2, dashArray: "5, 10",
      }).addTo(map);
      setTempPolygon(newTempPolygon);
    }

    L.circleMarker(latlng, {
      color: "red", fillColor: "#f03", fillOpacity: 0.8, radius: 5, weight: 2,
    }).addTo(NodeLayerGroup);

  }, [map, drawing, drawingPolygon, lineData, polygonData, tempLine, tempPolygon, calculateDistance, formatDistance, addSegmentMarker, NodeLayerGroup, setDistanceGroupValues, setCurrentDrawingSegmentLayers]);


  const handleMouseMove = useCallback((e) => {
    if (!map || (!drawing && !drawingPolygon)) return;

    const latlng = e.latlng;

    if (drawing && tempLine && lineData.length > 0) {
      const lastPoint = lineData[lineData.length - 1];
      tempLine.setLatLngs([lastPoint, latlng]);
    }

    if (drawingPolygon && tempPolygon && polygonData.length > 0) {
      const updatedPolygonPoints = [...polygonData, latlng];
      tempPolygon.setLatLngs(updatedPolygonPoints);

      if (updatedPolygonPoints.length >= 2) { // 至少两个点才能形成预览面积的形状
        // 为了计算预览面积，临时闭合多边形
        const previewCoordsForArea = updatedPolygonPoints.length >=3 ? [...updatedPolygonPoints, updatedPolygonPoints[0]] : [...updatedPolygonPoints];
        const areaInSqMeters = calculateArea(previewCoordsForArea); // turf.area处理少于3个点的情况
        if (areaInSqMeters > 0) { // 只有面积大于0才显示
             tempPolygon.unbindTooltip().bindTooltip(`面积: ${formatArea(areaInSqMeters)}`, {
                permanent: true, direction: "center", className: "polygon-tooltip-preview", offset: [0, -10], opacity: 0.8,
             }).openTooltip();
        } else {
            tempPolygon.unbindTooltip(); // 点不够时不显示tooltip
        }
      }
    }
  }, [map, drawing, drawingPolygon, lineData, polygonData, tempLine, tempPolygon, calculateArea, formatArea]);

  const handleDeleteMeasurement = useCallback((itemId) => {
    const itemToDelete = drawnItems.find(item => item.id === itemId);
    if (itemToDelete && map) {
      if (map.hasLayer(itemToDelete.layer)) {
        map.removeLayer(itemToDelete.layer);
      }
      itemToDelete.segmentLayers.forEach(segment => {
        if (map.hasLayer(segment)) {
          map.removeLayer(segment);
        }
      });

      clearMarkers(); // 清除之前的分段标记
      clearNodes();   // 清除之前的节点
      // TODO: 精确删除与此测量相关的标记和节点
    }
    setDrawnItems(prevItems => prevItems.filter(item => item.id !== itemId));
  }, [map, drawnItems, setDrawnItems]);


  const bindTooltipAndEvents = useCallback((item) => {
    if (!map || !item || !item.layer) return;

    let textContent;
    let tooltipOptions;

    const onDelete = () => handleDeleteMeasurement(item.id);

    if (item.type === 'distance') {
      textContent = `总长: ${formatDistance(item.rawValue)}`;
      tooltipOptions = {
        permanent: true, opacity: 0.9, sticky: false, direction: "center",
        offset: [0, -15], className: "distance-tooltip", interactive: true
      };
    } else if (item.type === 'area') {
      textContent = `总面积: ${formatArea(item.rawValue)}`;
      tooltipOptions = {
        permanent: true, opacity: 0.9, sticky: false, direction: "center",
        offset: [0, -10], className: "polygon-tooltip", interactive: true
      };
    } else {
      return; // 未知类型
    }

    const tooltipElement = createMarkerHtmlElement(textContent, true, onDelete);
    item.layer.unbindTooltip().bindTooltip(tooltipElement, tooltipOptions).openTooltip();

  }, [map, formatDistance, formatArea, createMarkerHtmlElement, handleDeleteMeasurement]);


  const stopDrawing = useCallback(() => {
    if (!map || lineData.length === 0) {
      setDrawing(false);
      if (tempLine && map.hasLayer(tempLine)) map.removeLayer(tempLine);
      setTempLine(null);
      setLineData([]); // 清空数据
      setDistanceGroupValues([]);
      setCurrentDrawingSegmentLayers([]); // 清空当前线段
      map.getContainer().style.cursor = '';
      return;
    }

    if (tempLine && map.hasLayer(tempLine)) map.removeLayer(tempLine);

    // 移除最后一个点（由双击触发的click事件产生的点）
    const updatedLineData = lineData.slice(0, -1);
    const updatedDistanceValues = distanceGroupValues.slice(0, -1);
    
    // 清除最后一个标记点
    const layers = markersLayerGroup.getLayers();
    if (layers.length > 0) {
      markersLayerGroup.removeLayer(layers[layers.length - 1]);
    }
    
    // 清除最后一个红点
    const nodeLayers = NodeLayerGroup.getLayers();
    if (nodeLayers.length > 0) {
      NodeLayerGroup.removeLayer(nodeLayers[nodeLayers.length - 1]);
    }

    const totalDistanceInMeters = updatedDistanceValues.reduce((sum, dist) => sum + parseFloat(dist), 0);
    
    // 创建一个对用户不可见的线，用于绑定总长度tooltip
    const finalLineLayer = L.polyline(updatedLineData, { opacity: 0 }).addTo(map);

    const newItem = {
      id: getGuid(),
      type: 'distance',
      layer: finalLineLayer,
      points: [...updatedLineData],
      rawValue: totalDistanceInMeters,
      segmentLayers: [...currentDrawingSegmentLayers],
    };

    setDrawnItems(prev => [...prev, newItem]);
    bindTooltipAndEvents(newItem);

    setDrawing(false);
    setLineData([]);
    setTempLine(null);
    setDistanceGroupValues([]);
    setCurrentDrawingSegmentLayers([]);
    map.getContainer().style.cursor = '';
  }, [map, lineData, tempLine, distanceGroupValues, currentDrawingSegmentLayers, bindTooltipAndEvents, setDrawnItems]);


  const stopDrawingPolygon = useCallback(() => {
    if (!map || polygonData.length < 3) {
      setDrawingPolygon(false);
      if (tempPolygon && map.hasLayer(tempPolygon)) map.removeLayer(tempPolygon);
      setTempPolygon(null);
      setPolygonData([]);
      setCurrentDrawingSegmentLayers([]);
      map.getContainer().style.cursor = '';
      // 清除节点和标记，因为没有形成有效的面
      clearNodes(); // 根据需求决定是否在此处清除
      clearMarkers();
      return;
    }

    if (tempPolygon && map.hasLayer(tempPolygon)) map.removeLayer(tempPolygon);

    const finalPolygonLayer = L.polygon(polygonData, {
      color: "#1890ff", weight: 3, opacity: 0.9, fillOpacity: 0.2,
    }).addTo(map);

    const areaInSquareMeters = calculateArea(polygonData);

    const newItem = {
      id: getGuid(),
      type: 'area',
      layer: finalPolygonLayer,
      points: [...polygonData],
      rawValue: areaInSquareMeters,
      segmentLayers: [...currentDrawingSegmentLayers],
    };
    
    setDrawnItems(prev => [...prev, newItem]);
    bindTooltipAndEvents(newItem);

    setDrawingPolygon(false);
    setPolygonData([]);
    setTempPolygon(null);
    setCurrentDrawingSegmentLayers([]);
    map.getContainer().style.cursor = '';
  }, [map, polygonData, tempPolygon, calculateArea, currentDrawingSegmentLayers, bindTooltipAndEvents, setDrawnItems]);


  const handleDoubleClick = useCallback((e) => {
    L.DomEvent.preventDefault(e);
    if (drawing) {
      stopDrawing();
    } else if (drawingPolygon) {
      stopDrawingPolygon();
    }
  }, [drawing, drawingPolygon, stopDrawing, stopDrawingPolygon]);


  const commonStartDrawCleanup = useCallback(() => {
    if (tempLine && map && map.hasLayer(tempLine)) map.removeLayer(tempLine);
    setTempLine(null);
    if (tempPolygon && map && map.hasLayer(tempPolygon)) map.removeLayer(tempPolygon);
    setTempPolygon(null);
    
    clearMarkers(); // 清除之前的分段标记
    clearNodes();   // 清除之前的节点
    setCurrentDrawingSegmentLayers([]); // 清空当前绘制的线段层
  }, [map, tempLine, tempPolygon, clearMarkers, clearNodes]);


  const startDrawingLine = useCallback(() => {
    if (!map) return;
    if (drawingPolygon) stopDrawingPolygon(); // 先完成或取消画面
    if (drawing) stopDrawing(); // 如果已在画线，先完成上一次的

    commonStartDrawCleanup();
    map.getContainer().style.cursor = 'crosshair';
    setDrawing(true);
    setDrawingPolygon(false);
    setLineData([]);
    setDistanceGroupValues([]);
  }, [map, drawing, drawingPolygon, stopDrawing, stopDrawingPolygon, commonStartDrawCleanup]);

  const startDrawingPoly = useCallback(() => {
    if (!map) return;
    if (drawing) stopDrawing(); // 先完成或取消画线
    if (drawingPolygon) stopDrawingPolygon(); // 如果已在画面，先完成上一次的

    commonStartDrawCleanup();
    map.getContainer().style.cursor = 'crosshair';
    setDrawing(false);
    setDrawingPolygon(true);
    setPolygonData([]);
  }, [map, drawing, drawingPolygon, stopDrawing, stopDrawingPolygon, commonStartDrawCleanup]);


  const clearAllDrawings = useCallback(() => {
    if (!map) return;
    if (drawing) stopDrawing();
    if (drawingPolygon) stopDrawingPolygon();

    setDrawing(false);
    setDrawingPolygon(false);

    drawnItems.forEach(item => {
      if (map.hasLayer(item.layer)) map.removeLayer(item.layer);
      item.segmentLayers.forEach(segment => {
        if (map.hasLayer(segment)) map.removeLayer(segment);
      });
    });
    setDrawnItems([]);

    clearMarkers();
    clearNodes();

    setLineData([]);
    setPolygonData([]);
    setDistanceGroupValues([]);
    setCurrentDrawingSegmentLayers([]);
    map.getContainer().style.cursor = '';
  }, [map, drawing, drawingPolygon, stopDrawing, stopDrawingPolygon, drawnItems, clearMarkers, clearNodes]);


  useEffect(() => {
    if (!map) return;
    if (!map.hasLayer(markersLayerGroup)) markersLayerGroup.addTo(map);
    if (!map.hasLayer(NodeLayerGroup)) NodeLayerGroup.addTo(map);

    map.on('click', handleMapClick);
    map.on('mousemove', handleMouseMove);
    map.on('dblclick', handleDoubleClick);

    return () => {
      map.off('click', handleMapClick);
      map.off('mousemove', handleMouseMove);
      map.off('dblclick', handleDoubleClick);

      if (tempLine && map.hasLayer(tempLine)) map.removeLayer(tempLine);
      if (tempPolygon && map.hasLayer(tempPolygon)) map.removeLayer(tempPolygon);
    };
  }, [map, handleMapClick, handleMouseMove, handleDoubleClick, markersLayerGroup, NodeLayerGroup, tempLine, tempPolygon]);

  useEffect(() => {
    if (!map) return;
    drawnItems.forEach(item => {
      if (item.layer && map.hasLayer(item.layer)) {
        // 重新绑定tooltip，它会使用最新的单位和格式化函数
        bindTooltipAndEvents(item);
      }
    });
  }, [map, distanceUnit, areaUnit, drawnItems, bindTooltipAndEvents]);


  const menuTitle = (
    <Button type="default" className={styles.measureDropdownButton}>
      测量 <DownOutlined />
    </Button>
  );

  const measurementItems = [
    {
      key: 'distance',
      label: ( <a onClick={startDrawingLine}> <AimOutlined className={styles.measureIcon} /> 测距 </a> ),
      className: drawing ? styles.activeMenuItem : ''
    },
    {
      key: 'area',
      label: ( <a onClick={startDrawingPoly}> <BorderOutlined className={styles.measureIcon} /> 测面 </a> ),
      className: drawingPolygon ? styles.activeMenuItem : ''
    },
    {
      key: 'clear',
      label: ( <a onClick={clearAllDrawings}> <ClearOutlined className={styles.measureIcon} /> 清空测量 </a> ),
    },
  ];

  // 使用useImperativeHandle暴露方法给父组件
  // useImperativeHandle(ref, () => ({
  //   startDrawingLine,
  //   startDrawingPoly,
  //   clearAllDrawings
  // }));

  return (
    <>
      {map && (
        <div className={styles.measureBar}>
          <Dropdown menu={{ items: measurementItems }} placement="bottomRight" arrow>
            {menuTitle}
          </Dropdown>

          {(drawing || drawnItems.some(item => item.type === 'distance')) && (
            <div className={styles.unitSelectorContainerLeaflet}>
              <span className={styles.unitLabel}>距离单位:</span>
              <Select
                size="small"
                value={distanceUnit.key}
                onChange={(value) => {
                  const selectedUnit = Object.values(DISTANCE_UNITS).find(u => u.key === value);
                  if (selectedUnit) setDistanceUnit(selectedUnit);
                }}
                className={styles.unitSelect}
              >
                {Object.values(DISTANCE_UNITS).map(unit => (
                  <Select.Option key={unit.key} value={unit.key}>{unit.label}</Select.Option>
                ))}
              </Select>
            </div>
          )}

          {(drawingPolygon || drawnItems.some(item => item.type === 'area')) && (
            <div className={styles.unitSelectorContainerLeaflet}>
              <span className={styles.unitLabel}>面积单位:</span>
              <Select
                size="small"
                value={areaUnit.key}
                onChange={(value) => {
                  const selectedUnit = Object.values(AREA_UNITS).find(u => u.key === value);
                  if (selectedUnit) setAreaUnit(selectedUnit);
                }}
                className={styles.unitSelect}
                style={{width: '100px'}} // 面积单位的标签可能更长
              >
                {Object.values(AREA_UNITS).map(unit => (
                  <Select.Option key={unit.key} value={unit.key}>{unit.label}</Select.Option>
                ))}
              </Select>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default LeafletMeasure;