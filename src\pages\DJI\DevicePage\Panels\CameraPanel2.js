import { isEmpty } from "@/utils/utils";
import { useModel } from "umi";
import { getGrayStyle } from "./helper";
import { color } from "echarts";
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";
import { Dropdown, <PERSON><PERSON>, Tooltip } from "antd";
import styles from "./CameraPanel2.less";
import { checkIfFlyer } from "@/utils/utils";
import QRCoder from "@/components/QRcoder/QRcoder";
import { useMemo } from "react";
import { FJStart, JCStart,reOpenCamera } from "@/pages/DJI/DRCPage/Panels/RtmpChange";
import { HGet2 } from "@/utils/request";

const CameraPanel2 = ({ onFrush, setShowSlidingBar }) => {
  const w1 = 150;
  const h1 = 50;
  const { fj } = useModel("droneModel");
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const { cameraJT, setCameraJT } = useModel("rtmpModel");
  const { fjVideo } = useModel("stateModel");
  const VQualityList = ["自适应", "流畅", "标清", "高清", "超清"];

  const VideoSave = (z) => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: [z],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const ImgSave = (z) => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["visable"],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const CameraJT = (z) => {
    const data = {
      video_id: `${device.SN2}/${device.Camera2}/normal-0`,
      video_type: z,
    };
    DoCMD(device.SN, "live_lens_change", data);
    setCameraJT(z);
    //  VideoSave(z);
    // ImgSave(z);
    // if(z=='ir'){
    //     setTimeout(CameraIRType, 1000);
    // }
    // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
  };

  const CameraIRType = () => {
    let data = {};
    data[device.Camera2] = { thermal_current_palette_style: 6 };
    let s = {};
    s.bid = getGuid();
    s.tid = getGuid();
    // s.gateway = sn
    s.data = data;
    s.timestamp = dayjs().valueOf();
    DoCMD2(`thing/product/${device.SN}/property/set`, s);
  };

  const getItem = (title, onClick) => {
    return {
      key: title,
      label: (
        <a style={{ zIndex: 10015 }} onClick={onClick}>
          {title}
        </a>
      ),
    };
  };

  const chageFbl = (id, i) => {
    const data = {
      video_id: id,
      video_quality: i,
    };
    DoCMD(device.SN, "live_set_quality", data);
  };

  const changeFBL = (id) => {
    return [
      getItem("流畅", () => {
        if (checkIfFlyer()) chageFbl(id, 1);
      }),
      getItem("标清", () => {
        if (checkIfFlyer()) chageFbl(id, 2);
      }),
      getItem("高清", () => {
        if (checkIfFlyer()) chageFbl(id, 3);
      }),
      getItem("超清", () => {
        if (checkIfFlyer()) chageFbl(id, 4);
      }),
      getItem("自适应", () => {
        if (checkIfFlyer()) chageFbl(id, 0);
      }),
    ];
  };

  const changeJT = () => {
    const list = [getItem("广角镜头", () => {
      if (checkIfFlyer()) {
        CameraJT("wide");
        // 关闭变焦刻度条
        setShowSlidingBar(false);
      };
    }),
    getItem("变焦镜头", () => {
      // setShowSlidingBar(true);
      if (checkIfFlyer()) {
        CameraJT("zoom");
        // 打开变焦刻度条
        setShowSlidingBar(true);
      };
    }),];
    if (device.Model2 == "M3TD" || device.Model2 == "M30T") {
      list.push(getItem("红外镜头", () => {
        if (checkIfFlyer()) {
          CameraJT("ir");
          // 关闭变焦刻度条
          setShowSlidingBar(false);
        }
      }))
    }
    return list;
  };

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);
  };

  const getButton = (title, items, onClick) => {
    if (items.length == 0) {
      return (
        <div
          style={{
            fontSize: 13.0,
            margin: 2.0,
            cursor: 'pointer',
            userSelect: 'none'
          }}
          onClick={onClick} // 绑定点击事件
        >
          {title}
        </div>
      );
    }
    return (
      <Dropdown
        menu={{
          items: items,
        }}
        placement="bottomLeft"
        arrow
        //  theme="dark"
        style={{
          // zIndex: 1000000,
          width: 180,
          zIndex: 10005,
        }}
      >
        <div style={{ fontSize: 13.0, margin: 2.0 }}>{title}</div>
      </Dropdown>
    );
  }
  // 无fj信息的时候 不显示
  if (isEmpty(fj)) return;
  // console.log('sdata',fjVideo);

  const getJTNM = () => {
    if (isEmpty(fjVideo)) return "直播镜头";
    if (fjVideo.video_type == "wide") return "广角镜头";
    if (fjVideo.video_type == "zoom") return "变焦镜头";
    if (fjVideo.video_type == "ir") return "红外镜头";
    return "直播镜头";
  };

  const getFBL = () => {
    if (isEmpty(fjVideo)) return "分辨率";
    return VQualityList[fjVideo.video_quality];
  };
  let qrCoderArr = [
    {
      key: '1',
      label: (
        <Tooltip color="#264392" title={
          <QRCoder SN={device.SN2}></QRCoder>
        }>
          <div>无人机二维码</div>
        </Tooltip>
      ),
    },
    {
      key: '2',
      label: (
        <Tooltip color="#264392" title={
          <QRCoder SN={device.SN}></QRCoder>
        }>
          <div>机场二维码</div>
        </Tooltip>
      ),
    },
  ]


  //const fSty={userSelect:'none', opacity:0.6,cursor:'pointer',  fontFamily:'MiSan', textAlign:'center',fontSize:14.0,color:'white',fontWeight:'bold',paddingTop:8.0}
  return (
    <div className={styles.camerasBar}>
      {/* <div className={styles.camerasBarItem}  onClick={()=>CameraJT('wide')}>广角</div> */}
      <div>
        <img></img>
      </div>
      <div className={styles.camerasBarItem}>
        {getButton('重载镜头', [], () => {
          if (checkIfFlyer()) {
            reOpenCamera();
          }
        })}
      </div>
      <div className={styles.camerasBarItem}>
        {getButton(getJTNM(), changeJT())}
      </div>
      <div className={styles.camerasBarItem} style={{ marginLeft: 2.0 }}>
        {getButton(
          getFBL(),
          changeFBL(`${device.SN2}/${device.Camera2}/normal-0`)
        )}
      </div>
      <div className={styles.camerasBarItem}>
        {getButton('直播分享', qrCoderArr)}
      </div>
      {/*
      <div className={styles.camerasBarItem} style={{ marginLeft: 2.0 }}>
        <div
          onClick={() => {
            if (checkIfFlyer()) {
              onFrush();
            }
          }}
        >
          刷新
        </div>
      </div> */}

      {/* <div className={styles.camerasBarItem}  onClick={()=>CameraJT('ir')}>红外</div> */}
    </div>
  );
};

export default CameraPanel2;

<div></div>;
