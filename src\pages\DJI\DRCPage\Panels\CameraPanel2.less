.camera_panel{
    position: absolute;
    top:80px;
    right:40px;
    z-index: 10;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;

}
.camera_panel_btn{
    background:rgba(65, 65, 65, 0.8);
    border-radius:5px;
    user-select: none;
    cursor: pointer;
    font-family: "MiSan";
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    padding: 4px 10px;
}

.temperature_list {
    position: absolute;
    top: 35px;
    left: -20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 10px;
    max-height: 0; 
    overflow: hidden;
    transition: max-height 0.3s ease; 
    background-color: #333333; 
    box-shadow: 0px 0px 8px rgb(104, 83, 241);
    z-index: 1;
    font-size: 13px;
    font-weight: lighter;
}
.temperature_list>div:nth-child(1) {
    margin: 10px 10px 0;
}
.temperature_list>div:last-child {
    margin: 0 0 10px;
}

.temperature:hover .temperature_list {
    max-height: 500px; 
}
.temperature_list>div:hover{
    color: rgb(255, 179, 0);
}