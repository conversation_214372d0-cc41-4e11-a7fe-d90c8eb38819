import React, { useEffect, useState } from 'react';
import { Radio, Select, Space } from 'antd';
import { axiosApi } from '@/services/general';
import useStore from "../../store"
export default function SelectNav({ width }) {
    const { region, setRegion, surveytask_select, setSurveytask_select, waylineData, setWaylineData } = useStore();
    const [planOptions, setPlanOptions] = useState([])

    const handleChange = value => {
        console.log(`Selected: ${value}`);
        getGetByTaskId(value);
    };
    useEffect(() => {
        getPlanOptions();
    }, [])
    const getGetByTaskId = async (taskId) => {
        let res = await axiosApi(`/api/v1/Wayline/GetByTaskId?ID=${taskId}`, "GET", null);
        console.log("??????", res);
    }
    const getPlanOptions = async () => {
        const res = await axiosApi('/api/v1/surveytask/GetAllList', 'GET', null);
        console.log("/surveytask/GetAllList", res);

        if (res.data) {
            setPlanOptions(res.data)
        }
    }
    let options = planOptions?.map((item, index) => ({ label: item.TaskDesc, value: item.TaskID }))
    return <div>
        <Select
            size={"small"}
            placeholder="选择区域"
            onChange={handleChange}
            style={{ width: width ? width : 96 }}
            options={options}
        />
    </div>;
}