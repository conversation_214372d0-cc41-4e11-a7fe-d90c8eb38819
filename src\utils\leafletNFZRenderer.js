/**
 * Leaflet 禁飞区渲染器
 * 在 Leaflet 地图上渲染禁飞区数据
 */

import L from 'leaflet';

/**
 * Leaflet 禁飞区渲染器类
 */
export class LeafletNFZRenderer {
  constructor(map) {
    this.map = map;
    this.nfzLayers = new Map(); // 存储禁飞区图层
    this.nfzLayerGroup = null;  // 禁飞区图层组
    this.isVisible = true;      // 是否可见
    this.onNFZClick = null;     // 点击回调函数
    
    this.init();
  }

  /**
   * 初始化渲染器
   */
  init() {
    if (!this.map) {
      throw new Error('LeafletNFZRenderer: Invalid map instance');
    }

    // 创建专用图层组
    this.nfzLayerGroup = L.layerGroup();
    
    // 根据初始可见性状态决定是否添加到地图
    if (this.isVisible) {
      this.nfzLayerGroup.addTo(this.map);
    }
  }

  /**
   * 处理禁飞区点击事件
   * @param {Object} nfzData - 禁飞区数据
   * @param {Object} event - 点击事件
   */
  handleNFZClick(nfzData, event) {
    // 创建弹出窗口内容
    const popupContent = this.createPopupContent(nfzData);
    
    // 显示弹出窗口
    const popup = L.popup()
      .setLatLng(event.latlng)
      .setContent(popupContent)
      .openOn(this.map);

    // 触发自定义回调
    if (this.onNFZClick) {
      this.onNFZClick(nfzData, event);
    }
  }

  /**
   * 创建弹出窗口内容
   * @param {Object} nfzData - 禁飞区数据
   * @returns {string} HTML内容
   */
  createPopupContent(nfzData) {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h4 style="margin: 0 0 10px 0; color: #1890ff;">${nfzData.name}</h4>
        <p><strong>等级:</strong> ${nfzData.levelConfig.name}</p>
        <p><strong>类型:</strong> ${nfzData.geometry?.type === 'circle' ? '圆形区域' : '多边形区域'}</p>
        ${nfzData.height ? `<p><strong>高度限制:</strong> ${nfzData.height}m</p>` : ''}
        ${nfzData.description ? `<p><strong>描述:</strong> ${nfzData.description}</p>` : ''}
        ${nfzData.address ? `<p><strong>地址:</strong> ${nfzData.address}</p>` : ''}
      </div>
    `;
  }

  /**
   * 渲染禁飞区数据
   * @param {Array} nfzAreas - 处理后的禁飞区数据
   * @param {Object} options - 渲染选项
   */
  renderNFZAreas(nfzAreas, options = {}) {
    if (!Array.isArray(nfzAreas)) {
      console.warn('LeafletNFZRenderer: Invalid nfzAreas data');
      return;
    }

    // 清除现有数据
    this.clearNFZAreas();

    const renderOptions = {
      showLabels: true,
      enableClick: true,
      maxDisplayCount: 100,
      ...options
    };

    // 限制显示数量
    const areasToRender = nfzAreas.slice(0, renderOptions.maxDisplayCount);
    
    areasToRender.forEach((area, index) => {
      try {
        this.renderSingleArea(area, renderOptions);
        
        // 渲染子区域（倒序渲染，确保后添加的在上层）
        if (area.subAreas && area.subAreas.length > 0) {
          area.subAreas.slice().reverse().forEach(subArea => {
            this.renderSingleArea(subArea, { ...renderOptions, isSubArea: true });
          });
        }
      } catch (error) {
        console.error('LeafletNFZRenderer: Error rendering area:', area, error);
      }
    });

    console.log(`LeafletNFZRenderer: Rendered ${this.nfzLayers.size} NFZ layers`);
    
    // 确保图层组在地图上
    if (this.isVisible && !this.map?.hasLayer(this.nfzLayerGroup)) {
      this.nfzLayerGroup.addTo(this.map);
    }
  }

  /**
   * 渲染单个禁飞区域
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   */
  renderSingleArea(area, options) {
    if (!area.geometry) {
      console.warn('LeafletNFZRenderer: Area has no geometry:', area);
      return;
    }

    let layer = null;

    if (area.geometry.type === 'circle') {
      layer = this.renderCircleArea(area, options);
    } else if (area.geometry.type === 'polygon') {
      layer = this.renderPolygonArea(area, options);
    }

    if (layer) {
      // 添加禁飞区数据到图层
      layer.nfzData = area;
      
      // 设置点击事件
      if (options.enableClick) {
        layer.on('click', (event) => {
          this.handleNFZClick(area, event);
        });
      }

      // 设置鼠标悬停效果
      layer.on('mouseover', function(e) {
        this.setStyle({
          weight: 3,
          opacity: 0.8,
          fillOpacity: 0.6
        });
      });

      layer.on('mouseout', function(e) {
        this.setStyle({
          weight: 2,
          opacity: 0.7,
          fillOpacity: area.geometry.opacity
        });
      });

      // 存储图层引用
      this.nfzLayers.set(area.id, layer);
      
      // 添加到图层组
      this.nfzLayerGroup.addLayer(layer);
    }
  }

  /**
   * 渲染圆形禁飞区
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   * @returns {Object} Leaflet图层
   */
  renderCircleArea(area, options) {
    const geometry = area.geometry;
    const center = [geometry.center[1], geometry.center[0]]; // Leaflet使用[lat, lng]格式
    
    const circleOptions = {
      color: geometry.color,
      fillColor: geometry.color,
      fillOpacity: geometry.opacity,
      weight: 2,
      opacity: 0.7,
      radius: geometry.radius
    };

    const circle = L.circle(center, circleOptions);

    // 添加标签（只有父级区域显示标签）
    if (options.showLabels && !options.isSubArea) {
      const tooltip = L.tooltip({
        permanent: true,
        direction: 'center',
        className: 'nfz-label',
        opacity: 0.8
      }).setContent(area.name);

      circle.bindTooltip(tooltip);
    }

    return circle;
  }

  /**
   * 渲染多边形禁飞区
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   * @returns {Object} Leaflet图层
   */
  renderPolygonArea(area, options) {
    const geometry = area.geometry;
    
    if (!geometry.coordinates || !geometry.coordinates[0]) {
      console.warn('LeafletNFZRenderer: Invalid polygon coordinates:', geometry);
      return null;
    }

    // 转换坐标为Leaflet格式 [lat, lng]
    const latlngs = geometry.coordinates[0].map(coord => [coord[1], coord[0]]);

    const polygonOptions = {
      color: geometry.color,
      fillColor: geometry.color,
      fillOpacity: geometry.opacity,
      weight: 2,
      opacity: 0.7
    };

    const polygon = L.polygon(latlngs, polygonOptions);

    // 添加标签（只有父级区域显示标签）
    if (options.showLabels && !options.isSubArea) {
      // 计算多边形中心点
      const center = this.calculatePolygonCenter(geometry.coordinates[0]);
      if (center) {
        const tooltip = L.tooltip({
          permanent: true,
          direction: 'center',
          className: 'nfz-label',
          opacity: 0.8
        }).setContent(area.name);

        // 在中心点添加标签
        const marker = L.marker([center[1], center[0]], {
          icon: L.divIcon({
            className: 'nfz-label-marker',
            html: '',
            iconSize: [0, 0]
          })
        });

        marker.bindTooltip(tooltip);
        this.nfzLayerGroup.addLayer(marker);
      }
    }

    return polygon;
  }

  /**
   * 计算多边形中心点
   * @param {Array} coordinates - 坐标数组
   * @returns {Array} 中心点坐标 [lng, lat]
   */
  calculatePolygonCenter(coordinates) {
    if (!coordinates || coordinates.length === 0) return null;

    let totalLng = 0;
    let totalLat = 0;
    let count = 0;

    coordinates.forEach(coord => {
      if (coord && coord.length >= 2) {
        totalLng += coord[0];
        totalLat += coord[1];
        count++;
      }
    });

    if (count === 0) return null;

    return [totalLng / count, totalLat / count];
  }

  /**
   * 清除所有禁飞区
   */
  clearNFZAreas() {
    if (this.nfzLayerGroup) {
      this.nfzLayerGroup.clearLayers();
    }
    this.nfzLayers.clear();
  }

  /**
   * 设置禁飞区可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.isVisible = visible;
    if (this.nfzLayerGroup) {
      if (visible) {
        if (!this.map.hasLayer(this.nfzLayerGroup)) {
          this.nfzLayerGroup.addTo(this.map);
        }
      } else {
        if (this.map.hasLayer(this.nfzLayerGroup)) {
          this.map.removeLayer(this.nfzLayerGroup);
        }
      }
    }
  }

  /**
   * 获取禁飞区可见性
   * @returns {boolean} 是否可见
   */
  getVisible() {
    return this.isVisible;
  }

  /**
   * 设置点击回调函数
   * @param {Function} callback - 回调函数
   */
  setClickCallback(callback) {
    this.onNFZClick = callback;
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    // 清除数据
    this.clearNFZAreas();
    
    // 移除图层组
    if (this.nfzLayerGroup && this.map) {
      this.map.removeLayer(this.nfzLayerGroup);
    }
    
    this.nfzLayerGroup = null;
    this.map = null;
    this.nfzLayers.clear();
  }
}

/**
 * 创建Leaflet禁飞区渲染器实例
 * @param {Object} map - Leaflet Map实例
 * @returns {LeafletNFZRenderer} 渲染器实例
 */
export function createLeafletNFZRenderer(map) {
  return new LeafletNFZRenderer(map);
}

export default {
  LeafletNFZRenderer,
  createLeafletNFZRenderer
};
