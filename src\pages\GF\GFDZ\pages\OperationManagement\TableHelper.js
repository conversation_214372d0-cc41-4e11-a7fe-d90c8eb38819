import { Input, InputNumber } from 'antd';
// 中文字段映射表
export const columnTranslation = {
    ID:'ID',
    sn:'SN码',
    problem:'组件问题',
    ni:'子阵编号',
    groups:'光伏子串',
    Xmmc:'项目名称',
    Xmgm:'项目规模',
    Xmgq:'项目工期',
    Zdmj:'占地面积',
    Xmwz:'项目位置',
    Xgdwmc:'相关单位名称',
    Xmjj:'项目简介',
    State:'状态',
    policy:'所属项目',
    OrgCode:'组织码',
    ZiName:'子阵名称',
    ZiArea:'子阵编号',
    guarea:'组串范围',
};

export const TableHelper = {
    
}


// 字段组件映射表
export const FIELD_COMPONENT_MAPPING = {
    Xmjj: 'textarea',
    State: 'number',
    // ...其他字段配置
  };

export const getFieldComponent = (fieldType) => {
    switch(fieldType) {
        case 'number':
            return <InputNumber style={{ width: '100%' }} />;
        case 'textarea':
            return <Input.TextArea rows={3} />;
        default:
            return <Input />;
    }
};
