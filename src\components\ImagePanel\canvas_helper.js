export function InitImgCanvas(id,img1,setData) {
  const cc = document.getElementById(id);
  const ctx = cc.getContext('2d');
  const img = new Image()
  img.src = img1
  let w1=cc.offsetWidth
  let h1=cc.offsetHeight

  img.onload = function () {

     
     let w2=img.width/img.height*h1;
     if(w2>w1){
       h1=img.height/img.width*w1;
     }else{
       w1=w2;
     }
    
     cc.width = w1;
     cc.height = h1;


     ctx.drawImage(img, 0, 0,img.width,img.height,0,0,w1,h1);
     setData(ctx,img,w1,h1)
  }
//  return {ctx,img,w1,h1,ImgW:img.width,h2}
  // const xx=xx3(); 
}