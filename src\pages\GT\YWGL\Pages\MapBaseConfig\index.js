import React, { useState, useEffect } from 'react';
import { Layout, Button, Form, Input, Select, Table, Modal, message, Space, Popconfirm, Tooltip } from 'antd';

import { PlusOutlined, EditOutlined, DeleteOutlined, ReloadOutlined} from '@ant-design/icons';
import './index.less';
import api from '@/pages/GT/utils/api';

const { Header, Sider, Content } = Layout;


// 配置项常量
const configs = [
  {
    name: '地图坐标系标识',
    value: 'Srs',
    placeHolder: {
      wkid: '如：4490',
      wkt: 'GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137,298.257223563]]]'
    }
  },
  { name: '地图初始视图范围', value: 'Extent', placeHolder: '如：{"xmin":97,"ymin":21,"xmax":106,"ymax":29}' },
  { name: '地图坐标系参数(proj4)', value: 'proj', placeHolder: '如：+proj=longlat +ellps=GRS80 +no_defs' },
  { name: '地图初始显示等级', value: 'InitLevel', placeHolder: '如：10，注意：地图初始视图范围和地图初始等级不能同时设置' },
  { name: '三维地图初始显示高度', value: 'InitHeight', placeHolder: '如：5000000' },
  { name: '地图初始显示中心', value: 'InitCenter', placeHolder: '如：[-112, 38]，注意：地图初始视图范围和地图初始显示中心不能同时设置' },
  { name: '地图显示最小等级', value: 'MinZoom', placeHolder: '如：2' },
  { name: '地图显示最大等级', value: 'MaxZoom', placeHolder: '如：22' },
  { name: '地图行政区划服务', value: 'DistrictConfig', placeHolder: '' },
  { name: '几何服务地址', value: 'Geo', placeHolder: '如： https://192.168.20.8:6443/arcgis/rest/services/Utilities/Geometry/GeometryServer' }
];

// 格式化 Placeholder
const formatPlaceholder = (placeholder) => {
  if (typeof placeholder === 'object' && placeholder !== null) {
    // 特殊处理 Srs 的 placeholder
    if (placeholder.wkid && placeholder.wkt) {
      return `wkid: ${placeholder.wkid}, wkt: ${placeholder.wkt}`;
    }
    // 其他对象类型可以简单 stringify 或自定义格式
    return JSON.stringify(placeholder);
  }
  return placeholder || ''; // 返回字符串或空字符串
};

export default function MapBaseConfig() {
  const [baseConfigForm] = Form.useForm();
  const [data, setData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [configValuePlaceholder, setConfigValuePlaceholder] = useState('');
  const [editingRecord, setEditingRecord] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await api.queryMapBaseConfig();
      console.log('queryMapBaseConfig', res);
      setData(res.data);
    } catch (error) { 
      message.error('获取地图基本配置失败');
      console.error('Error in queryMapBaseConfig:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const columns = [
    {
      title: '配置名称',
      dataIndex: 'configName',
      key: 'configName',
    },
    {
      title: '配置值',
      dataIndex: 'configValue',
      key: 'configValue',
      // 长文本换行处理
      render: (text) => (
        <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', maxHeight: '100px', overflowY: 'auto' }}>
          {text}
        </div>
      ),
    },
    {
      title: '配置描述',
      dataIndex: 'configDescrition',
      key: 'configDescrition',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑">
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Popconfirm title="确定删除吗?"
            onConfirm={() => {
              handleDeleteSelected([record.rid], true)
            }}
            okText="确定" cancelText="取消">
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    // TODO: Implement add functionality
    console.log('Add clicked');
    const newRecord = {
      configName: '',
      configValue: '',
      configDescrition: '',
    };
    baseConfigForm.setFieldsValue(newRecord);
    setIsModalVisible(true);
  };

  // 处理配置名称选择变化
  const handleConfigNameChange = (value) => {
    const selectedConfig = configs.find(config => config.value === value);
    if (selectedConfig) {
      setConfigValuePlaceholder(formatPlaceholder(selectedConfig.placeHolder));
      // 当配置名称改变时，清空配置值，让用户重新输入
      baseConfigForm.setFieldsValue({ configValue: '' });
    } else {
      setConfigValuePlaceholder('');
    }
  };

  const handleEdit = (record) => {
    console.log('Edit clicked', record);
    setEditingRecord(record);
    const selectedConfig = configs.find(c => c.value === record.configName);
    setConfigValuePlaceholder(selectedConfig ? formatPlaceholder(selectedConfig.placeHolder) : '');
    baseConfigForm.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const executeDeleteLogic = async (keys) => {
    const hideMessage = message.loading('正在删除配置...', 0)
    try {
      console.log('Executing delete for keys:', keys)
      await Promise.all(keys.map(rid => handleDelete(rid)))

      hideMessage()
      message.success({
        content: `成功删除 ${keys.length} 条配置`,
        duration: 2,
        className: 'custom-message-success'
      })
      setSelectedRowKeys([]) // 清空选中项
      fetchData() // 重新获取数据
    } catch (error) { // handleDelete 内部的错误应该向上抛出，在这里统一处理
      hideMessage()
      console.error('Error during deletion:', error)
      message.error(`删除 ${keys.length} 条配置失败`) // 统一错误提示
    }
  }

  const handleDelete = async(rid) => {
    console.log('Delete clicked', rid);
    try {
      const res = await api.deleteMapBaseConfig({ rid });
      if (res.code !== 1) {
        throw new Error(res.message || '删除失败');
      }
    }
    catch (error) {
      console.error('Error in deleteMapBaseConfig:', error);
      throw error;
    }
  };

  const handleDeleteSelected = async (keys = selectedRowKeys, skipConfirm = false) => {
    const ridsToDelete = Array.isArray(keys) ? keys : selectedRowKeys
    if (ridsToDelete.length === 0) {
      message.warning('请先选择要删除的配置')
      return
    }
  
    if (skipConfirm) {
      // 如果跳过确认，直接执行删除逻辑
      await executeDeleteLogic(ridsToDelete)
    } else {
      // 否则，显示 Modal.confirm
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${ridsToDelete.length} 条配置吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          await executeDeleteLogic(ridsToDelete)
        }
      })
    }
  };

  const handleSaveConfig = async (type) => {
    try {
      const values = await baseConfigForm.validateFields();
      console.log('Form values:', values, {...values});
      if (type === 'add') {
        const res = await api.updateMapBaseConfig({...values});
        const newData = await api.queryMapBaseConfig();
        setData(newData.data);
        setIsModalVisible(false);
        setEditingRecord(null);
        message.success('保存成功');
      }
      if (type === 'edit') {
        const res = await api.updateMapBaseConfig({...values, rid: editingRecord.rid});
        let newData = await api.queryMapBaseConfig();
        setData(newData.data);
        setIsModalVisible(false);
        setEditingRecord(null);
        message.success('保存成功');
      }
    }
    catch (error) {
      console.error('Error in handleSaveConfig:', error);
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => {
      setSelectedRowKeys(keys);
    },
  };


  return (
    <div className="map-base-config-container">
      {/* Toolbar */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新建
          </Button>
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleDeleteSelected} 
            disabled={selectedRowKeys.length === 0}
          >
            删除
          </Button>
          <Button icon={<ReloadOutlined />} onClick={fetchData} loading={loading}>
            刷新
          </Button>
        </Space>
      </div>

      {/* Configuration Table */}
      <Table
        rowKey="rid"
        columns={columns}
        dataSource={data}
        loading={loading}
        rowSelection={rowSelection}
        pagination={{ pageSize: 10 }}
        bordered
      />

      {/* Edit Modal */}
      <Modal
        title={editingRecord ? '编辑地图基本配置' : '添加地图基本配置'}
        open={isModalVisible}
        onOk={() => handleSaveConfig(editingRecord ? 'edit' : 'add')}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingRecord(null);
        }}
      >
        <Form form={baseConfigForm} layout="vertical">
          <Form.Item label="配置名称" name="configName" rules={[{ required: true, message: '请输入配置名称' }]}>
            <Select
              placeholder="请选择配置名称"
              onChange={handleConfigNameChange}
            >
              {configs.map(config => (
                <Select.Option key={config.value} value={config.value}>
                  {config.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="配置值" name="configValue" rules={[{ required: true, message: '请输入配置值' }]}>
            <Input placeholder={configValuePlaceholder} />
          </Form.Item>
          <Form.Item label="配置描述" name="configDescrition">
            <Input placeholder="请输入配置描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}