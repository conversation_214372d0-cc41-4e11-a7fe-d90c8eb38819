import { Space, Tag, message, Modal, Switch } from "antd";
import { isEmpty } from "@/utils/utils";
import EditForm from "./form_edit";
import { getGuid } from "@/utils/helper";
import { timeFormat } from "@/utils/helper";
import { Post2 } from "@/services/general";

const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const { confirm } = Modal;

const deteleData = async (record, refrush) => {
  const xx = await Post2("/api/v1/MessageData/Delete", record);

  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("删除成功！");
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除记录",
    //icon: <ExclamationCircleFilled />,
    content: "确定删除该记录吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deteleData(record, refrush);
    },
    onCancel() {
    },
  });
};

const getDataItem = (title, data) => {
  return {
    title: getTableTitle(title),
    dataIndex: data,
    key: data,
    align: "center",
  };
};

const TableCols = (refrush, editForm, detailForm) => {
  const list = [];
  //list.push(getColData("单位","FDepartment"));
  // list.push(getDataItem("状态", "State"));

  list.push(getDataItem("消息类型", "MType"));
  list.push({
    title: getTableTitle("消息内容"),
    render: (record) => (
      <div style={{ maxWidth: window.innerWidth / 2, whiteSpace: "normal" }}>
        {record.MData}
      </div>
    ),
  });
  // list.push(getDataItem("通知时间", "CreateTM"));
  list.push({
    title: getTableTitle("通知时间"),
    align: "center",
    render: (record) => (
      <Space size="middle">{timeFormat(record.CreateTM)}</Space>
    ),
  });

  list.push({
    title: getTableTitle("消息状态"),
    align: "center",
    render: (record) => (
      <Space size="middle">{record.State == 0 ? 
        <div style={{color:'#7f434a'}}>未阅</div>
        :
        <div style={{color:'#64c52e'}}>已阅</div> 
        }</Space>
    ),
  });
  list.push({
    title: getTableTitle("操作"),
    align: "center",
    render: (record) => (
      <Space size="middle">
        <Tag>
          <a onClick={() => detailForm(record)}>详情</a>
        </Tag>
        <Tag>
          <EditForm
            key={getGuid()}
            refrush={refrush}
            data0={record}
            isTitle={false}
          ></EditForm>
        </Tag>
        <Tag>
          <a onClick={() => showDeleteConfirm(record, refrush)}>删除</a>
        </Tag>
      </Space>
    ),
  });
  return list;
};

export default TableCols;
