import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Descriptions, DatePicker, Radio, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { getImgUrl, isEmpty } from '@/utils/utils';

import locale from 'antd/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { HGet2, HPost2 } from '@/utils/request';
import { useModel } from 'umi';
import TableCols  from './table_task';
import LastPageButton from '@/components/LastPageButton';
import {getVideoDuration,downLoadClick} from './help';
import {getPStrArea} from '@/pages/Maps/helper.js';
import AddButton from '@/components/AddButton';
import LoadPanel from '@/components/IfShowPanel/load_panel';
import { timeFormat } from '@/utils/helper';

const FlightTaskFileAddDescriptions = ({data0,refrush}) => {

  const [fdata,setFData]=useState(data0);
  const [loading, setLoading] = useState(false);
  const [tList, setTList] = useState([]);
  const {setPage,lastPage}=useModel('pageModel');


  const onSave =async (e) => {

  //  const user=JSON.parse( localStorage.getItem('user'));

   const xx=await  HPost2('/api/v1/FlightTaskFile/Add',fdata);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
    }
    
    refrush();
    lastPage();
  };

  const getCost=(n,v1)=>{
      if(n==0) return 1500;
      if(v1<=1) return 3000;
      return Math.ceil(v1-1)*1000+3000;
  }

 const getPanel=(data)=>{
    if(loading){
      return <LoadPanel />
    }

    return <Descriptions title={'外飞任务信息'}  labelStyle={{paddingLeft:12.0,width:160}}  bordered column={2} extra={<AddButton style={{marginRight:48.0}} onClick={onSave}>保存</AddButton>}>

    <Descriptions.Item label="单位">
      <Input
        value={data.FDepartment}
        onChange={(e)=>{setFData({...data,FDepartment:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="时间" >
      <Input
        value={data.FTime}
        onChange={(e)=>{setFData({...data,FTime:e.target.value})}}>
      </Input>
    </Descriptions.Item>

   

    <Descriptions.Item label="飞行任务" span={2}>
      <Input
        value={data.FName}
        onChange={(e)=>{setFData({...data,FName:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="相关函告" span={2}>
      <Input
        value={data.FPaper}
        onChange={(e)=>{setFData({...data,FPaper:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="飞行时长(分钟)">
    <InputNumber
        style={{width:'100%'}}
        value={data.FlyTime}
        onChange={(e)=>{
          const g1=((e+Number(data.RecordTime))*0.6).toFixed(1)
          setFData({...data,FlyTime:e.toFixed(0),UsedNet:g1})
          }}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="录制时长(分钟)">
    <InputNumber
        style={{width:'100%'}}
        value={data.RecordTime}
        onChange={(e)=>{
          const g1=((e+Number(data.FlyTime))*0.6).toFixed(1)
          setFData({...data,RecordTime:e.toFixed(0),UsedNet:g1})
          }}>
      </InputNumber>
    </Descriptions.Item>


    <Descriptions.Item label="消耗流量(G)">
    <InputNumber
        style={{width:'100%'}}
        value={data.UsedNet}
        onChange={(e)=>{setFData({...data,UsedNet:e.toFixed(1)})}}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="飞行面积(平方公里)">
      <InputNumber
        style={{width:'100%'}}
        value={data.FlyArea}
        onChange={(e)=>{
          const c1=getCost(data.VideoCount, e);
          setFData({...data,FlyArea:e,FlyCost:c1})
          
          }}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="预计费用(元)">
    <InputNumber
        style={{width:'100%'}}
        value={data.FlyCost}
        onChange={(e)=>{setFData({...data,FlyCost:e})}}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="影像(视频、图片)">
    <Input
        style={{width:'100%'}}
        value={data.Media}
        onChange={(e)=>{setFData({...data,Media:e.target.value})}}>
      </Input>
    </Descriptions.Item>
   

    <Descriptions.Item label="备注">
      <Input
        defaultValue={data.Remark}
        onChange={(e)=>{setFData({...data,Remark:e.target.value})}}>
      </Input>
    </Descriptions.Item>

  </Descriptions>}

  return  <Card title={<LastPageButton title="添加外飞任务" />} bordered={false}   > <Row>
      <Col span={4}></Col>
       <Col style={{paddingLeft:24.0}} span={16}>{getPanel(fdata)}</Col>
 
  </Row></Card>
}



export default FlightTaskFileAddDescriptions;