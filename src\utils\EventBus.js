// 简单的事件总线实现
class EventBus {
  constructor() {
    this.events = {};
  }

  // 订阅事件
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
    
    // 返回取消订阅的函数
    return () => {
      this.events[eventName] = this.events[eventName].filter(
        eventCallback => eventCallback !== callback
      );
    };
  }

  // 发布事件
  emit(eventName, data) {
    if (this.events[eventName]) {
      this.events[eventName].forEach(callback => {
        callback(data);
      });
    }
  }

  // 移除特定事件的所有监听器
  off(eventName) {
    if (this.events[eventName]) {
      delete this.events[eventName];
    }
  }
}

// 创建单例实例
const eventBus = new EventBus();

export default eventBus; 