import React from 'react';
import { Map, Marker } from 'react-amap';
import icon2 from '@/assets/icons/feiji.png'
import icon1 from '@/assets/icons/device.png'
import { isEmpty } from '@/utils/utils';
import { useModel } from 'umi';

window._AMapSecurityConfig = {
  securityJsCode:'88ba28663b4a46ae74e95dfd0d39347a',
}



const AMAP_KEY = '3b1158f01c7dd24f4470a8fbeb76a884'; // 需要替换成自己的高德地图API key
const mapStyle = "amap://styles/blue"; // 地图样式为‘靛青蓝’
//const center = { longitude: 114.298572, latitude: 30.584355 }; // 地图中心点位置，设置在武汉市
const zoom = 13; // 地图缩放级别

const  DJBaseMap=({h1,sn,device})=> {
  const {gps}=useModel('gpsModel');

  const getMarker=(lat,lng,icon)=>{
   return <Marker
    visiable={true}
    position={{latitude:lat,longitude:lng}}
  >
    <img height={48.0} width={48.0} src={icon}></img>
    </Marker>
  }


  if(isEmpty(device)) return <div/>

  return (
    <div style={{height:h1,width:'100%'}}>
    <Map
      amapkey={AMAP_KEY}
      zoom={zoom}
      center={{latitude:device.Lat,longitude:device.Lng}}
      mapStyle={mapStyle}
      viewMode="3D"
      pitch= {70}
    >
      {getMarker(device.Lat,device.Lng,icon1)}
      {gps[2]>device.Height? getMarker(gps[0],gps[1],icon2):null}
    </Map></div>
  );
}
export default DJBaseMap;

