import React, { useEffect } from 'react';
import styles from "./index.less";
import { useLocation } from 'umi';
import useContentStore from "@/pages/GT/DZJC/store";

const DataAcquisition: React.FC = () => {
  const location = useLocation();
  const { region } = useContentStore(); // 获取区域状态
  const currentPath = location.pathname;

  // 模拟根据 region 加载数据的方法
  const fetchDataByRegion = (regionId: string) => {
    console.log(`正在根据区域 ID "${regionId}" 请求数据...`);
    // 这里可以调用 API 接口获取数据
    // 示例：
    // api.fetchData(regionId).then(data => setData(data));
  };

  useEffect(() => {
    if (region) {
      fetchDataByRegion(region);
    }
  }, [region]); 

  return (
    <div className={styles.container}>
      <h2>数据管理页面</h2>
    </div>
  );
};

export default DataAcquisition;

