import { getBodyH } from '@/utils/utils';


import { Card,List,Button} from 'antd';

import { getImgUrl } from '@/utils/helper';


const getPanel=(list)=>{
 return <List
  grid={{ gutter: 16, column: 3 }}
  dataSource={list}
  pagination={{ pageSize: 6}}
 // style={{height:getBodyH(300)}}
  renderItem={item => (
    <List.Item>
      <Card title={item.title}
      size='small'

      bodyStyle={{height:36,fontSize:12}}
       cover={
         <img style={{width:'100%',height:'100%'}} src={getImgUrl(item.ObjectName)}
         onClick={() => {
          // const w = window.open('about:blank');
          // w.location.href = getImgUrl(item.ObjectName)

         }}
          />

       }
      >{item.FileName}</Card>
    </List.Item>
  )}
 />
 };



const TaskDetailPage2=(mList,lastPage)=>{

  return  <div style={{height:getBodyH(56),width:'100%'}}><Card style={{height:getBodyH(56),width:'100%'}} title={'航线记录'}  extra={<Button onClick={()=>lastPage()}>返回</Button>}>
            {getPanel(mList)}
      </Card></div>
}

export default TaskDetailPage2;
