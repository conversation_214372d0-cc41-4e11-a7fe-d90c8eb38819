

import { Modal } from 'antd';

import {
  <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Polyline,Polygon, Tooltip, Circle,
  GeoJSON,
  
  LayerGroup,
  LayersControl,

} from 'react-leaflet';

import { isEmpty } from '@/utils/utils';

const { BaseLayer,Overlay } = LayersControl;

const colors = [
  '#D7F9F0',
  '#A6E1E0',
  '#72BED6',
  '#5B8FF9',
  '#3474DB',
  '#005CBE',
  '#00419F',
  '#00287E'
];

const WeiXingDT="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
const HuiBaiDT="https://thematic.geoq.cn/arcgis/rest/services/ChinaOnlineStreetGray/MapServer/tile/{z}/{y}/{x}"
const GaoDeDT="http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"

export const GetDiTuUrl = () => {
  const xx=localStorage.getItem('baseMap')
    if(!isEmpty(xx)){
      return xx;
    }
  return WeiXingDT;
}

export const GetDiTuGps = (url) => {
   if (!url) return false; // 添加空值检查
   // 判断是否非 火星或百度 坐标系
   return isGCJ02(url) || isBaidu(url);
}

const GCJ02_MAP_DOMAINS = [
  'map.gtimg.com',    // 腾讯地图
  'autonavi.com',     // 高德地图
  'thematic.geoq.cn'  // GeoQ地图
]

/**
 * 判断地图URL是否使用GCJ-02坐标系
 * @param {string} url 地图服务URL
 * @returns {boolean} 是否为GCJ-02坐标系
 */
export const isGCJ02 = (url) => {
  if (!url) return false
  return GCJ02_MAP_DOMAINS.some(domain => url.includes(domain))
}

/**
 * 判断地图URL是否使用百度坐标系
 * @param {string} url 地图服务URL
 * @returns {boolean} 是否为百度坐标系
 */
export const isBaidu = (url) => {
  if (!url) return false
  return url.includes("map.bdimg.com");
}

export const GetDiTu = (cc) => {
  var items = [];
  if(isEmpty(cc)) cc="高德底图";
//   items.push(<BaseLayer checked={cc==="卫星底图"} name="卫星底图">
//   <TileLayer
//       attribution={null}
//       url="http://47.241.97.160:31403/map?x={x}&y={y}&z={z}"
//   />
// </BaseLayer>);
  
  items.push(<BaseLayer checked={cc==="水系底图"}  name="水系底图">
  <TileLayer
      attribution={null}
   
      url="http://thematic.geoq.cn/arcgis/rest/services/ThematicMaps/WorldHydroMap/MapServer/tile/{z}/{y}/{x}"
  />
</BaseLayer>);


items.push(<BaseLayer checked={cc==="卫星底图"}  name="卫星底图">

<TileLayer
    attribution={null}
    url="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
/>
</BaseLayer>);

items.push(<BaseLayer checked={cc==="灰白底图"}  name="灰白底图">
  <TileLayer
     attribution={null}
      url="https://thematic.geoq.cn/arcgis/rest/services/ChinaOnlineStreetGray/MapServer/tile/{z}/{y}/{x}"
  />
</BaseLayer>);

items.push(
<BaseLayer checked={cc==="高德底图"}  name="高德底图">
  <TileLayer
      attribution={null}
      url="http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
  />
</BaseLayer>);
  return items;
}



export const GetDiTu2 = () => {
  var items = [];
  items.push(<BaseLayer  name="卫星底图">
  <TileLayer
      attribution={null}
      url="http://47.241.97.160:31403/map?x={x}&y={y}&z={z}"
  />
</BaseLayer>);
  
  items.push(<BaseLayer name="水系底图">
  <TileLayer
      attribution={null}
   
      url="http://thematic.geoq.cn/arcgis/rest/services/ThematicMaps/WorldHydroMap/MapServer/tile/{z}/{y}/{x}"
  />
</BaseLayer>);

items.push(<BaseLayer checked name="灰白底图">
  <TileLayer
     attribution={null}
      url="https://thematic.geoq.cn/arcgis/rest/services/ChinaOnlineStreetGray/MapServer/tile/{z}/{y}/{x}"
  />
</BaseLayer>);

items.push(
<BaseLayer  name="高德底图">
  <TileLayer
      attribution={null}
      url="http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
  />
</BaseLayer>);


  return items;
}









