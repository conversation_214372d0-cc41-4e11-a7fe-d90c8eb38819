import J<PERSON>Z<PERSON> from "jszip";
import FileSaver from "file-saver";
import { DownLoadFile } from "@/services/general";
import {useEffect, useState } from "react";
import { HGet2 } from "@/utils/request";
import {message ,Modal} from "antd";
import { getDeviceName, getImgUrl, isEmpty } from "@/utils/utils";

const DownLoadBtn = ({record}) =>{
    const [count, setCount] = useState();
    const [loadings, setLoadings] = useState(false);
    const { confirm } = Modal;

    let title = loadings ? " 停止下载" : "下载文件";

    const beginDownLoad=async (record)=>{
      
        confirm({
          title: '下载媒体文件',
          //icon: <ExclamationCircleFilled />,
          content: '确定下载全部媒体文件吗？',
          okText: '下载',
         // okType: 'danger',
          cancelText: '取消',
          onOk() {
            downLoadMedia(record);
          },
          onCancel() {
           // console.log('Cancel');
          },
        });
    
    }

    const downLoadMedia2 = async (record) => {
      const mL = await HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`);
      if (mL) {
        handleBatchDownload(mL);
      }else{
        message.info('没有文件可下载')
      }
    };

    const downLoadMedia = async (record) => {
      const list=[]
      const sL = record.TaskID.split(",");
      for (let i = 0; i < sL.length; i++) {
        if (sL[i].length > 5) {
          const mList = await HGet2(`/api/v1/Media/GetListByTaskId?id=${sL[i]}`);
          if (!isEmpty(mList)) {
            list.push(...mList);
          }
        }
      }
      const qianz=record.FName+"-"+record.FTime

      if (list) {
        handleBatchDownload(list,qianz);
      }else{
        message.info('没有文件可下载')
      }

    }
  
    const getImgType=(xx)=>{
      if(xx.includes(".mp4")) return "视频";
      return "照片";
  }

    const handleBatchDownload = async (selectImgList,qianzhui) => {
      setLoadings(!loadings);
    
      const zip = new JSZip();
      const cache = {};
      let n1=0;
      let n2=0;
        for (let i = 0; i < selectImgList.length; i++) {
          if (loadings) {
            break
          }
            const item = selectImgList[i];
            const data =DownLoadFile( getImgUrl(item.ObjectName));

            const arr_name = item.ObjectName.split("/");

            let file_name = arr_name[arr_name.length - 1];
            const type1=getImgType(item.FileName);
            let qianzhui2="";
            if(type1=="照片") {
              n1=n1+1;
              qianzhui2=qianzhui+"-"+type1+n1;
            }
            if(type1=="视频") {
              n2=n2+1;
              qianzhui2=qianzhui+"-"+type1+n2;
            }
            file_name = qianzhui2+`${item.FileName.slice(item.FileName.indexOf('.'),item.FileName.length)}`

            zip.file(file_name, data, {binary: true});
            cache[file_name] = await data;
            setCount(`${i + 1} / ${selectImgList.length}`);
          };
          zip
          .generateAsync({type: "blob"})
          .then((content) => {
            if(content.size>22){
                FileSaver.saveAs(content,qianzhui+".zip");
                setLoadings(false);
            }
          });
        }
  
    function process() {
      if (loadings) {
        return count;
        //   return  <Progress
        //     type="dashboard"
        //     steps={8}
        //     percent={count}
        //     trailColor="rgba(0, 0, 0, 0.06)"
        //     strokeWidth={20}
        //     size={10}
        //   />
      }
    }
    return (
      <a onClick={() => beginDownLoad(record)}>
        <span>{loadings ? count : ""}</span>
        {title}
      </a>
    );
  };
  export default DownLoadBtn
  