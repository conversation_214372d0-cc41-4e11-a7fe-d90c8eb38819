import { useMapEvents, useMap<PERSON><PERSON>, Mark<PERSON>, Popup, <PERSON>ygon, Polyline } from 'react-leaflet';
import { Button, message } from 'antd';
import { redIcon, redIcon2 } from './dt_market';
import { HGet2, HPost2 } from '@/utils/request';
import { ConvertPList, GetDistance, IsAQQ, aqqPoints, getPList } from './helper';
import { isEmpty } from '@/utils/utils';
import { useModel } from 'umi';
import { Gcj02ToWgs84, Wgs84ToGcj02 } from './gps_helper';
import { GetDiTuGps } from './ditu';
import { getGuid } from '@/utils/helper';
import { FlyToXuanTing } from '@/pages/DJI/FlyToPage/helper';
import { FlyToPoint2 } from '@/pages/DJI/FlyToPage/helper';


const DrawWayLinePanel = (props) => {

  const { gps, sn, baseMap, ifDraw } = props;
  const { position, setPosition, ifP, setIfP, pList, setPList, ifDrawLine } = useModel('mapModel');
  const { jcData } = useModel('dockModel')
  const { DoCMD } = useModel('cmdModel');

  const device = JSON.parse(localStorage.getItem('device'))

  const getP = (p1) => {
    const pb1 = GetDiTuGps(baseMap);
    if (pb1) {
      return Gcj02ToWgs84(p1[0], p1[1]);
    }
    return p1;
  }

  const getP2 = (p1) => {
    const pb1 = GetDiTuGps(baseMap);
    if (pb1) {
      return Wgs84ToGcj02(p1[0], p1[1]);
    }
    return p1;
  }

  //console.log('LocationMarker',props)
  const map = useMapEvent('click', (e) => {
    // console.log('draw',e)
    if (ifDrawLine) {
      const p2 = getP([e.latlng.lat, e.latlng.lng])
      const x2 = [...pList, p2];
      setPList(x2);
    }
    //if (ifP) {
    if (true) {
      const p2 = getP([e.latlng.lat, e.latlng.lng])
      setPosition(p2);
    }
  })

  const FlyToPoint = (sn, lat, lng, h1) => {
    // if(isEmpty(h1)){
    //     message.info('请先起飞机场');
    //     return;
    // }
    // if(isEmpty(h1)){
    //   h1=device.Height+50
    // }

    // if((h1-device.Height)<50){
    //   h1=device.Height+50
    // }

    // const xx = IsAQQ(lat, lng)
    // if (!xx) {
    //   message.info("该点不在安全区，禁止手动飞行！");
    //   return;
    // }
    // const h2=h1-device.Height
    const v1 = GetDistance(lat, lng, device.Lat, device.Lng);
    if (v1 > 10000) {
      message.info('该点距离太远，建议采用航线飞行！');
      return;
    }
    //  
    // FlyToPoint2(sn,lat,lng,150)
    //FlyToPoint(sn,lat,lng,h1)
    HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
    // if(ifP){
    //   HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
    // }else{
    //   HGet2("/api/v1/WayLine/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h2)
    // }

    // if(gps[2]-device.Height>10){
    //   HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
    // }else{
    //   HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +(h1-device.Height))
    // }

    //HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1)
  }

  const XuanTingPoint = (sn, lat, lng, h1, r1) => {
    // 
    if (jcData.current.mode_code != 0) {
      message.info('当前机场状态不支持飞行，请在机场空闲时操作！');
      return
    }
    const v1 = GetDistance(lat, lng, device.Lat, device.Lng);
    if (v1 > 10000) {
      message.info('该点距离太远，建议采用航线飞行！');
      return;
    }
    HGet2("/api/v1/WayLine/XuanTing2?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1 + "&r1=" + r1)
  }

  const LookToPoint = (sn, lat, lng, h1) => {

    const data = {
      "locked": true,
      "payload_index": device.Camera2,
      "latitude": lat,
      "longitude": lng,
      "height": h1
    }
    HPost2("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_look_at", data)
  }

  const StartHuanRao = (sn, lat, lng) => {

    const data = {
      "latitude": lat,
      "longitude": lng,
      "height": gps[2],
    }
    DoCMD(sn, 'poi_mode_enter', data);
  }

  // const FlyTheLine = (sn,pL) => {

  //   const data = {
  //     "PList":pL,
  //     "SN":sn,
  //     "Height":device.Height+100
  //   }
  //   HPost2("/api/v1/WayLine/FlyTo", data)
  // }

  // const deletePoint=(p1)=>{
  //   const pL2=[]
  //     pList.forEach(e => {
  //         console.log('draw',e,p1)
  //         if(e!=p1){
  //           pL2.push(e)
  //         }
  //     });
  //   setPList(pL2);
  // }  

  const getMarker = (p1) => {
    const p2 = getP2(p1);
    return <Marker position={p2} icon={redIcon2} />
  }

  const getPopPanel = () => {
    if (jcData.current.mode_code == 0) {
      return <Popup autoClose={true} style={{ width: 360 }}>
        <Button style={{ margin: 4.0 }} onClick={(e) => {
          XuanTingPoint(sn, position[0], position[1], 150, "-30");
          e.stopPropagation();
        }}>云台30度悬停</Button>
        <Button style={{ margin: 4.0 }} onClick={(e) => {
          XuanTingPoint(sn, position[0], position[1], 150, "-90");
          e.stopPropagation();
        }}>云台垂直悬停</Button>
      </Popup>
    }

    return <Popup autoClose={true} style={{ width: 360 }}>
      <Button style={{ margin: 4.0 }} onClick={(e) => {
        FlyToPoint(sn, position[0], position[1], gps[2]);
        e.stopPropagation();
      }}>飞至该点</Button>
      <Button style={{ margin: 4.0 }} onClick={(e) => {
        LookToPoint(sn, position[0], position[1], gps[2]);
        e.stopPropagation();
      }}>朝向该点</Button>
      {/* <Button  style={{margin:4.0}} onClick={(e)=>{StartHuanRao(sn,position[0],position[1]);
      e.stopPropagation();
    }}>绕点飞行</Button> */}
    </Popup>
  }

  const getMarker2 = (p1) => {
    if (isEmpty(p1)) return null;
    const p2 = getP2(p1);
    const sn = device.SN;

    return <Marker icon={redIcon} position={{ lat: p2[0], lng: p2[1] }} >
      {getPopPanel()}
    </Marker>
  }

  const getWayLine = (pL2) => {
    //const pL=pList;
    const list = [];

    //if(ifP){
    list.push(getMarker2(position))
    //}




    if (!ifDrawLine) return list;
    if (isEmpty(pL2)) return list;
    const pL = ConvertPList(pL2, baseMap);
    list.push(<Polyline key={getGuid()} weight={2} color={'green'} positions={pL} />);
    let i = 1;
    pL2.forEach(p => {
      list.push(getMarker(p));
      i++;
    })
    return list;
  }

  const getPanel = () => {

    const list = []
    const p2 = getP2(position)
    list.push(<Polyline weight={1} color={'green'} positions={aqqPoints} />)
    return list;
  }
  //if(gps[2]<860) return <div/>;

  return getWayLine(pList);

}

export default DrawWayLinePanel;