.measure-marker {
  background: transparent;
  border: none;
}

.markerContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(40, 40, 40, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(40, 40, 40, 1);
  }
}

.markerText {
  color: #fff;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
}

.deleteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

:global {
  .leaflet-tooltip-pane{
    z-index: 652 !important;
  }
  .leaflet-tooltip.polygon-tooltip,
  .leaflet-tooltip.distance-tooltip,
  .leaflet-tooltip.polygon-tooltip-preview {
    background: rgba(40, 40, 40, 0.9);
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    color: white;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: rgba(40, 40, 40, 1);
      transform: translateY(-1px);
    }

    &::before {
      display: none;
    }

    > .markerContainer {
      background-color: transparent;
      box-shadow: none;
      padding: 0;
    }
  }

  .leaflet-tooltip.polygon-tooltip-preview {
    background: rgba(40, 40, 40, 0.85);
    font-style: italic;
  }
}

.measureBar {
  position: absolute;
  top: 20px;
  right: 140px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.95);
  }

  .measureDropdownButton {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
}

.measureIcon {
  margin-right: 8px;
  font-size: 24px;
}

.unitSelector {
  display: flex;
  align-items: center;
  gap: 5px;
  // background-color: rgba(22, 28, 36, 0.8);
  padding: 4px 8px;
  border-radius: 4px;

  span {
    font-size: 12px;
  }

  .select {
    min-width: 70px;
    color: white;

    &.area {
      min-width: 90px;
    }
  }
}

.drawingCursor {
  cursor: crosshair !important;
}