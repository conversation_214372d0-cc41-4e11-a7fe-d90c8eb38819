import React from 'react';
import { Cesium } from "umi";
import { Select, Descriptions, Slider } from 'antd';

function ActionListInfo({ wayline, seleteIndex, setWaylineCopy, PTZ_headingPitchRoll_Change }) {
    // 动作参数输入框改变
    function InputonChange(e, item, index, ind) {
        if (e !== null) {
            wayline.PList[seleteIndex].ActionList[index].ActionParam[ind] = `${e}`
            if (wayline.PList[seleteIndex].PType === '0') {
                PTZ_headingPitchRoll_Change({
                    heading: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[0]),
                    pitch: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[1].ActionParam[1]),
                    roll: 0
                })
            } else if (wayline.PList[seleteIndex].PType === '3') {
                PTZ_headingPitchRoll_Change({
                    heading: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[1]),
                    pitch: Cesium.Math.toRadians(wayline.PList[seleteIndex].ActionList[0].ActionParam[0]),
                    roll: 0
                })
            }
            setWaylineCopy({ ...wayline, })
        }
    }
    // 动作参数选择框改变
    function handleChange(value, item, index, ind) {
        wayline.PList[seleteIndex].ActionList[index].ActionParam[ind] = value
        setWaylineCopy({ ...wayline, })
    };
    return <div style={{ maxHeight: 'calc(100% - 160px)', overflowY: 'auto' }}>
        <hr></hr>
        <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between', rowGap: '8px', background: '#fff', borderRadius: '4px', padding: '0 8px 0 8px' }}>
            {wayline.PList[seleteIndex].ActionList && wayline.PList[seleteIndex].ActionList.map((item, index) => {
                if (item.ActionName === '0') {
                    return <div style={{ width: '100%' }} key='俯仰角'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>俯仰角</div>
                        <Slider
                            marks={{
                                '-90': '-90°',
                                '-65': '-65°',
                                '-40': '-40°',
                                '-15': '-15°',
                                '10': '10°',
                                '35': '35°',
                            }}
                            size="small"
                            min={-90}
                            max={35}
                            precision={2}
                            style={{ width: '95%' }}
                            value={item.ActionParam[1]}
                            onChange={(e) => InputonChange(e, item, index, 1)} />
                    </div>
                } else if (item.ActionName === '1') {
                    return <div style={{ width: '100%' }} key='录像镜头'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>录像镜头</div>
                        <Select
                            style={{ width: '100%' }}
                            placeholder={'请选择录像镜头'}
                            value={item.ActionParam[0]}
                            size="small"
                            onChange={(value) => handleChange(value, item, index, 0)}
                            options={[
                                { value: 'zoom', label: '变焦' },
                                { value: 'wide', label: '广角' },
                                { value: 'ir', label: '红外' },
                            ]}
                        />
                    </div>
                } else if (item.ActionName === '2') {
                    return <div style={{ width: '100%' }} key='录像镜头'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>录像镜头</div>
                        <Select
                            style={{ width: '100%' }}
                            placeholder={'请选择录像镜头'}
                            value={item.ActionParam[0]}
                            size="small"
                            onChange={(value) => handleChange(value, item, index, 0)}
                            options={[
                                { value: 'zoom', label: '变焦' },
                                { value: 'wide', label: '广角' },
                                { value: 'ir', label: '红外' },
                            ]}
                        />
                    </div>
                } else if (item.ActionName === '3') {
                    return <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap' }} key='拍照'>
                        <div style={{ width: '100%' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>偏航角</div>
                            <Slider
                                marks={{
                                    '-180': '-180°',
                                    '-135': '-135°',
                                    '-90': '-90°',
                                    '-45': '-45°',
                                    '0': '0°',
                                    '45': '45°',
                                    '90': '90°',
                                    '135': '135°',
                                    '180': '180°'
                                }}
                                min={-180}
                                max={180}
                                precision={2}
                                style={{ width: '95%' }}
                                value={item.ActionParam[1]}
                                onChange={(e) => InputonChange(e, item, index, 1)} />
                        </div>
                        <div style={{ width: '100%' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>俯仰角</div>
                            <Slider
                                marks={{
                                    '-90': '-90°',
                                    '-65': '-65°',
                                    '-40': '-40°',
                                    '-15': '-15°',
                                    '10': '10°',
                                    '35': '35°',
                                }}
                                size="small"
                                min={-90}
                                max={35}
                                precision={2}
                                style={{ width: '95%' }}
                                value={item.ActionParam[0]}
                                onChange={(e) => InputonChange(e, item, index, 0)} />
                        </div>
                        <div style={{ width: '100%' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>照片格式</div>
                            <Select
                                style={{ width: '100%' }}
                                placeholder={'请选择照片格式'}
                                value={item.ActionParam[2]}
                                onChange={(value) => handleChange(value, item, index, 2)}
                                options={[
                                    { value: 'zoom', label: '变焦' },
                                    { value: 'wide', label: '广角' },
                                    { value: 'ir', label: '红外' },
                                ]}
                            />
                        </div>
                        <div style={{ width: '100%' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>变焦倍率</div>
                            <Slider
                                marks={{
                                    '1': '1',
                                    '112': '122'
                                }}
                                min={1}
                                max={112}
                                precision={2}
                                style={{ width: '95%' }}
                                value={item.ActionParam[3]}
                                onChange={(e) => InputonChange(e, item, index, 3)} />
                        </div>
                    </div>
                } else if (item.ActionName === '4') {
                    return <div style={{ width: '100%' }} key='悬停时间'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }} >{'悬停时间'}</div>
                        <Slider
                            marks={{
                                '0': '0秒',
                                '20': '20秒',
                                '40': '40秒',
                                '60': '60秒',
                                '80': '80秒',
                                '100': '100秒',
                            }}
                            size="small"
                            min={0}
                            max={100}
                            precision={2}
                            style={{ width: '95%' }}
                            value={item.ActionParam[0]}
                            onChange={(e) => InputonChange(e, item, index, 0)} />
                    </div>
                } else if (item.ActionName === '6') {
                    return <div style={{ width: '100%' }} key='照片格式'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }} >{'照片格式'}</div>
                        <Select
                            style={{ width: '100%' }}
                            size="small"
                            placeholder={'请选择照片格式'}
                            value={item.ActionParam[0]}
                            onChange={(value) => handleChange(value, item, index, 0)}
                            options={[
                                { value: 'zoom', label: '变焦' },
                                { value: 'wide', label: '广角' },
                                { value: 'ir', label: '红外' },
                            ]}
                        />
                    </div>
                } else if (item.ActionName === '7') {
                    return <div style={{ width: '100%' }} key='偏航角'>
                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }} >{'偏航角'}</div>
                        <Slider
                            marks={{
                                '-180': '-180°',
                                '-135': '-135°',
                                '-90': '-90°',
                                '-45': '-45°',
                                '0': '0°',
                                '45': '45°',
                                '90': '90°',
                                '135': '135°',
                                '180': '180°'
                            }}
                            size="small"
                            min={-180}
                            max={180}
                            precision={2}
                            style={{ width: '95%' }}
                            value={item.ActionParam[0]}
                            onChange={(e) => InputonChange(e, item, index, 0)} />
                    </div>
                }
            })}
        </div>
    </div>
}
export default ActionListInfo;