import "leaflet/dist/leaflet.css";
import { GetDiTuUrl } from "@/pages/Maps/ditu";
import L from "leaflet";
import { getMarket5, WaylinePointMarker } from "@/pages/Maps/dt_market";
import { useModel } from "umi";
import DevicePage from "../../DevicePage";
import { isEmpty } from "@/utils/utils";
import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from "react";
import MeasurePanel from "./MeasurePanel";
import ToggleBaseMapButton from "../../DevicePage/ToggleBaseMapButton";
import useConfigStore from "@/stores/configStore";
import { GetDiTuGps } from "@/pages/Maps/ditu";
import { wgs84Togcj02 } from "@/pages/Maps/gps_helper";
import { message } from "antd";

const OrgMap2 = forwardRef((props, ref) => {
  const { data, currentRoute, isMapFullScreen } = props;
  const { setMapSelf, ZSMapUrl,headerHeight } = useConfigStore();
  const { setPage } = useModel("pageModel");
  const { mapUrl, setMapUrl, baseMapLayers } = useModel("mapModel");
  const mapRef = useRef({});
  const baseLayerRef = useRef(null);
  const routeLayerRef = useRef(null);
  const routePointsRef = useRef([]);

  // 解析点位数据，支持两种格式
  const parsePoints = (pointData) => {
    // 如果是字符串格式 ("lng,lat,alt;lng,lat,alt;...")
    if (typeof pointData === 'string') {
      const list = [];
      const pointArray = pointData.split(";");
      pointArray.forEach((p) => {
        const coords = p.split(",");
        if (coords.length >= 2) {
          // [纬度, 经度] 格式，注意顺序!
          let lat = Number(coords[1]);
          let lng = Number(coords[0]);
          
          // 如果需要坐标转换
          if (GetDiTuGps(mapUrl)) {
            const converted = wgs84Togcj02(lng, lat);
            list.push([converted[0], converted[1]]);
          } else {
            list.push([lat, lng]);
          }
        }
      });
      return list;
    } 
    // 如果是对象数组格式 ([{latitude, longitude, ...}, ...])
    else if (Array.isArray(pointData)) {
      return pointData.map(point => {
        // 如果需要坐标转换
        if (GetDiTuGps(mapUrl)) {
          const converted = wgs84Togcj02(point.longitude, point.latitude);
          return [converted[0], converted[1]];
        }
        return [point.latitude, point.longitude];
      });
    }
    
    return [];
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    leafletMap: mapRef.current,
    setMapCenter: (lat, lng) => {
      if (mapRef.current) {
        console.log('移动地图中心到:', lat, lng);
        let center = [lat, lng];
        // 根据地图类型进行坐标转换
        if (GetDiTuGps(mapUrl)) {
          center = wgs84Togcj02(lng, lat);
        }
        mapRef.current.setView(center, 15); // 不使用动画的即时移动
      }
    },
    // 添加平滑移动方法
    flyToCenter: (lat, lng, options = {}) => {
      if (mapRef.current) {
        console.log('平滑移动地图中心到:', lat, lng);
        let center = [lat, lng];
        // 根据地图类型进行坐标转换
        if (GetDiTuGps(mapUrl)) {
          center = wgs84Togcj02(lng, lat);
        }
        // 使用Leaflet的flyTo方法实现平滑移动
        const { duration = 1.5, zoom = 15, easeLinearity = 0.25 } = options;
        mapRef.current.flyTo(center, zoom, {
          duration: duration,      // 动画持续时间（秒）
          easeLinearity: easeLinearity  // 动画的平滑程度
        });
      }
    },
    // 添加显示航线方法（使用新系统风格）
    showWayLine: (routeData) => {
      if (mapRef.current && routeData) {
        // 清除之前的航线图层
        if (routeLayerRef.current) {
          mapRef.current.removeLayer(routeLayerRef.current);
        }

        // 清除之前添加的点位标记
        routePointsRef.current.forEach(marker => {
          if (marker && mapRef.current.hasLayer(marker)) {
            mapRef.current.removeLayer(marker);
          }
        });
        routePointsRef.current = [];

        // 尝试从航线数据中提取点位信息
        let points = [];
        
        // 判断点位数据来源
        if (routeData.PointList && Array.isArray(routeData.PointList)) {
          points = parsePoints(routeData.PointList);
        } else if (routeData.pointList && Array.isArray(routeData.pointList)) {
          points = parsePoints(routeData.pointList);
        } else if (routeData.PointList && typeof routeData.PointList === 'string') {
          points = parsePoints(routeData.PointList);
        } else if (routeData.pointList && typeof routeData.pointList === 'string') {
          points = parsePoints(routeData.pointList);
        }

        if (points.length > 0) {
          // 创建航线图层
          routeLayerRef.current = L.polyline(points, {
            color: '#ff5722',
            weight: 4,
            opacity: 0.8,
            dashArray: '10, 10',
          }).addTo(mapRef.current);

          // 添加航线起点和终点标记
          const startIcon = L.divIcon({
            html: '<div style="background-color: #4CAF50; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white;"></div>',
            className: 'route-start-marker',
            iconSize: [14, 14],
          });

          const endIcon = L.divIcon({
            html: '<div style="background-color: #F44336; width: 10px; height: 10px; border-radius: 50%; border: 2px solid white;"></div>',
            className: 'route-end-marker',
            iconSize: [14, 14],
          });

          const startMarker = L.marker(points[0], { icon: startIcon })
            .bindTooltip('起点: ' + (routeData.taskName || ''), { permanent: false })
            .addTo(mapRef.current);
          
          const endMarker = L.marker(points[points.length - 1], { icon: endIcon })
            .bindTooltip('终点', { permanent: false })
            .addTo(mapRef.current);
          
          routePointsRef.current.push(startMarker, endMarker);

          // 调整地图以显示整个航线
          mapRef.current.fitBounds(routeLayerRef.current.getBounds(), {
            padding: [50, 50],
            animate: true,
            duration: 1
          });
        } else {
          console.log('航线数据中没有有效的点位信息');
        }
      }
    },
    // 使用原有系统风格显示航线的方法
    showWayLineOriginal: (routeData) => {
      if (mapRef.current && routeData) {
        // 清除之前的航线图层和点位标记
        if (routeLayerRef.current) {
          mapRef.current.removeLayer(routeLayerRef.current);
        }
        
        // 清除之前添加的点位标记
        routePointsRef.current.forEach(marker => {
          if (marker && mapRef.current.hasLayer(marker)) {
            mapRef.current.removeLayer(marker);
          }
        });
        routePointsRef.current = [];

        // 尝试从航线数据中提取点位信息
        let points = [];
        
        // 判断点位数据来源
        if (routeData.PointList && Array.isArray(routeData.PointList)) {
          points = parsePoints(routeData.PointList);
        } else if (routeData.pointList && Array.isArray(routeData.pointList)) {
          points = parsePoints(routeData.pointList);
        } else if (routeData.PointList && typeof routeData.PointList === 'string') {
          points = parsePoints(routeData.PointList);
        } else if (routeData.pointList && typeof routeData.pointList === 'string') {
          points = parsePoints(routeData.pointList);
        }

        if (points.length > 0) {
          // 创建航线图层 - 使用与WayLineMap相同的黄色实线
          routeLayerRef.current = L.polyline(points, {
            color: 'yellow',
            weight: 2,
            opacity: 1.0,
            dashArray: null, // 实线
          }).addTo(mapRef.current);

          // 给航线添加提示信息
          routeLayerRef.current.bindTooltip(routeData.taskName || "航线", {
            sticky: true
          });

          // 添加航线点位标记
          points.forEach((point, index) => {
            // 创建与WaylinePointMarker相同风格的标记
            const marker = L.marker(point, {
              icon: L.icon({
                iconUrl: require('@/assets/icons/red.png'),
                iconAnchor: [9, 18],
                iconSize: [18, 18],
              })
            }).bindTooltip(`点位${index + 1}`, {
              direction: "bottom",
              offset: [0, 5]
            });
            
            marker.addTo(mapRef.current);
            routePointsRef.current.push(marker);
          });

          // 调整地图以显示整个航线
          mapRef.current.fitBounds(routeLayerRef.current.getBounds(), {
            padding: [50, 50],
            animate: true,
            duration: 1
          });
        } else {
          console.log('航线数据中没有有效的点位信息');
        }
      }
    }
  }));

  // 监听currentRoute变化，显示航线
  useEffect(() => {
    if (currentRoute && mapRef.current && ref.current) {
      // 使用原系统风格的航线绘制方法
      if (ref.current.showWayLineOriginal) {
        ref.current.showWayLineOriginal(currentRoute);
      }
    }
  }, [currentRoute]);

  useEffect(() => {
    mapRef.current = L.map("mapDiv", {
      maxZoom: 22,
      minZoom: 3,
      zoomControl: false,
      attributionControl: false,
    });

    if(mapRef.current){
      setMapSelf(mapRef.current);
    }

    if (!isEmpty(data)) {
      const p2 = getCenter(data);
      mapRef.current.setView(p2, 9);
    }
    //  else {
    //   if (navigator.geolocation) {
    //     navigator.geolocation.getCurrentPosition(
    //       (position) => {
    //         let latitude = position.coords.latitude;
    //         let longitude = position.coords.longitude;
    //         let loction = [latitude, longitude];
    //         mapRef.current.setView(loction, 8);
    //       },
    //       (error) => {
    //         console.log(error.message);
    //       }
    //     );
    //   } else {
    //     console.log("您的浏览器不支持地理位置API");
    //   }
    // }
  }, []);

  const getCenter = (data) => {
    let lat = 0;
    let lng = 0;
    let nn = 0;
    data.forEach((e) => {
      lat = lat + e.Lat;
      lng = lng + e.Lng;
      nn = nn + 1;
    });
    const center = [lat / nn, lng / nn];
    if (GetDiTuGps(mapUrl)) {
      return wgs84Togcj02(center[1], center[0]);
    }
    return center;
  };

  const updateCenter = (newCenter, newZS, newZoom) => {
    if (!mapRef.current) return;
    if (newCenter) {
      mapRef.current.setView(newCenter, 18);
    }
  };

  const layers = useRef({});
  
    useEffect(() => {
      //切换正射
      if (mapRef.current && ZSMapUrl) {
        let item = ZSMapUrl;
        let layer = L.tileLayer(item.Url, {
          tms: item.MapType === 1 ? true : false,
          maxZoom: item.MaxZoom,
          minZoom: item.MinZoom,
          opacity: 1.0,
          attribution: "",
        });
        let center = L.latLng(item.Lat, item.Lng);
        mapRef.current.setView(center, 15);
        if (!isEmpty(layers.current)) {
          mapRef.current.removeLayer(layers.current);
        }
        if (layer) {
          layer.addTo(mapRef.current);
        }
        layers.current = layer;
      }
    }, [ZSMapUrl]);

  useEffect(() => {
    if (mapRef.current && mapUrl) {
      if (baseLayerRef.current) {
        mapRef.current.removeLayer(baseLayerRef.current);
      }
      baseLayerRef.current = L.tileLayer(mapUrl, {
        maxZoom: 22,
        maxNativeZoom: 18,
        zIndex: -1000,
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      }).addTo(mapRef.current);
    }
  }, [mapUrl]);

  useEffect(() => {
    if (mapRef.current) {
      const dt_bz2 =
        "http://t0.tianditu.gov.cn/cia_w/wmts?" +
        "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
        "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
        "&tk=d48a48254cdbd79edd0ed2c541639813";
      L.tileLayer(dt_bz2, { maxZoom: 20, zIndex: -999 }).addTo(mapRef.current);

      // L.tileLayer(GetDiTuUrl()).addTo(mapRef.current);
      //const dt_bz="http://t0.tianditu.gov.cn/cia_c/wmts?tk=d26935ae77bbc1fb3a6866a5b6ff573f"
      // const dt_bz="https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&VERSION=1.0.0&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=d26935ae77bbc1fb3a6866a5b6ff573f"
      const dt_bz =
        "https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=d26935ae77bbc1fb3a6866a5b6ff573f";
      L.tileLayer(
        "http://***************:9001/6251daf8-4127-40e0-980d-c86f8a765b20/map/mianzhu/{z}/{x}/{y}.png"
      ).addTo(mapRef.current);
      //  L.tileLayer("https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/filezip2/202302/{z}/{x}/{y}.png",{tms: true, maxZoom: 20,
      //   minZoom: 1, opacity: 1.0, attribution: ""}).addTo(mapRef.current);
      // L.tileLayer("/map/v1/Map/GetTile?p1=cyl20240610&z={z}&x={x}&y={y}",{tms: true, opacity: 1.0,maxZoom:20,minZoom:1, attribution: ""}).addTo(mapRef.current);
    }
  }, []);

  useEffect(() => {
    if (!isEmpty(data)) {
      // 无人机设备列表数据刷新时，不重新设置地图中心点 
      // const p2 = getCenter(data);
      // mapRef.current.setView(p2, 8);
      const mL = getHomeIcon2();
      mL.forEach((e) => {
        e.addTo(mapRef.current);
      });
      // L.geoJSON(line,{style:{
      //   // "color": "#5790ca",
      //   "color": "#d9d6c3",
      //   // "color":"#147b99",
      //   "weight": 8,
      //   "opacity": 1,
      //   "fillColor": 'transparent',
      //   "fillOpacity": 0,
      //   }}
      //  ).addTo(mapRef.current);
    }
  }, [data]);

  const eventHandlers = {
    click: (e2) => {
      const e = e2.target.options.data;
      localStorage.setItem("device", JSON.stringify(e));
      setPage(<DevicePage device={e} />);
    },
  };

  const eventHandlers2 = (e2) => {
    if(e2.IsAuth === 1) return message.warning("此设备未授权");
    localStorage.setItem("device", JSON.stringify(e2));
    setPage(<DevicePage device={e2} />);
  };

  //data所有设备的数据
  const prevCoordSystemRef = useRef(GetDiTuGps(mapUrl));
  
  useEffect(() => {
    if (!isEmpty(data)) {
      const currentCoordSystem = GetDiTuGps(mapUrl);
      const coordSystemChanged = currentCoordSystem !== prevCoordSystemRef.current;
      
      // 只有在坐标系统变化时才更新标记
      if (coordSystemChanged) {
        // 清除之前的标记
        mapRef.current.eachLayer((layer) => {
          if (layer._icon && layer._icon.className.includes('leaflet-marker-icon')) {
            mapRef.current.removeLayer(layer);
          }
        });
        
        // 添加新的标记
        const mL = getHomeIcon2();
        mL.forEach((e) => {
          e.addTo(mapRef.current);
        });
      }
      
      // 更新引用值
      prevCoordSystemRef.current = currentCoordSystem;
    }
  }, [data, mapUrl])

  const getHomeIcon2 = () => {
    const list = [];
    data.forEach((e) => {
      // 创建设备数据的副本，避免修改原始数据
      const deviceData = {...e};
      
      // 根据地图类型进行坐标转换
      if (GetDiTuGps(mapUrl)) {
        const convertedCoords = wgs84Togcj02(deviceData.Lng, deviceData.Lat);
        deviceData.Lat = convertedCoords[0];
        deviceData.Lng = convertedCoords[1];
      }
      
      list.push(getMarket5(deviceData, () => eventHandlers2(e)));
    });
    return list;
  };

  return (
    <div
      id="mapDiv"
      style={{
        position: "absolute",
        width: "100%",
        height: `calc(100vh - ${headerHeight}px)`,
        overflow: "hidden",
        ...(isMapFullScreen ? {
          position: "fixed",
          width: "100%",
          height: `100vh`,
          top: 0,
        } : {}),
      }}
    >
      {/* 暂时关闭测量工具 */}
      {/* {isEmpty(mapRef.current) ? null : (
        <MeasurePanel
          left={400}
          map={mapRef.current}
          updateCenter={updateCenter}
          showOrthofire={false}
          showBZBtn={true}
        />
      )} */}

      {/* <ToggleBaseMapButton /> */}
    </div>
  );
});

export default OrgMap2;
