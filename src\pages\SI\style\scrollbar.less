// SI 系统统一滚动条样式配置
// 全局滚动条样式（适用于 WebKit 内核浏览器：Chrome、Safari、Edge）

// 通配符选择器 - 确保所有元素都应用滚动条样式
* {
  // WebKit 滚动条样式 (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    width: 5px !important;
    height: 5px !important;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #08E7CB !important;
    border-radius: 3px !important;
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(8, 231, 203, 0.2) !important;
  }

  // 隐藏滚动条箭头按钮
  &::-webkit-scrollbar-button {
    display: none !important;
  }
}

// 专门针对 Ant Design 组件的额外保险配置
.ant-table,
.ant-table-wrapper,
.ant-table-container,
.ant-table-body,
.ant-table-content,
.ant-table-tbody,
.ant-table-thead,
.ant-table-header,
.ant-table-fixed-left,
.ant-table-fixed-right,
.ant-select-dropdown,
.ant-dropdown-menu,
.ant-menu,
.ant-tree,
.ant-list {
  scrollbar-width: thin;
  scrollbar-color: #08E7CB rgba(8, 231, 203, 0.2) !important;
}