.map-container {
  width: 100%;
  height: calc(100vh - 56px);
  position: relative;
}

.tree-panel {
  width: 300px;
  min-height: 300px;
  max-height: 90%;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  overflow-y: auto;
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  .ant-tree-list {
    color: #333 !important;
  }
}

.view-toggle {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;

  .view-toggle-button {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    position: relative;
    transition: all 0.3s;

    &.active {
      color: #1890ff;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -12px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #1890ff;
      }
    }

    &:hover:not(.active) {
      color: #40a9ff;
    }
  }
}

.map-view {
  width: 100%;
  height: 100%;
}

.toggle-button {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 8px 16px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  cursor: pointer;
  font-weight: bold;
  color: #333;
}

.layer-list-container {
  background-color: #fff !important; // 使用!important覆盖全局样式
  color: rgba(0, 0, 0, 0.85) !important; // 使用!important覆盖全局样式

  h3 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .layer-list {
    .layer-item {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.85) !important;

      .layer-name {
        flex: 1;
        color: rgba(0, 0, 0, 0.85) !important;
      }

      .layer-controls {
        display: flex;
        gap: 8px;

        button {
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          background: #fff;
          color: rgba(0, 0, 0, 0.85) !important;
          cursor: pointer;

          &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
          }

          &.hidden {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .empty-message {
    text-align: center;
    padding: 20px 0;
    color: #999 !important;
  }
}

.layer-list-container {
  background: #fff;
  color: rgba(0, 0, 0, 0.85);

  h3 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: rgba(0, 0, 0, 0.85);
  }

  .layer-list {
    .layer-item {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.85);

      .layer-name {
        flex: 1;
        color: rgba(0, 0, 0, 0.85);
      }

      .layer-controls {
        display: flex;
        gap: 8px;

        button {
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          background: #fff;
          color: rgba(0, 0, 0, 0.85);
          cursor: pointer;

          &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
          }

          &.hidden {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .empty-message {
    text-align: center;
    padding: 20px 0;
    color: #999;
  }
}
