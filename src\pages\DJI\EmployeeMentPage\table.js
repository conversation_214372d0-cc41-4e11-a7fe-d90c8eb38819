import { Space, Tag, message, Modal, Switch } from "antd";
import { downloadFile, isEmpty } from "@/utils/utils";
import "./table.css";
import { HGet2, HPost2 } from "@/utils/request";
import { Post2 } from "@/services/general";
import AdminDiv from "@/components/AdminDiv";
import UserEditForm from './form_edit'


const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const { confirm } = Modal;

const toFly = async (record) => {
  console.log("toFly");
  await HGet2("/api/v1/WayLine/Fly?fID=" + record.WanLineId);
};

const onChange = (e) => {
  console.log("ddd", e);
  if (e) {
    updateCron();
  }
};

const updateCron = async (record, refrush) => {
  if (isEmpty(record)) return;

  const xx = await HGet2("/api/v1/CronJob/ChangeState?id=" + record.ID);

  console.log("UpdateCronJob", xx);
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("更新成功！");
    refrush();
  }
};

const deleteCronJob = async (record, refrush) => {
  const xx = await Post2("/api/v1/UserInfo/Delete", record);

  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("删除成功！");
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除用户",
    content: "确定删除该用户吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deleteCronJob(record, refrush);
    },
    onCancel() {},
  });
};

const TableCols = (UserList,refrush, showMap) => {
  return [
    {
      title: getTableTitle("组织名称"),
      dataIndex: "OrgName",
      key: "OrgName",
      align: "center",
    },
    {
      title: getTableTitle("用户名称"),
      dataIndex: "Name",
      key: "Name",
      align: "center",
    },
    {
      title: getTableTitle("用户权限"),
      dataIndex: "Authority",
      key: "Authority",
      align: "center",
    },

    // {
    //   title: getTableTitle("联系电话"),
    //   dataIndex: "Phone",
    //   key: "Phone",
    //   align: "center",
    // },

    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
           <Tag>
               <UserEditForm key={record.ID} UserList={UserList} record={record} refrush={refrush}></UserEditForm>
          </Tag>
          <Tag>
            <AdminDiv>
              <a onClick={() => showDeleteConfirm(record, refrush)}>删除</a>
            </AdminDiv>
          </Tag>
        </Space>
      ),
    },
  ];
};

export default TableCols;
