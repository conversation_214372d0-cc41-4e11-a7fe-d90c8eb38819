// import lineImg from '@/assets/img/line.png';
export const mainThemes = {
  default: {
    primaryColor: '#0066ff',//主题色
    primarySimpleColor: '#4aa1f2',//辅助色(主题浅色)
    lineGradient: 'linear-gradient(90deg, #0066ff, #1890ff)',//线性渐变色
    buttonBackImg: '@/assets/img/line.png',//按钮背景图片
  },
  dark: {
    primaryColor: '#0ac7b0',
    primarySimpleColor: '#6accc0',
    lineGradient: 'linear-gradient(90deg, #0ac7b0, #077f71)',
     buttonBackImg: `@/assets/img/line.png`,//按钮背景图片
  },
};

export function setMainTheme(themeName) {
  if (!themeName) {
    //如果传过来了模式值，则使用传过来的模式值，
    //没有传值就能自动切换成自己手动设置的，需要在某些路由下的页面里执行这个函数应用样式
    let hashIncludes = ['system', 'SI'];
    let location = new URL(window.location);
    let hash = location.hash;
    let isInclude = hashIncludes.filter(item => hash.includes(item));
    if (isInclude) {
      themeName = 'dark';
    }
  }
  const theme = mainThemes[themeName];
  if (!theme) return;
  Object.keys(theme).forEach(key => {
    document.documentElement.style.setProperty(`--${key}`, theme[key]);
  });
}
