import { useEffect, useState } from "react";
import LeafletMap from "../Maps/LeafletMap";
import LeftPage from "@/pages/GF/GFDZ/pages/RealTimeMonitoring/LeftPage/LeftPage";
import RightPage from "@/pages/GF/GFDZ/pages/RealTimeMonitoring/RightPage/RightPage";
import MyModal from "@/pages/GT/components/MyModal";
import LeftBox from "@/pages/GT/components/LeftBox";
import RightBox from "@/pages/GT/components/RightBox";
import { Select, Space, ConfigProvider } from "antd";

export default function RealTimeMonitoring() {
  const [isMyModalOpen, setIsMyModalOpen] = useState(true);
  const [isMyModalOpen2, setIsMyModalOpen2] = useState(true);

  const closeMyModal = () => {
    setIsMyModalOpen(false);
  };
  const handleChange = (value) => {
    console.log(`selected ${value}`);
  };
  return (
    <div style={{ position: "relative" }}>
      <div style={{ position: "absolute", zIndex: 1, width: "100vw" }}>
        <LeafletMap />
      </div>
      <LeftBox child={<LeftPage />} />
      <RightBox child={<RightPage />} />
      <div></div>

      <div
        style={{
          position: "absolute",
          top: 10,
          left: "35%",
          zIndex: 1,
        }}
      >
        <ConfigProvider
          theme={{
            components: {
              Input: {
                colorBgContainer: "rgba(21, 33, 33,0.5)",
                colorText: "#fff",
                colorTextPlaceholder: "rgba(255,255,255,0.5)",
              },
              Button: {
                defaultBg: "none",
                defaultActiveBg: "none",
                defaultHoverBg: "none",
              },
              Select: {
                colorText: "#26dda0", // 设置选择器的文本颜色为白色
                colorTextPlaceholder: "rgb(255, 255, 255)", // 设置占位符颜色
                colorBgContainer: "rgba(21, 33, 33, 0.5)", // 下拉框背景
                colorBgElevated: "rgba(21, 33, 33, 0.5)", // 下拉框悬浮背景
              },
            },
          }}
        >
          <Select
            defaultValue="区域"
            style={{
              width: 120,
              color: "white",
            }}
            onChange={handleChange}
            options={[
              {
                value: "区域",
                label: "区域",
              },
              {
                value: "lucy",
                label: "Lucy",
              },
            ]}
          />
          <Select
            defaultValue="缺陷类型"
            style={{
              width: 120,
            }}
            onChange={handleChange}
            options={[
              {
                value: "缺陷类型",
                label: "缺陷类型",
              },
              {
                value: "lucy",
                label: "Lucy",
              },
            ]}
          />
        </ConfigProvider>
      </div>
      <MyModal
        title={"001无人机"}
        content={
          <div>
            <div>无人机编号:UAV-0025</div>
            <div>当前任务‌：故障复检（组件GJ-202503-25-36-045）</div>
            <div>剩余电量‌：65%（预计续航38分钟）</div>
            <div>
              飞行路径‌：N25°36'12", E103°45'30"→N25°36'15", E103°45'35"
            </div>
            <div>数据传输状态:已回传85%（含红外图像12张）</div>
          </div>
        }
        isOpen={isMyModalOpen}
        onClose={closeMyModal}
      />
      <MyModal
        title={"001无人机"}
        status={"遮挡"}
        content={
          <div>
            <div>无人机编号:UAV-0025</div>
            <div>‌组件编号‌：YX-202503-25-36-001</div>
            <div>实时发电效率‌：98.2%（理论值：150W）</div>
            <div>热成像温度‌：42.5℃（环境温度：25℃）</div>
            <div>
              故障记录：2025/03/19{" "}
              <span style={{ textDecoration: "underline", color: "red" }}>
                遮挡故障（待处理）
              </span>
            </div>
            <div>最近巡检时间：2025/03/19 10:15:0</div>
          </div>
        }
        isOpen={isMyModalOpen2}
        onClose={() => {
          setIsMyModalOpen2(false);
        }}
        isCenterBtn={"无人机核查"}
      />
    </div>
  );
}
