import styles from "./TemperaturePoint.less";
import { useModel } from "umi";
import { useEffect, useRef, useState, useCallback } from "react";

export default function TemperaturePoint() {
  const device = JSON.parse(localStorage.getItem("device") || "{}");
  const { DoCMD, temperatureMode } = useModel("cmdModel");
  const { cameraOsdData } = useModel("drcModel");
  const containerRef = useRef(null); 

  // 温度文本的位置和内容
  const [displayedTemperature, setDisplayedTemperature] = useState(null);
  const [displayPosition, setDisplayPosition] = useState({ x: 0, y: 0 });

  // 监听 cameraOsdData 变化，更新显示的点和温度
  useEffect(() => {
    const irPoint = cameraOsdData?.ir_thermometry?.ir_metering_point;
    if (irPoint && containerRef.current) {
      //当前的盒子宽高，也是父盒子的宽高
      const width = containerRef.current.offsetWidth;
      const height = containerRef.current.offsetHeight;

      const pointX = irPoint.x;
      const pointY = irPoint.y;
      const temperature = irPoint.temperature;

      // 计算显示位置
      const calculatedX = width * pointX;
      const calculatedY = height * pointY;

      // 更新显示文本和位置
      if (typeof temperature === 'number') { 
        setDisplayedTemperature(temperature.toFixed(2));
        setDisplayPosition({ x: calculatedX, y: calculatedY });
      } else {
        setDisplayedTemperature(null); 
      }
    } else {
        setDisplayedTemperature(null);
    }
  }, [cameraOsdData]); 

 
  const handleClick = useCallback((e) => {
    // 点测温模式1
    if (temperatureMode !== 1 || !containerRef.current) {
      return;
    }

    const { offsetX, offsetY } = e.nativeEvent; 
    const width = containerRef.current.offsetWidth;
    const height = containerRef.current.offsetHeight;

    const x = offsetX / width;
    const y = offsetY / height;

    const data = {
      payload_index: device.Camera2, 
      x: Math.abs(x), // 上云api规则在0-1之间
      y: Math.abs(y), 
    };

    DoCMD(device.SN, "ir_metering_point_set", data);

  }, [DoCMD, device, temperatureMode]); 

  return (
    <div ref={containerRef} className={styles.TemperaturePoint} onClick={handleClick}>
      {displayedTemperature && (
        <div
          className={styles.temperatureDisplay}
          style={{
            position: "absolute",
            left: displayPosition.x,
            top: displayPosition.y,
            transform: 'translate(-50%, -100%)', 
            whiteSpace: 'nowrap',
            color:displayedTemperature > 0 ? "red" : "green",
            textShadow: '0px 0px 10px #ffffff',
          }}
        >
          {displayedTemperature}
        </div>
      )}
    </div>
  );
}
