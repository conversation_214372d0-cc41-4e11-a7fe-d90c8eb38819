.fly-app-container {
  min-height: 100vh;
  width: 100%;
  color: #fff;
  background-color: none;
  padding: 40px 40px;
  display: flex;
  box-sizing: border-box;

  .content-wrapper {
    width: 100%;
    padding: 40px 20px;
    overflow: auto;
  }

  .app-list {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    justify-content: center;
    gap: 24px;

    .ant-col {
      min-width: 280px;
      max-width: 100%; // 防止卡片溢出
      margin-bottom: 0;
      width: 100% !important; // 强制宽度充满单元格

      @media (max-width: 1600px) {
        min-width: 320px;
      }

      @media (max-width: 576px) {
        min-width: 100%;
      }

      // &:hover {
      //   .app-title {
      //     background: rgba(11, 26, 54, 0.95);
      //     transform: translateY(-5px);
      //     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      //   }
      // }
    }

    .app-card {
      width: 100%;
      height: 100%;
      background-image: url('@/assets/border2.png');
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);

        .ant-card-cover {
          // border: 3px solid rgb(48, 172, 116) !important;
        }

        .ant-card-body {
          .app-title {
            color: rgb(48, 172, 116);
          }
        }
      }

      .app-image {
        width: 100%;
        height: auto;
        aspect-ratio: 16/9;
        object-fit: cover;
        max-height: 45vh;
      }

      .app-title {
        padding: 8px 12px;
        // border-radius: 4px;
        color: #fff;
        font-size: 1.1rem;
        text-align: start;
        white-space: nowrap;
        overflow: visible;
        text-overflow: clip;
        z-index: 1;
        transition: all 0.3s ease;
        position: relative; // 新增相对定位
        padding-right: 32px; // 给箭头留出空间

        @media (max-width: 1600px) {
          font-size: 0.9rem;
          padding: 6px 10px;
        }

        @media (max-width: 1200px) {
          white-space: normal;
          line-height: 1.4;
        }

        .title-arrow {
          margin-left: 8px;
          font-size: 0.9em;
          opacity: 0.8;
          position: absolute;
          right: 12px;
          top: 50%; // 新增
          transform: translateY(-50%); // 新增垂直居中
          line-height: 1; // 防止图标自带高度影响
        }
      }

      .ant-card-cover {
        margin-top: 0px;
        margin-inline-start: 0px;
        margin-inline-end: 0px;
        padding: 10px;
        // border: 3px solid rgba(87, 121, 193, 0.7) !important;
        border-radius: 7px;
        transition: all 0.3s ease;
        // box-sizing: border-box;
      }

      .ant-card-body {
        padding: 0 0 0 0;
      }
    }
  }
}

:global {
  .ant-card {
    background: transparent;

    .ant-card-body {
      padding: 0 !important;
      background: transparent;
    }

    .ant-card-hoverable:hover {
      border-color: rgba(24, 144, 255, 0.7);
    }
  }
}