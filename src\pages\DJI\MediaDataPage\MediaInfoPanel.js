import { Card, Row, Col, Descriptions, Button, Form, Select } from "antd";
import {
  LeftCircleTwoTone,
  RightCircleTwoTone,
  EyeOutlined,
} from "@ant-design/icons";
import { getDeviceName, getImgUrl, isEmpty } from "@/utils/utils";
import { getBodyH } from "@/utils/utils";
import LocationMap from "@/pages/Maps/LocationMap";
import { useEffect, useState } from "react";
import { getGuid, timeFormat22 } from "@/utils/helper";
import LastPageButton from "@/components/LastPageButton";
import AddButton from "@/components/AddButton";
import ImagePreview from "./ImagePreview";
import PopupModal from "@/components/PopupModal";
import Panorama from "@/components/Panorama";
import MyModal from "@/components/MyModal";


const MediaInfoPanel = ({ mList, record }) => {
  // useEffect(() => {
  //   console.log('@@@record', record);
  // }, [])
  if (isEmpty(mList)) return <div></div>;

  let [selectIndex, setSelectIndex] = useState(mList.indexOf(record));
  const h1 = getBodyH(160);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpen2, setIsModalOpen2] = useState(false);
  const [form] = Form.useForm();

  const ImageDownload = (imageUrl) => {
    fetch(imageUrl)
      .then((response) => {
        if (!response.ok) throw new Error("网络响应不正常");
        return response.blob(); // 将响应转换为 Blob
      })
      .then((blob) => {
        const url = URL.createObjectURL(blob); // 创建 URL
        const a = document.createElement("a");
        a.href = url;
        let everything = mList[selectIndex];
        let suffix = everything.FileName.slice(
          everything.FileName.indexOf("."),
          everything.FileName.length
        );
        a.download =
          getDeviceName(everything.SN) +
          "-" +
          everything.WayLineNM +
          "-" +
          "航点" +
          everything.HangDianIndex +
          suffix; // 下载时的文件名
        document.body.appendChild(a);
        a.click(); // 触发下载
        document.body.removeChild(a);
        URL.revokeObjectURL(url); // 释放 URL 对象
      })
      .catch((error) => console.error(error));
  };
  function formatFileSizeMB(fileSizeString) {
    // 从字符串中提取数值和单位
    const match = fileSizeString.match(/([\d.]+)([A-Za-z]+)/);

    if (!match) {
      return false;
    }

    const sizeValue = parseFloat(match[1]); // 文件大小数值
    const sizeUnit = match[2].toUpperCase(); // 文件大小单位（转换为大写）

    // 将文件大小转换为 MB
    let sizeInMB = sizeValue;
    switch (sizeUnit) {
      case "KB":
        sizeInMB = sizeValue / 1024;
        break;
      case "MB":
        break;
      case "GB":
        sizeInMB = sizeValue * 1024;
        break;
      default:
        return false;
    }

    return sizeInMB;
  }

  const dowLoadBtn = (
    <div>
      {formatFileSizeMB(mList[selectIndex].Size) > 15 && (
        <span style={{ marginRight: 30 }}>
          <Button
            type="primary"
            icon={<EyeOutlined style={{ color: "#1fffeb" }} />}
            onClick={() => {
              setIsModalOpen(true);
            }}
          >
            全景预览
          </Button>
        </span>
      )}
      {/* AI识别按钮 */}
      <span style={{ marginRight: 10 }}>
        <AddButton
          onClick={() => {
            setIsModalOpen2(true);
          }}
        >
          AI识别
        </AddButton>
      </span>
      <span>
        <AddButton
          onClick={() => {
            ImageDownload(getImgUrl(mList[selectIndex].ObjectName));
          }}
        >
          下载图片
        </AddButton>
      </span>
    </div>
  );
  
  // 假数据
  const modelOptions = [
    { label: '人车识别', value: '1' },
    { label: '耕地识别', value: '2' },
    { label: '安全帽识别', value: '3' },
  ];

  const onOk = () => {
    setIsModalOpen2(false);
  };
  const onCancel = () => {
    setIsModalOpen2(false);
  };

  const getItem = (label, val, dw, cols) => {
    return (
      <Descriptions.Item
        span={cols}
        key={getGuid()}
        labelStyle={{
          fontSize: 13.0,
          fontWeight: "bold",
        }}
        contentStyle={{
          fontSize: 13.0,
          fontWeight: "bold",
        }}
        label={label}
      >
        {val + dw}
      </Descriptions.Item>
    );
  };

  const getPanel = (list) => {
    return (
      <Descriptions
        style={{ fontSize: 14.0, color: "rgba(0,0,0,0.7)" }}
        title="详细信息"
        column={2}
      >
        {list}
      </Descriptions>
    );
  };

  const panel2 = (data) => {
    console.log(data);

    const getMediaType = (e) => {
      if (e == 2) return "广角照片";
      if (e == 3) return "红外照片";
      if (e == 4) return "变焦照片";
      return "可见光";
    };
    const list = [];
    list.push(getItem("照片名称", data.FileName, "", 2));
    list.push(getItem("航线名称", data.WayLineNM, "", 2));
    list.push(getItem("拍摄机场", getDeviceName(data.SN), "", 1));
    list.push(getItem("照片类型", getMediaType(data.MediaType), "", 1));
    list.push(getItem("照片大小", "", data.Size, 1));
    list.push(getItem("拍摄时间", "", timeFormat22(data.CreatedTime), 1));
    list.push(getItem("拍摄点位", "航点", data.HangDianIndex, 1));
    list.push(getItem("经度", "", data.Lng.toFixed(5), 1));
    list.push(getItem("纬度", "", data.Lat.toFixed(5), 1));
    list.push(
      getItem("飞行高度", "", data.RelativeAltitude.toFixed(1) + "米", 1)
    );
    list.push(getItem("偏航角", "", data.RelativeAltitude.toFixed(1) + "°", 1));
    list.push(
      getItem("飞行高度", "", data.RelativeAltitude.toFixed(1) + "°", 1)
    );
    return getPanel(list);
  };

  let timer = null;
  function selectImgHandle(direction) {
    //节流点击
    if (timer !== null) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      imgHandleOff(direction);
    }, 200);
  }

  function imgHandleOff(direction) {
    if (direction === "left") {
      if (selectIndex <= 0) {
        return;
      }
      setSelectIndex((prevIndex) => prevIndex - 1);
    }
    if (direction === "right") {
      if (selectIndex >= mList.length - 1) {
        return;
      }
      setSelectIndex((prevIndex) => prevIndex + 1);
    }
  }

  function updateSelectIndex(e) {
    //同步更新图片索引
    setSelectIndex(e);
  }
  useEffect(() => {
    const handleKeyDown = (event) => {
      event.preventDefault();
      event.stopPropagation();

      let rightKey = event.key === "ArrowRight" || event.key === "d";
      let leftKey = event.key === "ArrowLeft" || event.key === "a";

      if (leftKey) {
        if (selectIndex <= 0) {
          return;
        }
        setSelectIndex((prevIndex) => prevIndex - 1);
      }
      if (rightKey) {
        if (selectIndex >= mList.length - 1) {
          return;
        }
        setSelectIndex((prevIndex) => prevIndex + 1);
      }
    };
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectIndex]);

  const fhBtn = <LastPageButton title="照片详情" />;
  return (
    <>
      <PopupModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <Panorama picture={mList[selectIndex].ObjectName} />
      </PopupModal>
      <Card
        title={fhBtn}
        style={{ width: "100%", height: getBodyH(90) }}
        extra={dowLoadBtn}
      >
        <Row>
          <Col span={17}>
            <div
              style={{
                textAlign: "center",
                display: "flex",
                justifyContent: "space-evenly",
                alignItems: "center",
              }}
            >
              <LeftCircleTwoTone
                onClick={() => selectImgHandle("left")}
                twoToneColor={selectIndex <= 0 ? "#ccc" : ""}
                style={{ fontSize: 40 }}
              />
              <ImagePreview
                mList={mList}
                selectIndex={selectIndex}
                updateSelectIndex={updateSelectIndex}
              ></ImagePreview>
              <RightCircleTwoTone
                onClick={() => selectImgHandle("right")}
                twoToneColor={selectIndex >= mList.length - 1 ? "#ccc" : ""}
                style={{ fontSize: 40 }}
              />
            </div>
          </Col>
          <Col span={7}>
            <div
              style={{
                width: "100%",
                marginLeft: 20.0,
                height: h1 - 220,
                marginRight: 10.0,
              }}
            >
              {panel2(mList[selectIndex])}
              <div style={{ marginTop: 10.0, marginRight: 10.0 }}>
                <LocationMap
                  lat={mList[selectIndex]?.Lat}
                  lng={mList[selectIndex]?.Lng}
                  title={mList[selectIndex]?.FileName}
                  h1={214}
                />
              </div>
            </div>
          </Col>
        </Row>
      </Card>
      <MyModal
        visible={isModalOpen2}
        onOk={onOk}
        onCancel={onCancel}
        title={'目标识别'}
        form={form}
      >
        <Form.Item
          label="选择识别模型"
          name="model"
          rules={[{ required: true, message: '请选择识别模型' }]}
        >
          <Select
            placeholder="请选择AI模型"
            options={modelOptions}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </MyModal>
    </>
  );
};

export default MediaInfoPanel;
