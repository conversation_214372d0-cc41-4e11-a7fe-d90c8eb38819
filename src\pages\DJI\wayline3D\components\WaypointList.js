import React from 'react';
import { Card } from 'antd';
import { DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { PointImg } from '@/utils/cesium_help';
import LastPageButton from "@/components/LastPageButton";


function WaypointList({ wayline, seleteIndex, SwitchWaypoint, deleteWaypoint, SwitchWaypointAction, editWaypointIndex }) {
    function actionActuatorFuncToString(actionActuatorFunc) {
        if (actionActuatorFunc === 'takePhoto') {
            return '拍'
        } else if (actionActuatorFunc === 'startRecord') {
            return '开'
        } else if (actionActuatorFunc === 'stopRecord') {
            return '结'
        } else if (actionActuatorFunc === 'zoom') {
            return '变'
        } else if (actionActuatorFunc === 'rotateYaw') {
            return '偏'
        } else if (actionActuatorFunc === 'gimbalRotate') {
            return '转'
        } else if (actionActuatorFunc === 'hover') {
            return '悬'
        } else if (actionActuatorFunc === 'panoShot') {
            return '全'
        }
    }
    return <Card title={<LastPageButton title="航点列表" />}>
        <div style={{ width: '100%', overflowY: 'auto',position: 'relative', }}>
            {wayline.Folder.PlacemarkList.map((item, index) => {
                return <div key={index} style={{ marginBottom: 8.0, background: item.index === seleteIndex ? 'rgba(95, 147, 204,0.5)' : '', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                    <div style={{ cursor: 'pointer', width: 35, }} onClick={() => { SwitchWaypoint(index)}}><img src={PointImg(index + 1)}></img></div>
                    <div style={{ cursor: 'pointer', marginLeft: 10, height: 26, width: 'calc(100% - 75px)', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }} >{
                        item.actionGroup && item.actionGroup.actionList.map((ite, inde) => {
                            return <div key={inde} onClick={() => SwitchWaypointAction(index, inde)} style={{ width: '20px', height: '20px', backgroundColor: '#29e88b', marginRight: 5, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>{actionActuatorFuncToString(ite.actionActuatorFunc)}</div>
                        })
                    }</div>
                    <div style={{ cursor: 'pointer', height: 20, width: 20, marginLeft: 5, display: 'flex', justifyContent: 'center', alignItems: 'center', }} onClick={() => { editWaypointIndex(index) }}>
                        <SettingOutlined />
                    </div>
                    <div style={{ cursor: 'pointer', height: 20, width: 20, marginLeft: 5, display: 'flex', justifyContent: 'center', alignItems: 'center', }} onClick={() => { deleteWaypoint(index) }}><DeleteOutlined /></div>
                </div>
            })}
        </div>
    </Card>
}
export default WaypointList;