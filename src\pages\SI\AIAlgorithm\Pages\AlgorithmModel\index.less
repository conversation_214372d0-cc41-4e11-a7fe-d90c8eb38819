.ai-algorithm-container {
  padding: 24px;
  height: 100%;
  width: 100%;
  color: #fff;
  background-color: none;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;

    .page-title {
      color: #fff;
      font-size: 22px;
      margin: 0;
      font-weight: bold;
    }

    .new-button {
      background: #1890ff;
      border: none;
      height: 36px;
      padding: 0 16px;
      border-radius: 4px;
      font-size: 14px;

      &:hover {
        background: #40a9ff;
      }
    }
  }

  .algorithm-list {
    padding: 10px 26px;
    .algorithm-card-col {
      display: flex;
      justify-content: center;
    }
    .algorithm-card {
      margin: 0 16px;
      padding: 16px;
      width: 322px;
      height: 366px;
      border-radius: 4px;
      border: 1px solid rgba(87, 121, 193, 0.7) !important;
      background: rgba(11, 26, 54, 0.9);
      overflow: hidden;
      transition: all 0.3s ease;
      margin-bottom: 16px;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border-color: rgba(87, 121, 193, 1);
      }
      .ant-card-body{
        padding: 5px 0;
      }
      :global(.ant-card-body) {
        padding: 0 !important;
        color: #fff;
        background: transparent;
      }

      :global(.ant-card-cover) {
        margin: 0;
      }

      .image-container {
        position: relative;
        height: 180px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          filter: brightness(0.95);
        }
      }
      .algorithm-name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        font-weight: bold;
        font-size: 15px;

        .algorithm-tag {
          margin-left: 8px;
          padding: 0 5px;
          height: 18px;
          line-height: 18px;
          font-size: 12px;
          border-radius: 2px;
          font-weight: normal;
        }
      }
      .accuracy-container {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          margin-right: 4px;
        }
      }
      .accuracy-badge {
        background: rgba(0, 168, 150, 0.3);
        border: none;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        z-index: 2;
        width: 32px;
        height: 32px;
      }

      .algorithm-description {
        margin-top: 10px;
        margin-bottom: 6px;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .algorithm-stats {
        margin-top: 8px;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);

        .stats-item {
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .update-time {
        margin-top: 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

// 修改antd卡片的默认样式
:global {
  .ant-card {
    background: transparent;

    .ant-card-cover {
      margin: 0;
    }

    .ant-card-body {
      padding: 0 !important;
      background: transparent;
    }

    .ant-tag-orange {
      color: #fff;
      background: #faa713;
      border-color: #faa713;
    }

    .ant-tag-blue {
      color: #fff;
      background: #1890ff;
      border-color: #1890ff;
    }

    .ant-card-hoverable:hover {
      border-color: rgba(24, 144, 255, 0.7);
    }
  }
}
