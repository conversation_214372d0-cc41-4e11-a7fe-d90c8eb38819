@back-color: #1e63a2;
@back-color2: #417aaf;
@back-color-none: none;
@font-color: #fff;
@font-color-active: #1e63a2;
@line-color: #0ce7f3;


#Firedangerwarning {
    max-width: 450px;
    max-height: 300px;
    position: fixed;
    bottom: 0px;
    right: 0;
    z-index: 1010;
    color: #fff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden!important;

    .ant-card .ant-card-extra {
        color: @font-color;
    }

    .ant-card {
        background: linear-gradient(135deg, #1e63a2, rgba(10, 28, 71, 0.6));
        border-image: linear-gradient(133deg, #0cebf7, #18416d 70%, #0071d2) 1 1;

        .ant-card-head {
            color: #fff;

        }
    }

    .ant-table-wrapper .ant-table-thead>tr>th,
    .ant-table-wrapper .ant-table-thead>tr>td {
        //table表头
        background: @back-color;
        color: @font-color;
    }

    .ant-table-wrapper .ant-table-tbody>tr>td {
        //table表头下内容区列
        background: @back-color2;
        color: @font-color;
    }

    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container {
        //table最上部与最左边的边框颜色
        border-color: @line-color;
    }


    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>th,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,
    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>td {
        border-color: @line-color;
        //table里面的每个小框的颜色
    }

    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container {
        //滚动条上部的border
        // border-top: none;
    }

    .ant-table-wrapper .ant-table-tbody .ant-table-row>.ant-table-cell-row-hover {
        //table里面的鼠标悬停的颜色
        background: @font-color-active;
    }

    .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected>.ant-table-cell {
        //table里面的选中的颜色
        background: @font-color-active;
    }


    .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td {
        height: 50px;
        white-space: nowrap; // 禁止换行 多余内容用省略号显示
        overflow: hidden;
        text-overflow: ellipsis;

        // 鼠标悬停的时候 允许换行显示
        &:hover {
            overflow: visible;
            white-space: normal;
        }
    }

    // 隐藏滚动条
    .ant-table-body {

        // Chrome/Safari
        &::-webkit-scrollbar {
            display: none;
        }

        // Firefox
        scrollbar-width: none;
        // IE/Edge
        -ms-overflow-style: none;
    }

    .ant-table-cell.ant-table-cell-scrollbar {
        display: none;
    }

    .ant-table-wrapper .ant-table-tbody>tr.ant-table-placeholder {
        //表格的空白内容
        background: @back-color-none;
    }

    // 尾部导航选择页面按钮样式
    .ant-pagination {
        .ant-pagination-item {
            // margin: 0 1px 0 1px;
            background: none;
            // color:@font-color;
            transition: all 0.3s ease;

            &:hover {
                transform: scale(1.2);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            a {
                color: @font-color;
            }
        }

        .ant-pagination-item-active {
            background: @back-color2 !important;
            border: 1.5px solid @font-color !important;

            a {
                color: #fff !important;
                font-weight: bold;
            }
        }
    }

    .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
    .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
        //尾部导航省略号样式
        color: @font-color;
    }

    .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper {
        //尾部导航快速跳转输入框样式
        // border:1px solid @line-color;
        color: @font-color;
    }

    .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper input {
        //尾部导航快速跳转输入框样式
        border-color: @line-color;
        background: none;
        color: @font-color;
    }

    // 尾部导航箭头样式
    .ant-pagination-prev,
    .ant-pagination-next {
        .ant-pagination-item-link {
            border: 0.5px solid @line-color !important;
            color: @font-color;
        }

        &:hover {
            .ant-pagination-item-link {
                transform: scale(1.2);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            }
        }
    }
}


.warning-box {
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    cursor: pointer;
    text-overflow: ellipsis;
}

.warning-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.warning-item div {
    margin-right: 10px;
}

.warning-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.flyToPoint {
    display: inline-block;
    cursor: pointer;
    padding: 2px;
    background: #3590f9;
    border-radius: 5px;
    font-size: 12px;
    transition: all 0.3s ease;
    color: #fff;
}

.flyToPoint:hover {
    background: #2a77cf;
}