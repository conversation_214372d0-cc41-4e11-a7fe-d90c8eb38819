import { useState } from "react";
import { isEmpty } from "@/utils/utils";
import LeafletMap from "./Maps/LeafletMap";
import LeftPage2 from "@/pages/GT/DZZH/Pages/LeftPage/LeftPage2";
import RightPage2 from "@/pages/GT/DZZH/Pages/RightPage/RightPage2";
import LeftBox from "@/pages/GT/components/LeftBox";
import RightBox from "@/pages/GT/components/RightBox";
import { useModel,history } from "umi";
import { Modal } from "antd";
import { axiosApi } from "@/services/general";
import './index.css';
import DevicePage from '@/pages/DJI/DevicePage';
import MyButton from "@/pages/GT/components/MyButton/MyButton";
const HomePage = () => {

  const { setPage,nearWRJlist,DZZHPoint,isModalOpen, setIsModalOpen } = useModel('pageModel');

  const closeMyModal = () => {
    setIsModalOpen(false);
  };

  // 处理设备点击
  const handelDeviceClick = async (device) => {
    try{
      console.log(device);
      // 发送请求 自动生成航线 返回WayLineId
      const url = `/api/v1/Survey/SurveyTaskCreate?tablename=DZZH&keyId=${DZZHPoint.灾害体编号}&TaskDesc=${DZZHPoint.灾害体名称}巡检&TaskType=测点&lat=${DZZHPoint.geometry[1]}&lng=${DZZHPoint.geometry[0]}&sn=${device.SN}`;
      const WayLineId = await axiosApi(url, 'GET', null);

      // 将WayLineId 调用航线飞行方法 直接航线飞行
      const flyUrl = `/api/v1/WayLine/Fly?fID=${WayLineId.data}`;
      await axiosApi(flyUrl, 'GET', null);

      // 跳转到device页面
      const getDeviceUrl = `/api/v1/Device/GetBySN?sn=${device.SN}`;
      const d1 = await axiosApi(getDeviceUrl, 'GET', null);
      console.log("设备信息", d1);
      if (isEmpty(d1)) {
        return;
      }
      localStorage.setItem("device", JSON.stringify(d1));
      // 将航线ID放到localStorage中
      localStorage.setItem("WanLineId", WayLineId.data);
      // 跳转到device页面
      history.push("/gt/WRJ");
      // 显示返回按钮
      localStorage.setItem('showBackButton', 'true');
      setPage(<DevicePage device={d1} />);
    } catch (error) {
        console.log("error", error);
      }
  }
  return (
    <div style={{ position: "relative" }}>
      <div style={{ position: "absolute", zIndex: 1, width: "100vw" }}>
        <LeafletMap />
      </div>
      <LeftBox child={<LeftPage2 />}/>
      <RightBox child={<RightPage2/>}/>


      <Modal
        title="附近可用无人机"
        open={isModalOpen}
        onCancel={closeMyModal}
        footer={null}
        width={600}
        styles={{
          body: {
            maxHeight: "600px",
            overflowY: "auto",
            padding: "16px 24px"
          },
        }}
      >
        {/* {console.log("刚点击的地质灾害点信息",DZZHPoint)} */}
        <div style={{ padding: '16px 0' }}>
          {nearWRJlist.map(device => (
            <div 
              key={device.ID}
              className="modal-device-item"
            >
              <div className="device-content">
                <img 
                  src={require("@/assets/icons/device.png")} 
                  alt="device" 
                  className="device-image"
                />
                <div className="device-info">
                  <div style={{ marginBottom: 8 }}>
                    <span>设备名称:</span>
                    <span>{device.DName}</span>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <span>当前位置:</span>
                    <span>{device.Location}</span>
                  </div>
                  <div>
                    <span>距离(km):</span>
                    <span>{device.Remark}</span>
                  </div>
                </div>
              </div>
              <MyButton 
                type="primary"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handelDeviceClick(device);
                }}
                style={{ marginTop: 8 }}
              >
                确认起飞
              </MyButton>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default HomePage;
