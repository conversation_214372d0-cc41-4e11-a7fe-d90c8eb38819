import React from 'react';
import { Card } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { PointImg } from '@/utils/cesium_help';
import { getBodyH } from '@/utils/utils';

function WaypointList({wayline, seleteIndex, seleteIndexChange, delete_function}) {
    // PType转字符串
    function PTypeToString(PType) {
        if (PType === '0') {
            return '姿态调整'
        } else if (PType === '1') {
            return '开始录像'
        } else if (PType === '2') {
            return '结束录像'
        } else if (PType === '3') {
            return '拍照'
        } else if (PType === '4') {
            return '悬停'
        } else if (PType === '6') {
            return '全景拍照'
        } else if (PType === '' || PType === 'Waypoint') {
            return '普通航点'
        }
    }
    return <Card title='测点列表'>
        <div style={{ width: '100%', height: getBodyH(160), overflowY: 'auto' }}>{wayline.PList.map((item, index) => {
            return <div key={index} style={{ marginBottom: 8.0, background: item.Index === seleteIndex ? 'rgba(95, 147, 204,0.5)' : '', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <div style={{ cursor: 'pointer' }} onClick={() => seleteIndexChange(index)}><img src={PointImg(index + 1)}></img></div>
                <div style={{ cursor: 'pointer', marginLeft: 10, height: 26, width: 165, display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }} onClick={() => seleteIndexChange(index)}>{PTypeToString(item.PType)}</div>
                <div style={{ cursor: 'pointer', height: 20, width: 20, marginLeft: 5, display: 'flex', justifyContent: 'center', alignItems: 'center', }} onClick={() => { delete_function(index) }}><DeleteOutlined /></div>
            </div>
        })}</div>
    </Card>
}
export default WaypointList;