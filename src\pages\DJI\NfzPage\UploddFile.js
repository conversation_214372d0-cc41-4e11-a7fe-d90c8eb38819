import React, { useState } from "react";
import {
  Button,
  Descriptions,
  Input,
  message,
  Modal,
  Select,
  Tooltip,
  Upload,
} from "antd";
import { InboxOutlined } from "@ant-design/icons";
import { axiosApi } from "@/services/general";
import { downloadFile2, getImgUrl } from "@/utils/utils";

const { Dragger } = Upload;

export default function UploddFile({ device, refrush }) {
  const typeList = [
    { value: "nfz", label: "禁飞区" },
    { value: "dfence", label: "电子围栏" },
  ];
  const token = localStorage.getItem("token");
  const [canSee, setCanSee] = useState(false);
  const [MType, setMType] = useState(typeList[0].value);
  const [MTitle, setMTitle] = useState(null);
  const [MContent, setMContent] = useState(null);
  const [Remark, setRemark] = useState(null);
  const [uploadFile, setUploadFile] = useState(null); // 保存上传的文件
  const [FileName, setFileName] = useState(null);
  const [StyleFile, setStyleFile] = useState(null);

  const upProps = {
    name: "file",
    multiple: false, // 禁止多文件上传
    headers: {
      authorization: "authorization-text",
      auth: token,
    },
    beforeUpload: (file) => {
      if (!file.name.includes(".shp")) {
        return message.warning("请上传shp文件！");
      }
      setUploadFile(file); // 保存上传的文件
      return false; // 阻止自动上传
    },
    // onRemove: () => {
    //   setUploadFile(null); // 移除上传的文件
    // },
  };

  const UploadFileSave = async () => {
    if (!uploadFile) {
      message.warning("请选择要上传的文件！");
      return;
    }
    if (!device.SN) {
      message.warning("设备SN为空,请先选择设备！");
    }
    const formData = new FormData();
    formData.append("File", uploadFile);
    formData.append("OrgCode", device.SN);
    formData.append("MType", MType);
    formData.append("MTitle", MTitle);
    formData.append("MContent", MContent);
    formData.append("Remark", Remark);

    try {
      const res = await axiosApi(
        "/api/v1/FlyArea/AddByFile",
        "POST",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (res.code === 1) {
        message.success("上传成功");
        handleClearState();
        refrush();
      } else {
        message.warning(res.msg);
      }
    } catch (error) {
      message.error("保存失败，请重试");
    }
  };

  const handleClearState = () => {
    setMType(typeList[0].value);
    setMTitle(null);
    setMContent(null);
    setRemark(null);
    setUploadFile(null);
    setStyleFile(null);
    setCanSee(false);
  };

  return (
    <div>
      <Button
        className="QieHaun_Button"
        onClick={() => {
          setCanSee(true);
        }}
      >
        上传
      </Button>
      <Modal
        title={null}
        onOk={UploadFileSave}
        open={canSee}
        onCancel={() => {
          handleClearState();
        }}
        style={{ minWidth: 900, overflowX: "auto" }}
      >
        <div>
          <Descriptions title="上传文件" column={2} colon={false} bordered>
            <Descriptions.Item label="禁飞区类型" span={24}>
              <Select
                allowClear
                onClear={() => setMType(null)}
                placeholder={"选择禁飞区类型"}
                defaultValue={typeList[0]?.value}
                onSelect={(e) => {
                  setMType(e);
                }}
                style={{ width: 200 }}
              >
                {typeList.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Descriptions.Item>
            <Descriptions.Item label="测试区域" span={24}>
              <Input
                defaultValue={MTitle}
                onChange={(e) => {
                  setMTitle(e.target.value);
                }}
                allowClear
              />
            </Descriptions.Item>
            <Descriptions.Item label="测试说明" span={24}>
              <Input
                defaultValue={MContent}
                onChange={(e) => {
                  setMContent(e.target.value);
                }}
                allowClear
              />
            </Descriptions.Item>
            <Descriptions.Item label="测试备注" span={24}>
              <Input
                defaultValue={Remark}
                onChange={(e) => {
                  setRemark(e.target.value);
                }}
                allowClear
              />
            </Descriptions.Item>
            <Descriptions.Item label={"上传文件"} span={24}>
              <Dragger {...upProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text" style={{ fontSize: 12 }}>
                  {uploadFile
                    ? `已选择文件：${uploadFile.name}`
                    : "点击或拖拽文件到此处上传.shp格式的文件"}
                </p>
              </Dragger>
              {StyleFile && (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    marginTop: 10,
                  }}
                >
                  <Tooltip title={StyleFile}>
                    <Button type="dashed" size="small">
                      当前文件：
                      {StyleFile.slice(
                        StyleFile.indexOf("/") + 1,
                        StyleFile.length
                      )}
                    </Button>
                  </Tooltip>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      downloadFile2(getImgUrl(StyleFile));
                    }}
                  >
                    下载
                  </Button>
                </div>
              )}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Modal>
    </div>
  );
}
