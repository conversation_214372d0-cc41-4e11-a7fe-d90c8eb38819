import React, { useState, useEffect, useRef } from "react";
import "leaflet/dist/leaflet.css";
import { isEmpty, getBodyH } from "@/utils/utils";
import { Get2 } from "@/services/general";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import L from "leaflet";
import "leaflet-side-by-side";
import { Row, Col, Button, Radio, List, message, Card } from "antd";
import VirtualList from "rc-virtual-list";

const JuanLianMap = () => {
  const mapRef = useRef({});
  const [isLeft, setIsLeft] = useState("left");
  const [layer1, setLayer1] = useState({});
  const [layer2, setLayer2] = useState({});
  const layers = useRef({});
  const [Lat, setLat] = useState();
  const [Lng, setLng] = useState();
  const [mapList, setMapList] = useState([]);
  const [ifLoad, setIfLoad] = useState(true);
  const ContainerHeight = getBodyH(216);

  const getMList = async () => {
    let pst = await Get2("/api/v1/MapData/GetAllList");
    if (pst && pst.length > 0) {
      setIfLoad(false);
      setMapList(pst);
    }
  };

  useEffect(() => {
    getMList();
  }, []);

  useEffect(() => {
    mapRef.current = L.map("mapDiv", { attributionControl: false }).setView(
      [Lat ? Lat : 30.06346956642108, Lng ? Lng : 105.31706203454723],
      16
    );

    mapRef.current.on("dblclick", (e) => {
      L.popup()
        .setLatLng(e.latlng)
        .setContent(`此地的经纬度:${e.latlng.lat + "," + e.latlng.lng}`)
        .openOn(mapRef.current);
    });
  }, []);

  useEffect(() => {
    if (!isEmpty(layer1) && !isEmpty(layer2)) {
      updateMapLayers();
    }
  }, [layer1, layer2]);

  const updateMapLayers = () => {
    if (!isEmpty(layers.current)) {
      mapRef.current.removeLayer(layers.current.left);
      mapRef.current.removeLayer(layers.current.right);
      mapRef.current.removeControl(layers.current.control);
    }

    layer1.layer.addTo(mapRef.current);
    layer2.layer.addTo(mapRef.current);
    layers.current.left = layer1.layer;
    layers.current.right = layer2.layer;
    layers.current.control = L.control.sideBySide(layer1.layer, layer2.layer);
    layers.current.control.addTo(mapRef.current);
  };

  const onMapClick = (item) => {
    let center = L.latLng(item.Lat, item.Lng);
    mapRef.current.setView(center);
    if (isLeft === "left") {
      setLayer1(item);
      if (isEmpty(layer2)) {
        message.info("请选择右侧地图");
        return;
      }
    } else {
      setLayer2(item);
      if (isEmpty(layer1)) {
        message.info("请选择左侧地图");
        return;
      }
    }
  };

  const getType = (x1) => {
    if (isLeft === "left" && layer1.name === x1) {
      return "primary";
    } else if (isLeft === "right" && layer2.name === x1) {
      return "primary";
    }
    return "text";
  };

  const data = mapList
    .filter((e) => e.MapType === 1 || e.MapType === 0)
    .map((e) => ({
      name: e.MapName,
      layer: L.tileLayer(e.Url, {
        tms: e.MapType === 0 ? false : true,
        maxZoom: e.MaxZoom,
        minZoom: e.MinZoom,
        opacity: 1.0,
        attribution: "",
      }),
      Lat: e.Lat,
      Lng: e.Lng,
    }));

  useEffect(() => {
    if (data.length > 0) {
      // 组件加载后自动选择第一个地图
      const firstMap = data[0];
      if (isLeft === "left") {
        setLayer1(firstMap);
        message.info(
          `已选择左侧地图：${firstMap.name}\u00A0,\u00A0\u00A0请再选择右侧地图`
        );
      } else {
        setLayer2(firstMap);
        message.info(`已选择右侧地图：${firstMap.name}`);
      }
      onMapClick(firstMap);
    }
  }, [mapList]);

  const getList = () => {
    return (
      <List>
        <VirtualList
          data={data}
          height={ContainerHeight}
          itemHeight={47}
          itemKey="i"
        >
          {(item) => (
            <List.Item key={item.name}>
              <Button
                type={getType(item.name)}
                style={{ width: "100%",borderColor:'#ccc' }}
                onClick={() => onMapClick(item)}
              >
                {item.name}
              </Button>
            </List.Item>
          )}
        </VirtualList>
      </List>
    );
  };

  return (
    <div>
      <Row>
        <Col span={4}>
          <Card title="正射对比">
            <Radio.Group
              value={isLeft}
              buttonStyle="solid"
              onChange={(e) => setIsLeft(e.target.value)}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginBottom: 20,
              }}
            >
              <Radio.Button value="left">左侧地图</Radio.Button>
              <Radio.Button value="right">右侧地图</Radio.Button>
            </Radio.Group>
            {getList()}
          </Card>
        </Col>
        <Col span={20}>
          <div
            id="mapDiv"
            style={{ width: "100%", height: "calc(100vh - 47px)" }}
          ></div>
        </Col>
      </Row>
    </div>
  );
};

export default JuanLianMap;
