import { Get2 } from "@/services/general";
import { List, Card, Badge, Progress } from "antd";
import { useState } from "react";
import { useEffect } from "react";
import { useModel } from "umi";
import ModelInfoPage from "./detail";
import LastPageButton from "@/components/LastPageButton";
import dImg from "@/assets/icons/device.png";
import { isEmpty } from "@/utils/utils";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import commonStyles from "@/pages/common.less"
const { Meta } = Card;
const ModeCodeJson = {
  0: "空闲中",
  1: "现场调试",
  2: "远程调试",
  3: "固件升级中",
  4: "作业中",
};

const DeviceListPage = () => {
  const [MList, setModelList] = useState([]);
  const [dL, setDL] = useState([]); //在线列表
  const [ifLoad, setIfload] = useState(true);
  const [dLNull, setDLNull] = useState([]); //离线列表

  const { setPage, setModal, open, setOpen } = useModel("pageModel");

  const getOsdData2 = async (e) => {
    const xx = await Get2("/api/v1/Device/GetJcOsdData1?sn=" + e.SN);
    return xx;
  };

  const getDList = async () => {
    const xx = await Get2("/api/open/Device/GetAllList");
    // for (let i = 0; i < xx.length; i++) {
    //     const dd = await getOsdData2(xx[i]);
    //     xx[i].OsdData = dd;
    // }
    let DeviceOnline = [];
    let DeviceNotOnline = [];
    xx.map((e) => {
      if (e.OsdData) {
        DeviceOnline.push(e);
      } else {
        DeviceNotOnline.push(e);
      }
    });
    setDL(DeviceOnline);
    setDLNull(DeviceNotOnline);
    setIfload(false);
  };

  useEffect(() => {
    getDList();
  }, []);

  const refrush = async () => {
    setOpen(false);
    getDList();
  };

  const getColor = (d1) => {
    if (isEmpty(d1.OsdData)) return "red";
    if (d1.OsdData.mode_code == 0) return "green";
    return "orange";
  };

  const getDCColor = (dc) => {
    if (dc < 30) return "red";
    if (dc < 60) return "orange";
    return "green";
  };

  const getZT = (d1) => {
    if (isEmpty(d1.OsdData)) return "离线";
    return ModeCodeJson[d1.OsdData.mode_code];
  };

  const onClickD = (item) => {
    localStorage.setItem("device", JSON.stringify(item));
    setPage(<ModelInfoPage device2={item}></ModelInfoPage>);
  };

  const dStatusDiv = (d1) => {
    return (
      <div
        style={{ height: 36.0, width: "100%", fontSize: 14.0, color: "white" }}
      >
        <span style={{ width: 20.0 }}>
          <Badge color={getColor(d1)} status={"success"} text={""} />
        </span>
        <span style={{ width: 30.0, marginLeft: 4.0, }}>
          {getZT(d1)}{" "}
        </span>
        {isEmpty(d1.OsdData) ? null : (
          <span style={{ marginLeft: 16.0 }}>
            <Progress
              strokeColor={getDCColor(
                d1.OsdData.drone_charge_state?.capacity_percent
              )}
              steps={6}
              percent={d1.OsdData.drone_charge_state?.capacity_percent}
              format={(percent) => (
                <span>{percent + "%"}</span>
              )}
              size="small"
            />
          </span>
        )}
      </div>
    );
  };

  if (ifLoad) return <LoadPanel />;

  return (
    <Card title={<LastPageButton title="设备列表" />}>
      <div className={commonStyles.my_scroll_y}>
        <List
          header={<div>在线设备</div>}
          grid={{ gutter: 16, column: 4 }}
          dataSource={dL}
          renderItem={(item) => (
            <List.Item>
              <Card
                style={{ height: 120.0 }}
                hoverable={true}
                onClick={() => onClickD(item)}
              >
                {/* {item.OrgName} */}
                <Meta
                  avatar={<img src={dImg} height={48} width={48} />}
                  title={item.DName}
                  description={dStatusDiv(item)}
                />
              </Card>
            </List.Item>
          )}
        />
        <List
          header={<div>离线设备</div>}
          grid={{ gutter: 16, column: 4 }}
          dataSource={dLNull}
          renderItem={(item) => (
            <List.Item>
              <Card
                style={{ height: 120.0 }}
                hoverable={true}
                onClick={() => onClickD(item)}
              >
                {/* {item.OrgName} */}
                <Meta
                  avatar={<img src={dImg} height={48} width={48} />}
                  title={item.DName}
                  description={dStatusDiv(item)}
                />
              </Card>
            </List.Item>
          )}
        />
      </div>
    </Card>
  );
};

export default DeviceListPage;
