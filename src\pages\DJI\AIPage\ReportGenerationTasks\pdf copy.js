
import { useState } from 'react';
import { Viewer,Worker} from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import zh_CN from '@react-pdf-viewer/locales/lib/zh_CN.json';

export default function pdf(props)  {

const defaultLayoutPluginInstance = defaultLayoutPlugin();
const [fileUrl,setFileUrl] = useState(props?.jobUrl)
  return (
    <>
    <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js"></Worker>
    <div
    className="rpv-core__viewer"
    style={{
        display: 'flex',
        flexDirection: 'column',
        height: '750px',
        width:'43vw'
    }}
    >
    <Viewer 
    localization={zh_CN}
    plugins={[defaultLayoutPluginInstance]}
    fileUrl="https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/111/CDZS/%E5%85%89%E4%BC%8F%E7%94%B5%E7%AB%99%E6%96%BD%E5%B7%A5%E8%BF%9B%E5%BA%A6%20AI%20%E8%AF%86%E5%88%AB%E6%8A%A5%E5%91%8A_20240923.pdf" />
    </div>
    </>
  );
}
