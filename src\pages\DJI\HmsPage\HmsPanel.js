import { isEmpty } from "@/utils/utils";

import { Card, Space} from 'antd';
//import BaseMap from "@/pages/Maps/BaseMap";
import {GetWebSocketUrl} from '@/utils/websocket';
import useWebSocket from 'react-use-websocket';
import {HmsJson} from './hms.js';
import DeviceJson from './device.json';
import { getBaseColor } from "@/utils/helper";
import { useModel } from "umi";

import styles from './HmsPanel.less';
import { useEffect, useState } from "react";
import { QRCode } from "antd/lib/index.js";

const ModeCodeJson={"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"}
const CoverStateJson={"0":"关闭","1":"打开","2":"半开","3":"舱盖状态异常"}

const ModeCodeReasonJson={"0":"","1":"电池电量不足（返航、降落）","2":"电池电压不足（返航、降落）","3":"电压严重过低（返航、降落）","4":"遥控器按键请求（起飞、返航、降落）","5":"App 请求（起飞、返航、降落）","6":"遥控信号丢失（返航、降落、悬停）","7":"导航、SDK 等外部设备触发（起飞、返航、降落）","8":"进入机场限飞区（降落）","9":"虽然触发了返航但是因为距离 Home 点距离太近（降落）","10":"虽然触发了返航但是因为距离 Home 点距离太远（降落）","11":"执行航点任务时请求（起飞）","12":"返航阶段到达 Home 点上方后请求（降落）","13":"飞行器高度下降，距地面 0.7m（二段降落限低）时，继续下降导致（降落）","14":"App、SDK 等设备强制突破限低保护进行（降落）","15":"因为周围有航班经过而请求（返航、降落）","16":"因为高度控制失败请求（返航、降落）","17":"智能低电量返航后进入（降落）","18":"AP 控制飞行模式（手动飞行）","19":"硬件异常（返航、降落）","20":"防触地保护结束（降落）","21":"返航取消 (悬停)","22":"返航时遇到障碍物（降落）","23":"机场场景下大风触发（返航）"}
const HmsPanel =()=>{
    const {hms } = useModel('eventModel');
    const [mr,setMR]=useState('')
    const {modeReason}=useModel('stateModel');

    const getModeR=(i)=>{
        const xx=ModeCodeReasonJson[i];
        if(isEmpty(xx)) return ''
        return xx;
    }

    useEffect(()=>{
      setMR(getModeR(modeReason.current));
    },[modeReason.current])

   // const hms={"list":[{"args":{"component_index":0,"sensor_index":6},"code":"0x1A420BC6","device_type":"0-91-0","imminent":0,"in_the_sky":1,"level":0,"module":3}]}
    const getHmsStr=(e)=>{
     // 
      if(isEmpty(e["device_type"])){
        return "";
      }

      let key="dock_tip_"+e.code;
      if (!isEmpty( HmsJson[key])){
        return "机场 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

      key="fpv_tip_"+e.code;
      if (!isEmpty( HmsJson[key])){
        return "无人机 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

      key="fpv_tip_"+e.code+"_in_the_sky";
      if (!isEmpty( HmsJson[key])){
        return "无人机 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

    }
    const getMsg=(list)=>{
        let x=""
        list.forEach(e => {
           x=x+getHmsStr(e);
        });
        x=x.replaceAll("undefined","")
        return x
    }

    const getHmsPanel=()=>{
       // console.log('HmsPanel',hms)
    //    return  <div className={styles.tipsBar} >{'adsfdsafdsaf'}</div>

        if(isEmpty(hms)) return <div/>
        const data=hms;
        if(isEmpty(data.list)) return <div/>
        const xx=getMsg(data.list)
        if(isEmpty(xx)) return <div/>
        // console.log(data)
        return  <div className={styles.tipsBar} >{xx}</div>
    }
    
    const getReasonPanel=()=>{
     // return <div className={styles.tipsBar} style={{marginTop:24.0}} >{'dasfdasfdsa'}</div>

      if(isEmpty(mr)) return <div/>
      return <div className={styles.tipsBar} >{mr}</div>
    }
  
    return <div>{getHmsPanel()}
    {getReasonPanel()}
    </div>
  
  }
   

export default HmsPanel;
