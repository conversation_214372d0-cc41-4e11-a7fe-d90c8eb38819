import { MainColor } from "@/utils/colorHelper";
import {DigitalFlop} from "@jiaminghi/data-view-react";
import { Divider ,Row,Col} from "antd";

const MainDataPanel=()=>{
    const getItem=(title,content)=>{
        const config= {
            number: [208],
            content: '{nt} 公里',
            style: {
                fill: 'MainColor',
                fontWeight: 'bold',
              },
          }

        return <Col  style={{marginLeft:12.0,width:160.0}}><div style={{height:100,width:100.0}}>

             {/* <div ><DigitalFlop config={config} style={{width: '200px', height: '50px'}} /></div> */}
             <div style={{textAlign:'center', color:'white',fontSize:20.0,fontWeight:'bold'}}>{title}</div>
             <div style={{background:'white',height:2.0,marginTop:8.0,marginBottom:8.0,width:100}}></div>
             <div style={{textAlign:'center', color:'white',fontSize:16.0,fontWeight:'bold'}}>{content}</div>
            
            </div></Col> 
    }

    return <div style={{paddingLeft:72.0,marginLeft:24.0}}><Row>
        {getItem('208 公里','巡检覆盖里程')}
        {getItem('2 处','当前拥堵点位')}
        {getItem('92 %','全线畅通率')}
        </Row></div>;
}

export default MainDataPanel;