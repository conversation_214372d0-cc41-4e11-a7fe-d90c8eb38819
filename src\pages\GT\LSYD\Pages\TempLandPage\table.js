import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const parseChineseDate = (dateStr) => {
  const [year, month, day] = dateStr.split(/[年月日]/).filter(Boolean).map(Number)
  return new Date(year, month - 1, day) // （JavaScript的Date对象月份从0开始）
}

const handleIsOverTime = (time) =>{
  const jssj = parseChineseDate(time);
  const now = new Date();
  const threeMonthsLater = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
  if( jssj < now ) {
    return <Space size="middle">
             <span style={{color: "red"}}>已超时</span>
          </Space>
  }
  if ( jssj <= threeMonthsLater ){
    return <Space size="middle">
              <span style={{color: "orange"}}>即将超时</span>
          </Space>
  }else{
    return <Space size="middle">
              <span style={{color: "green"}}>未超时</span>
          </Space>
  }
}



const TableCols = () => {

  return [
    {
      title: getTableTitle("状态"),
      align: "center",
      render: (record) => (
        handleIsOverTime(record.jssj)
      ),
    },
    {
      title: getTableTitle("项目名称"),
      dataIndex: "xmmc",
      key: "xmmc",
      align: "center",
    },

    {
      title: getTableTitle("用地位置"),
      dataIndex: "ydwz",
      key: "ydwz",
      align: "center",
    },

    {
      title: getTableTitle("用地定位"),
      dataIndex: "yddw",
      key: "yddw",
      align: "center",
    },

    {
      title: getTableTitle("用地面积"),
      dataIndex: "ydmj",
      key: "ydmj",
      align: "center",
    },

    {
      title: getTableTitle("占用耕地"),
      dataIndex: "zygd",
      key: "zygd",
      align: "center",
    },
    {
      title: getTableTitle("永久基本农田"),
      dataIndex: "yjjbnt",
      key: "yjjbnt",
      align: "center",
    },
    {
      title: getTableTitle("开始时间"),
      dataIndex: "kssj",
      key: "kssj",
      align: "center",
    },
    {
      title: getTableTitle("结束时间"),
      dataIndex: "jssj",
      key: "jssj",
      align: "center",
    },
    {
      title: getTableTitle("批准文号"),
      dataIndex: "pzwh",
      key: "pzwh",
      align: "center",
    },
    {
      title: getTableTitle("批准时间"),
      dataIndex: "pzsj",
      key: "pzsj",
      align: "center",
    },

    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <MyButton
            style={{ padding: "2px 8px",color:'red',background:'none' }}
            onClick={() => {
              console.log('删除');
            }}
          >
            删除
          </MyButton>
        </Space>
      ),
    },
  ];
};

export default TableCols;
