/**
 * 地图引擎检测工具
 * 用于检测当前地图实例的类型（Leaflet/Cesium）
 */

/**
 * 检测地图引擎类型
 * @param {Object} mapInstance - 地图实例
 * @returns {string} 'leaflet' | 'cesium' | 'unknown'
 */
export function detectMapEngine(mapInstance) {
  if (!mapInstance) {
    console.warn('MapEngineDetector: mapInstance is null or undefined');
    return 'unknown';
  }

  try {
    // 检测 Cesium Viewer
    if (mapInstance.scene && 
        mapInstance.camera && 
        mapInstance.entities && 
        mapInstance.imageryLayers &&
        typeof mapInstance.flyTo === 'function') {
      return 'cesium';
    }

    // 检测 Leaflet Map
    if (mapInstance.getCenter && 
        mapInstance.getZoom && 
        mapInstance.getBounds &&
        mapInstance.on &&
        typeof mapInstance.setView === 'function') {
      return 'leaflet';
    }

    // 通过构造函数名称检测
    const constructorName = mapInstance.constructor.name;
    if (constructorName === 'Viewer') {
      return 'cesium';
    }
    if (constructorName === 'Map') {
      return 'leaflet';
    }

    // 通过原型链检测
    if (mapInstance._container && mapInstance._layers) {
      return 'leaflet';
    }

    if (mapInstance.cesiumWidget && mapInstance.scene) {
      return 'cesium';
    }

    console.warn('MapEngineDetector: Unable to detect map engine type', mapInstance);
    return 'unknown';

  } catch (error) {
    console.error('MapEngineDetector: Error detecting map engine:', error);
    return 'unknown';
  }
}

/**
 * 验证地图引擎是否支持
 * @param {string} engineType - 引擎类型
 * @returns {boolean} 是否支持
 */
export function isSupportedEngine(engineType) {
  return ['leaflet', 'cesium'].includes(engineType);
}

/**
 * 获取地图引擎信息
 * @param {Object} mapInstance - 地图实例
 * @returns {Object} 引擎信息对象
 */
export function getMapEngineInfo(mapInstance) {
  const engineType = detectMapEngine(mapInstance);
  
  const info = {
    type: engineType,
    supported: isSupportedEngine(engineType),
    instance: mapInstance
  };

  // 添加引擎特定信息
  if (engineType === 'cesium') {
    info.version = mapInstance.cesiumWidget?.cesiumVersion || 'unknown';
    info.hasScene = !!mapInstance.scene;
    info.hasCamera = !!mapInstance.camera;
    info.hasEntities = !!mapInstance.entities;
  } else if (engineType === 'leaflet') {
    info.version = mapInstance.getContainer?.()?.getAttribute?.('data-leaflet-version') || 'unknown';
    info.hasContainer = !!mapInstance._container;
    info.hasLayers = !!mapInstance._layers;
  }

  return info;
}

/**
 * 检查地图实例是否准备就绪
 * @param {Object} mapInstance - 地图实例
 * @returns {boolean} 是否准备就绪
 */
export function isMapReady(mapInstance) {
  const engineType = detectMapEngine(mapInstance);
  
  if (engineType === 'cesium') {
    return !!(mapInstance.scene && 
             mapInstance.camera && 
             mapInstance.scene.globe &&
             !mapInstance.isDestroyed());
  }
  
  if (engineType === 'leaflet') {
    return !!(mapInstance._container && 
             mapInstance._loaded &&
             mapInstance.getCenter());
  }
  
  return false;
}

export default {
  detectMapEngine,
  isSupportedEngine,
  getMapEngineInfo,
  isMapReady
};
