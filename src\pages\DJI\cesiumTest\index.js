import { useState, useEffect, useRef } from 'react';
import { GetCesiumViewer, Cartesian3_TO_Position, SetCameraControl, DrawLookCone, SetCamera, PointImg2 } from '@/utils/cesium_help';
import { Cesium, useModel } from "umi";
import { getRtmpWebrtcUrl } from "@/utils/config";
import { getRedlineUrl } from '@/utils/utils';
import { axiosApi } from "@/services/general";
import bgmap from '@/assets/img/bgmap.png';
import { getMqttTcpAddress } from "@/utils/config";


const CesoiumTest = ({ showCanvas, setShowCanvas, sn, drc, fj }) => {

    const [redlineData, setRedlineData] = useState(null);
    const { DoCMD, DoCMD2 } = useModel("cmdModel");
    const redlineDataRef = useRef();
    const seq = useRef(0);

    const pointImgRef = useRef(new Image());
    pointImgRef.current.src = bgmap;

    const device = JSON.parse(localStorage.getItem("device"));

    // 大地图viewer
    const viewer = useRef(null);
    const url = getRtmpWebrtcUrl() + sn;
    var canvas = null
    var ctx = null

    // 进入指令飞行模式
    const beginX = () => {
        seq.current = 0;
        const data = {
            hsi_frequency: 1,
            mqtt_broker: {
                address: getMqttTcpAddress(),
                client_id: device.SN2,
                enable_tls: false,
                expire_time: 1872744922,
                password: "",
                username: "",
            },
            osd_frequency: 10,
        };
        const data2 = {
            payload_index: device.Camera2,
        };
        DoCMD(device.SN, "drc_mode_enter", data);
        DoCMD(device.SN, "payload_authority_grab", data2);
    };

    // 返回缩放后的坐标数组
    const scaleCoordinates = (points, canvas) => {

        const cameraWidth = 5280;
        const cameraHeight = 3956;

        const canvasAspect = canvas.width / canvas.height;
        const originalAspect = cameraWidth / cameraHeight;
        let scale, offsetX = 0, offsetY = 0;

        // 判断是否宽度方向需要裁剪
        if (originalAspect > canvasAspect) {
            // 高度撑满，宽度方向有裁剪
            scale = canvas.height / cameraHeight;
            const scaledWidth = cameraWidth * scale;
            offsetX = (canvas.width - scaledWidth) / 2;
        } else {
            // 宽度撑满，高度方向有裁剪
            scale = canvas.width / cameraWidth;
            const scaledHeight = cameraHeight * scale;
            offsetY = (canvas.height - scaledHeight) / 2;
        }

        return points.map(point => ({
            x: point[0] * scale + offsetX,
            y: point[1] * scale + offsetY
        }));
    };

    // 获取红线点位
    useEffect(() => {
        const fetchRedlineData = async () => {
            try {
                const res = await axiosApi(getRedlineUrl(sn), 'GET', {});
                setRedlineData(res);

            } catch (error) {
                console.error("获取红线数据失败:", error);
            }
        };

        if(showCanvas == true){
            fetchRedlineData();
        }
    }, [sn,showCanvas]);

    useEffect(() => {
        redlineDataRef.current = redlineData;
    }, [redlineData]);

    // 页面载入
    useEffect(() => {
        viewer.current = GetCesiumViewer('cesisss')
        viewer.current.scene.camera.frustum.fov = Cesium.Math.toRadians(39.78 /0.5678233438485805)
        viewer.current.scene.camera.frustum.aspectRatio = (940 / 705)

        canvas = document.getElementById("canvas");
        const dpr = window.devicePixelRatio || 1;
        canvas.width = canvas.offsetWidth * dpr;
        canvas.height = canvas.offsetHeight * dpr;
        ctx = canvas.getContext("2d");

        // "http://8.137.54.85:9000/300bdf2b-a150-406e-be63-d28bd29b409f/dszh/1748398014403562192_OUT/B3DM/tileset.json"

        viewer.current.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(103.99177815222232, 30.762915428775877, 5000),
            orientation: {
                heading: 0,
                pitch: Cesium.Math.toRadians(-90),
                roll: 0,
            },
        });


        // 监听Cesium场景更新事件，以便在每次渲染时更新所有HTML弹窗的位置
        viewer.current.scene.postRender.addEventListener(() => {

            ctx.clearRect(0, 0, canvas.width, canvas.height); // 清除之前的线条，实现动态效果
            const currentData = redlineDataRef.current; // 从ref获取最新数据
            if (!currentData) {
                return; // 数据不存在时直接返回
            }
            // 绘制点位
            currentData.Point?.forEach(point => {
                const img = pointImgRef.current;
                const windowPosition = new Cesium.Cartesian2()
                Cesium.SceneTransforms.worldToWindowCoordinates(
                    viewer.current.scene,
                    Cesium.Cartesian3.fromDegrees(point.coordinates[0], point.coordinates[1], point.coordinates[2]),
                    windowPosition
                )

                // 计算缩放比例
                const scaleX = (canvas.offsetWidth / viewer.current.canvas.clientWidth) * dpr;
                const scaleY = (canvas.offsetHeight / viewer.current.canvas.clientHeight) * dpr;

                // 计算实际坐标
                const x = windowPosition.x * scaleX;
                const y = windowPosition.y * scaleY;

                // 绘制图标（箭头底部中心对准坐标点）
                if (img.complete) {
                    ctx.drawImage(img, x - img.width / 2, y - img.height, img.width, img.height);

                    if (point.lable) {
                        ctx.save(); // 保存当前绘图状态
                        ctx.font = 'bold 20px "Microsoft YaHei", Arial'; // 使用更通用的字体
                        ctx.fillStyle = '#ff0000'; // 先用红色测试可见性
                        ctx.strokeStyle = '#ffffff';
                        ctx.lineWidth = 2;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'bottom'; // 文字底部对齐坐标


                        const textY = y - img.height + 35;

                        // 先绘制描边文字
                        ctx.strokeText(point.lable, x, textY);
                        // 再绘制填充文字
                        ctx.fillText(point.lable, x, textY);
                        ctx.restore(); // 恢复绘图状态
                    }
                }
            });


            // 绘制折线 
            currentData.LineString?.forEach((item) => {
                if (item.coordinates.length < 2) return;

                ctx.beginPath();
                ctx.lineWidth = (item.lineWidth);
                ctx.strokeStyle = item.strokeStyle || '#00f';


                item.coordinates.forEach((coord, index) => {
                    const windowPosition = new Cesium.Cartesian2();
                    const positionValid = Cesium.SceneTransforms.worldToWindowCoordinates(
                        viewer.current.scene,
                        Cesium.Cartesian3.fromDegrees(coord[0], coord[1], coord[2]),
                        windowPosition
                    );

                    if (positionValid) {
                        const x = windowPosition.x * (canvas.offsetWidth / viewer.current.canvas.clientWidth) * dpr;
                        const y = windowPosition.y * (canvas.offsetHeight / viewer.current.canvas.clientHeight) * dpr;
                        if (index === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                });

                ctx.stroke();
                ctx.closePath();
            });

            // 绘制面
            currentData.Polygon?.forEach((polygon) => {
                // 设置样式属性
                ctx.lineWidth = polygon.lineWidth;
                ctx.strokeStyle = polygon.strokeStyle || '#00f';
                ctx.fillStyle = polygon.fillStyle || 'rgba(0, 0, 255, 0.2)';

                // 遍历多边形的每个环（支持多边形+孔洞）
                polygon.coordinates.forEach(ring => {
                    if (ring.length < 3) return;  // 每个环至少需要3个点

                    ctx.beginPath();

                    // 遍历环中的每个坐标点
                    ring.forEach((point, index) => {
                        const windowPosition = new Cesium.Cartesian2();
                        const positionValid = Cesium.SceneTransforms.worldToWindowCoordinates(
                            viewer.current.scene,
                            Cesium.Cartesian3.fromDegrees(
                                point[0],  // 经度
                                point[1],  // 纬度 
                                point[2]   // 高度
                            ),
                            windowPosition
                        );

                        if (positionValid) {
                            const x = windowPosition.x * (canvas.offsetWidth / viewer.current.canvas.clientWidth) * dpr;
                            const y = windowPosition.y * (canvas.offsetHeight / viewer.current.canvas.clientHeight) * dpr;

                            if (index === 0) {
                                ctx.moveTo(x, y);
                            } else {
                                ctx.lineTo(x, y);
                            }
                        }
                    });

                    ctx.closePath();
                    ctx.fill();
                    ctx.stroke();
                });
            });


        })
    }, []);

    useEffect(() => {
        if (drc) {
            viewer.current.scene.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(drc.longitude, drc.latitude, drc.height),
                orientation: {
                    heading: Cesium.Math.toRadians(drc.attitude_head),
                    pitch: Cesium.Math.toRadians(drc.gimbal_pitch),
                    roll: Cesium.Math.toRadians(drc.gimbal_roll),
                },
            });
        }
    }, [drc]);

    useEffect(() => {
        // 打开画布 进入指令飞行模式
        if (showCanvas == true) {
            beginX();
        }
    }, [showCanvas]);

    // 如果mode_code在 4 自动起飞 10 自动降落 则隐藏画布
    useEffect(() => {
        if (fj?.data?.mode_code == 4 || fj?.data?.mode_code == 10) {
            if (showCanvas == true) {
                console.log('@@@起飞或者返航中 隐藏画布');
                setShowCanvas(false);
            }
        }
    }, [fj]);

    // useEffect(() => {
    //     console.log('@@@fj', fj);
    // }, [fj]);

    // useEffect(() => {
    //     console.log('@@@drc', drc);
    // }, [drc]);

    return (
        <div style={{
            height: '100%',
            background: '#ffffff',
            position: 'relative',
            width: '100%',
            overflow: 'hidden'
        }}>
            <div style={{
                width: '100%',
                // 通过padding-top实现宽高比例固定 (705/940 = 0.75)
                paddingTop: '75%',
                position: 'relative',
                overflow: 'hidden'
            }}>
                <div
                    id="cesisss"
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        zIndex: 1
                    }}
                ></div>
                <canvas
                    id="canvas"
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        zIndex: 3,
                        pointerEvents: 'none',
                        display: showCanvas ? 'block' : 'none'
                    }}
                ></canvas>
            </div>
        </div>
    )

};

export default CesoiumTest;
