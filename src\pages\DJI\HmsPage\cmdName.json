{"live_camera_change": "直播相机切换", "live_start_push": "开始直播", "live_stop_push": "停止直播", "live_set_quality": "设置直播清晰度", "live_lens_change": "设置直播镜头", "file_upload_callback": "媒体文件上传结果上报", "highest_priority_upload_flighttask_media": "媒体文件上传优先级上报", "upload_flighttask_media_prioritize": "调整上传的文件为最高优先级", "storage_config_get": "获取上传临时凭证", "device_exit_homing_notify": "设备返航退出状态通知", "flighttask_progress": "上报航线任务进度", "flighttask_ready": "任务就绪通知", "return_home_info": "返航信息", "flighttask_create": "创建航线任务（废弃）", "flighttask_prepare": "下发任务", "flighttask_execute": "执行任务", "flighttask_undo": "取消任务", "flighttask_pause": "航线暂停", "flighttask_recovery": "航线恢复", "return_home": "一键返航", "return_home_cancel": "取消返航", "flighttask_resource_get": "任务资源获取", "hms": "健康告警", "drone_open": "飞行器开机进度", "drone_close": "飞行器关机进度", "device_reboot": "机场重启进度", "cover_close": "关闭舱盖进度", "cover_open": "打开舱盖进度", "charge_open": "打开充电进度", "charge_close": "关闭充电进度", "drone_format": "飞行器数据格式化进度", "device_format": "机场数据格式化进度", "esim_activate": "eSIM 激活进度", "esim_operator_switch": "eSIM 的运营商切换进度", "debug_mode_open": "调试模式开启", "debug_mode_close": "调试模式关闭", "supplement_light_open": "打开补光灯", "supplement_light_close": "关闭补光灯", "battery_maintenance_switch": "电池保养状态切换", "air_conditioner_mode_switch": "机场空调工作模式切换", "alarm_state_switch": "机场声光报警开关", "battery_store_mode_switch": "电池运行模式切换", "sdr_workmode_switch": "增强图传开关", "sim_slot_switch": "eSIM和SIM切换", "ota_progress": "固件升级进度", "ota_create": "固件升级", "fileupload_progress": "文件上传进度通知", "fileupload_list": "获取设备可上传的文件列表", "fileupload_start": "发起日志文件上传", "fileupload_update": "上传状态更新", "config": "获取配置", "fly_to_point_progress": "flyto 执行结果事件通知", "takeoff_to_point_progress": "一键起飞结果事件通知", "drc_status_notify": "DRC 链路状态通知", "joystick_invalid_notify": "Joystick 控制无效原因通知", "camera_photo_take_progress": "上报拍照进度", "flight_authority_grab": "飞行控制权获取", "payload_authority_grab": "负载控制权获取", "drc_mode_enter": "进入指令飞行控制模式", "drc_mode_exit": "退出指令飞行控制模式", "takeoff_to_point": "一键起飞", "fly_to_point": "飞向目标点", "fly_to_point_stop": "结束飞向目标点任务", "fly_to_point_update": "更新目标点", "camera_frame_zoom": "负载控制-框选变焦", "camera_mode_switch": "负载控制—切换相机模式", "camera_photo_take": "负载控制—开始拍照", "camera_photo_stop": "负载控制—停止拍照", "camera_recording_start": "负载控制—开始录像", "camera_recording_stop": "负载控制—停止录像", "camera_screen_drag": "负载控制—画面拖动控制", "camera_aim": "负载控制—双击成为 AIM", "camera_focal_length_set": "负载控制—变焦", "gimbal_reset": "负载控制—重置云台", "camera_look_at": "负载控制—Look At", "camera_screen_split": "负载控制—分屏", "photo_storage_set": "负载控制—照片存储设置", "video_storage_set": "负载控制—视频存储设置", "camera_exposure_mode_set": "负载控制—相机曝光模式设置", "camera_exposure_set": "负载控制—相机曝光值调节", "camera_focus_mode_set": "负载控制—相机对焦模式设置", "camera_focus_value_set": "负载控制—相机对焦值设置", "camera_point_focus_action": "负载控制—点对焦", "ir_metering_mode_set": "负载控制—红外测温模式设置", "ir_metering_point_set": "负载控制—红外测温点设置", "ir_metering_area_set": "负载控制—红外测温区域设置", "drone_control": "DRC-飞行控制", "drone_emergency_stop": "DRC-飞行器急停", "heart_beat": "DRC-心跳", "hsi_info_push": "DRC-避障信息上报", "delay_info_push": "DRC-图传链路延时信息上报", "osd_info_push": "DRC-高频 osd 信息上报", "airsense_warning": "Airsense 告警通知", "flight_areas_sync_progress": "自定义飞行区文件同步状态", "flight_areas_drone_location": "自定义飞行区告警信息推送", "flight_areas_update": "自定义飞行区更新指令", "flight_areas_get": "自定义飞行区文件获取", "psdk_ui_resource_upload_result": "psdk-ui资源包上传结果上报", "psdk_floating_window_text": "psdk-浮窗文本推送", "speaker_audio_play_start_progress": "喊话器-音频播放进度通知", "psdk_widget_value_set": "psdk-设置控件值", "psdk_input_box_text_set": "psdk-发送文本框内容", "speaker_audio_play_start": "喊话器-开始播放音频", "speaker_tts_play_start": "喊话器-开始播放TTS文本", "speaker_replay": " 喊话器-重新播放", "speaker_play_stop": "喊话器-停止播放", "speaker_play_mode_set": "喊话器-设置播放模式", "speaker_play_volume_set": "喊话器-设置音量", "custom_data_transmission_from_esdk": "自定义消息推送cloud", "custom_data_transmission_to_esdk": "cloud-自定义消息推送到esdk"}