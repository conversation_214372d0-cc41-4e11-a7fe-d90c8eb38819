import { Space, Tag, message, Modal, Switch, Badge, Image } from 'antd';
import { downloadFile, getImgUrl, isEmpty } from '@/utils/utils';
import './table.css';
import { timeFormat } from '@/utils/helper';
import { HGet2, HPost2 } from '@/utils/request';
import { Post2 } from '@/services/general';

const getTableTitle = (title) => { return <div style={{ fontWeight: 'bold', textAlign: 'center' }}>  {title}</div> }

const { confirm } = Modal;


const updateCron = async (record, refrush) => {
  if (isEmpty(record)) return;

  const xx = await HGet2("/api/v1/Danger/ChangeState?id=" + record.ID);

  console.log('UpdateDanger', xx)
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err)
  } else {
    message.info("更新成功！")
    refrush();
  }
}

const deleteDanger = async (record, refrush) => {

  const xx = await Post2("/api/v1/Danger/Delete", record);

  console.log('deleteDanger', xx)
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err)
  } else {
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: '删除事件',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该事件吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteDanger(record, refrush);
    },
  });
};

const TableCols = (refrush, showMap) => {
  return [
    {
      title: getTableTitle('现场照片'),
      dataIndex: 'ImgObjectName',
      key: 'ImgObjectName',
      align: 'center',
      width:220,
      render: (e) => {
        return <Image src={getImgUrl(e)} height={120} width={200}/>
      }
    },
    
    // {
    //   title: getTableTitle('事件类型'),
    //   dataIndex: 'DangerType',
    //   key: 'DangerType',
    //   align: 'center',

    // },
    
    {
      title: getTableTitle('事件标题'),
      dataIndex: 'Title',
      key: 'Title',
      align: 'center',
    },
    {
      title: getTableTitle('主要内容'),
      dataIndex: 'Content',
      key: 'Content',
      align: 'center',
     
    },
   
    {
      title: getTableTitle('紧急程度'),
      dataIndex: 'Level',
      key: 'Level',
      align: 'center',
      render: (e) => {
        if (e == '一般')
          return <Badge color={'green'} text={e} />
        return <Badge color={'orange'} text={e} />
      }
    },
    {
      title: getTableTitle('事件状态'),
      align: 'center',
      render: (record) => {
        if (record.State >= 1)
          return <Badge color={'green'} text="已完成" />
        return <Badge color={'orange'} text="处理中" />
      }
    },
    {
      title: getTableTitle('创建时间'),
      dataIndex: 'CreateTM',
      key: 'CreateTM',
      align: 'center',
      render: (e) => {
        return timeFormat(e)
      }
    },
    {
      title: getTableTitle('操作'),
      align: 'center',
      render: (record) => (
        <Space size="middle">
          <Tag><a onClick={() => showMap(record)}>事件详情</a></Tag>
          <Tag><a onClick={() => showDeleteConfirm(record, refrush)}>删除</a></Tag>
        </Space>)
    }];
}



export default TableCols;
