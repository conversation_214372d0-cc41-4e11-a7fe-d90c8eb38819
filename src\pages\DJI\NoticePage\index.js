import { useState, useEffect } from "react";
import { Card, Table } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2 } from "@/services/general";
import NoticeAddForm from "./form_add";
import TableCols from "./table";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import AddButton from "@/components/AddButton";

const NoticeListPage = () => {
  const [NoticeList, setNoticeList] = useState([]);
  const { setModal, setOpen } = useModel("pageModel");

  const getNoticeData = async () => {
    let pst = await Get2("/api/v1/Notice/GetAllList", {});
    if (isEmpty(pst)) pst = [];
    setNoticeList(pst);
  };

  useEffect(() => {
    getNoticeData();
  }, []);

  const refrush = async () => {
    getNoticeData();
    setOpen(false);
  };

  const getExr = (
    <AddButton
      onClick={() => {
        setModal(
          <NoticeAddForm
            org={{ OrgName: "", OrgCode: "", ZhiBan: "", JianKong: "" }}
            refrush={refrush}
          />
        );
        setOpen(true);
      }}
    >
      添加申请
    </AddButton>
  );

  return (
    <div style={{ margin: 0, height: getBodyH(56)}}>
      <Card
        title={<LastPageButton title="公告发布" />}
        bordered={false}
        extra={getExr}
      >
        <div>
          {isEmpty(NoticeList) ? (
            <div />
          ) : (
            <Table
              pagination={false}
              bordered
              dataSource={NoticeList}
              columns={TableCols(refrush, (e) => {
                setModal(
                  <NoticeAddForm refrush={refrush} org={e}></NoticeAddForm>
                );
                setOpen(true);
              })}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default NoticeListPage;
