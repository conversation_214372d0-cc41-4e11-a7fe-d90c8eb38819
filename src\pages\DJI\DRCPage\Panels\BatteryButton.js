import React, { useRef, useEffect } from 'react';
 
const BatteryButton = ({value, color = 'white'}) => {
  const canvasRef = useRef(null);
  const w1=21
  const h1=15
  const c1=5
  if(value==0) value=1;
  useEffect(() => {
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    const drawV=(v)=>{
      
        const x = (v-1)*(w1/c1);
        
        const width = 3;
        const height = v*((h1-c1)/(c1-1));
        const y = h1-height;
        const fillColor = color; // 填充颜色
        // 绘制矩形
        context.fillStyle = fillColor;
        context.fillRect(x, y, width, height);
    }
    // 设置矩形的属性

    for(let i=1;i<=value;i++){
        drawV(i);
    }
   
  }, []); // 空依赖数组确保只在组件挂载时执行
 
  return (
    <canvas style={{}} ref={canvasRef} width={w1} height={h1}></canvas>
  );
};
 
export default BatteryButton;