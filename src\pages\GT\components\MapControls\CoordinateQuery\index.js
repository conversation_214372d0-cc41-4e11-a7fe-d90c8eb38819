import React, { useState, useCallback } from 'react'
import { Dropdown, Button, Input, Form, message, Space, Tooltip } from 'antd'
import { AimOutlined, DownOutlined, EnvironmentOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons'
import styles from './index.module.less'

/**
 * 坐标查询组件
 * 提供坐标定位和拾取功能
 */
const CoordinateQuery = ({
  onLocationTo, // 定位到指定坐标的回调函数
  onPickCoordinate, // 开始拾取坐标的回调函数
  onClearMarkers, // 清除所有标记的回调函数
  isPickingMode = false, // 是否处于拾取模式
  pickedCoordinate = null, // 拾取到的坐标
  className = ''
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false)
  const [form] = Form.useForm()

  // 处理定位功能
  const handleLocationSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields(['longitude', 'latitude'])
      const { longitude, latitude } = values
      
      // 验证坐标范围
      if (longitude < -180 || longitude > 180) {
        message.error('经度范围应在-180到180之间')
        return
      }
      if (latitude < -90 || latitude > 90) {
        message.error('纬度范围应在-90到90之间')
        return
      }

      if (onLocationTo) {
        onLocationTo({ longitude: parseFloat(longitude), latitude: parseFloat(latitude) })
        message.success('定位成功')
      }
      setDropdownVisible(false)
    } catch (error) {
      console.error('坐标定位失败:', error)
    }
  }, [form, onLocationTo])

  // 处理拾取坐标功能
  const handlePickCoordinate = useCallback(() => {
    if (onPickCoordinate) {
      onPickCoordinate()
    }
    setDropdownVisible(false)
  }, [onPickCoordinate])

  // 处理清除标记功能
  const handleClearMarkers = useCallback(() => {
    if (onClearMarkers) {
      onClearMarkers()
      message.success('已清除所有坐标标记')
    }
    setDropdownVisible(false)
  }, [onClearMarkers])

  // 复制坐标到剪贴板
  const handleCopyCoordinate = useCallback(async (coordinate) => {
    try {
      const coordinateText = `${coordinate.longitude.toFixed(6)}, ${coordinate.latitude.toFixed(6)}`
      await navigator.clipboard.writeText(coordinateText)
      message.success('坐标已复制到剪贴板')
    } catch (error) {
      console.error('复制坐标失败:', error)
      message.error('复制失败')
    }
  }, [])

  // 格式化坐标显示
  const formatCoordinate = useCallback((coordinate) => {
    if (!coordinate) return ''
    return `${coordinate.longitude.toFixed(6)}°, ${coordinate.latitude.toFixed(6)}°`
  }, [])

  // 下拉菜单内容
  const dropdownContent = (
    <div className={styles.coordinateQueryPanel}>
      {/* 坐标定位部分 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>
          <EnvironmentOutlined />
          <span>坐标定位</span>
        </div>
        <Form form={form} layout="vertical" className={styles.locationForm}>
          <Form.Item
            name="longitude"
            label="经度 (°)"
            rules={[
              { required: true, message: '请输入经度' },
              { 
                type: 'number', 
                min: -180, 
                max: 180, 
                message: '经度范围应在-180到180之间',
                transform: (value) => parseFloat(value)
              }
            ]}
          >
            <Input 
              placeholder="请输入经度，如：104.063379"
              type="number"
              step="0.000001"
            />
          </Form.Item>
          <Form.Item
            name="latitude"
            label="纬度 (°)"
            rules={[
              { required: true, message: '请输入纬度' },
              { 
                type: 'number', 
                min: -90, 
                max: 90, 
                message: '纬度范围应在-90到90之间',
                transform: (value) => parseFloat(value)
              }
            ]}
          >
            <Input 
              placeholder="请输入纬度，如：30.659835"
              type="number"
              step="0.000001"
            />
          </Form.Item>
          <Button 
            type="primary" 
            onClick={handleLocationSubmit}
            className={styles.locationButton}
            icon={<AimOutlined />}
          >
            定位到此坐标
          </Button>
        </Form>
      </div>

      {/* 坐标拾取部分 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>
          <AimOutlined />
          <span>坐标拾取</span>
        </div>
        <div className={styles.pickSection}>
          <Button 
            type={isPickingMode ? 'primary' : 'default'}
            onClick={handlePickCoordinate}
            className={styles.pickButton}
            icon={<AimOutlined />}
          >
            {isPickingMode ? '拾取中...' : '点击地图拾取坐标'}
          </Button>
          
          {pickedCoordinate && (
            <div className={styles.pickedCoordinate}>
              <div className={styles.coordinateDisplay}>
                <span className={styles.coordinateText}>
                  {formatCoordinate(pickedCoordinate)}
                </span>
                <Tooltip title="复制坐标">
                  <Button 
                    type="text" 
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopyCoordinate(pickedCoordinate)}
                    className={styles.copyButton}
                  />
                </Tooltip>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 标记管理部分 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>
          <DeleteOutlined />
          <span>标记管理</span>
        </div>
        <Button 
          type="default"
          danger
          onClick={handleClearMarkers}
          className={styles.clearButton}
          icon={<DeleteOutlined />}
        >
          清除所有标记
        </Button>
      </div>
    </div>
  )

  return (
    <div className={`${styles.coordinateQueryContainer} ${className}`}>
      <Dropdown
        popupRender={() => dropdownContent}
        trigger={['click']}
        placement="topLeft"
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        overlayClassName="coordinateQueryDropdown"
      >
        <Button
          type="default"
          className={`${styles.coordinateQueryButton} ${isPickingMode ? styles.active : ''}`}
          icon={<AimOutlined />}
        >
          坐标查询
          <DownOutlined />
        </Button>
      </Dropdown>
    </div>
  )
}

export default CoordinateQuery
