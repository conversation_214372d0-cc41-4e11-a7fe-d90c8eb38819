import { useState, useEffect } from "react";
import { Card, Table } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2 } from "@/services/general";
import { getGuid } from '@/utils/helper';
import EditForm from "./form_edit";
import UpdateForm from "./form_update"
import DetailForm from "./detail";
import TableCols from "./table";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import AddButton from "@/components/AddButton";

const ListPage = (props) => {
  const {  setOpen, setPage, } = useModel("pageModel");
  const [xList, setXList] = useState([]);

  const ifPush = (e) => {
    return true;
  };

  const getData = async () => {
    setOpen(false);
    let pst = await Get2("/api/v1/MessageData/GetAllList", {});
    if (isEmpty(pst)) pst = [];
    const xL = [];
    pst.forEach((e) => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    setXList([...xL]);
  };

  useEffect(() => {
    getData();
  }, []);

  const getExr = () => {
    return (
      <div style={{ display: "flex", justifyContent: "flex-start" }}>
        <UpdateForm key={getGuid()} refrush={getData} data0={{}}></UpdateForm>
        <AddButton
          type="primary"
          style={{marginLeft:12.0}}
          onClick={() => {
            getData();
          }}
        >
          刷新
        </AddButton>
      </div>
    );
  };

  const editForm = (data) => {
    // setPage(<EditForm refrush={getData} data0={data}></EditForm>);
    return <EditForm refrush={getData} data0={data}></EditForm>
  }
  const detailForm = (data) => {
    setPage(<DetailForm record={data}></DetailForm>);
  };

  return (
    <div style={{ margin: 0, background: "#F5F5FF" }}>
      <Card
        title={<LastPageButton title="通知列表" />}
        bordered={false}
        extra={getExr()}
      >
        <div>
          {isEmpty(xList) ? (
            <div />
          ) : (
            <Table
              pagination={true}
              bordered
              dataSource={xList}
              columns={TableCols(getData, editForm, detailForm)}
              size="small"
              style={{background: '#fff'}}
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default ListPage;
