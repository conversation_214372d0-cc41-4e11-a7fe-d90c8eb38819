import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (currentTablePage, algorithmTypes = []) => {
  
  // 动态生成算法结果列
  const algorithmColumns = algorithmTypes.map(type => ({
    title: getTableTitle(type.type_name),
    key: type.type_name,
    align: "center",
    render: (_, record) => {
      try {
        const result = JSON.parse(record.AlgorithmResult)
        return result.find(t => t.type_name === type.type_name)?.count || '-'
      } catch {
        return '-'
      }
    }
  }))

  return [
    {
      title: getTableTitle("照片组"),
      key: "serial",
      align: "center",
      render: (text, record, index) => {
        // 当前页码从父组件传递过来
        return (currentTablePage - 1) * 7 + index + 1;
      }

    },
    ...algorithmColumns,
  ];
};

export default TableCols;
