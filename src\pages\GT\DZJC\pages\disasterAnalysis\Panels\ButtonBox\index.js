import styles from "./index.less";
import { useState, useEffect } from "react";
import { Button, Col, Popover } from "antd";
import { Get2, Post2 } from "@/services/general";
import { CloseOutlined } from "@ant-design/icons";
import useConfigStore from "@/stores/configStore";

export default function ButtonBox({ setStatus }) {
  let buttonList = ["时序选择", "模型对比", "空间量测", "截图勾绘", "变化检测"];
  const [MapData, setMapData] = useState([]);
  const [open, setOpen] = useState(false);
  const { setZSMapUrl } = useConfigStore();

  const getMList = async () => {
    let pst = await Get2("/api/v1/MapData/GetAllList");
    if (pst && pst.length > 0) {
      setMapData(pst);
    }
  };

  useEffect(() => {
    getMList();
  }, []);

  const handleOpenChange = (newOpen) => {
    setOpen(!open);
  };
  const handleClickBtn = (e) => {
    console.log(e);
    setStatus(e);
  };
  const content = (
    <div className={styles["scroll_container"]}>
      {MapData.filter((item) => item.MapType === 0 || item.MapType === 1).map(
        (item, index) => (
          <Button
            key={index}
            style={{ width: "100%" }}
            onClick={() => {
              setZSMapUrl(item);
            }}
          >
            {item.MapName}
          </Button>
        )
      )}
    </div>
  );

  return (
    <div className={styles.ButtonBox}>
      {/* {buttonList.map((item) => getItem(item))} */}
      <Col>
        <Popover
          content={content}
          title={
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <div></div>
              <CloseOutlined onClick={handleOpenChange} />
            </div>
          }
          trigger="click"
          open={open}
          onOpenChange={handleOpenChange}
        >
          <Button
            className={styles.items}
            onClick={(e) => handleClickBtn("时序选择")}
          >
            时序选择
          </Button>
        </Popover>
      </Col>
      <Col>
        <Button
          className={styles.items}
          onClick={(e) => {
            handleClickBtn("模型对比");
          }}
        >
          模型对比
        </Button>
      </Col>
      {/* <Col>
        <Button
          className={styles.items}
          onClick={(e) => handleClickBtn("空间量测")}
        >
          空间量测
        </Button>
      </Col>
      <Col>
        <Button
          className={styles.items}
          onClick={(e) => handleClickBtn("空间量测")}
        >
          截图勾绘
        </Button>
      </Col> */}
      <Col>
        <Button
          className={styles.items}
          onClick={(e) => handleClickBtn("空间量测")}
        >
          变化监测
        </Button>
      </Col>
    </div>
  );
}
