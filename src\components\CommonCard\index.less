.customCard {
  padding: 34px;
  background-image: url('@/pages/SI/assets/image/container_border.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
}

.title {
  background-image: url('@/pages/SI/assets/image/title_bg.png');
  background-position: left bottom;
  background-repeat: no-repeat;
  padding: 0px 0px 20px 13px;
  font-size: 24px;
  line-height: 26px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.4);
  position: relative;
}

.extra {
  position: absolute;
  right: 0;
  top: 32px;
}

.content {
  margin-top: 30px;
  padding: 0 5px;
  flex: 1;
  // max-height: calc(100% - 46px - 30px);
  &.scroll {
    overflow-y: auto;
  }
}