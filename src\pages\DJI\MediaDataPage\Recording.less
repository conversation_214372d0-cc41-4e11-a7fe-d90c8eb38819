@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.title {
    text-align: center;
    margin-top:20px;
    margin-bottom: 20px;
}

.content{
  line-height: 30px;
  font-size: 16px;

}

.container{
  max-width: 1200px;
  margin:0 auto;
  min-height: 800px;
}

.imgdiv{
  display: flex;
  justify-content: center;
  align-items: center;  

  max-width: 1200px;
  margin:0 auto;
  padding:12px;

}

.imgxys {
  display: inline-block;
  height: auto;
  max-width: 80%;
  max-height:800px;
}


.coverCardList {
  margin-bottom: -24px;

  .card {
    :global {
      .ant-card-meta-title {
        margin-bottom: 4px;
        & > a {
          color: @heading-color;
          display: inline-block;
          max-width: 100%;
        }
      }
      .ant-card-meta-description {
        height: 44px;
        line-height: 22px;
        overflow: hidden;
      }
    }

    &:hover {
      :global {
        .ant-card-meta-title > a {
          color: @primary-color;
        }
      }
    }
  }

  .cardItemContent {
    display: flex;
    margin-top: 16px;
    margin-bottom: -4px;
    line-height: 20px;
    height: 20px;
    & > span {
      color: @text-color-secondary;
      flex: 1;
      font-size: 12px;
    }
    .avatarList {
      flex: 0 1 auto;
    }
  }
  .cardList {
    margin-top: 24px;
  }

  :global {
    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
}


