.aircraft-lens-panel {
  width: 25vw;
  display: flex;
  background-color: #0000003d;
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(calc(-50% - 145px));
  z-index: 1000;
  justify-content: center;
  align-items: center;
  min-height: 30px;
  padding: 0 10px;

  .panel-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    width: 100%;
  }

  .info-item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: MiSan;
    font-size: 14px;
    color: white;
    white-space: nowrap;
    padding: 4px 0;
    min-width: 40px;

    .icon {
      margin-left: 4px;
      display: inline-flex;
      align-items: center;
    }
  }

  .battery-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0 4px 5px;
    min-width: 20px;
    canvas {
      vertical-align: middle; // 确保canvas垂直居中
      display: inline-block; // 确保canvas正确显示
    }
  }

  @media screen and (max-width: 1200px) {
    width: 20vw;
  }

  @media screen and (max-width: 768px) {
    width: 20vw;
  }
}
