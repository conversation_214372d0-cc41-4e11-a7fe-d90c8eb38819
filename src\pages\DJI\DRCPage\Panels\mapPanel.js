import { HGet2 } from "@/utils/request";
import { Dropdown, Button } from "antd";
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import { useModel } from "umi";
import { getGuid } from "@/utils/helper";
import { isEmpty } from "@/utils/utils";
import { useState } from "react";
import mapImg from "@/assets/drcImgs/map.png";
import DJBaseMap from "@/pages/Maps/DJBaseMap";
import Map3D from '@/pages/Cesium';

const MapPanel = () => {
  const { mapUrl, setMapUrl } = useModel('mapModel');
  const { setIfFJRtmp, setIfJCRtmp } = useModel('rtmpModel');
  const [open,setOpen]=useState(false);
  const { DoCMD } = useModel('cmdModel')
  const device = JSON.parse(localStorage.getItem('device'))
  const [title,setTitle]=useState('地形图');

  //if (isEmpty(device)) return;

  const goUrl = async (url) => {
    HGet2(url)
  }


  const changeFBL = (id, i) => {
    const data = {
      "video_id": id,
      "video_quality": i
    }
    DoCMD(device.SN, "live_set_quality", data);
  }

  const setDiTu = (i) => {
    const WeiXing = "https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
    const HuiBai = "https://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=d26935ae77bbc1fb3a6866a5b6ff573f"
    const GaoDe = "http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
    if (i == 0) {
      localStorage.setItem('baseMap', WeiXing)
      setMapUrl(WeiXing)
      setTitle('卫星图')
    }
    if (i == 1) {
      localStorage.setItem('baseMap', HuiBai)
      setMapUrl(HuiBai)
      setTitle('灰白图')
    }
    if (i == 2) {
      localStorage.setItem('baseMap', GaoDe)
      setMapUrl(GaoDe)
      setTitle('地形图')
    }
  }

  const getItem = (title, onClick) => {
    return {
      key: getGuid(),
      label: (<a onClick={onClick}>{title}</a>),
    };
  }



  const items = () => {
    const device = JSON.parse(localStorage.getItem('device'))
    return [
      {
        key: '3-1',
        label: (<a onClick={() => setDiTu(0)}>卫星图</a>),
      },
      {
        key: '3-2',
        label: (<a onClick={() => setDiTu(2)}>路网图</a>),
      },
      {
        key: '3-2',
        label: (<a onClick={() => setDiTu(1)}>灰白图</a>),
      },
      {
        key: '3-3',
        label: (<a onClick={() => setDiTu(1)}>三维图</a>),
      },
    ];
  }



const panel2=()=>{
    return <div style={{userSelect:'none',borderRadius:3.0, cursor:'pointer', position: 'absolute',background:'red', zIndex: 1001,top:64,left:8}} onClick={()=>setOpen(true)}><img src={mapImg} style={{borderRadius:3.0,}} height={45} width={48}/></div>
}
const panel3=()=>{
  if (title=='三维图') return<Map3D h1={240}/>
  
  return <DJBaseMap device={device} sn={device.SN} h1={'240px'} isDrc={true} />
}
const panel1=()=>{
 // setDiTu(2);
  return <div
  style={{
    zIndex: 1001,
    position: 'absolute',
    width: 320,
    top: 64,
    left: 8,
    border:'1px 1px 1px 1px',
    borderRadius:5.0
  }}
>
  <div
    style={{
      position: 'absolute',
      zIndex: 1009,
      width: '100%',
      background: 'rgba(255,255,255,0.5)',
    }}
  >
    <Button style={{width:80}}  type="text" onClick={()=>setDiTu(0)}>卫星图</Button>
    <Button style={{width:80}} type="text" onClick={()=>setDiTu(2)}>地形图</Button>
    <Button style={{width:80}} type="text" onClick={()=>setTitle('三维图')}>三维图</Button>
    <Button style={{width:80}} type="text" onClick={()=>setOpen(false)}>隐藏</Button>
    
  </div>
   {panel3()}
</div>
}

return open?panel1(): panel2();

}
export default MapPanel;
