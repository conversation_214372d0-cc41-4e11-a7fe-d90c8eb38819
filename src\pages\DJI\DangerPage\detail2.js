import { getBodyH, getImgUrl, isEmpty } from '@/utils/utils';
import {Row,Col,Card,Image, Descriptions,Button} from 'antd';
import { HPost2 } from '@/utils/request';
import { useEffect, useState } from 'react';
import { Get2 } from '@/services/general';
import { useSearchParams,history } from 'umi';
import { ArrowLeftOutlined } from '@ant-design/icons';

const DangerDetailPage2=()=>{
    const [danger, setDanger] = useState({});
    const [searchParams, setSearchParams] = useSearchParams();
    const xxx=searchParams.get('id')

    useEffect(() => {
        const getData=async()=>{
            const d1=await Get2('/api/v2/Danger/GetByID?id='+xxx,{})
            localStorage.setItem('orgId',d1.OrgCode);
            setDanger(d1);
        }
        getData();
    },[]);

    


    const h1=getBodyH(190)

   const dPanel=()=>{
    return <Descriptions style={{textAlign:'center'}}  column={2}>
        <Descriptions.Item label="事件类型">{danger.DangerType}</Descriptions.Item>
        <Descriptions.Item label="紧急程度">{danger.Level}</Descriptions.Item>
        <Descriptions.Item label="事件来源">{'AI识别'}</Descriptions.Item>
        <Descriptions.Item label="事件状态">{danger.State==2?'已处理':'处理中'}</Descriptions.Item>
        {/* <Descriptions.Item label="主要内容">{danger.Content}</Descriptions.Item> */}
    </Descriptions>
}

const onOK=async()=>{
    danger.State=2
    const xx=await  HPost2('/api/v1/Danger/OK',danger);
    setDanger({...danger})
}


const btn=()=>{
    if(isEmpty(danger)) return null;
    if( danger.State==2) return null;
    return <Button type="primary" onClick={()=>{onOK()}}>标记已处理</Button>
  
}

const getTitleButton=(title)=>{
    return <div><Button type='text' icon={<ArrowLeftOutlined />} onClick={()=>{history.push('/eventList')}}></Button> <span>{title}</span></div>
} 

    return isEmpty(danger)?<div></div>: <div>
  
      
            <Card bordered={true} style={{ width: '100%', height: h1, marginLeft: 8.0 }} title={getTitleButton(danger.Title)} extra={btn()}>
                <div style={{textAlign:'left'}}>
                {dPanel()}
                <Image style={{width: '100%'}}
                src={getImgUrl(danger.ImgObjectName)}/>
                </div>
            </Card>

    

</div>


}

export default DangerDetailPage2;
