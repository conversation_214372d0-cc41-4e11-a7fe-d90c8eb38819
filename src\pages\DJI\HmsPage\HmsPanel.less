.tipsBar {
    color: #e2b12e;
    position: relative;
    padding: 0;
    z-index: 0;
    padding: 4px 20px;
    font-size: 13px;
    margin-top: -10px;
    width: max-content;
    text-shadow: #000 0 0 6px;
}

.tipsBar::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    left: 0;
    top: 50%;
    transform: translateY(-50%) skewX(-15deg);
    z-index: -1;
    border-style: solid;
    border-width: 1px 6px 1px 6px;
    border-image: linear-gradient(45deg, #e2b12e,  #e2b12e) 1;
    background: url('@/assets/img/line.png') center bottom no-repeat, linear-gradient(0deg, rgba(226, 178, 46, 0.176),rgb(70 52 1 / 37%));
    background-size: auto 100%;
    box-shadow: rgba(19, 8, 0, 0.5) 2px 2px 8px;
    backdrop-filter: blur(1px);
}