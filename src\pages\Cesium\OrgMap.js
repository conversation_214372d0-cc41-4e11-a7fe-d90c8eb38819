
import React, { useEffect, useRef, useState } from "react";
import { Cesium, useModel } from "umi";
import {
  Camera,
} from "cesium";
import "./index.css";
import { isEmpty } from "@/utils/utils";
import FeiJi from "@/assets/Cesium_Air.glb";
import { MainColor } from "@/utils/colorHelper";
import treeImg from "@/assets/mapImgs/tree.png";
import DevicePage from "../DJI/DevicePage";
import gbData from "./gbData";
import { allClear } from "./helper";


const DDD = (viewer) => {
  if (isEmpty(viewer)) return;
  const xxx = async () => {
    try {
      const tileset = await Cesium.Cesium3DTileset.fromUrl(
        //"https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/667edb33915b45218945367bbef08b2e/tileset.json"
        //   "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/bd6c5b018dd046b59bcccf42e7ce64d5/tileset.json"
        "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/667edb33915b45218945367bbef08b2e/tileset.json",

        // "https://cce95e19-1f9d-42e1-b6c3-7e0825651a20.oss-cn-chengdu.aliyuncs.com/3d/tileset.json"
        //"http://*************:1281/osgb2/tileset.json", //数据地址
        // "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/ShuYuanDaDao/shuyuandadao/tileset.json"
        {
          // dynamicScreenSpaceError: true,
          // dynamicScreenSpaceErrorDensity: 2.0e-4,
          // dynamicScreenSpaceErrorFactor: 24.0,
          // dynamicScreenSpaceErrorHeightFalloff: 1.0,
          skipLevelOfDetail: true, // 启用
          baseScreenSpaceError: 100, // 基础屏幕错误阈值
          skipScreenSpaceErrorFactor: 16, // 跳过屏幕错误因子
          skipLevels: 1, // 跳过级别
          immediatelyLoadDesiredLevelOfDetail: false, // 是否立即加载所需细节级别
          loadSiblings: false, // 是否加载兄弟节点
          maximumScreenSpaceError: 2.0,
          optimizeForCesium: true,
        }
      );

      viewer.scene.primitives.add(tileset);
      //  viewer.flyTo(tileset);
    } catch (error) {
      console.error(`Error creating tileset: ${error}`);
    }
  };
  xxx();
};

const gbPList = [
  [32.15164983, 105.5380648, 806.843, 60, "庞统柏"],
  // [32.15184261, 105.5383173, 796.547, 80, "羽扇柏"],
  [32.15231467, 105.5387422, 802.727, 90, "汉砖柏"],
  [32.15243525, 105.5388007, 800.925, 110, "帅大柏"],
  [32.15267175, 105.538787, 799.249, 130, "阿斗柏"],
  [32.1530735, 105.5390088, 801.565, 140, "三国鼎立柏"],
  [32.15312128, 105.5390473, 800.778, 180, "剑阁柏"],
  [32.154077, 105.5393033, 801.117, 140, "隆中对柏"],
  [32.15439503, 105.5395044, 799.103, 130, "夫妻柏"],
  [32.15460806, 105.5395194, 798.908, 120, "结义柏"],
  [32.15494061, 105.5396271, 801.673, 100, "张飞柏"],
  [32.15510275, 105.5396425, 800.562, 80, "宋柏"],
  [32.15537361, 105.5396747, 800.613, 60, "张飞植柏"],
];

const bsData = {
  庞统柏: {
    sl: 2300,
    jj: "1233",
    h1: 29,
    r1: 5,
    url: "",
  },
};

const Map3D = ({ h1 }) => {
  // const ref = React.createRef();
  const device = JSON.parse(localStorage.getItem("device"));
  const { pList } = useModel("gpsModel");
  const [open, setIfOpen] = useState(false);
  const [divP, setDivP] = useState({ left: 250, top: 300, title: "庞统柏" });

  const divH1 = 320;
  const divW1 = 240;
  const { setPage } = useModel("pageModel");
  const vX = useRef({});

  let FJEntity;
  const pX = Cesium.Cartesian3.fromDegrees(
    device.Lng,
    device.Lat,
    device.Height
  );
  let pL = [];
  //pL.push(pX)

  // Camera.DEFAULT_VIEW_RECTANGLE = Rectangle.fromDegrees(103.55, 31.02, 103.6, 31.05)
  Camera.DEFAULT_VIEW_FACTOR = 0.0001;

  const getJD = (p1, p2) => {
    //
    // console.log(p1);
    const x1 = p2.x;
    const y1 = p2.y;
    const x2 = p1.x;
    const y2 = p1.y;
    const jd = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;
    // console.log('getJD',jd);
    return jd;
  };

  const getJD2 = () => {
    //  console.log(p1);
    if (pList.current.length < 3) return 0;
    const p1 = getPoint(pList.current[pList.current.length - 1]);
    const p2 = getPoint(pList.current[pList.current.length - 2]);
    return getJD(p1, p2);
  };

  const getPoint = (p) => {
    return Cesium.Cartesian3.fromDegrees(p[1], p[0], p[2]);
  };

  const getGps2 = () => {
    if (pList.current.length == 0)
      return Cesium.Cartesian3.fromDegrees(
        device.Lng,
        device.Lat,
        device.Height
      );
    return getPoint(pList.current[pList.current.length - 1]);
  };

  const getPList = () => {
    //  console.log('getPList',pList.current);
    if (isEmpty(pList.current))
      return [
        Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, device.Height),
      ];
    const list = [];
    pList.current.forEach((e) => {
      list.push(getPoint(e));
    });
    pL = list;
    return list;
  };

  const FJIcon = (viewer) => {
    if (isEmpty(viewer)) return;

    FJEntity = viewer.entities.add({
      name: "gltf",
      position: new Cesium.CallbackProperty(() => {
        const p1 = getGps2();
        return p1;
      }, false),

      orientation: new Cesium.CallbackProperty(() => {
        const jd = getJD2();
        const p1 = getGps2();
        return Cesium.Transforms.headingPitchRollQuaternion(
          p1,
          new Cesium.HeadingPitchRoll(
            Cesium.Math.toRadians(200 - jd),
            Cesium.Math.toRadians(0),
            Cesium.Math.toRadians(0)
          )
        );
      }, false),

      model: {
        uri: FeiJi, // 模型路径
        // show:true,
        scale: 0.5,
        // color:new  Cesium.Color(255, 255, 255, 0.6),
      },
    });
    // viewer.flyTo(FJEntity);
    //viewer.trackedEntity = FJEntity;
  };

  const poiIconLabelAdd = (viewer, lon, lat, height, h2, name, color, url) => {
    // h2=60
    viewer.entities.add({
      name: name,
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height + h2),
      // 图标
      billboard: {
        image: url,
        width: 45,
        height: 45,
      },
      data: name,
      label: {
        //文字标签
        text: name,
        font: "20px sans-serif",
        style: Cesium.LabelStyle.FILL,
        // 对齐方式(水平和竖直)
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(30, -2),
        showBackground: true,
        backgroundColor: new Cesium.Color.fromBytes(0, 70, 24),
      },
    });

    // 先画线后画点，防止线压盖点
    let linePositions = [];
    linePositions.push(new Cesium.Cartesian3.fromDegrees(lon, lat, height));
    linePositions.push(
      new Cesium.Cartesian3.fromDegrees(lon, lat, height + h2)
    );
    viewer.entities.add({
      polyline: {
        positions: linePositions,
        width: 1.5,
        material: new Cesium.PolylineDashMaterialProperty({
          //虚线
          color: Cesium.Color.GREEN,
          dashLength: 20, //短划线长度pixel
          gapColor: Cesium.Color.TRANSPARENT, //空隙颜色
        }),
      },
    });

    // 画点
    const xx = viewer.entities.add({
      // 给初始点位设置一定的离地高度，否者会被压盖
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height),
      point: {
        color: color,
        pixelSize: 15,
      },
    });

    // viewer.flyTo(xx);
  };

  const BiaoZhu = (viewer) => {
    if (isEmpty(viewer)) return;
    gbPList.forEach((e) => {
      poiIconLabelAdd(
        viewer,
        e[1],
        e[0],
        e[2],
        60,
        e[4],
        "green",
        require("@/assets/mapImgs/tree.png")
      );
    });
    poiIconLabelAdd(
      viewer,
      device.Lng,
      device.Lat,
      device.Height,
      60,
      device.DName,
      "green",
      require("@/assets/icons/device.png")
    );

    viewer.scene.fxaa = false;
    viewer.scene.postProcessStages.fxaa.enabled = false;
    if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
      //判断是否支持图像渲染像素化处理
      viewer.resolutionScale = window.devicePixelRatio;
    }
  };

  const FJLine = (viewer) => {
    if (isEmpty(viewer)) return;

    viewer.entities.add({
      // 实体的唯一标识
      id: "myLine",
      // 线条属性
      polyline: {
        // 定义线条的位置
        positions: new Cesium.CallbackProperty(() => {
          const pp = getPList();
          return pp;
        }, false),
        // 线条的宽度
        width: 2,
        // 线条的颜色
        material: Cesium.Color.RED,
      },
    });
  };

  const ZhengShe = async (viewer) => {
    const weather = new Cesium.UrlTemplateImageryProvider({
      url: "/map/v1/Map/GetTile?p1=cyl20240610&z={z}&x={x}&y={reverseY}",
      layer: "your-layer-name",
      style: "default",
      format: "image/png",
      tileMatrixSetID: "EPSG:900913",
    });

    viewer.imageryLayers.addImageryProvider(weather);

    // viewer.imageryLayers.get(0).show = false;
  };

  const ZhengShe2 = (viewer) => {
    const tilesetUrl =
      "http://112.44.103.230:12311/map/v1/Map/GetTile?p1=202303&z={z}&x={x}&y={y}";

    var provider = new Cesium.UrlTemplateImageryProvider({
      url: tilesetUrl,
      layer: "yourLayerName", // 你的图层名称，根据实际情况填写
      style: "default", // 图层风格，根据实际情况填写
      format: "image/png", // 图片格式
      maximumLevel: 18, // 最大级别，根据实际切片数量决定
      minimumLevel: 1, // 最小级别，从1开始
      tilingScheme: new Cesium.WebMercatorTilingScheme(),
      tileMatrixSetID: "default028mm",
    });

    // 将自定义TMS图层添加到Cesium的imagery集合中
    viewer.imageryLayers.addImageryProvider(provider);

    // 可选：如果想要隐藏默认的底图，可以将其设置为不可见
    viewer.imageryLayers.get(0).show = false;
  };

  const ZheZhao = (viewer) => {
    let holeArr = [];
    const cyl = [
      105.53591251, 32.15261623, 105.53411007, 32.15221656, 105.53215742,
      32.15021818, 105.53016186, 32.14984575, 105.52739382, 32.14923715,
      105.52718997, 32.14846503, 105.52725434, 32.14754756, 105.52683592,
      32.14652107, 105.52595615, 32.14532197, 105.52612782, 32.14492227,
      105.52892804, 32.1460487, 105.53104162, 32.14692985, 105.5325222,
      32.14852861, 105.53359509, 32.14888288, 105.53504884, 32.14943245,
      105.53592324, 32.15002288, 105.53667963, 32.14999563, 105.53814411,
      32.14994567, 105.5392921, 32.14946424, 105.54061174, 32.14888288,
      105.54085851, 32.14875571, 105.54097652, 32.14905547, 105.54069757,
      32.14980942, 105.54078341, 32.15085403, 105.54113746, 32.1515353,
      105.54171681, 32.15260715, 105.54193139, 32.15325207, 105.5415076,
      32.15373802, 105.54131448, 32.1542921, 105.54097652, 32.15493247,
      105.5408746, 32.1556137, 105.54093897, 32.15589528, 105.54125011,
      32.15613144, 105.54136276, 32.15627676, 105.54136813, 32.156631,
      105.54115891, 32.15723048, 105.54085851, 32.15782087, 105.54048836,
      32.15822959, 105.54052055, 32.15867919, 105.54062784, 32.15903342,
      105.54054201, 32.15933315, 105.54045618, 32.15953297, 105.54054737,
      32.1598872, 105.54054737, 32.16003252, 105.53973734, 32.16018692,
      105.53911507, 32.16046394, 105.53877175, 32.16060927, 105.5383265,
      32.16046394, 105.53776324, 32.16015968, 105.53747892, 32.15989174,
      105.53739309, 32.15950118, 105.5375433, 32.15896984, 105.53698003,
      32.15826593, 105.53676546, 32.15789353, 105.53684056, 32.15740759,
      105.53688884, 32.15666279, 105.53655088, 32.1563131, 105.53620219,
      32.15599519, 105.53610027, 32.15572724, 105.53616464, 32.1554502,
      105.5365026, 32.15527763, 105.53689957, 32.15498242, 105.53700686,
      32.15465997, 105.53592324, 32.15271615, 105.53588569, 32.15263667,
    ];
    holeArr.push({ positions: Cesium.Cartesian3.fromDegreesArray(cyl) });
    const e1 = viewer.entities.add({
      polygon: {
        // hierarchy: Cesium.Cartesian3.fromDegreesArray([60, 0, 60, 90, 160, 90, 160, 0]),
        hierarchy: {
          // 添加外部区域为1/4半圆，设置为180会报错
          positions: Cesium.Cartesian3.fromDegreesArray([
            60, 0, 60, 90, 160, 90, 160, 0,
          ]),
          // 中心挖空的“洞”
          holes: holeArr,
        },
        material: new Cesium.Color(15 / 255.0, 38 / 255.0, 84 / 255.0, 0.7),
      },
    });
    //   surfaceMaterial = new Cesium.ImageMaterialProperty({
    //     image: require("../../assets/img/map/night_area.png"),    //随便找一张地型贴图
    //     repeat: new Cesium.Cartesian2(1, 1)
    // })

    // 在Viewer中添加贴地线实体
    const e2 = viewer.entities.add({
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray(cyl),
        width: 3.0,
        material: new Cesium.Color(2 / 255.0, 197 / 255.0, 249 / 255.0, 1),
        clampToGround: true, // 确保贴地
      },
    });

    // viewer.flyTo(e2);
  };

  const setEye = (viewer) => {
    var center = Cesium.Cartesian3.fromDegrees(105.53591251, 32.15261623, 2500);
    // const center = Cesium.Cartesian3.fromDegrees(105.31706203454723, 30.06346956642108, 2500);

    viewer.scene.camera.setView({
      destination: center,
      //position:center2,
      orientation: {
        heading: 90,
        pitch: 30,
        roll: 0,
      },
    });
  };
  // 左键双击
  const setDivPosition = (viewer, entity) => {
    //
    const entityPosition = entity.position.getValue(viewer.clock.currentTime);
    const screenPosition = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      viewer.scene,
      entityPosition
    );
    if (screenPosition) {
      let leftOffset = screenPosition.x + 36;
      let topOffset = screenPosition.y - 36;
      //
      setDivP({ top: topOffset, left: leftOffset, title: entity["_data"] });
    }
  };

  const goToDevice = (e) => {
    localStorage.setItem("device", JSON.stringify(e));
    setPage(<DevicePage device={e} />);
  };

  const leftDouBleClick = (viewer) => {
    // 添加用户输入监听范围（element）
    let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

    // 处理用户输入事件
    handler.setInputAction((click) => {
      const pickedObject = viewer.scene.pick(click.position);
      if (isEmpty(pickedObject)) {
        setIfOpen(false);
        return;
      }

      if (Cesium.defined(pickedObject)) {
        // 显示弹框
        //
        if (isEmpty(pickedObject.id)) {
          setIfOpen(false);
        } else {
          if (pickedObject.id["_data"] == device.DName) {
            //
            goToDevice(device);
            return;
          }
          setDivPosition(viewer, pickedObject.id);
          setIfOpen(true);
        }
        //  const nm=pickedObject.id['_data'];

        //  setIfOpen(true);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  };

  useEffect(() => {
    const imgP = new Cesium.UrlTemplateImageryProvider({
      url: "http://112.44.103.230:12311/map/v1/Map/GetTile?p1=cyl20240610&z={z}&x={x}&y={y}",
      tilingScheme: new Cesium.GeographicTilingScheme(),
      maximumLevel: 20,
    });

    const tms = new Cesium.UrlTemplateImageryProvider({
      url: "http://thematic.geoq.cn/arcgis/rest/services/ThematicMaps/WorldHydroMap/MapServer/tile/{z}/{y}/{x}",
      // tilingScheme : new Cesium.GeographicTilingScheme(),
      maximumLevel: 20,
    });

    const tms2 = Cesium.ImageryLayer(
      new Cesium.UrlTemplateImageryProvider({
        url: "http://112.44.103.230:12311/map/v1/Map/GetTile?p1=202303&z={z}&x={x}&y={y}",
        tilingScheme: new Cesium.GeographicTilingScheme(),
        maximumLevel: 20,
      })
      // {...layerOption}
    );
    const viewer2 = new Cesium.Viewer("cesiumContainer", {
      terrain: Cesium.Terrain.fromWorldTerrain(),
      infoBox: false,
      imageryProvider: null,
      baseLayerPicker: false,
      sceneModePicker: false,
      homeButton: true,
      fullscreenButton: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      animation: false,
      geocoder: false,
      shouldAnimate: true,
      sceneMode: 3,
    });

    DDD(viewer2);
    // ZhengShe(viewer2);
    // ZheZhao(viewer);

    FJIcon(viewer2);
    FJLine(viewer2);
    BiaoZhu(viewer2);
    setEye(viewer2);
    leftDouBleClick(viewer2);
    vX.current = viewer2;

    return () => {
      allClear(vX.current);
    };
  }, []);

  const divPanel = () => {
    // return <div/>
    const col1 = "rgba(80,60,185,0.8)";
    if (open) {
      //
      return (
        <div
          style={{
            userSelect: "none",
            background: MainColor,
            width: divW1,
            position: "absolute",
            zIndex: 1000,
            top: divP.top,
            left: divP.left,
          }}
        >
          <div
            style={{
              height: 36.0,
              paddingTop: 2.0,
              background: "rgba(255,255,255,0.5)",
              fontWeight: "bold",
              fontFamily: "MiSan",
              fontSize: 18.0,
            }}
          >
            <span>
              <img src={treeImg} height={32} width={32}></img>
            </span>
            <span style={{ marginLeft: 8.0 }}>{divP.title}</span>
          </div>
          <div
            style={{
              color: "white",
              lineHeight: "150%",
              fontFamily: "MiSan",
              margin: 8.0,
            }}
          >
            {gbData[divP.title].content}{" "}
          </div>
        </div>
      );
    }
  };

  return (
    <div>
      {" "}
      <div
        style={{ height: h1, width: "100%", visibility: open }}
        id="cesiumContainer"
      ></div>
      {divPanel()}
    </div>
  );
};

export default Map3D;
