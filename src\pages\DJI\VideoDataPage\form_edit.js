import { Input, Card, Tag, DatePicker, Radio, Descriptions, Select, Row, Col, Button, Form, Modal, message, Table, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';


const { TextArea } = Input;
const WayLineTypeList = ["现场巡查", "路标巡查", "边坡巡查"]


const WayLineEditForm = ({w1,refrush}) => {


 // console.log('www', props);
 // const refrush = props.data;

  const [wType, setWType] = useState('');
  const [wName, setWName] = useState(w1.WayLineName);
  const [canSee, setCanSee] = useState(false);


  const onFormChange = (e) => {
    console.log(e)
  }

  const Save=async ()=>{
     const xx=await Post2("/api/v1/WayLine/Update",{...w1,WayLineName:wName})
     setCanSee(false);
     refrush();
  }
  const getTypeList = () => {
    const list = []
    WayLineTypeList.map(p => {
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }


  return<div><a  onClick={()=>setCanSee(true)}>编辑</a> <Modal  title={null} footer={null} onOk={null} open={canSee}  onCancel={()=>setCanSee(false)}>
  <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}

    >

      <Descriptions title="航线编辑" column={1}  colon={false} >
       

        <Descriptions.Item label="航线名称"><Input defaultValue={wName} onChange={(e) => { setWName(e.target.value) }} /></Descriptions.Item>

       
        <Descriptions.Item  >
          <Button type='primary' onClick={()=>{Save()}} style={{marginLeft:64.0}}>
            保存
          </Button>
      </Descriptions.Item>
 

      </Descriptions>


      </div>
      </Modal>  
    </div>




}



export default WayLineEditForm;