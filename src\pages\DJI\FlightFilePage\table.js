import { Space, Tag, message,Modal,Switch } from 'antd';
import { downloadFile, getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { getGuid, timeFormat, timeFormat2 } from '@/utils/helper';
import { HGet2, HPost2 } from '@/utils/request';
import { Post2 } from '@/services/general';
import {handleBatchDownload2} from '@/utils/filezip';
import DownLoadBtn from './DownLoadBtn';

const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const { confirm } = Modal;

const toFly=async(record)=>{
  console.log('toFly')
 await HGet2("/api/v1/WayLine/Fly?fID="+record.WanLineId)

 
}

const onChange=(e)=>{
  console.log('ddd',e);
  if(e){
    updateCron()
  }
}

const updateCron=async(record,refrush)=>{
  if(isEmpty(record)) return;

  const xx=await HGet2("/api/v1/FlightTaskFile/ChangeState?id="+record.ID);

  console.log('UpdateFlightTaskFile',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("更新成功！")
    refrush();
  }
}

const deleteFlightTaskFile=async(record,refrush)=>{
 
  
  const xx=await Post2("/api/v1/FlightTaskFile/Delete",record);

  console.log('deleteFlightTaskFile',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}



const downloadTaskFile2 = async (record) => {

  const list = []
  const sL = record.TaskID.split(",");
  for (let i = 0; i < sL.length; i++) {
    if (sL[i].length > 5) {
      const mList = await HGet2(`/api/v1/Media/GetListByTaskId?id=${sL[i]}`);
      if (!isEmpty(mList)) {
        list.push(...mList);
      }
    }
  }
  const qianz=record.FDepartment+"-"+record.FName+"-"+record.FTime
  handleBatchDownload2(list,qianz)
  return list;

}


const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除记录',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该记录吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteFlightTaskFile(record,refrush);
    },
    onCancel() {
     // console.log('Cancel');
    },
  });
};

const TableCols =(refrush,editForm,downloadTaskFile)=>{return [

  {
    title: getTableTitle('单位'),
    dataIndex: 'FDepartment',
    key: 'FDepartment',
    align:'center',
  },

          {
            title: getTableTitle('时间'),
            dataIndex: 'FTime',
            key: 'FTime',
            align:'center',
           // width:200,
          },
          {
            title: getTableTitle('飞行任务'),
            dataIndex: 'FName',
            key: 'FName',
            align:'center',
            width:300,
          },
          {
            title: getTableTitle('相关函告'),
            dataIndex: 'FPaper',
            key: 'FPaper',
            align:'center',
           // width:200,
          },
          {
            title: getTableTitle('飞行时长(分钟)'),
            dataIndex: 'FlyTime',
            key: 'FlyTime',
            align:'center',
            render:(record) => (Number(record).toFixed(0)),
           // width:200,
          },

          {
            title: getTableTitle('录制时长(分钟)'),
            dataIndex: 'RecordTime',
            key: 'RecordTime',
            align:'center',
            render:(record) => (Number(record).toFixed(0)),
           // width:200,
          },
          {
            title: getTableTitle('消耗流量(G)'),
            dataIndex: 'UsedNet',
            key: 'UsedNet',
            align:'center',
            render:(record) => (Number(record).toFixed(1)),
           // width:200,
          },
          {
            title: getTableTitle('飞行面积(平方公里)'),
            dataIndex: 'FlyArea',
            key: 'FlyArea',
            align:'center',
            render:(record) => (Number(record).toFixed(1)),
           // width:200,
          },
          {
            title: getTableTitle('预计费用(元)'),
            dataIndex: 'FlyCost',
            key: 'FlyCost',
            align:'center',
            render:(record) => (Number(record).toFixed(0)),
           // width:200,
          },
          {
            title: getTableTitle('影像(视频、图片)'),
            dataIndex: 'Media',
            key: 'Media',
            align:'center',
            render:(record) => ((record).replaceAll("：",":")),
           // width:200,
          },
          // {
          //   title: getTableTitle('备注'),
          //   dataIndex: 'Remark',
          //   key: 'Remark',
          //   align:'center',
          //  // width:200,
          // },
        
        {
            title: getTableTitle('操作'),
            align:'center',
            render: (record) => (
              <Space size="middle">
                {/* <Tag><a onClick={()=>showMap(record)}>航线点位</a></Tag> */}
                <Tag><a onClick={()=>editForm(record)}>编辑</a></Tag>
                <Tag><DownLoadBtn record={record}/></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除</a></Tag>
              </Space>)
          }];
        }



export default TableCols;
