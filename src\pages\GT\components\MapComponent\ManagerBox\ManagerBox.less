.ManagerBox{
   display: flex;
   justify-content: center;
   align-items: center;
  transform: all 0.5s;
}
.ManagerBox_flex{
    width: 350px;
    color:#fff;
    position: absolute;
    top:0;
    left: 0;
    margin-left: 70px;
    background: rgba(97, 97, 97, 0.374);
    border:1px solid #1abc9c;
}
.ManagerBox_title{
    padding: 10px;
    border-bottom: 1px solid #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.ManagerBox_content{
    padding-left: 10px;
    display: flex;
    font-size: 12px;
}
.ManagerBox_left{
    flex: 3;
    padding: 8px;
}
.ManagerBox_left_item{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin:10px 0;
}
.ManagerBox_right{
    flex: 1;
    border-left: 1px solid #fff;
}
.ManagerBox_right_item{
    padding: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    border-bottom: 1px solid #fff;

}
.ManagerBox_right_item:last-child {
    border-bottom: none;
}
.ManagerBox_right_item:hover{
    background: rgba(3, 3, 3, 0.582);
}