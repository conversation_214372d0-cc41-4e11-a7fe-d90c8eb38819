# DynamicBackgroundMask 动态背景遮罩组件

## 概述

`DynamicBackgroundMask` 是一个通用的背景遮罩组件，用于为页面添加 `dynamicBackgroundMask.png` 背景图片效果。该组件将原本分散在各个页面中的背景图片样式进行了统一封装，便于维护和重用。

## 功能特性

- 📦 **开箱即用**：简单导入即可使用，无需额外配置
- 🎨 **样式统一**：统一管理背景图片样式，保证视觉一致性
- 🔧 **高度可定制**：支持自定义样式类名和内联样式
- 🚀 **轻量级**：组件体积小，对性能影响微乎其微
- 📱 **响应式**：自适应容器大小，支持不同屏幕尺寸

## 安装使用

### 基础用法

```jsx
import DynamicBackgroundMask from '@/components/DynamicBackgroundMask';

function MyPage() {
  return (
    <div style={{ position: 'relative', height: '100vh' }}>
      {/* 页面内容 */}
      <div>我的页面内容</div>
      
      {/* 添加背景遮罩 */}
      <DynamicBackgroundMask />
    </div>
  );
}
```

### 自定义样式

```jsx
import DynamicBackgroundMask from '@/components/DynamicBackgroundMask';

function MyPage() {
  return (
    <div style={{ position: 'relative', height: '100vh' }}>
      <div>我的页面内容</div>
      
      {/* 使用自定义样式 */}
      <DynamicBackgroundMask 
        className="my-custom-mask"
        style={{ opacity: 0.8, zIndex: -2 }}
      />
    </div>
  );
}
```

### 在遮罩层上添加内容

```jsx
import DynamicBackgroundMask from '@/components/DynamicBackgroundMask';

function MyPage() {
  return (
    <div style={{ position: 'relative', height: '100vh' }}>
      <div>我的页面内容</div>
      
      {/* 在遮罩层上添加额外内容 */}
      <DynamicBackgroundMask>
        <div className="overlay-content">
          遮罩层上的内容
        </div>
      </DynamicBackgroundMask>
    </div>
  );
}
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| className | string | '' | 额外的样式类名 |
| style | React.CSSProperties | {} | 自定义内联样式 |
| children | React.ReactNode | - | 子组件（在遮罩层上显示的内容） |
| ...restProps | any | - | 其他 HTML div 元素属性 |

## 重要说明

### 1. 容器要求
使用该组件的父容器必须设置 `position: relative`，确保遮罩层能够正确定位。

```css
.parent-container {
  position: relative;
  /* 其他样式 */
}
```

### 2. z-index 层级
默认情况下，组件的 z-index 设置为 -1，确保背景在内容下方。如需调整层级，可通过 style 属性覆盖：

```jsx
<DynamicBackgroundMask style={{ zIndex: -2 }} />
```

### 3. 图片路径
组件内部使用的图片路径为 `@/pages/SI/assets/image/dynamicBackgroundMask.png`，请确保该文件存在。

## 已替换的原有实现

该组件已替换了以下文件中的原有 backgroundImage 实现：

- `src/pages/SI/layouts/index.js` 和 `index.less`
- `src/pages/SI/DynamicMonitor/index.js` 和 `index.less`
- `src/pages/SI/ControlCenter/index.js` 和 `index.module.less`
- `src/pages/DJI/OrgPage/JiaoTongHome.js` 和 `indexPage.css`

## 文件结构

```
src/components/DynamicBackgroundMask/
├── index.js              # 主组件文件
├── index.module.less      # 样式文件
├── index.d.ts            # TypeScript 声明文件
└── README.md             # 使用说明文档
```

## 维护说明

- 如需修改背景图片，只需更新 `index.module.less` 中的图片路径
- 如需调整默认样式，修改 `.backgroundMask` 类的样式定义
- 添加新功能时，请同步更新 TypeScript 声明文件 