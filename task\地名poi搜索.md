# Task: 地名POI搜索功能实现

## 📋 基本信息
- **优先级**: High
- **状态**: Pending
- **预估工时**: 8小时
- **创建时间**: 2024-12-19

## 🔗 依赖关系
- **前置任务**: 无
- **后置任务**: 地图功能优化相关任务

## 📝 任务描述

在 `src/pages/GT/YZT/pages/Map/components/ToolBox/index.js` ToolBox组件中新增地名POI搜索功能，为用户提供便捷的地点搜索和定位服务。

### 核心功能
1. **搜索界面**: 点击"地点搜索"按钮后显示搜索框，支持地点名称和省份/城市（可选）输入
2. **结果展示**: 查询结果列表显示，最多显示5个结果
3. **地图标注**: 在地图上添加markers标记搜索结果位置
4. **坐标转换**: 通过 `GetDiTuGps` 判断是否需要将高德API返回的坐标转换为WGS84
5. **地图兼容**: 兼容 Leaflet (2DMap.js) 和 Cesium (3DMap.js) 两种地图引擎
6. **交互功能**: 点击结果可跳转到对应位置，显示地名tooltip
7. **历史记录**: 保存搜索历史，便于用户快速访问
8. **清除功能**: 支持清除搜索结果和地图markers

## ✅ 验收标准
- [ ] 在ToolBox组件中成功集成地点搜索功能
- [ ] 搜索框UI/UX设计符合专业标准（使用@21st-dev/magic设计）
- [ ] 搜索结果最多显示5个，按距离排序
- [ ] 搜索防抖处理，避免频繁请求
- [ ] 加载状态显示loading效果
- [ ] 空状态显示"没有查询到相关地点"
- [ ] 搜索历史功能正常保存和展示
- [ ] 错误处理机制完善，用户体验良好
- [ ] 地图markers样式美观，tooltip信息完整
- [ ] 坐标转换功能正确，兼容不同地图引擎
- [ ] 清除功能正常，可清空所有搜索相关内容

## 🛠️ 技术要求

### 核心依赖
```js
import { GetDiTuGps } from "@/pages/Maps/ditu";
import { gcj02Towgs84 } from "@/pages/Maps/gps_helper";
import { HGet2 } from "@/utils/request";
```

### API接口
- **搜索接口**: `/api/v2/Map/GetPlace2?p1=${keyword}&p2=${region}`
- **高德API**: `https://restapi.amap.com/v5/place/text?keywords=${p1}&region=${p2}&key=${apiKey}&city_limit=true`

### 技术栈
- **UI组件**: React + Antd + @21st-dev/magic
- **地图引擎**: Leaflet + Cesium
- **状态管理**: React Hooks + localStorage
- **样式方案**: Less Modules + Tailwind CSS

### 设计规范
- **搜索框**: 使用@21st-dev/magic设计初版UI
- **Marker样式**: 常见地图标记样式，支持编号显示
- **响应式设计**: 移动端友好
- **交互动效**: 平滑过渡和反馈

## 📚 相关文档
- [高德地图POI搜索API文档](https://amap.apifox.cn/api-14645146)
- [Leaflet Marker文档](https://leafletjs.com/reference.html#marker)
- [Cesium Entity文档](https://cesium.com/learn/cesiumjs/ref-doc/Entity.html)


## 🔧 技术实现细节

### 架构设计（独立可复用组件）

基于复用性需求和NFZButton的成功模式，采用独立组件架构设计：

### 组件结构设计
```
src/components/PlaceSearchButton/
├── index.js (主组件 - 独立可复用)
├── components/
│   ├── SearchPanel.js (搜索面板)
│   ├── ResultsList.js (结果列表)
│   └── SearchHistory.js (搜索历史)
├── hooks/
│   └── usePlaceSearch.js (搜索逻辑Hook)
├── utils/
│   └── markerManager.js (标记管理器)
└── index.module.less (样式文件)
```

### 复用现有工具函数
```js
// 复用现有的工具函数，避免重复开发
import { detectMapEngine, isMapReady } from '@/utils/mapEngineDetector'
import { GetDiTuGps } from '@/pages/Maps/ditu'
import { gcj02Towgs84 } from '@/pages/Maps/gps_helper'
import { HGet2 } from '@/utils/request'
```

### 核心组件设计
```js
// src/components/PlaceSearchButton/index.js
const PlaceSearchButton = ({
  mapInstance,              // 地图实例（Leaflet或Cesium）
  onPlaceSelect,           // 地点选择回调
  onSearchResults,         // 搜索结果回调
  position = 'top-left',   // 按钮位置
  maxResults = 5,          // 最大结果数
  enableHistory = true,    // 是否启用历史记录
  customStyle = {},        // 自定义样式
  className = '',          // 自定义类名
  size = 'large'           // 按钮大小
}) => {
  // 组件内部状态管理
  const [isSearchMode, setIsSearchMode] = useState(false)
  const [searchResults, setSearchResults] = useState([])
  const [searchHistory, setSearchHistory] = useState([])
  const [loading, setLoading] = useState(false)
  const [markers, setMarkers] = useState([])
  
  // 地图引擎检测和适配
  const mapEngineRef = useRef(null)
  
  // 自包含的搜索逻辑
  const handleSearch = useCallback(async (keyword, region) => {
    // 搜索API调用和结果处理
  }, [])
  
  // 地图标记管理（兼容多种引擎）
  const addMarkersToMap = useCallback((results) => {
    // 自动检测地图引擎类型并添加标记
  }, [])
  
  return (
    <div style={getPositionStyle()}>
      {/* 搜索按钮和面板 */}
    </div>
  )
}
```

### 数据处理和转换
```js
// 格式化搜索结果
const formatSearchResults = useCallback((pois) => {
  if (!pois || pois.length === 0) return []
  
  return pois.slice(0, 5).map((poi, index) => {
    const location = poi.location.split(',')
    const lng = parseFloat(location[0])
    const lat = parseFloat(location[1])
    
    return {
      id: poi.id || `place_${index}`,
      name: poi.name,
      address: poi.address,
      pname: poi.pname,
      cityname: poi.cityname,
      adname: poi.adname,
      lng,
      lat,
      distance: poi.distance || 0,
      index: index + 1
    }
  })
}, [])

// 搜索防抖处理
const debouncedSearch = useCallback(
  debounce(async (keyword, region) => {
    if (!keyword.trim()) return
    
    setPlaceSearchLoading(true)
    try {
      const data = await HGet2(`/api/v2/Map/GetPlace2?p1=${keyword}&p2=${region}`)
      
      if (data?.pois && data.pois.length > 0) {
        const results = formatSearchResults(data.pois)
        setPlaceSearchResults(results)
        
        // 保存搜索历史
        saveSearchHistory(keyword, region)
        
        // 添加地图标记
        if (mapRef.current) {
          mapRef.current.addPlaceMarkers(results)
        }
        
        message.success(`找到 ${results.length} 个相关地点`)
      } else {
        setPlaceSearchResults([])
        message.info('没有查询到相关地点')
      }
    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败: ' + error.message)
      setPlaceSearchResults([])
    } finally {
      setPlaceSearchLoading(false)
    }
  }, 500),
  []
)
```

### 搜索历史管理
```js
// 搜索历史相关方法
const saveSearchHistory = useCallback((keyword, region) => {
  const historyItem = {
    id: Date.now(),
    keyword,
    region,
    timestamp: new Date().toISOString()
  }
  
  const currentHistory = JSON.parse(localStorage.getItem('placeSearchHistory') || '[]')
  const newHistory = [historyItem, ...currentHistory.slice(0, 9)] // 保留最近10条
  
  localStorage.setItem('placeSearchHistory', JSON.stringify(newHistory))
  setPlaceSearchHistory(newHistory)
}, [])

const loadSearchHistory = useCallback(() => {
  const history = JSON.parse(localStorage.getItem('placeSearchHistory') || '[]')
  setPlaceSearchHistory(history)
}, [])

const clearSearchHistory = useCallback(() => {
  localStorage.removeItem('placeSearchHistory')
  setPlaceSearchHistory([])
  message.success('搜索历史已清除')
}, [])
```

### API参数说明
- **keywords**: 地点关键字，多个关键字用"|"分割，最大80字符
- **region**: 搜索区域，可选参数，支持citycode、adcode、cityname
- **city_limit**: true (限制在城市内搜索)

### 错误处理机制
```js
// 统一错误处理
const handleSearchError = useCallback((error, context) => {
  console.error(`${context}失败:`, error)
  
  let errorMessage = '操作失败'
  if (error.message) {
    errorMessage = error.message
  } else if (error.response?.data?.message) {
    errorMessage = error.response.data.message
  }
  
  message.error(`${context}: ${errorMessage}`)
  setPlaceSearchLoading(false)
}, [])

// 网络状态检查
const checkNetworkStatus = useCallback(() => {
  if (!navigator.onLine) {
    message.warning('网络连接异常，请检查网络设置')
    return false
  }
  return true
}, [])
```

## 📋 参考实现

### 现有代码参考
**文件**: `src/pages/DJI/OrgPage/Panels/MeasurePanel.js`
**函数**: `onSearch`, `searchPlace`

```js
// API调用示例
import { HGet2 } from "@/utils/request";
const data = await HGet2(`/api/v2/Map/GetPlace2?p1=${keyword}&p2=${region}`);

// 坐标转换示例
import { GetDiTuGps } from "@/pages/Maps/ditu";
import { gcj02Towgs84 } from "@/pages/Maps/gps_helper";

if (GetDiTuGps(mapUrl)) {
  const [lat, lng] = gcj02Towgs84(originalLng, originalLat);
}
```

### 后端接口信息
**接口**: `MapGetPlace2`
**高德API**: `https://restapi.amap.com/v5/place/text`
**参数**:
- `keywords`: 地点关键字
- `region`: 搜索区域
- `city_limit`: true (限制在城市内)

### 使用方式
```js
// 在任意地图页面中使用
import PlaceSearchButton from '@/components/PlaceSearchButton'

// 基础使用
<PlaceSearchButton
  mapInstance={mapRef.current}
  position="top-left"
  onPlaceSelect={(place) => {
    console.log('选中地点:', place)
  }}
/>

// 高级配置
<PlaceSearchButton
  mapInstance={mapRef.current}
  position="top-left"
  maxResults={5}
  enableHistory={true}
  customStyle={{ top: '80px' }}
  onPlaceSelect={(place) => {
    // 自定义地点选择处理
    console.log('选中地点:', place)
  }}
  onSearchResults={(results) => {
    // 自定义搜索结果处理
    console.log('搜索结果:', results)
  }}
/>
```

### ToolBox集成方案
```js
// 在ToolBox中集成PlaceSearchButton
const ToolBox = ({ mapInstance, ...otherProps }) => {
  const [showPlaceSearch, setShowPlaceSearch] = useState(false)
  
  const menuItems = [
    // ... 现有菜单项
    {
      key: 'place-search',
      label: (
        <div className={`${styles.toolboxMenuItem} ${showPlaceSearch ? styles.active : ''}`}>
          <span className={styles.menuItemText}>地点搜索</span>
          {showPlaceSearch && <span className={styles.menuItemStatus}>已启用</span>}
        </div>
      ),
      onClick: () => setShowPlaceSearch(!showPlaceSearch)
    }
  ]
  
  return (
    <>
      <Dropdown menu={{ items: menuItems }}>
        {/* ToolBox按钮 */}
      </Dropdown>
      
      {/* 条件渲染PlaceSearchButton */}
      {showPlaceSearch && (
        <PlaceSearchButton
          mapInstance={mapInstance}
          position="top-left"
          onPlaceSelect={(place) => {
            // 处理地点选择
          }}
          onClose={() => setShowPlaceSearch(false)}
        />
      )}
    </>
  )
}
```

## 🔄 架构优势

### 独立组件架构优势

- **跨页面复用**: 可在任意地图页面直接使用，无需重复开发
- **统一体验**: 所有页面的搜索功能保持一致的交互和样式
- **快速集成**: 只需传入地图实例即可使用

#### 2. 自包含设计
- **API集成**: 组件内部处理所有搜索相关的API调用
- **状态管理**: 独立的状态管理，不依赖外部状态
- **错误处理**: 内置完善的错误处理和用户反馈机制

#### 3. 地图引擎兼容
- **自动检测**: 自动识别Leaflet或Cesium地图引擎
- **统一接口**: 提供统一的标记添加/清除接口
- **坐标转换**: 自动处理不同坐标系的转换

#### 4. 灵活配置
- **位置可配**: 支持top-left、top-right、bottom-left、bottom-right
- **样式定制**: 支持自定义样式和类名
- **功能开关**: 可选择启用/禁用历史记录等功能

### 与现有架构对比

| 特性 | 独立组件方案 | ToolBox集成方案 |
|------|-------------|----------------|
| **复用性** | ✅ 高 - 任意页面可用 | ❌ 低 - 需重复实现 |
| **维护性** | ✅ 高 - 集中维护 | ❌ 低 - 分散维护 |
| **一致性** | ✅ 高 - 统一体验 | ⚖️ 中 - 依赖实现 |
| **开发效率** | ✅ 高 - 快速集成 | ❌ 低 - 重复开发 |
| **测试性** | ✅ 高 - 独立测试 | ❌ 低 - 依赖环境 |

## 🔄 更新记录
- 2024-12-19 - 初始需求文档创建
- 2024-12-19 - 完善技术实现方案和验收标准
- 2024-12-19 - 新增复用性架构设计方案（参考NFZButton模式）

