
import  {  useState, useEffect, useRef } from 'react';

//import img1 from "./img/0a7d370ae1f2843d945ccecddebb176.jpg";
//import img2 from "./img/222.jpg";
import { Col, Row } from 'antd';
import {InitImgCanvas} from './canvas_helper';
import { getBodyH, isEmpty } from '@/utils/utils';
import { getGuid } from '@/utils/helper';

const ImgLookPage=({img1})=>{
    const c1 =useRef(null);

    const Img1=useRef(null);
 
    const [w1,setW1]=useState(0)
    const [h1,setH1]=useState(0)
    const scale=useRef(1.0);
    const dx=useRef(0);
    const dy=useRef(0);
    const [p1,setP1]=useState([])
    const [isDragging, setIsDragging] = useState(false);
    const setData1=(ctx,img,w,h)=>{
            c1.current=ctx;
            Img1.current=img;
            setW1(w)
            setH1(h)
    }


    useEffect(() => {
        InitImgCanvas("imgLookPanel",img1,setData1)
    }, [img1]);


    const drawPanel=()=>{
        
        if(c1.current==null) return;
        c1.current.clearRect(0, 0, w1, h1);
        c1.current.drawImage(Img1.current, dx.current, dy.current, Img1.current.width * scale.current, Img1.current.height * scale.current, 0, 0, w1, h1);
     
    }

    const onMouseDown=(e)=>{
         setP1([e.clientX,e.clientY]);
        setIsDragging(true)
     }
 
     const handleMouseMove = (event) => {
        if (!isDragging) return;
        let dx2=event.movementX;
        let dy2=event.movementY;
        dx2=dx.current-dx2
        dy2=dy.current-dy2
        if(dx2<0) dx2=0;
        if(dy2<0) dy2=0;
        if(dx2>(1-scale.current)*Img1.current.width)dx2=(1-scale.current)*Img1.current.width;
        if(dy2>(1-scale.current)*Img1.current.height)dy2=(1-scale.current)*Img1.current.height;
        dx.current=dx2;
        dy.current=dy2;
        setP1([]);
        drawPanel();
      };
      
     const onMouseUp=(e)=>{
        // console.log(e);
        setIsDragging(false);
        return;
     }



     const HandleWheel=(e)=>{
         const y2=e.deltaY/5000
         
         let y3=y2+scale.current;

         if(y3>1) y3=1;
         if(y3<0.1) y3=0.1;
         scale.current=y3;
         console.log('HandleWheel',e.deltaY,scale.current)
         drawPanel();
     }

    return <div  style={{height:getBodyH(120),width:'80%'}}>


            <canvas  style={{height:'100%',width:'100%',padding:12.0}} id={'imgLookPanel'}  onMouseDown={onMouseDown}  onMouseUp={onMouseUp} onMouseMove={handleMouseMove} onWheel={HandleWheel}></canvas>



        </div>

}

export default ImgLookPage;