import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Row, Col, Tooltip, ConfigProvider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import './index.less';
import { useModel } from 'umi';
import locale from 'antd/locale/zh_CN';
import { queryPage } from '@/utils/MyRoute';
import getMenuItem from '@/pages/SI/utils/getMenuItem';
// 组件
import MyMenu from '@/pages/SI/AIAlgorithm/MyMenu/index';
import HeadTabs from '@/pages/SI/components/HeadTabs';

// 组件
import ModelListPage from '@/pages/DJI/AIPage/ModelListPage3';
import TargetRecognition from '@/pages/GT/ZZCH/Pages/TaskManagement/Pages/TargetRecognition'
import DangerListPage from "@/pages/DJI/DangerPage";

// 导入自定义SVG图标
import aistoreSVG from '@/pages/SI/assets/image/aistore.svg';


// 算法仓菜单项
const menuItems = [
  getMenuItem('AI算法仓', 'AI算法仓',  <img src={aistoreSVG} alt="AI算法仓" style={{ width: '16px', height: '16px' }} />),
  getMenuItem('目标识别', '目标识别', <img src={aistoreSVG} alt="目标识别" style={{ width: '16px', height: '16px' }} />),
  getMenuItem('变化检测', '变化检测', <img src={aistoreSVG} alt="变化检测" style={{ width: '16px', height: '16px' }} />),
  getMenuItem('语义分割', '语义分割', <img src={aistoreSVG} alt="变化检测" style={{ width: '16px', height: '16px' }} />),
  getMenuItem('事件处理', '事件处理', <img src={aistoreSVG} alt="事件处理" style={{ width: '16px', height: '16px' }} />)
];

// 子页面映射表
const subPageMap = {
  AI算法仓: <ModelListPage isSipage={true} />,
  目标识别: <TargetRecognition  taskType='目标识别'/>,
  变化检测: <TargetRecognition  taskType='变化检测'/>,
  语义分割: <TargetRecognition  taskType='语义分割'/>,
  事件处理: <DangerListPage/>,
};

const AIAlgorithm = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { page, setPage } = useModel('pageModel');

  // 初始化页面
  useEffect(() => {
    setPage(subPageMap['AI算法仓']);
  }, []);

  // 处理菜单点击
  const handleMenuSelect = key => {
    // 设置当前页面
    setPage(subPageMap[key]);
  };
  // 处理标签页切换
  const handleTabChange = key => {
    if (key) {
      setPage(subPageMap[key]);
    } else {
      console.warn('[ControlCenter] 未找到标签页key对应的标题:', key);
    }
  };

  return (
    <div style={{ height: '100%', width: '100vw', overflow: 'hidden', position: 'relative' }}>
      {/* 使用原有的MyMenu组件 */}
      <MyMenu
        
        handlePageChange={handleMenuSelect}
        setCollapsed={setCollapsed}
        collapsed={collapsed}
        menuItems={menuItems}
      />

      {/* 主内容区域 */}
      <div
        style={{
          marginLeft: collapsed ? 75 : 75, // 这儿的宽度需要根据MyMenu的width来调整
          transition: 'margin-left 0.2s',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <ConfigProvider locale={locale}>
          {/* <HeadTabs handletabChange={handleTabChange} menuItems={menuItems} /> */}

          <div style={{ height: '100%', width: '100%', overflow: 'hidden' }}>{page}</div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default AIAlgorithm;
