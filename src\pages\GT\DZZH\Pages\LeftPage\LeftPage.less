.curtainNavBox{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    width: 500px;
}
.curtainNav {
    margin: 0 5px;
    color: #fff; /* 字体颜色 */
   
    padding: 10px; /* 内边距 */
}

.curtainNav > div {
    display: inline-block; /* 使 div 在一行中排列 */
}

.curtainNav > div:not(:last-child)::after {
    content: '|'; /* 添加竖线 */
    margin-left: 30px; /* 左边距 */
    color: #fff; /* 竖线颜色 */
}

.curtainNavClick{
    background-color: #333; /* 背景颜色 */
    color: rgb(255, 191, 0);
}