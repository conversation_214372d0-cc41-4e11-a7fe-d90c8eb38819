// 火情图标
export function huoqing(props) {
  let { width, height, fillColor } = props;
  return (
    <svg
      t="1733392554017"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="4243"
      width={width ? width : 200}
      height={height ? height : 200}
    >
      <path
        d="M512 1024C229.602358 1024 0 794.279074 0 511.946105 0 229.591579 229.656253 0 512 0S1024 229.656253 1024 512C1024 794.397642 794.343747 1024 512 1024z m0-961.988716c-248.260716 0-449.988716 201.674105-449.988716 449.934821A450.279747 450.279747 0 0 0 512 961.934821c248.260716 0 449.988716-201.728 449.988716-449.988716S760.260716 62.011284 512 62.011284z m-45.002105 750.538105a134.251789 134.251789 0 0 1-44.430821-162.309389 83.299705 83.299705 0 0 0 63.779031 50.229895 142.907284 142.907284 0 0 1 26.171284-172.937432c0 78.222821 108.080505 132.376253 108.080506 176.753179a302.306358 302.306358 0 0 1-31.85179 107.293642 215.384926 215.384926 0 0 0 179.771284-156.510316 193.191074 193.191074 0 0 1-73.512421 55.985853 163.268716 163.268716 0 0 0 73.447748-136.245895c0-44.376926-29.006147-60.814821-45.390148-84.032673-16.448674-23.153179 0-65.643789 0-65.64379a63.779032 63.779032 0 0 0-33.845894 59.898611 211.698526 211.698526 0 0 1-22.204632 63.714358c0-162.298611-78.265937-253.154358-78.265937-289.845895a160.487747 160.487747 0 0 1 29.006148-81.176253 276.307537 276.307537 0 0 0-240.586106 289.89979c-27.130611-14.508463-30.935579-73.436968-30.935579-73.436969a241.491537 241.491537 0 0 0-49.324463 276.372211 504.594863 504.594863 0 0 1-55.080421-33.856674 214.468716 214.468716 0 0 0 222.218779 175.836969h2.953432z"
        fill={fillColor ? fillColor : "#FF0000"}
        p-id="4244"
      ></path>
    </svg>
  );
}

// 火情图标
export let huoqingIcon = `<svg
    t="1733392554017"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="4243"
    width="20"
    height="20"
  >
    <path
      d="M512 1024C229.602358 1024 0 794.279074 0 511.946105 0 229.591579 229.656253 0 512 0S1024 229.656253 1024 512C1024 794.397642 794.343747 1024 512 1024z m0-961.988716c-248.260716 0-449.988716 201.674105-449.988716 449.934821A450.279747 450.279747 0 0 0 512 961.934821c248.260716 0 449.988716-201.728 449.988716-449.988716S760.260716 62.011284 512 62.011284z m-45.002105 750.538105a134.251789 134.251789 0 0 1-44.430821-162.309389 83.299705 83.299705 0 0 0 63.779031 50.229895 142.907284 142.907284 0 0 1 26.171284-172.937432c0 78.222821 108.080505 132.376253 108.080506 176.753179a302.306358 302.306358 0 0 1-31.85179 107.293642 215.384926 215.384926 0 0 0 179.771284-156.510316 193.191074 193.191074 0 0 1-73.512421 55.985853 163.268716 163.268716 0 0 0 73.447748-136.245895c0-44.376926-29.006147-60.814821-45.390148-84.032673-16.448674-23.153179 0-65.643789 0-65.64379a63.779032 63.779032 0 0 0-33.845894 59.898611 211.698526 211.698526 0 0 1-22.204632 63.714358c0-162.298611-78.265937-253.154358-78.265937-289.845895a160.487747 160.487747 0 0 1 29.006148-81.176253 276.307537 276.307537 0 0 0-240.586106 289.89979c-27.130611-14.508463-30.935579-73.436968-30.935579-73.436969a241.491537 241.491537 0 0 0-49.324463 276.372211 504.594863 504.594863 0 0 1-55.080421-33.856674 214.468716 214.468716 0 0 0 222.218779 175.836969h2.953432z"
      fill="#FF0000"
      p-id="4244"
    ></path>
  </svg>`;

export function LocationIcon() {
  // 坐标图标
  return `<svg t="1735898735803" class="icon" viewBox="0 0 1024 1024"
   version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11647" width="30" height="30"><path d="M512.5 124.4c39.9 0 78.5 7.8 114.9 23.2 35.2 14.9 66.7 36.2 93.9 63.3 27.1 27.1 48.4 58.7 63.3 93.9 15.4 36.4 23.2 75 23.2 114.9 0 30.7-12.6 72.1-36.5 120-26.6 53.4-67.5 115.5-121.3 184.6-53.1 68.2-107 127.6-137.4 160-30.5-32.5-84.4-91.9-137.4-160-53.9-69.2-94.7-131.3-121.3-184.6-23.9-47.8-36.5-89.3-36.5-120 0-39.9 7.8-78.5 23.2-114.9 14.9-35.2 36.2-66.7 63.3-93.9 27.1-27.1 58.7-48.4 93.9-63.3 36.2-15.4 74.8-23.2 114.7-23.2m0-60c-48 0-94.5 9.4-138.3 27.9-42.3 17.9-80.3 43.5-112.9 76.1-32.6 32.6-58.2 70.6-76.1 112.9-18.5 43.8-27.9 90.3-27.9 138.3 0 81.3 57.4 196.2 170.5 341.5C410.3 867.3 494.1 952 495 952.8c4.6 4.7 11 7.3 17.5 7.3 6.6 0 12.9-2.6 17.5-7.3 0.8-0.8 84.6-85.6 167.2-191.6C810.4 616 867.8 501.1 867.8 419.7c0-47.9-9.4-94.5-27.9-138.3-17.9-42.3-43.5-80.3-76.1-112.9-32.6-32.6-70.6-58.2-112.9-76.1-43.9-18.6-90.5-28-138.4-28z" p-id="11648" fill="#f5de19"></path><path d="M510.7 355c38.8 0 70.4 31.6 70.4 70.4s-31.6 70.4-70.4 70.4c-38.8 0-70.4-31.6-70.4-70.4s31.5-70.4 70.4-70.4m0-60c-71.9 0-130.4 58.5-130.4 130.4s58.5 130.4 130.4 130.4 130.4-58.5 130.4-130.4S582.6 295 510.7 295z" p-id="11649" fill="#f5de19"></path></svg>
  `;
}

export function BianJiaoIcon(props) {
  //变焦图标
  let { width, height, fillColor } = props;
  return (
    <svg
      t="1734415125298"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15708"
      width={width ? width : 200}
      height={height ? height : 200}
    >
      <path
        d="M972.8 750.557867V921.6a51.2 51.2 0 0 1-46.2848 50.961067L921.6 972.8h-171.4176v-34.133333H921.6a17.066667 17.066667 0 0 0 16.7936-13.994667L938.666667 921.6v-171.042133h34.133333z m-887.466667 2.218666v159.744c0 13.994667 6.519467 24.1664 12.765867 25.873067l1.877333 0.273067h174.6944v34.133333H100.010667c-26.7264 0-46.4896-24.917333-48.605867-54.954667l-0.170667-5.3248v-159.744h34.133334zM512 182.613333a329.386667 329.386667 0 1 1 0 658.773334 329.386667 329.386667 0 0 1 0-658.773334zM311.637333 532.48H227.703467a285.047467 285.047467 0 0 0 263.850666 263.816533v-83.968A201.454933 201.454933 0 0 1 311.637333 532.48z m484.6592 0h-83.968a201.454933 201.454933 0 0 1-179.848533 179.882667v83.933866a285.047467 285.047467 0 0 0 263.816533-263.850666zM532.48 345.975467V392.533333a17.066667 17.066667 0 0 1-17.066667 17.066667h-6.826666a17.066667 17.066667 0 0 1-17.066667-17.066667v-46.557866a167.3216 167.3216 0 0 0-145.544533 145.578666L392.533333 491.52a17.066667 17.066667 0 0 1 17.066667 17.066667v6.826666a17.066667 17.066667 0 0 1-17.066667 17.066667h-46.557866a167.3216 167.3216 0 0 0 145.578666 145.544533L491.52 631.466667a17.066667 17.066667 0 0 1 17.066667-17.066667h6.826666a17.066667 17.066667 0 0 1 17.066667 17.066667v46.557866a167.3216 167.3216 0 0 0 145.544533-145.578666L631.466667 532.48a17.066667 17.066667 0 0 1-17.066667-17.066667v-6.826666a17.066667 17.066667 0 0 1 17.066667-17.066667h46.557866a167.3216 167.3216 0 0 0-145.544533-145.544533z m-40.96-118.272a285.047467 285.047467 0 0 0-263.816533 263.850666h83.968a201.454933 201.454933 0 0 1 179.882666-179.882666V227.669333z m40.96 0v83.968a201.454933 201.454933 0 0 1 179.882667 179.848533h83.933866A285.047467 285.047467 0 0 0 532.48 227.669333zM273.749333 51.2v34.133333H100.010667c-6.144 0-13.2096 8.874667-14.4384 22.050134l-0.2048 4.096V273.408h-34.133334V111.479467c0-30.549333 18.261333-57.173333 44.1344-60.040534l4.642134-0.238933H273.749333z m650.274134 0c26.7264 0 46.4896 24.917333 48.605866 54.954667l0.170667 5.3248v161.553066h-34.133333V111.479467c0-13.994667-6.519467-24.1664-12.765867-25.873067L923.989333 85.333333h-173.192533v-34.133333h173.192533z"
        fill={fillColor ? fillColor : "#FF0000"}
        p-id="15709"
      ></path>
    </svg>
  );
}

export function CanCelToHomeIcon(props) {
  let { width, height, fillColor } = props;
  // 取消返航图标
  return (
    <svg
      t="1735192336908"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="5748"
      width={width ? width : 200}
      height={height ? height : 200}
    >
      <path
        d="M907.904 697.472c-0.896 121.728-97.408 219.264-219.52 219.264-121.472-0.128-218.752-97.024-218.88-219.008 0-122.368 97.792-219.008 219.264-219.264 120.832-0.256 219.008 98.304 219.136 219.008z m-270.848 3.2c-1.92 2.048-3.456 3.712-5.12 5.376-20.096 20.096-40.192 39.936-60.032 60.288-3.968 4.096-7.808 9.472-9.344 14.848-4.608 15.744 10.496 38.016 27.776 42.624 11.904 3.2 21.248-1.024 29.44-9.216 20.224-20.224 40.576-40.32 60.544-60.672 4.096-4.224 6.4-4.48 10.624-0.128 20.096 20.352 40.32 40.448 60.928 60.288 4.352 4.096 9.728 8.064 15.36 9.6 15.488 4.48 38.016-11.008 42.112-27.392 3.072-12.16-0.896-21.76-9.344-30.08-20.224-20.096-40.576-40.192-60.928-60.288-1.664-1.536-3.2-3.328-4.992-5.248 2.048-2.176 3.584-3.84 5.248-5.504 22.144-22.144 44.416-44.288 66.432-66.56 12.672-12.8 13.824-27.008 2.816-40.576-16.384-19.968-34.048-25.856-55.168-3.712-20.352 21.504-41.984 41.856-62.72 63.104-4.224 4.352-6.528 3.968-10.496-0.256-21.376-22.016-43.008-43.904-64.896-65.408-4.352-4.352-9.984-8.448-15.744-10.24-16.128-4.864-38.656 10.88-43.136 28.672-2.944 11.904 2.176 20.736 9.984 28.672 21.76 22.016 43.52 43.904 65.28 65.92 1.792 1.792 3.328 3.712 5.376 5.888zM298.624 371.072c-27.392 0-53.76 0.128-80.256-0.256-1.536 0-4.224-2.816-4.224-4.48-1.28-24.576-1.024-49.28 4.096-73.216 8.576-39.552 28.416-72.832 60.544-98.688 14.08-11.392 28.928-21.12 45.44-28.288 25.728-11.264 52.864-15.488 81.024-13.44 21.12 1.536 41.216 6.528 60.416 14.976 29.952 13.056 54.4 33.792 73.344 60.032 9.088 12.544 15.104 27.52 21.376 41.984 10.88 25.216 12.16 51.968 12.032 78.976-0.128 25.984-0.128 52.096 0 78.08 0 4.224-0.768 7.168-5.12 9.088-28.288 12.672-53.76 29.696-76.288 50.816-0.64 0.512-1.408 0.896-3.072 2.048-0.128-3.2-0.384-5.504-0.384-7.936 0-49.536 0.128-98.944 0-148.48-0.128-42.24-19.712-72.576-57.856-89.984-19.456-8.832-40.192-9.088-61.184-3.584-28.416 7.424-46.976 25.984-60.16 50.688-6.272 11.776-9.088 24.704-9.088 38.272v35.84c-0.256 2.304-0.384 4.608-0.64 7.552zM844.672 461.44c-2.816-1.664-4.352-2.304-5.76-3.328-22.656-15.104-46.976-26.88-73.088-34.688-4.992-1.536-6.656-3.968-6.528-9.088 0.256-15.744 0-31.616 0.128-47.36 0-4.224-1.408-6.272-5.376-8.064-36.096-16.256-61.824-41.6-70.912-81.536-1.28-5.632-2.56-11.264-3.968-16.768-2.688-10.368-2.688-20.608-0.128-30.976 1.408-5.632 2.816-11.136 3.968-16.768 7.808-37.376 31.232-63.232 64.384-78.592 23.808-11.008 49.664-16.64 77.056-9.472 20.224 5.248 39.04 11.904 54.912 25.6 17.792 15.36 31.872 33.152 37.376 56.576 2.816 11.52 7.296 23.424 6.784 34.944-2.048 52.992-22.784 91.264-72.96 115.84-4.096 2.048-5.632 4.096-5.632 8.576 0.128 28.8 0.128 57.6 0.128 86.4 0 2.432-0.128 4.992-0.384 8.704z m-43.904-153.6c34.56 0 62.592-28.032 62.72-62.592 0.128-34.688-28.416-63.104-63.104-62.848-34.56 0.256-62.464 28.416-62.336 62.976 0 34.56 28.032 62.464 62.72 62.464zM214.656 760.96v-77.312h84.096v77.312h111.872c0.384 0.768 0.64 1.536 1.024 2.304-51.456 53.504-102.912 106.88-154.752 160.768-52.224-54.272-104.064-108.16-157.184-163.2 39.296 0.128 76.8 0.128 114.944 0.128zM214.4 427.776h84.352c0.128 2.432 0.256 4.48 0.256 6.528 0 21.376-0.128 42.624 0.128 64 0 5.12-1.152 7.04-6.656 7.04-23.936-0.256-47.744-0.256-71.68 0-5.248 0-6.912-1.664-6.912-6.912 0.256-21.376 0.128-42.624 0.128-64 0-2.176 0.256-4.224 0.384-6.656zM298.752 561.664v64.768h-83.84v-64.768h83.84z"
        p-id="5749"
        fill={fillColor ? fillColor : "#FF0000"}
      ></path>
    </svg>
  );
}

export function ShowQiIcon({width, height,fillColor}) {
  //收起图标
  return (
    <svg
      t="1735525429740"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="11681"
      width={width? width : "200"}
      height={height? height : "200"}
    >
      <path
        d="M775.314286 1007.908571V16.091429c-10.24 0-19.017143-1.462857-29.257143-1.462858-275.017143 0-497.371429 222.354286-497.371429 497.371429s222.354286 497.371429 497.371429 497.371429c10.24 0 19.017143-1.462857 29.257143-1.462858zM560.274286 512L687.542857 687.542857h-106.788571L453.485714 512l127.268572-175.542857H687.542857l-127.268571 175.542857z m0 0"
        fill={fillColor?fillColor:"#0070c9"}
        p-id="11682"
      ></path>
    </svg>
  );
}
