
export function importAllSvgFromFolder(req, folder) {
    const requireAll = requireContext =>
        requireContext.keys().map(key => {
            const name = key.replace(/\.\/(.*)\.\w+$/, '$1');
            return { name, value: requireContext };
        });

    return requireAll(req).reduce((cur, next) => {
        return { ...cur, [next.name]: require(`@/assets/${folder}/${next.name}.svg`) };
    }, {});
}
