

export const dataSourceHandler = (data) => {

    // 类别映射关系
    const categoryMap = {
        '0': '行人',
        '1': '人',
        '2': '摩托车',
        '3': '汽车',
        '4': '面包车',
    };
    // 处理原始数据
    const processdData = {
        ...data,
        result: data.result.map((item) => {
            // 初始化所有类别数量为0
            const counts = Object.values(categoryMap).reduce((acc, cur) => {
                acc[cur] = 0;
                return acc;
            }, {});

            // 解析IdentifyCount字符串
            if (item.IdentifyCount) {
                item.IdentifyCount.split(';').forEach(pair => {
                    const [key, value] = pair.split('=');
                    if (key && categoryMap[key]) {
                        counts[categoryMap[key]] = parseInt(value, 10) || 0;
                    }
                });
            }
            // 合并原始对象和统计结果
            return {
                ...item,
                ...counts
            };
        })
    }
    return processdData;
};