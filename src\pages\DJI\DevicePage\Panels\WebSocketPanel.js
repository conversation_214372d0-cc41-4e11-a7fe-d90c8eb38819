import { useModel } from 'umi';
import { useEffect } from 'react';

const WebsocketPanel=({device})=>{
    const { NewDroneConn} = useModel('droneModel');
    const { EventMqttConn } = useModel('eventModel');
    const {StateMqttConn} =useModel('stateModel');
    const { CmdMqttConn } =useModel('cmdModel');
    const { DrcMqttConn } =useModel('drcModel');
    const { DockMqttConn } = useModel('dockModel');

    useEffect(()=>{
        NewDroneConn(device);
        EventMqttConn(device.SN);
        DockMqttConn(device.SN);
        StateMqttConn(device.SN,device.SN2);
        CmdMqttConn(device.SN);
        DrcMqttConn(device.SN);
    },[]);
    return <div></div>
}

export default WebsocketPanel;