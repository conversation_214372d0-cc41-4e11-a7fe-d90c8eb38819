import { Input, Tooltip, Checkbox, Radio, Descriptions, Button, Form, Modal, message, Upload } from 'antd';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';
import { InputNumber } from 'antd';
import { getGuid } from '@/utils/helper';
import { OssPutFile } from '@/utils/AliossHelper';
import ComStyles from '@/pages/common.less';

const { TextArea } = Input;
const WayLineTypeList = [
  { label: '航点航线', value: '1' },
  // { label: '垂直航线', value: '2' },
  { label: '面状航线', value: '3' },
  { label: '带状航线', value: '4' },
  { label: '几何体航线', value: '5' },
]
const FileTypeList = ["大疆kmz航线文件", "kml文件"]

const WayLineAddForm = (props) => {
  const refrush = props.data;

  const [devices, setDevices] = useState([]);
  const [sn, setSN] = useState('');
  const [wType, setWType] = useState('');
  const [wName, setWName] = useState('');
  const [remark, setRemark] = useState('');
  const [canSee, setCanSee] = useState(false);
  const [ifDZ, setIfDZ] = useState(false);
  const [ifRecord, setIfRecord] = useState(false);
  const [fH1, setFH1] = useState(0);
  const [fR1, setFR1] = useState(-90);
  const [sfile, setSFile] = useState({})
  const [index, setIndex] = useState({})

  useEffect(() => {
    getDevices();
  }, []);


  const getDevices = async () => {
    let pst = await Get2('/api/v1/Device/GetAllList', {});
    setDevices(pst);
  };

  const onFormChange = (e) => {
    console.log(e)
  }

  const handleFileChange = async () => {
    const object = "waylines/" + getGuid() + '_' + sfile.name
    const res = await OssPutFile(sfile, object);
    const xx = await Post2('/api/v1/WayLine/KML', { Sn: sn, WName: wName, WType: wType, Remark: remark, Object: object, FH1: Number(fH1), FR1: fR1, IfDZ: ifDZ, IfRecord: ifRecord })
    if (xx == "ok") {
      message.info("上传成功");
      refrush();
      setCanSee(false)
    }
  };


  const upProps = () => {
    return {
      name: 'file',
      action: '/api/v1/WayLine/Upload',
      //showUploadList: false,
      multiple: false,
      beforeUpload: file => {
        setSFile(file);
        return false;
      },
    }
  };


  const pbV = () => {
    if (isEmpty(sn) || isEmpty(wType) || isEmpty(wName)) {
      return true
    }
    return false
  }


  const getSNList = () => {
    const list = []
    devices.map(p => {
      list.push(<Radio value={p}>{p.DName}</Radio>)
    })
    return list
  }


  const getTypeList = (xL) => {
    const list = []
    xL.map(p => {
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }

  const getToolTip = (tip, title) => {
    return <Tooltip title={title}>
      <span>{tip}</span>
    </Tooltip>
  }

  return <div><Button className={ComStyles.addButton} type="primary" onClick={() => setCanSee(true)}>{getToolTip("KML文件上传", "坐标要求cgcs2000或wgs84")}</Button>
    <Modal title={null} footer={null} onOk={null} open={canSee} onCancel={() => setCanSee(false)}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}

      >
        <Descriptions title="航线上传" column={2}   >
          <Descriptions.Item span={2} label="选择机场"><Radio.Group onChange={(e) => {
            setSN(e.target.value.SN);
            setFH1((e.target.value.Height + 100).toFixed(0))
            setIndex(e.target.value);
          }} >
            {getSNList()}
          </Radio.Group></Descriptions.Item>
          <Descriptions.Item span={2} label="航线类型"><Radio.Group onChange={(e) => { setWType(e.target.value) }} >
            {/* {getTypeList(WayLineTypeList)} */}
            {WayLineTypeList.map((item, index) => {
              return <Radio key={item.value} value={item.value}>{item.label }</Radio>
            })}
          </Radio.Group></Descriptions.Item>
          <Descriptions.Item span={2} label="航线名称"><Input onChange={(e) => { setWName(e.target.value) }} /></Descriptions.Item>
          <Descriptions.Item label={getToolTip("航点顺序", "选择后,生成航线的航点顺序倒转")}><Checkbox defaultChecked={ifDZ} onChange={(e) => setIfDZ(e.target.checked)}>倒转</Checkbox></Descriptions.Item>
          <Descriptions.Item label={getToolTip("是否录像", "选择后，自动从第一个航点录像至最后一个航点")}><Checkbox defaultChecked={ifRecord} onChange={(e) => setIfRecord(e.target.checked)}>录像</Checkbox></Descriptions.Item>
          <Descriptions.Item label={getToolTip("飞行高度", "绝对高度，当前机场海拔高度：" + index.Height)}><InputNumber value={fH1} onChange={(e) => setFH1(e)} /></Descriptions.Item>
          <Descriptions.Item label={getToolTip("云台角度", "-90度~15度之间")}><InputNumber value={fR1} min={-90} max={15} onChange={(e) => { setFR1(e) }} /></Descriptions.Item>
          <Descriptions.Item span={2} label="备注信息"><TextArea rows={4} onChange={(e) => { setRemark(e.target.value) }} /></Descriptions.Item>
          <Descriptions.Item span={2} label="上传航线"><Upload {...upProps()}>
            <Button disabled={pbV()} >
              选择文件
            </Button>
          </Upload>
          </Descriptions.Item>
          <Form.Item label={" "} colon={false}>
            <Button type="primary" onClick={handleFileChange}>
              提交
            </Button>
          </Form.Item>
        </Descriptions>
      </div>
    </Modal>
  </div>

}



export default WayLineAddForm;