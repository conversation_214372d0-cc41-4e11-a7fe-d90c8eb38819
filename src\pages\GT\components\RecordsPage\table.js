import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import RecordsDetailPage2 from "@/pages/GT/components/RecordsDetailPage2";

const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (handlePageChange, getRecords, openEditModal, taskTypeList) => {

  const { setPage, } = useModel("pageModel");

  const handleEnableSmartSurveyTask = async (record) => {
    try {
      const res = await axiosApi(
        `/api/v1/Survey/EnableSmartSurveyTask`,
        "POST",
        { surveyTaskID: record.TaskID },
      );
      if (res.code === 1) {
        message.success(res.data.message);
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log('error', error);
      message.error("启用失败");
    }
  };

  const handleDisableSmartSurveyTask = async (record) => {
    try {
      const res = await axiosApi(
        `/api/v1/Survey/DisableSmartSurveyTask`,
        "POST",
        { surveyTaskID: record.TaskID },
      );
      if (res.code === 1) {
        message.success(res.data.message);
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log('error', error);
      message.error("停止失败");
    }
  };

  const handleExecuteSmartSurveyTask = async (record) => {
    try {
      const res = await axiosApi(
        `/api/v1/Survey/ExecuteSmartSurveyTask`,
        "POST",
        { surveyTaskID: record.TaskID },
      );
      if (res.code === 1) {
        message.success('执行成功');
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log('error', error);
      message.error("立即执行失败");
    }
  };

  const handleDeleteSmartSurveyTask = async (record) => {
    try {
      const res = await axiosApi(
        `/api/v1/Survey/DeleteSmartSurveyTask?surveyTaskID=${record.TaskID}`,
        "DELETE",
        {},
      );
      if (res.code === 1) {
        message.success(res.data.message);
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log('error', error);
      message.error("删除失败");
    }
  };

  const handleEdit = async (record) => {
    try {
      // 先获取到详情信息
      const res = await axiosApi(
        `/api/v1/Survey/GetSurveyTaskContentDetail?surveyTaskID=${record.TaskID}`,
        "GET",
        {},
      );
      if (res.code === 1) {
        openEditModal(res.data);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log('error', error);
      message.error("获取信息失败");
    }
  };

  const DetailButtonWithState = ({ record }) => {
    const [popoverVisible, setPopoverVisible] = useState(false);
    const [days, setDays] = useState(7);
    const { setPage } = useModel("pageModel");

    return (
      <Popover
        title="选择查询天数"
        content={
          <div style={{
            width: 250,
            display: 'flex',
            gap: 8,
            alignItems: 'center'
          }}>
            <InputNumber
              addonBefore="最近"
              addonAfter="天"
              min={1}
              value={days}
              onChange={v => setDays(v)}
              style={{ flex: 1 }}
            />
            <MyButton
              type="primary"
              size="small"
              onClick={() => {
                setPopoverVisible(false);
                setPage({
                  title: "巡检详情",
                  key: "/gy/xjxq",
                  children: <RecordsDetailPage2 record={record} days={days} />,
                });
              }}
            >
              确认
            </MyButton>
          </div>
        }
        trigger="click"
        open={popoverVisible}
        onOpenChange={v => setPopoverVisible(v)}
      >
        <MyButton
          style={{ padding: "2px 8px", color: '#17AF91', background: 'none', border: 'none' }}
          onClick={() => setPopoverVisible(true)}
        >
          详情
        </MyButton>
      </Popover>
    );
  };

  return [
    // {
    //   title: getTableTitle("组织码"),
    //   dataIndex: "OrgCode",
    //   key: "OrgCode",
    //   align: "center",
    // },
    {
      title: getTableTitle("任务ID"),
      dataIndex: "TaskID",
      key: "TaskID",
      align: "center",
    },

    {
      title: getTableTitle("任务描述"),
      dataIndex: "TaskDesc",
      key: "TaskDesc",
      align: "center",
    },

    {
      title: getTableTitle("应用场景"),
      // dataIndex: "TaskType",
      key: "TaskType",
      align: "center",
      render: (_, record) => (
        <span>
          {taskTypeList.find(item => item.TheURL === record.TaskType)?.Title || record.TaskType}
        </span>
      )
    },

    {
      title: getTableTitle("创建时间"),
      // dataIndex: "CreateTM",
      key: "CreateTM",
      align: "center",
      render: (_, record) => (
        <span>
          {isEmpty(record.CreateTM)
            ? "-"
            : timeFormat(record.CreateTM)
          }
        </span>
      )
    },
    {
      title: getTableTitle("状态"),
      // dataIndex: "TaskType",
      align: "center",
      render: (_, record) => (
        <>
          {record.State === 0 && (
            <Tag icon={<ExclamationCircleOutlined />} color="warning">未启用</Tag>
          )}
          {record.State === 1 && (
            <Tag icon={<CheckCircleOutlined />} color="success">已启用</Tag>
          )}
        </>
      )
    },

    {
      title: getTableTitle("操作"),
      align: "center",
      width: 400,
      render: (record) => (
        <Space
          size="middle"
        >

          {(record.State === 0) && (
            <MyButton
              style={{ padding: "2px 8px", color: 'orange', background: 'none' }}
              onClick={() => {
                handleEnableSmartSurveyTask(record);
              }}
            >
              启用
            </MyButton>
          )}
          {(record.State === 1) && (
            <MyButton
              style={{ padding: "2px 8px", color: 'red', background: 'none' }}
              onClick={() => {
                handleDisableSmartSurveyTask(record);
              }}
            >
              停止
            </MyButton>
          )}
          {(record.State === 1) && (
            <MyButton
              style={{ padding: "2px 8px", color: 'orange', background: 'none' }}
              onClick={() => {
                handleExecuteSmartSurveyTask(record);
              }}
            >
              立即执行
            </MyButton>
          )}
          <MyButton
            style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑
          </MyButton>
          {/* <MyButton
            style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
            onClick={() => {
              localStorage.setItem('record', JSON.stringify(record));
              handlePageChange('巡检详情');
            }}
          >
            详情
          </MyButton> */}
          {/* <DetailButtonWithState record={record} /> */}
          <MyButton
            style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
            onClick={() => {
              setPage({
                title: "巡检详情",
                key: "/gy/xjxq",
                children: <RecordsDetailPage2 record={record}/>,
              });
            }}
          >
            详情
          </MyButton>
          <MyButton
            style={{ padding: "2px 8px", color: 'red', background: 'none' }}
            onClick={() => {
              handleDeleteSmartSurveyTask(record);
            }}
          >
            删除
          </MyButton>
        </Space>

      )
    },
  ];
};

export default TableCols;
