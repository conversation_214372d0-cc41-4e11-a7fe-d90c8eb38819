import { Clone, downloadFile2, downloadFile, getBodyH, isEmpty, getVideoUrl,getVideoSLTUrl,getImgUrl } from '@/utils/utils';

import { useEffect, useState } from 'react';
import { Card, List, Row, Col, Button, Checkbox,Select ,DatePicker} from 'antd';

import { useModel,history } from 'umi';
import MediaInfoPanel from './MediaInfoPanel';

import LastPageButton from '@/components/LastPageButton';
import { timeFormat22 } from '@/utils/helper';
import dayjs from 'dayjs';
import DownLoadAndDel from '@/pages/DJI/MediaDataPage/DownLoadAndDel'
import useConfigStore from "@/stores/configStore";



const getImgUrl2 = (obj) => {
  return `/${obj}`
}

const { RangePicker } = DatePicker;

const MediaListPanel = ({ mList,refrush,doNotShowLastButton }) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [sList, setSList] = useState([]);

  const [sWay,setSWay]=useState('');
  const [sDate,setSDate]=useState({});

  const { setPage, lastPage, setModal, open, setOpen } = useModel("pageModel")
  // eslint-disable-next-line react-hooks/exhaustive-deps
  let page1 = localStorage.getItem('MediaListPage')
  if (isEmpty(page1)) {
    page1 = 1
  } else {
    page1 = Number(page1)
  }
  const IfSelected = (item) => {
    const xx = sList.indexOf(item.ID)
    return xx > -1;
  }

    const handleTableChange = (page) => {
    setTableCurrent(page);
  };

  useEffect(() => {
    let t1=dayjs('1900/1/1');
    let t2=dayjs('2900/1/1')
    if(!isEmpty(sDate)){
       t1=dayjs(sDate[0]);
       t2=dayjs(sDate[1]);
    }
   
    const ifPush=(e)=>{

      if(!isEmpty(sWay)){
          if(e.WayLineNM!=sWay){
             return false;
          }
      }
      if(!isEmpty(sDate)){
       // 
        
        const t3=dayjs(e.CreatedTime);
       
        if(t1.isAfter(t3)||(t2.isBefore(t3))){
         // console.log('time',t1.format('YYYY-MM-DD HH:mm:ss'),t2.format('YYYY-MM-DD HH:mm:ss'),t3.format('YYYY-MM-DD HH:mm:ss'));
           return false;
        }
      }
      return true;
    }
    const xx = () => {
      const xL = []
      mList.forEach(e => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
      setSList([...xL]);
    }
    xx();
  }, [sWay,sDate,refrush]);

  const downLoadClick = () => {

    mList.forEach(e => {
      if (IfSelected(e)) {
        downloadFile(getImgUrl2(e.ObjectName), e.FileName)
        // downloadFile(getImgUrl(e.ObjectName));
      }
    });
  }

  const selectClick = (item) => {
    const xx = sList.indexOf(item.ID)
    if (xx === -1) {
      sList.push(item.ID)

    } else {
      delete sList[xx]

    }
    const yy = Clone(sList)
    setSList(yy)
  }

  let [checkBoxList,setCheckBoxList] = useState([])
  const checkBoxChange = (e,item) => {
    if(!e){
      setCheckBoxList([])
    }
    if(e && e.target.checked && checkBoxList && !checkBoxList.includes(item.ID)){
      setCheckBoxList([...checkBoxList,item])
    } 
    if(e && !e.target.checked ){
      setCheckBoxList(checkBoxList.filter((value)=>{
        return value.ID !== item.ID
      }))
    }
  };


  const getItem=(item)=>{
    const onClick=(item)=>{
        if(doNotShowLastButton){
          history.push("/gt/WRJ");
          // 显示返回按钮
          localStorage.setItem('showBackButton', 'true');
        }
        setModal(<div style={{height:350,width:'100%'}} key={item.ID}>
        <div  style={{position:'absolute',left:20,top:18.0, fontWeight:'bold',height:36.0,color:'rgba(0,0,0,0.7)'}}>
          {item.WayLineNM+"-"+item.HangDianIndex}
        </div>
        <div style={{position:'absolute',cursor:'pointer',top:18.0,right:48,fontWeight:'bold', color:'rgba(0,0,0,0.7)'}}>{timeFormat22(item.CreatedTime)}</div>
          <video id={item.ID}  height={'100%'} width={'100%'} controls>
          <source src={getVideoUrl(item)} type="video/mp4" />
        </video>
        </div>);
        setOpen(true);
    }
  
    return   <div style={{cursor:'pointer',}} key={item}>
      
       <img style={{width:'100%', cursor:'pointer',height:'100%',borderRadius:5.0}} src={getImgUrl(item.SLTImg)}
         onClick={() => {
        onClick(item);
       }}
       
        />
        <div  style={{margin:4.0 ,cursor:'pointer',fontWeight:'bold',height:36.0,color:'rgba(0,0,0,0.7)'}}>
          <span><Checkbox key={item.ID} onChange={(e)=>checkBoxChange(e,item)} style={{margin:'0 15px 0 -5px'}}></Checkbox></span>
          <span>{item.WayLineNM+"-"+item.HangDianIndex}</span> <span style={{float:'right'}}>{item.Size}</span>  </div>
        {/* <div style={{width:'100%' ,position:'absolute',top:0,left:14,fontWeight:'bold', color:'white'}}>{item.WayLineNM}</div> */}
        <div style={{position:'absolute',cursor:'pointer',top:2,bottom:40,right:12,fontWeight:'bold', color:'white'}}>{timeFormat22(item.CreatedTime)}</div>
        {/* <div style={{position:'absolute',top:2,bottom:40,right:20,fontWeight:'bold', color:'white'}}>{item.Size}</div> */}
        </div>
     }
    
      //

     const getWaySelect = (wayList) => {
      
        const list = []
        wayList.forEach(e => {
          list.push(<Select.Option key={e} data={e}>{e}</Select.Option>)
        });
        return list;
    }

    const onWayLineSelected=(e)=>{
        setSWay(e);
    }

    const onDateSelected=(e)=>{
     // 
      setSDate(e);
  }

    const getExr=()=>{
      const wList=[]
      mList.forEach(e => {
          if(!wList.includes(e.WayLineNM)){
            wList.push(e.WayLineNM);
          }
      });
      return <div>

        <span>      <Select allowClear={true} style={{width:200}} onClear={()=>setSWay('')}
          placeholder={'选择航线'}
          onSelect={onWayLineSelected}>
          {getWaySelect(wList)}
        </Select></span>

        <span style={{marginLeft:12.0}}> <RangePicker  onChange={onDateSelected}/></span>
        <span><DownLoadAndDel selectImgList={mList} checkBoxList={checkBoxList} refrush={refrush} checkBoxChange={checkBoxChange}></DownLoadAndDel></span>

      </div>
    }
    
  

  return <Card title={ doNotShowLastButton ? '航拍影像' : <LastPageButton title="航拍视频"/>} style={{ height: getBodyH(56) }} extra={getExr()}>
    <List
      grid={{ gutter: 16, column: 5 }}
      dataSource={sList}
      pagination={{ defaultCurrent: page1, 
        showSizeChanger: false, 
        showQuickJumper: true,
        current: tableCurrent,
        onChange: (e) => { 
          handleTableChange(e);
          localStorage.setItem('MediaListPage', e) 
        }, 
        pageSize: 10, style: { marginTop:'-40px'} }}
      style={{ height: getBodyH(300) }}
      renderItem={item => (
        <List.Item>
          {getItem(item)}
        </List.Item>
      )}
    />
  </Card>
}

export default MediaListPanel;
