import { Input, Form, DatePicker, Radio,  Select,  Button, message,  Upload } from 'antd';
import  { useState, } from 'react';
import { isEmpty } from '@/utils/utils';
import { HPost2 } from '@/utils/request';
import { UploadOutlined } from '@ant-design/icons';
const DangerTypeList = ["钓鱼等外来人员进入", "漂浮物", "外来船只", "垃圾堆放","非法采砂"]
const LevelList = ["一般", "紧急"]

const CronAddForm = (props) => {
  const {sn,refrush } = props;
  const [type, setType] = useState({})
  const [name, setName] = useState("")
  const [content, setContent] = useState("")
  const [img,setImg]=useState("")
  const [level,setLevel]=useState("")

  const onSave =async (e) => {
    if (isEmpty(type)) {
      message.info("请先选择事件类型！");
      return;
    }

    if (isEmpty(level)) {
      message.info("请先选择紧急程度！");
      return;

    }

    const user=JSON.parse( localStorage.getItem('user'));
    console.log('CronAddForm', user);
    const data = {
      Level:level,
      Title:name,
      Content:content,
      ImgObjectName:img,
      DangerType:type,
    }



   const xx=await  HPost2('/api/v1/Danger/Add',data);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
      
    }
    
    refrush();
  };



  const onName = (e) => {
    setName(e.target.value);
  };



  const getTypeList=()=>{
    const list=[]
    DangerTypeList.map(p=>{
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }

  const getLevelList=()=>{
    const list=[]
    LevelList.map(p=>{
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }


  const onFileChange=(info)=>{
    
      console.log('123213',info)
      if (info.file.status === 'done') {
        message.success(`${info.file.response.slice(36)} 上传成功`);
        setImg(info.file.response)
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败！`);
      }
      

  }

  const token=localStorage.getItem('token');

  return <Form

    labelCol={{
      span: 4,
    }}

    wrapperCol={{
      span: 14,
    }}
    layout="horizontal"
    //disabled={componentDisabled}
    style={{
      maxWidth: 600,
    }}



  >

  <Form.Item label="事件类型">
      <Select
        onSelect={(e)=>setType(e)}>
        {getTypeList()}
      </Select>
    </Form.Item>


    <Form.Item label="事件标题">
      <Input
        onChange={(e)=>setName(e.target.value)}>

      </Input>
    </Form.Item>

    <Form.Item label="主要内容">
      <Input.TextArea
       onChange={(e)=>setContent(e.target.value)}>
          rows={4}
      </Input.TextArea>
    </Form.Item>

    <Form.Item label="紧急程度">
      <Select
        onSelect={(e)=>setLevel(e)}>
        {getLevelList()}
      </Select>
    </Form.Item>

    <Form.Item label="照片上传">
    <Upload
      action="/api/v1/File/Upload"
      listType="picture"
      maxCount={1}
      onChange={onFileChange}
      headers= {{
        authorization: 'authorization-text',
        'auth':token, }
      }
    >
      <Button icon={<UploadOutlined />}>Upload (Max: 1)</Button>
    </Upload>
    </Form.Item>
  

    <Form.Item label={" "} colon={false}>
      <Button type="primary" onClick={onSave}>
        提交
      </Button>
    </Form.Item>
  </Form>

}



export default CronAddForm;