import { Space, Tag, message, Modal } from "antd";
import { useState, useEffect, useRef } from 'react';
import { WayLineTypeToString, getDeviceName, isEmpty } from "@/utils/utils";
import "./table.css";
import styles from "./table.module.less";
import { timeFormat } from "@/utils/helper";
import { getImgUrl } from "@/utils/utils";
import { HGet2 } from "@/utils/request";
import { Post2 } from "@/services/general";
import WayLineEditPage from "./form_edit";
import DevicePage from "../DevicePage";
import { CheckIfCanFly } from "@/pages/DJI/FlyToPage/helper";
import WayLine3DPage from "@/pages/DJI/wayline3D/index";
import BeltRoute from "@/pages/DJI/BeltRoute/index";
import PlanarRoute from "@/pages/DJI/PlanarRoute/index";
import InspectionRoute from "@/pages/DJI/InspectionRoute/index";
import { axiosApi } from '@/services/general';


const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const { confirm } = Modal;
const toFly = async (record, setPage) => {
  if (isEmpty(record)) return;
  const pb1 = await CheckIfCanFly(record.SN);
  if (!pb1) return;

  localStorage.setItem("wayPoints", record.PointList);
  localStorage.removeItem("gpsPoints");
  await HGet2("/api/v1/WayLine/Fly?fID=" + record.WanLineId);
  const d1 = await HGet2("/api/v1/Device/GetBySN?sn=" + record.SN);
  if (isEmpty(d1)) {
    return;
  }
  localStorage.setItem("device", JSON.stringify(d1));
  // 将航线ID放到localStorage中
  localStorage.setItem("WanLineId", record.WanLineId);
  setPage(<DevicePage device={d1} />);
};

const deleteWayline = async (record, refrush) => {
  const xx = await Post2("/api/v1/WayLine/Delete", record);
  if (!isEmpty(xx.err)) {
    message.error("错误：" + xx.err);
  } else {
    message.success("删除成功！");
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除航线",
    //icon: <ExclamationCircleFilled />,
    content: "确定删除该航线吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deleteWayline(record, refrush);
    },
    onCancel() {
      // console.log('Cancel');
    },
  });
};
function revise(record, setPage) {
  if (record.WayLineType === "0") {
  } else if (record.WayLineType === "1") {
    setPage(<WayLine3DPage wayLineData={record} />);
  } else if (record.WayLineType === "2") {
    setPage(<InspectionRoute wayLineData={record} />);
  } else if (record.WayLineType === "3") {
    setPage(<PlanarRoute wayLineInfo={record} />);
  } else if (record.WayLineType === "4") {
    setPage(<BeltRoute wayLineInfo={record} />);
  }
}

const download = (url, title = "", artist = "") => {
  const eleLink = document.createElement("a"); // 新建A标签
  eleLink.href = url; // 下载的路径
  eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
  eleLink.style.display = "none";
  document.body.appendChild(eleLink);
  eleLink.click(); // 触发点击事件
  document.body.removeChild(eleLink);
};


const TableCols = (refrush, showMap, setPage, showModal) => {

  const columns = [
    {
      title: getTableTitle("所属机场"),
      dataIndex: "SN",
      key: "SN",
      align: "center",
      render: (record) => getDeviceName(record),
    },
    {
      title: getTableTitle("航线类型"),
      // dataIndex: "WayLineType",
      // key: "WayLineType",
      align: "center",
      render: (record) => <div>{WayLineTypeToString(record.WayLineType)}</div>,
    },
    {
      title: getTableTitle("航线名称"),
      dataIndex: "WayLineName",
      key: "WayLineName",
      align: "center",
    },
    // {
    //   title: getTableTitle('航线说明'),
    //   dataIndex: 'Remarks',
    //   key: 'Remarks',
    //   align:'center',
    // //  width:300,
    // //  className: 'table-header-cell'
    // },
    {
      title: getTableTitle("上传时间"),
      dataIndex: "CreateTime",
      key: "CreateTime",
      align: "center",
      render: (record) => timeFormat(record),
    },
    {
      title: getTableTitle("上传用户"),
      dataIndex: "UserName",
      key: "UserName",
      align: "center",
      render: (e) => (e.length < 2 ? "管理员" : e),
    },

    {
      title: getTableTitle("航线操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">

          <a style={{ color: "#18B092" }} onClick={() => toFly(record, setPage)}>立即执行</a>


          <a style={{ color: "#18B092" }} onClick={() => showModal(record)}>返航设高</a>


          <a style={{ color: "#18B092" }} onClick={() => showMap(record)}>航线点位</a>


          <a style={{ color: "#18B092" }} onClick={() => download(getImgUrl(record.ObjectName))}>下载</a>


          <WayLineEditPage
            key={record.ID}
            refrush={refrush}
            w1={record}
          ></WayLineEditPage>


          <a style={{ color: "#18B092" }} onClick={() => showDeleteConfirm(record, refrush)}>删除</a>

          {(record.WayLineType === "1" || record.WayLineType === "3" || record.WayLineType === "4") && (

            <a style={{ color: "#18B092" }} onClick={() => revise(record, setPage)}>修改</a>

          )}
        </Space>
      ),
    },
  ];
  const rowClassName = (record) => {
    let className;
    switch (record.WayLineType) {
      case "1":
        className = styles["xunjian-row"];
        break;
      case "2":
        className = styles["patrol-row"];
        break;
      case "3":
        className = styles["inspection-row"];
        break;
      case "4":
        className = styles["mapping-row"];
        break;
      case "自动创建测绘航线":
        className = styles["auto-mapping-row"];
        break;
      default:
        className = styles["default-row"];
    }

    return `${className}`;
  };

  return { columns, rowClassName };
};

export default TableCols;
