import { Space,message, Modal, } from "antd";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { axiosApi } from "@/services/general";
import { useModel } from "umi";
import {columnTranslation} from './TableHelper';

const getTableTitle = (title) => {
  const chineseTitile = columnTranslation[title] || title;
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {chineseTitile}</div>
  );
};


const TableCols = (apiData,getDefaultData,showEditModal) => {
  const { listPage } = useModel("pageModel");

  // 从数据中提取字段生成基础列
  const dynamicColumns = Object.keys(apiData[0] || {})
  .filter(key => key !== 'ID' && key !== 'sn' && key !== 'problem')   // 过滤掉ID，sn，problem字段
  .map(key => ({
    title: getTableTitle(key),
    dataIndex: key,
    key: key,
    align: "center"
  }));

  // 根据ID删除数据
  const deleteData = (ID) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条记录吗？",
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        try {
          if (!listPage) return;
          let url = "";
          if (listPage === "项目管理") {
            url = `/api/v1/Gf_Project/Delete?ID=${ID}`;
          } else if (listPage === "组串管理") {
            url = `/api/v1/Guangfu/Delete?ID=${ID}`;
          } else if (listPage === "子阵管理") {
            url = `api/v1/Guangfuzizheng/Delete?ID=${ID}`;
          } else {
            return;
          }
          await axiosApi(url, "POST", null);
          message.success("删除成功");
          getDefaultData();
        } catch (e) {
          console.log(e);
          message.error("删除失败");
        }
      }
    });
  };

  // 添加固定操作列
  return [
    ...dynamicColumns,
    {
      title: getTableTitle("操作"),
      align: "center",
      width: 1,
      minWidth: 200,
      render: (record) => (
        <Space 
          size={"middle"} // 按钮间距
          style={{ 
            display: 'flex',
            justifyContent: 'center',
            whiteSpace: 'nowrap'
          }}
        >
          <MyButton
            style={{ 
              padding: "2px 5px",
              background: "red",
              minWidth: 50
            }}
            onClick={() => {
              deleteData(record.ID);
            }}
          >
            删除
          </MyButton>
          <MyButton
            style={{ 
              padding: "2px 5px",
              background: "#52c41a",
              minWidth: 50
            }}
            onClick={() => showEditModal(record)}
          >
            修改
          </MyButton>
        </Space>
      )
    }
  ];
};


export default TableCols;
