stroes使用zustand进行全局状态管理，下面是 Zustand 的基本使用方法示例：

1. 创建 store：

```javascript
// src/store/index.js
import { create } from 'zustand'

// 创建 store
export const useStore = create((set) => ({
  // 状态
  count: 0,
  user: null,
  loading: false,

  // 修改状态的方法
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  setUser: (user) => set({ user }),
  setLoading: (loading) => set({ loading })
}))
```

2. 在组件中使用：

```javascript
// src/components/Counter.js
import { useStore } from '../store'

function Counter() {
  // 获取状态和方法
  const count = useStore((state) => state.count)
  const increment = useStore((state) => state.increment)
  const decrement = useStore((state) => state.decrement)

  return (
    <div>
      <h2>计数器: {count}</h2>
      <button onClick={increment}>+1</button>
      <button onClick={decrement}>-1</button>
    </div>
  )
}
```

3. 异步操作：

```javascript
// src/store/index.js
export const useStore = create((set) => ({
  user: null,
  loading: false,
  error: null,

  // 异步方法
  login: async (username, password) => {
    set({ loading: true, error: null })
    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        body: JSON.stringify({ username, password })
      })
      const user = await response.json()
      set({ user, loading: false })
    } catch (error) {
      set({ error: error.message, loading: false })
    }
  },

  logout: () => set({ user: null })
}))
```

4. 使用持久化：

```javascript
// src/store/index.js
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useStore = create(
  persist(
    (set) => ({
      user: null,
      token: null,
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token }),
      logout: () => set({ user: null, token: null })
    }),
    {
      name: 'user-storage' // localStorage 的 key
    }
  )
)
```

5. 在非组件中使用：

```javascript
// src/utils/auth.js
import { useStore } from '../store'

export const checkAuth = () => {
  const token = useStore.getState().token
  return !!token
}

export const getUser = () => {
  return useStore.getState().user
}
```

6. 订阅状态变化：

```javascript
// src/store/index.js
const unsub = useStore.subscribe(
  (state) => state.count,
  (count, prevCount) => {
    console.log('count changed from', prevCount, 'to', count)
  }
)

// 取消订阅
// unsub()
```

7. 组合多个 store：

```javascript
// src/store/userStore.js
export const useUserStore = create((set) => ({
  user: null,
  setUser: (user) => set({ user })
}))

// src/store/cartStore.js
export const useCartStore = create((set) => ({
  items: [],
  addItem: (item) => set((state) => ({ 
    items: [...state.items, item] 
  }))
}))

// 在组件中使用
function App() {
  const user = useUserStore((state) => state.user)
  const cartItems = useCartStore((state) => state.items)
  
  return (
    <div>
      <UserInfo user={user} />
      <Cart items={cartItems} />
    </div>
  )
}
```

8. 使用 devtools：

```javascript
// src/store/index.js
import { devtools } from 'zustand/middleware'

export const useStore = create(
  devtools((set) => ({
    count: 0,
    increment: () => set((state) => ({ count: state.count + 1 }))
  }))
)
```

9. 重置 store：

```javascript
// src/store/index.js
const initialState = {
  count: 0,
  user: null,
  loading: false
}

export const useStore = create((set) => ({
  ...initialState,
  reset: () => set(initialState)
}))
```

10. 基本使用模式：

```javascript
// 1. 获取单个状态
const count = useStore((state) => state.count)

// 2. 获取多个状态
const { user, loading } = useStore((state) => ({
  user: state.user,
  loading: state.loading
}))

// 3. 获取方法
const increment = useStore((state) => state.increment)

// 4. 条件渲染
function App() {
  const { user, loading, error } = useStore()

  if (loading) return <div>加载中...</div>
  if (error) return <div>错误: {error}</div>
  if (!user) return <Login />

  return <Dashboard user={user} />
}
```

使用建议：

1. 状态选择器：
```javascript
// 推荐
const count = useStore((state) => state.count)

// 不推荐（可能导致不必要的重渲染）
const { count } = useStore()
```

2. 状态更新：
```javascript
// 推荐
set((state) => ({ count: state.count + 1 }))

// 不推荐
set({ count: count + 1 })
```

3. 异步处理：
```javascript
// 推荐
const fetchData = async () => {
  set({ loading: true })
  try {
    const data = await api.getData()
    set({ data, loading: false })
  } catch (error) {
    set({ error, loading: false })
  }
}
```

4. 组件使用：
```javascript
// 推荐
function Component() {
  const value = useStore((state) => state.value)
  const setValue = useStore((state) => state.setValue)
  // ...
}
```

这些是 Zustand 的基本使用方法，它简单直观，适合大多数项目使用。
