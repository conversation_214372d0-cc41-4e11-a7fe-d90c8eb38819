import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';
import icon1 from '@/assets/icons/device.png';
import icon3 from '@/assets/icons/shexiangtou.png';
import icon4 from '@/assets/icons/xunluodui.png';
import icon6 from '@/assets/icons/liaowangta.png';
import { BorderBox7 } from '@jiaminghi/data-view-react';

const JianKongPanel = () => {
  const getItem=(img1,title,count,bCol)=>{
   return <div style={{background:bCol,marginTop:8.0, display: 'flex', height:36.0 ,fontSize:14.0}}>
    <div style={{width:50,marginLeft:8.0}}><img src={img1} height={36.0} width={36.0} /></div>
    <div style={{flex:'auto'}}><p style={{lineHeight:'36px',color:'white'}}>{title}</p></div>
    <div style={{width:50,marginRight:8.0}}><p style={{lineHeight:'36px',color:'white',textAlign:'right'}}>{count}</p></div>
  </div>
  }
  return <div style={{ height:260.0, width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.6)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="巡检设施情况" />
       <div style={{marginTop:24.0}}>
        {getItem(icon1,'全自动无人机场','2 座', `rgba(0,45,139,0.6)`)}
        {getItem(icon3,'监控摄像头','120 个','')}
        {getItem(icon6,'电子瞭望塔','2 座','')}
        {/* {getItem(icon5,'监控杆','80 个',`rgba(0,45,139,0.6)`)}
        {getItem(icon2,'巡逻车辆','5 辆','')} */}
        {getItem(icon4,'巡逻人员','5 人',`rgba(0,45,139,0.6)`)}
       </div>
    </BorderBox7>
  </div>
}

export default JianKongPanel;
