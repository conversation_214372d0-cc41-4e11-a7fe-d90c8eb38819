import { isEmpty } from "@/utils/utils";
import { Card} from 'antd';
import {HmsJson} from './hms.js';
import DeviceJson from './device.json';
import { getBaseColor } from "@/utils/helper";

const ModeCodeJson={"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"}
const CoverStateJson={"0":"关闭","1":"打开","2":"半开","3":"舱盖状态异常"}


const HmsPanel =()=>{
    const hms={"list":[{"args":{"component_index":0,"sensor_index":6},"code":"0x1A420BC6","device_type":"0-91-0","imminent":0,"in_the_sky":1,"level":0,"module":3}]}
    const getHmsStr=(e)=>{
     // 
      if(isEmpty(e["device_type"])){
        return "";
      }

      let key="dock_tip_"+e.code;
      if (!isEmpty( Hms<PERSON><PERSON>[key])){
        return "机场 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

      key="fpv_tip_"+e.code;
      if (!isEmpty( HmsJson[key])){
        return "无人机 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

      key="fpv_tip_"+e.code+"_in_the_sky";
      if (!isEmpty( HmsJson[key])){
        return "无人机 "+DeviceJson[e["device_type"]]+":"+ HmsJson[key].zh+"\n"
      }

    }
    const getMsg=(list)=>{
        let x=""
        list.forEach(e => {
           x=x+getHmsStr(e);
        });
        return x
    }
    console.log('HmsPanel',hms)
    if(isEmpty(hms)) return <div/>
    const data=hms
    if(isEmpty(data.list)) return <div/>
   // console.log(data)
    return <Card bordered={false} size="small" title='告警信息' headStyle={{ color: 'white' }} style={{ background: getBaseColor(), height: '100%', width: '100%' }}>
            <div style={{color:'white',fontSize:14.0}}>{getMsg(data.list)}</div>
    </Card>}

export default HmsPanel;
