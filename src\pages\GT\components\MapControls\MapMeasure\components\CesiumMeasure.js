import React, { useState, useEffect, useCallback, useRef } from 'react';
import * as Cesium from 'cesium';
import { Dropdown, Button, Select, InputNumber, Radio, Tooltip, Modal } from 'antd';
import { AimOutlined, BorderOutlined, ClearOutlined, DownOutlined, BoxPlotOutlined, QuestionCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '../index.module.less'; // 共享样式
import { getGuid } from '@/utils/helper';
import { GenerateTriangularMesh, Cartesian3_TO_Position } from '@/utils/cesium_help';
import * as turf from '@turf/turf';

// 定义测量单位
const DISTANCE_UNITS = {
  METERS: { key: 'm', label: '米', factor: 1 },
  KILOMETERS: { key: 'km', label: '公里', factor: 0.001 },
};

const AREA_UNITS = {
  SQUARE_METERS: { key: 'm²', label: '平方米', factor: 1 },
  SQUARE_KILOMETERS: { key: 'km²', label: '平方公里', factor: 0.000001 },
  HECTARES: { key: 'ha', label: '公顷', factor: 0.0001 },
};

const VOLUME_UNITS = {
  CUBIC_METERS: { key: 'm³', label: '立方米', factor: 1 },
  CUBIC_KILOMETERS: { key: 'km³', label: '立方公里', factor: 0.000000001 },
};

const CesiumMeasure = ({ viewer }) => {
  const [activeTool, setActiveTool] = useState(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [points, setPoints] = useState([]);
  const [measurements, setMeasurements] = useState([]);
  const [distanceUnit, setDistanceUnit] = useState(DISTANCE_UNITS.KILOMETERS);
  const [areaUnit, setAreaUnit] = useState(AREA_UNITS.SQUARE_KILOMETERS);
  const [volumeUnit, setVolumeUnit] = useState(VOLUME_UNITS.CUBIC_METERS);
  const [benchHeight, setBenchHeight] = useState(300);
  const [precision, setPrecision] = useState(20);

  const handlerRef = useRef(null);
  const tempEntitiesRef = useRef([]);
  const measurementEntitiesRef = useRef({});

  const clearTemporaryEntities = useCallback(() => {
    if (viewer && viewer.entities) {
        tempEntitiesRef.current.forEach(entity => viewer.entities.remove(entity));
    }
    tempEntitiesRef.current = [];
    
    // 重置距离测量实体引用
    distanceEntitiesRef.current = {
      polyline: null,
      points: [],
      labels: [],
      totalLabel: null
    };
    
    // 重置面积测量实体引用
    areaEntitiesRef.current = {
      polyline: null,
      polygon: null,
      points: [],
      labels: [],
      totalLabel: null
    };

    // 重置体积测量实体引用
    volumeEntitiesRef.current = {
      polyline: null,
      polygon: null,
      rectangle: null,
      points: [],
      labels: [],
      totalLabel: null
    };
  }, [viewer]);

  const resetDrawingState = useCallback(() => {
    setIsDrawing(false);
    setPoints([]);
    clearTemporaryEntities();
  }, [clearTemporaryEntities]);

  const createPointMarker = useCallback((position, isTemporary = false) => {
    if (!viewer || !viewer.entities) return null;
    // let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(position, viewer)
    const pointEntity = viewer.entities.add({
      // position: new Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
      position: position,
      point: {
        color: Cesium.Color.RED,
        pixelSize: 6,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 1,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    });
    if (isTemporary && pointEntity) { // 确保 pointEntity 成功创建
      tempEntitiesRef.current.push(pointEntity);
    }
    return pointEntity;
  }, [viewer]);

  const createDeleteButtonCanvas = useCallback(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 32; canvas.height = 32;
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0,0,32,32);
    
    // 绘制圆形背景
    ctx.beginPath();
    ctx.arc(16, 16, 12, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(40, 40, 40, 0.8)';
    ctx.fill();
    
    // 添加内部发光效果
    const gradient = ctx.createRadialGradient(16, 16, 8, 16, 16, 12);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    ctx.fillStyle = gradient;
    ctx.fill();
    
    // 绘制白色X图标
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.beginPath();
    ctx.moveTo(11, 11);
    ctx.lineTo(21, 21);
    ctx.moveTo(21, 11);
    ctx.lineTo(11, 21);
    ctx.stroke();
    
    return canvas;
  }, []);

  const createMeasureLabel = useCallback((position, text, measurementId, isTotal = false) => {
    if (!viewer || !viewer.entities) return { labelEntity: null, deleteBillboard: null };

    const labelEntity = viewer.entities.add({
      position: position,
      label: {
        text: text,
        font: '14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL,
        pixelOffset: new Cesium.Cartesian2(isTotal ? 0 : -20, isTotal ? -20 : 15),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.1, 0.1, 0.1, 0.9),
        backgroundPadding: new Cesium.Cartesian2(10, 6),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        eyeOffset: new Cesium.Cartesian3(0.0, 0.0, -10.0),
        horizontalOrigin: isTotal ? Cesium.HorizontalOrigin.CENTER : Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        translucencyByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 1.5e7, 0.4)
      },
      properties: { isMeasurementLabel: true, measurementId: measurementId, isTotalLabel: isTotal }
    });

    let deleteBillboard = null;
    if (isTotal && measurementId) {
      deleteBillboard = viewer.entities.add({
        position: position,
        billboard: {
          image: createDeleteButtonCanvas(),
          width: 24,
          height: 24,
          pixelOffset: new Cesium.Cartesian2(60, -50),
          eyeOffset: new Cesium.Cartesian3(0.0, 0.0, -11.0),
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 1.5e7, 0.8),
          translucencyByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 1.5e7, 0.6),
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT
        },
        properties: { isDeleteButton: true, measurementId: measurementId }
      });
    }
    return { labelEntity, deleteBillboard };
  }, [viewer, createDeleteButtonCanvas]);

  const deleteMeasurement = useCallback((measurementId) => {
    if (!viewer || !viewer.entities) return;
    const measurementData = measurementEntitiesRef.current[measurementId];
    if (measurementData) {
      measurementData.entities.forEach(entity => viewer.entities.remove(entity));
      if (measurementData.deleteBillboard) viewer.entities.remove(measurementData.deleteBillboard);
      delete measurementEntitiesRef.current[measurementId];
    }
    setMeasurements(prev => prev.filter(m => m.id !== measurementId));
  }, [viewer]);

  const formatDistance = useCallback((distanceInMeters) => {
    const convertedDistance = distanceInMeters * distanceUnit.factor;
    return `${convertedDistance.toFixed(2)} ${distanceUnit.label}`;
  }, [distanceUnit]);

  const formatArea = useCallback((areaInSquareMeters) => {
    const convertedArea = areaInSquareMeters * areaUnit.factor;
    return `${convertedArea.toFixed(2)} ${areaUnit.label}`;
  }, [areaUnit]);

  const formatVolume = useCallback((volumeInCubicMeters) => {
    const convertedVolume = volumeInCubicMeters * volumeUnit.factor;
    return `${convertedVolume.toFixed(2)} ${volumeUnit.label}`;
  }, [volumeUnit]);

  const getGeodesicDistance = useCallback((cartesianPoint1, cartesianPoint2) => {
    if (!viewer || !viewer.scene || !viewer.scene.globe || !viewer.scene.globe.ellipsoid || !cartesianPoint1 || !cartesianPoint2) return 0;
    const carto1 = Cesium.Cartographic.fromCartesian(cartesianPoint1);
    const carto2 = Cesium.Cartographic.fromCartesian(cartesianPoint2);
    const geodesic = new Cesium.EllipsoidGeodesic(carto1, carto2, viewer.scene.globe.ellipsoid);
    return geodesic.surfaceDistance;
  }, [viewer]);

  const calculateTotalDistance = useCallback((currentPoints) => {
    let totalDistance = 0;
    for (let i = 0; i < currentPoints.length - 1; i++) {
      totalDistance += getGeodesicDistance(currentPoints[i], currentPoints[i + 1]);
    }
    return totalDistance;
  }, [getGeodesicDistance]);

  const calculateSphericalArea = useCallback((cartesianPoints) => {
    if (!cartesianPoints || cartesianPoints.length < 3 || !viewer || !viewer.scene || !viewer.scene.globe) return 0;
    // 将Cartesian3坐标转换为经纬度数组
    const positions = [];
    cartesianPoints.forEach(point => {
      const cartographic = Cesium.Cartographic.fromCartesian(point);
      positions.push(
        Cesium.Math.toDegrees(cartographic.longitude),
        Cesium.Math.toDegrees(cartographic.latitude),
        cartographic.height
      );
    });

    // 使用GenerateTriangularMesh计算表面积
    const { surfaceArea } = GenerateTriangularMesh(viewer, positions, 'polygon', null, 20);
    return surfaceArea;
  }, [viewer]);

  const calculateVolumeData = useCallback((cartesianPoints, benchHeightValue, precisionValue) => {
    if (!cartesianPoints || cartesianPoints.length < 3 || !viewer || !viewer.scene || !viewer.scene.globe) {
      return { surfaceArea: 0, wf: 0, tf: 0 };
    }

    // 将Cartesian3坐标转换为经纬度数组
    const positions = [];
    cartesianPoints.forEach(point => {
      const cartographic = Cesium.Cartographic.fromCartesian(point);
      positions.push(
        Cesium.Math.toDegrees(cartographic.longitude),
        Cesium.Math.toDegrees(cartographic.latitude),
        cartographic.height
      );
    });

    // 使用GenerateTriangularMesh计算体积数据
    const { surfaceArea, wf, tf } = GenerateTriangularMesh(viewer, positions, 'square', benchHeightValue, precisionValue);
    return { surfaceArea, wf, tf };
  }, [viewer]);

  // 距离测量点位置引用
  const distancePointsRef = useRef([]);
  // 距离测量临时实体
  const distanceEntitiesRef = useRef({
    polyline: null,
    points: [],
    labels: [],
    totalLabel: null
  });

  const drawTemporaryDistance = useCallback((currentPoints, movingPoint) => {
    if (!viewer || !viewer.entities) return;

    // 清除所有临时实体
    clearTemporaryEntities();

    // 创建动态线实体
    distanceEntitiesRef.current.polyline = viewer.entities.add({
      polyline: {
        show: true,
        positions: new Cesium.CallbackProperty(() => {
          // 动态更新线的位置
          const positions = [...currentPoints];
          if (movingPoint && (currentPoints.length === 0 ||
            (currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)))) {
            positions.push(movingPoint);
          }
          return positions.map(p => p.clone());
        }, false),
        width: 2,
        material: Cesium.Color.YELLOW.withAlpha(0.8),
        clampToGround: true,
      }
    });
    tempEntitiesRef.current.push(distanceEntitiesRef.current.polyline);

    // 创建起点标签（仅当有点时创建）
    if (currentPoints.length > 0) {
      const startLabel = createMeasureLabel(currentPoints[0], "起点", null, false);
      if (startLabel.labelEntity) {
        distanceEntitiesRef.current.labels.push(startLabel.labelEntity);
        tempEntitiesRef.current.push(startLabel.labelEntity);
      }
    }

    // 创建总距离标签
    const labelPosition = currentPoints.length > 0 ? currentPoints[currentPoints.length - 1] : movingPoint;
    const totalLabel = createMeasureLabel(labelPosition, new Cesium.CallbackProperty(() => {
      const positions = [...currentPoints];
      if (movingPoint && positions.length > 0) {
        positions.push(movingPoint);
      }
      if (positions.length < 2) return "点击确定起点";
      const totalDistance = calculateTotalDistance(positions);
      return `总长: ${formatDistance(totalDistance)}\n双击结束`;
    }, false), null, true);

    if (totalLabel.labelEntity) {
      distanceEntitiesRef.current.totalLabel = totalLabel.labelEntity;
      tempEntitiesRef.current.push(totalLabel.labelEntity);
    }

    // 添加当前点标记
    currentPoints.forEach(p => {
      const point = createPointMarker(p, true);
      if (point) {
        distanceEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    });

    // 添加移动点标记
    if (movingPoint && currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)) {
      const point = createPointMarker(movingPoint, true);
      if (point) {
        distanceEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    }

    // 添加线段距离标签
    const positions = [...currentPoints];
    if (movingPoint && positions.length > 0) {
      positions.push(movingPoint);
    }

    for (let i = 1; i < positions.length; i++) {
      const dist = getGeodesicDistance(positions[i-1], positions[i]);
      const midpoint = Cesium.Cartesian3.midpoint(positions[i-1], positions[i], new Cesium.Cartesian3());
      const { labelEntity } = createMeasureLabel(midpoint, formatDistance(dist), null, false);
      if (labelEntity) {
        distanceEntitiesRef.current.labels.push(labelEntity);
        tempEntitiesRef.current.push(labelEntity);
      }
    }
  }, [viewer, createPointMarker, getGeodesicDistance, formatDistance, calculateTotalDistance, createMeasureLabel]);

  // 面积测量临时实体
  const areaEntitiesRef = useRef({
    polyline: null,
    polygon: null,
    points: [],
    labels: [],
    totalLabel: null
  });

  // 体积测量临时实体
  const volumeEntitiesRef = useRef({
    polyline: null,
    polygon: null,
    rectangle: null,
    points: [],
    labels: [],
    totalLabel: null
  });

  const drawTemporaryArea = useCallback((currentPoints, movingPoint) => {
    if (!viewer || !viewer.entities) return;

    // 清除所有临时实体
    clearTemporaryEntities();

    // 创建动态线实体
    areaEntitiesRef.current.polyline = viewer.entities.add({
      polyline: {
        show: true,
        positions: new Cesium.CallbackProperty(() => {
          const positions = [...currentPoints];
          if (movingPoint && (currentPoints.length === 0 || 
              (currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)))) {
            positions.push(movingPoint);
          }
          // 闭合多边形
          if (positions.length > 2) {
            positions.push(positions[0]);
          }
          return positions.map(p => p.clone());
        }, false),
        width: 2,
        material: Cesium.Color.YELLOW.withAlpha(0.8),
        clampToGround: true,
      }
    });
    tempEntitiesRef.current.push(areaEntitiesRef.current.polyline);

    // 创建动态面实体
    areaEntitiesRef.current.polygon = viewer.entities.add({
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          const positions = [...currentPoints];
          if (movingPoint && (currentPoints.length === 0 || 
              (currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)))) {
            positions.push(movingPoint);
          }
          const clonedPositions = positions.map(p => p.clone());
          return new Cesium.PolygonHierarchy(clonedPositions);
        }, false),
        material: Cesium.Color.YELLOW.withAlpha(0.3),
        clampToGround: true,
      }
    });
    tempEntitiesRef.current.push(areaEntitiesRef.current.polygon);

    // 创建起点标签（仅当有点时创建）
    if (currentPoints.length > 0) {
      const startLabel = createMeasureLabel(currentPoints[0], "起点", null, false);
      if (startLabel.labelEntity) {
        areaEntitiesRef.current.labels.push(startLabel.labelEntity);
        tempEntitiesRef.current.push(startLabel.labelEntity);
      }
    }

    // 创建总面积标签
    const labelPosition = currentPoints.length > 0 ? currentPoints[currentPoints.length - 1] : movingPoint;
    const areaLabel = createMeasureLabel(labelPosition, new Cesium.CallbackProperty(() => {
      const positions = [...currentPoints];
      if (movingPoint && positions.length > 0) {
        positions.push(movingPoint);
      }
      if (positions.length < 1) return "点击确定起点";
      if (positions.length < 3) return "点击确定点";
      const area = calculateSphericalArea(positions);
      return `面积: ${formatArea(area)}\n双击结束`;
    }, false), null, true);
    
    if (areaLabel.labelEntity) {
      areaEntitiesRef.current.totalLabel = areaLabel.labelEntity;
      tempEntitiesRef.current.push(areaLabel.labelEntity);
    }

    // 添加当前点标记
    currentPoints.forEach(p => {
      const point = createPointMarker(p, true);
      if (point) {
        areaEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    });

    // 添加移动点标记
    if (movingPoint && currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)) {
      const point = createPointMarker(movingPoint, true);
      if (point) {
        areaEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    }
  }, [viewer, createPointMarker, calculateSphericalArea, formatArea, createMeasureLabel, clearTemporaryEntities]);

  const drawTemporaryVolume = useCallback((currentPoints, movingPoint) => {
    if (!viewer || !viewer.entities) return;

    // 清除所有临时实体
    clearTemporaryEntities();

    // 创建动态线实体
    volumeEntitiesRef.current.polyline = viewer.entities.add({
      polyline: {
        show: true,
        positions: new Cesium.CallbackProperty(() => {
          const positions = [...currentPoints];
          if (movingPoint && (currentPoints.length === 0 ||
              (currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)))) {
            positions.push(movingPoint);
          }
          // 闭合多边形
          if (positions.length > 2) {
            positions.push(positions[0]);
          }
          return positions.map(p => p.clone());
        }, false),
        width: 2,
        material: Cesium.Color.ORANGE.withAlpha(0.8),
        clampToGround: true,
      }
    });
    tempEntitiesRef.current.push(volumeEntitiesRef.current.polyline);

    // 创建动态面实体
    volumeEntitiesRef.current.polygon = viewer.entities.add({
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          const positions = [...currentPoints];
          if (movingPoint && (currentPoints.length === 0 ||
              (currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)))) {
            positions.push(movingPoint);
          }
          const clonedPositions = positions.map(p => p.clone());
          return new Cesium.PolygonHierarchy(clonedPositions);
        }, false),
        material: Cesium.Color.ORANGE.withAlpha(0.3),
        clampToGround: true,
      }
    });
    tempEntitiesRef.current.push(volumeEntitiesRef.current.polygon);

    // 创建基准面矩形（如果有足够的点）
    if (currentPoints.length >= 3) {
      const allPoints = [...currentPoints];
      if (movingPoint) allPoints.push(movingPoint);

      // 计算边界矩形
      const cartographics = allPoints.map(point => Cesium.Cartographic.fromCartesian(point));
      const rectangle = Cesium.Rectangle.fromCartographicArray(cartographics);

      volumeEntitiesRef.current.rectangle = viewer.entities.add({
        rectangle: {
          coordinates: rectangle,
          material: Cesium.Color.WHITE.withAlpha(0.5),
          height: benchHeight,
        }
      });
      tempEntitiesRef.current.push(volumeEntitiesRef.current.rectangle);
    }

    // 创建起点标签（仅当有点时创建）
    if (currentPoints.length > 0) {
      const startLabel = createMeasureLabel(currentPoints[0], "起点", null, false);
      if (startLabel.labelEntity) {
        volumeEntitiesRef.current.labels.push(startLabel.labelEntity);
        tempEntitiesRef.current.push(startLabel.labelEntity);
      }
    }

    // 创建总体积标签
    const labelPosition = currentPoints.length > 0 ? currentPoints[currentPoints.length - 1] : movingPoint;
    const volumeLabel = createMeasureLabel(labelPosition, new Cesium.CallbackProperty(() => {
      const positions = [...currentPoints];
      if (movingPoint && positions.length > 0) {
        positions.push(movingPoint);
      }
      if (positions.length < 1) return "点击确定起点";
      if (positions.length < 3) return "点击确定点";
      const { surfaceArea, wf, tf } = calculateVolumeData(positions, benchHeight, precision);
      return `基准高度: ${benchHeight}m\n面积: ${formatArea(surfaceArea)}\n挖方: ${formatVolume(wf)}\n填方: ${formatVolume(tf)}\n双击结束`;
    }, false), null, true);

    if (volumeLabel.labelEntity) {
      volumeEntitiesRef.current.totalLabel = volumeLabel.labelEntity;
      tempEntitiesRef.current.push(volumeLabel.labelEntity);
    }

    // 添加当前点标记
    currentPoints.forEach(p => {
      const point = createPointMarker(p, true);
      if (point) {
        volumeEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    });

    // 添加移动点标记
    if (movingPoint && currentPoints.length > 0 && !Cesium.Cartesian3.equals(currentPoints[currentPoints.length - 1], movingPoint)) {
      const point = createPointMarker(movingPoint, true);
      if (point) {
        volumeEntitiesRef.current.points.push(point);
        tempEntitiesRef.current.push(point);
      }
    }
  }, [viewer, createPointMarker, calculateVolumeData, formatArea, formatVolume, benchHeight, precision, createMeasureLabel, clearTemporaryEntities]);

  // 基准面高度改变处理函数
  const handleBenchHeightChange = useCallback((value) => {
    setBenchHeight(value);
    // 如果正在绘制体积测量，实时更新预览
    if (activeTool === 'volume' && points.length >= 3) {
      drawTemporaryVolume(points, null);
    }
  }, [activeTool, points, drawTemporaryVolume]);

  // 采样精度改变处理函数
  const handlePrecisionChange = useCallback((value) => {
    if (activeTool === 'volume' && points.length >= 3) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined />,
        content: '改变采样精度需要重新计算，这可能会导致浏览器卡顿一段时间...',
        onOk() {
          setPrecision(value);
          // 实时更新预览
          drawTemporaryVolume(points, null);
        },
        onCancel() {
          console.log('取消精度更改');
        },
      });
    } else {
      setPrecision(value);
    }
  }, [activeTool, points, drawTemporaryVolume]);

  const finalizeMeasurement = useCallback(() => {
    if (!viewer || !viewer.entities) return;
    // 鼠标双击结束测量，使用CallbackProperty后，我们需要确保有足够的点
    const minPoints = activeTool === 'distance' ? 2 : 3;
    if (points.length < minPoints) {
      const toolName = activeTool === 'distance' ? '距离' : (activeTool === 'area' ? '面积' : '体积');
      const message = `请至少添加${minPoints}个点以完成${toolName}测量`;
      const { labelEntity } = createMeasureLabel(
        points[points.length - 1] || viewer.scene.camera.position,
        message,
        null,
        true
      );
      if (labelEntity) tempEntitiesRef.current.push(labelEntity);
      setTimeout(() => resetDrawingState(), 2000);
      return;
    }

    const measurementId = getGuid();
    let finalEntities = [];
    let measurementData = { id: measurementId, type: activeTool, points: [...points], value: 0, unit: '' };

    points.forEach(p => {
        const marker = createPointMarker(p, false);
        if(marker) finalEntities.push(marker);
    });

    let labelPosition;
    let labelText;
    let deleteBtnBillboard = null;

    if (activeTool === 'distance') {
      // 绘制线段
      const polyline = viewer.entities.add({
        polyline: {
          positions: points,
          width: 3,
          material: Cesium.Color.fromCssColorString('#1890ff'),
          clampToGround: true,
        },
      });
      if(polyline) finalEntities.push(polyline);
      const totalDistance = calculateTotalDistance(points);
      measurementData.value = totalDistance;
      measurementData.unit = distanceUnit.key;
      labelPosition = points[points.length - 1];
      labelText = `总长: ${formatDistance(totalDistance)}`;
    } else if (activeTool === 'area') {
      // 绘制多边形
      const polygon = viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(points),
          material: Cesium.Color.fromCssColorString('#1890ff').withAlpha(0.3),
          outline: true, outlineColor: Cesium.Color.fromCssColorString('#1890ff'),
          clampToGround: true,
        },
      });
      if(polygon) finalEntities.push(polygon);

      // 绘制边界线
      const borderPoints = [...points, points[0]];
      const borderPolyline = viewer.entities.add({
        polyline: {
          positions: borderPoints,
          width: 2, // 可以调整宽度
          material: Cesium.Color.fromCssColorString('#1890ff'),
          clampToGround: true,
        },
      });
      if (borderPolyline) finalEntities.push(borderPolyline);
      const area = calculateSphericalArea(points);
      measurementData.value = area;
      measurementData.unit = areaUnit.key;
      if (points.length > 0) {
        const boundingSphereOriginal = Cesium.BoundingSphere.fromPoints(points);
        const cartographicCenter = Cesium.Cartographic.fromCartesian(boundingSphereOriginal.center);
        if (cartographicCenter && viewer.scene.globe) {
            const terrainHeightCenter = viewer.scene.globe.getHeight(cartographicCenter);
            if (Cesium.defined(terrainHeightCenter)) {
                labelPosition = Cesium.Cartesian3.fromRadians(cartographicCenter.longitude, cartographicCenter.latitude, terrainHeightCenter);
            } else {
                labelPosition = boundingSphereOriginal.center;
            }
        } else {
            labelPosition = boundingSphereOriginal.center;
        }
      } else { 
        labelPosition = Cesium.Cartesian3.ZERO; 
      }
      labelText = `总面积: ${formatArea(area)}`;
    } else if (activeTool === 'volume') {
      // 绘制多边形
      const polygon = viewer.entities.add({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(points),
          material: Cesium.Color.ORANGE.withAlpha(0.3),
          outline: true, outlineColor: Cesium.Color.ORANGE,
          clampToGround: true,
        },
      });
      if(polygon) finalEntities.push(polygon);

      // 绘制边界线
      const borderPoints = [...points, points[0]];
      const borderPolyline = viewer.entities.add({
        polyline: {
          positions: borderPoints,
          width: 2,
          material: Cesium.Color.ORANGE,
          clampToGround: true,
        },
      });
      if (borderPolyline) finalEntities.push(borderPolyline);

      // 添加基准面矩形
      const cartographics = points.map(point => Cesium.Cartographic.fromCartesian(point));
      const rectangle = Cesium.Rectangle.fromCartographicArray(cartographics);
      const rectangleEntity = viewer.entities.add({
        rectangle: {
          coordinates: rectangle,
          material: Cesium.Color.WHITE.withAlpha(0.5),
          height: benchHeight,
        }
      });
      if (rectangleEntity) finalEntities.push(rectangleEntity);

      const { surfaceArea, wf, tf } = calculateVolumeData(points, benchHeight, precision);
      measurementData.value = { surfaceArea, wf, tf, benchHeight };
      measurementData.unit = volumeUnit.key;
      if (points.length > 0) {
        const boundingSphereOriginal = Cesium.BoundingSphere.fromPoints(points);
        const cartographicCenter = Cesium.Cartographic.fromCartesian(boundingSphereOriginal.center);
        if (cartographicCenter && viewer.scene.globe) {
            const terrainHeightCenter = viewer.scene.globe.getHeight(cartographicCenter);
            if (Cesium.defined(terrainHeightCenter)) {
                labelPosition = Cesium.Cartesian3.fromRadians(cartographicCenter.longitude, cartographicCenter.latitude, terrainHeightCenter);
            } else {
                labelPosition = boundingSphereOriginal.center;
            }
        } else {
            labelPosition = boundingSphereOriginal.center;
        }
      } else {
        labelPosition = Cesium.Cartesian3.ZERO;
      }
      labelText = `基准高度: ${benchHeight}m\n面积: ${formatArea(surfaceArea)}\n挖方: ${formatVolume(wf)}\n填方: ${formatVolume(tf)}`;
    }

    if (labelPosition && labelText) {
      const { labelEntity, deleteBillboard: dbb } = createMeasureLabel(labelPosition, labelText, measurementId, true);
      if(labelEntity) finalEntities.push(labelEntity);
      if (dbb) deleteBtnBillboard = dbb;
    }
    
    if (finalEntities.length > 0) {
        measurementEntitiesRef.current[measurementId] = { entities: finalEntities, deleteBillboard: deleteBtnBillboard };
        setMeasurements(prev => [...prev, measurementData]);
    }
    resetDrawingState();
    setActiveTool(null); // 完成测量后自动退出测量模式
  }, [points, activeTool, viewer, calculateTotalDistance, formatDistance, distanceUnit, calculateSphericalArea, formatArea, areaUnit, calculateVolumeData, formatVolume, volumeUnit, benchHeight, precision, resetDrawingState, createPointMarker, createMeasureLabel]);

  useEffect(() => { 
    if (!viewer || !viewer.entities) return;
    measurements.forEach(m => {
      const measurementEntry = measurementEntitiesRef.current[m.id];
      if (measurementEntry) {
        const labelEntity = measurementEntry.entities.find(e => e.label && e.properties?.isTotalLabel?.getValue());
        if (labelEntity && labelEntity.label) { // Check label exists
          if (m.type === 'distance') {
            labelEntity.label.text = `总长: ${formatDistance(m.value)}`;
          } else if (m.type === 'area') {
            labelEntity.label.text = `总面积: ${formatArea(m.value)}`;
          } else if (m.type === 'volume') {
            const { surfaceArea, wf, tf, benchHeight } = m.value;
            labelEntity.label.text = `基准高度: ${benchHeight}m\n面积: ${formatArea(surfaceArea)}\n挖方: ${formatVolume(wf)}\n填方: ${formatVolume(tf)}`;
          }
        }
      }
    });
  }, [distanceUnit, areaUnit, volumeUnit, measurements, formatDistance, formatArea, formatVolume, viewer]);

  // 删除按钮事件处理器
  const deleteButtonHandler = useRef(null);

  // 初始化删除按钮事件处理器
  useEffect(() => {
    if (!viewer || !viewer.scene || !viewer.scene.canvas) return;

    if (!deleteButtonHandler.current) {
      deleteButtonHandler.current = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      deleteButtonHandler.current.setInputAction(movement => {
        const pickedObject = viewer.scene.pick(movement.position);
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id) && pickedObject.id.properties) {
          const props = pickedObject.id.properties;
          if (props.isDeleteButton && props.measurementId) {
            deleteMeasurement(props.measurementId.getValue());
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }

    return () => {
      if (deleteButtonHandler.current) {
        deleteButtonHandler.current.destroy();
        deleteButtonHandler.current = null;
      }
    };
  }, [viewer, deleteMeasurement]);

  useEffect(() => {
    if (!viewer || !viewer.scene || !viewer.scene.canvas) return;

    if (!activeTool) {
        if (handlerRef.current) {
            handlerRef.current.destroy();
            handlerRef.current = null;
        }
        if (isDrawing || points.length > 0) {
            resetDrawingState();
        }
        viewer.scene.canvas.style.cursor = 'default';
        return;
    }

    viewer.scene.canvas.style.cursor = 'crosshair';
    if (!handlerRef.current) {
        handlerRef.current = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    }

    // 初始化动态绘制
    if (activeTool === 'distance') {
      drawTemporaryDistance(points, null);
    } else if (activeTool === 'area') {
      drawTemporaryArea(points, null);
    } else if (activeTool === 'volume') {
      drawTemporaryVolume(points, null);
    }

    handlerRef.current.setInputAction(movement => {
        let cartesian = viewer.scene.pickPosition(movement.position);
        if (!Cesium.defined(cartesian)) {
            const ray = viewer.camera.getPickRay(movement.position);
            cartesian = viewer.scene.globe.pick(ray, viewer.scene);
        }
        if (!Cesium.defined(cartesian)) return;

        const clickedCartesian = cartesian.clone();
        
        setIsDrawing(true);
        setPoints(prevPoints => {
            const newPoints = [...prevPoints, clickedCartesian];
            // 点击时更新动态绘制，但不需要传递movingPoint
            if (activeTool === 'distance') {
                drawTemporaryDistance(newPoints, null);
            } else if (activeTool === 'area') {
                drawTemporaryArea(newPoints, null);
            } else if (activeTool === 'volume') {
                drawTemporaryVolume(newPoints, null);
            }
            return newPoints;
        });
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handlerRef.current.setInputAction(movement => {
        let movingCartesian = viewer.scene.pickPosition(movement.endPosition);
        if (!Cesium.defined(movingCartesian)) {
            const ray = viewer.camera.getPickRay(movement.endPosition);
            movingCartesian = viewer.scene.globe.pick(ray, viewer.scene);
        }
        if (!Cesium.defined(movingCartesian)) return;
        
        const finalMovingCartesian = movingCartesian.clone();

        // 鼠标移动时更新动态绘制
        if (activeTool === 'distance') {
            drawTemporaryDistance(points, finalMovingCartesian);
        } else if (activeTool === 'area') {
            drawTemporaryArea(points, finalMovingCartesian);
        } else if (activeTool === 'volume') {
            drawTemporaryVolume(points, finalMovingCartesian);
        }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    handlerRef.current.setInputAction(() => {
        if (!isDrawing) return;
        finalizeMeasurement();
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

    return () => {
      if (handlerRef.current) {
        handlerRef.current.destroy();
        handlerRef.current = null;
      }
    };
  }, [
    viewer, activeTool, isDrawing, points,
    resetDrawingState, drawTemporaryDistance, drawTemporaryArea, drawTemporaryVolume,
    finalizeMeasurement, deleteMeasurement, createMeasureLabel,
    clearTemporaryEntities, createPointMarker
  ]);

  const toggleTool = (tool) => {
    if (activeTool === tool) { // 正在关闭当前工具
      if (isDrawing && points.length > 0) {
        finalizeMeasurement(); // 如果正在绘制，则先完成绘制
      }
      setActiveTool(null); // 这会触发useEffect中的清理逻辑
    } else { // 开启新工具或切换工具
      if (isDrawing && points.length > 0) {
        finalizeMeasurement(); // 如果正在绘制，则先完成当前工具的绘制
      }
      // 在设置新工具之前，先重置状态，为新工具做准备
      // setActiveTool 会触发 useEffect，但 resetDrawingState 确保了状态是干净的
      resetDrawingState(); 
      setActiveTool(tool);
    }
  };

  const clearAllMeasurements = () => {
    if (!viewer || !viewer.entities) return;
    Object.values(measurementEntitiesRef.current).forEach(data => {
      data.entities.forEach(entity => viewer.entities.remove(entity));
      if (data.deleteBillboard) viewer.entities.remove(data.deleteBillboard);
    });
    measurementEntitiesRef.current = {};
    setMeasurements([]);
    resetDrawingState(); 
    if (activeTool) {
      setActiveTool(null); 
    }
  };

  const measurementMenuItems = [
    {
      key: 'distance',
      label: ( <a onClick={() => toggleTool('distance')}> <AimOutlined style={{ marginRight: 8, fontSize: '24px' }} /> 测距 </a> ),
      className: activeTool === 'distance' ? styles.activeMenuItem : '',
    },
    {
      key: 'area',
      label: ( <a onClick={() => toggleTool('area')}> <BorderOutlined style={{ marginRight: 8, fontSize: '24px' }} /> 测面 </a> ),
      className: activeTool === 'area' ? styles.activeMenuItem : '',
    },
    {
      key: 'volume',
      label: ( <a onClick={() => toggleTool('volume')}> <BoxPlotOutlined style={{ marginRight: 8, fontSize: '24px' }} /> 方 </a> ),
      className: activeTool === 'volume' ? styles.activeMenuItem : '',
    },
    {
      key: 'clear',
      label: ( <a onClick={clearAllMeasurements}> <ClearOutlined style={{ marginRight: 8, fontSize: '24px' }} /> 清空测量 </a> ),
    },
  ];

  const menuTitle = ( <Button type="default" className={styles.measureDropdownButton}> 测量 <DownOutlined /> </Button> );

  return (
    <div className={styles.measureBar}>
      <Dropdown menu={{ items: measurementMenuItems }} placement="bottomRight" arrow>
        {menuTitle}
      </Dropdown>
      
      {(activeTool === 'distance' || measurements.some(m => m.type === 'distance')) && (
        <div className={styles.unitSelector}>
          <span>距离单位:</span>
          <Select
            size="small"
            value={distanceUnit.key}
            onChange={(value) => setDistanceUnit(Object.values(DISTANCE_UNITS).find(u => u.key === value))}
            className={styles.select}
          >
            {Object.values(DISTANCE_UNITS).map(unit => (
              <Select.Option key={unit.key} value={unit.key}>{unit.label}</Select.Option>
            ))}
          </Select>
        </div>
      )}
      {(activeTool === 'area' || measurements.some(m => m.type === 'area')) && (
        <div className={styles.unitSelector}>
          <span>面积单位:</span>
          <Select
            size="small"
            value={areaUnit.key}
            onChange={(value) => setAreaUnit(Object.values(AREA_UNITS).find(u => u.key === value))}
            className={`${styles.select} ${styles.area}`}
          >
            {Object.values(AREA_UNITS).map(unit => (
              <Select.Option key={unit.key} value={unit.key}>{unit.label}</Select.Option>
            ))}
          </Select>
        </div>
      )}
      {(activeTool === 'volume' || measurements.some(m => m.type === 'volume')) && (
        <div className={styles.unitSelector}>
          <span>体积单位:</span>
          <Select
            size="small"
            value={volumeUnit.key}
            onChange={(value) => setVolumeUnit(Object.values(VOLUME_UNITS).find(u => u.key === value))}
            className={`${styles.select} ${styles.volume}`}
          >
            {Object.values(VOLUME_UNITS).map(unit => (
              <Select.Option key={unit.key} value={unit.key}>{unit.label}</Select.Option>
            ))}
          </Select>
        </div>
      )}

      {activeTool === 'volume' && (
        <div className={styles.parameterPanel} style={{
          borderRadius: 5,
          width: 280,
          padding: 10,
          // backgroundColor: '#ffffff',
          // border: '1px solid #d9d9d9',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <div style={{
            height: 40,
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <Tooltip placement="bottom" title={'以此高度为平面进行填挖方计算(WGS84高度)'}>
              <div style={{ cursor: 'pointer', fontSize: 13 }}>
                <QuestionCircleOutlined /> 平整面高度:
              </div>
            </Tooltip>
            <Tooltip placement="top" title={'输入完成后按回车确认'}>
              <InputNumber
                style={{ width: 150, height: 30, backgroundColor: '#ffffff' }}
                precision={2}
                addonAfter={'米'}
                value={benchHeight}
                onPressEnter={(e) => {
                  handleBenchHeightChange(Number(e.target.value));
                }}
                onChange={handleBenchHeightChange}
              />
            </Tooltip>
          </div>
          <div style={{
            height: 40,
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <Tooltip placement="bottom" title={'采样密度越高，计算结果越准确，但是计算时间也会越长'}>
              <div style={{ cursor: 'pointer', fontSize: 13 }}>
                <QuestionCircleOutlined /> 采样密度:
              </div>
            </Tooltip>
            <Radio.Group
              options={[
                { label:  <span style={{ color: '#ffffff' }}>低</span>, value: 20 },
                { label:  <span style={{ color: '#ffffff' }}>中</span>, value: 40 },
                { label:  <span style={{ color: '#ffffff' }}>高</span>, value: 60 },
              ]}
              onChange={(e) => {
                handlePrecisionChange(e.target.value);
              }}
              value={precision}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CesiumMeasure;
