import { isEmpty } from "@/utils/utils";

import { Card, Space, Descriptions, Modal } from 'antd';
//import BaseMap from "@/pages/Maps/BaseMap";
import { useModel } from "umi";
import bpImg from "@/assets/drcImgs/bp3.png";
import tb1Img from "@/assets/drcImgs/tb2.png";


import { getBaseColor, getGuid, getLocation } from "@/utils/helper";
import { getAngle } from "./helper";


const ModeCodeJson = { "0": "空闲中", "1": "现场调试", "2": "远程调试", "3": "固件升级中", "4": "作业中" }
const CoverStateJson = { "0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常" }

const CoverRainJson = ["无雨","小雨","中雨","大雨"];

const OsdPanel = () => {

  //const { drc } = useModel('drcModel');
  const device = JSON.parse(localStorage.getItem('device'))

  

  const getItem = (label, val, dw) => {
    return <Descriptions.Item key={getGuid()} labelStyle={{ fontSize: 12.0, color: 'white' }} contentStyle={{ fontSize: 12.0, color: 'white' }} label={label}>{val + dw}</Descriptions.Item>
  }

  const getPanel = (list) => {
    return <Descriptions style={{ fontSize: 12.0, color: 'white' }} column={2}>
      {list}
    </Descriptions>
  }

  const panel2 = (data) => {

    const list = [];
    if(isEmpty(data)) return;
    list.push(getItem("经度", data?.longitude?.toFixed(3), ""));
    list.push(getItem("纬度", data?.latitude?.toFixed(3), ""));
    list.push(getItem("高度", data?.height?.toFixed(1), ""));
    list.push(getItem("相对高度", data?.height?.toFixed(1), ""));
    list.push(getItem("x速度", data?.speed_x?.toFixed(1), ""));
    list.push(getItem("y速度", data?.speed_y?.toFixed(1), ""));
    list.push(getItem("z速度", data?.speed_z?.toFixed(1), ""));
    list.push(getItem("云台pitch角", data?.gimbal_pitch?.toFixed(1), ""));
    list.push(getItem("云台roll角", data?.gimbal_roll?.toFixed(1), ""));
    list.push(getItem("云台yaw角", data?.gimbal_yaw?.toFixed(1), ""));
    return getPanel(list);
  }

  const panel3=()=>{
      <div style={{height:200,width:200}}><img src={bpImg}></img></div>
  }
  
  const getDeg=(lat,lng)=>{
      const d1=getAngle(lat,lng,device.Lat,device.Lng);
      const dx=Math.sin(d1)*80
      const dy=Math.cos(d1)*80

     // 
      return d1;
  }


  const getXY=(x0,y0,d1,dis)=>{
    let dx=Math.sin(d1)*dis
    let dy=Math.cos(d1)*dis
   // 
    if (d1<=90){
      return [x0+dx,y0-dy]
    }
    if (d1<=180){
      return [x0+dx,y0+dy]
    }
    if (d1<=270){
      return [x0-dx,y0+dy]
    }
    return [x0-dx,y0-dy]
}


  
  const jd1=getDeg(device.Lat+1,device.Lng+1);
  const p2=getXY(60,60,180,40)
  return  <div style={{height:120,width:120}}>
    <img draggable={false}  height={120} width={120} src={bpImg}></img>
    <img draggable={false} style={{transform: `rotate(${0}deg)`, position:'absolute',top:48,left:50}} height={20} width={20} src={tb1Img}></img>
    <img draggable={false} style={{transform: `rotate(${0}deg)`, position:'absolute',top:p2[1]-12,left:p2[0]-10}} height={20} width={20} src={tb1Img}></img>
    
    </div>
}

export default OsdPanel;
