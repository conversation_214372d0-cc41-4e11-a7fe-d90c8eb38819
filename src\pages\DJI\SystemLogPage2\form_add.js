import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Form, DatePicker, Radio, Descriptions, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';
import 'dayjs/locale/zh-cn';
import { HPost2 } from '@/utils/request';

const CronAddForm = (props) => {
  const {org, refrush } = props; 
  const [data,setData]=useState(org);

  const onSave =async (e) => {
   // 
    if (isEmpty(data['OrgName'])) { message.info("请输入组织名称！");return ; };
    if (isEmpty(data['OrgCode'])) { message.info("请输入组织代码！");return ; };
    if (isEmpty(data['ZhiBan'])) { message.info("请输入联系人！");return ; };
    if (isEmpty(data['JianKong'])) { message.info("请输入联系电话！");return ; };

   const xx=await  HPost2('/api/v1/OrgInfo/Add',data);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
    }
    refrush();
  };

  return <Form

    labelCol={{
      span: 4,
    }}

    wrapperCol={{
      span: 14,
    }}
    layout="horizontal"
    //disabled={componentDisabled}
    style={{
      maxWidth: 600,
    }}
  >

    <Form.Item label="组织名称">
      <Input
        defaultValue={org.OrgName}
        onChange={(e)=>setData({...data,OrgName:e.target.value})}>
      </Input>
    </Form.Item>
    <Form.Item label="组织代码">
      <Input
       defaultValue={org.OrgCode}
       onChange={(e)=>setData({...data,OrgCode:e.target.value})}>
      </Input>
    </Form.Item>
    <Form.Item label="联系人">
      <Input
      defaultValue={org.ZhiBan}
      onChange={(e)=>setData({...data,ZhiBan:e.target.value})}>
      </Input>
    </Form.Item>
    <Form.Item label="联系电话">
      <Input
        defaultValue={org.JianKong}
        onChange={(e)=>setData({...data,JianKong:e.target.value})}>
      </Input>
    </Form.Item>

    <Form.Item label={" "} colon={false}>
      <Button type="primary" onClick={onSave}>
        提交
      </Button>
    </Form.Item>
  </Form>

}



export default CronAddForm;