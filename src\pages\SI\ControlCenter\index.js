/** 云控中心 */

import React, { useState, useEffect, lazy, Suspense } from 'react';
import { useModel, useLocation } from 'umi';
import { ConfigProvider } from 'antd';
import locale from 'antd/locale/zh_CN';
// 图标
import { BranchesOutlined } from '@ant-design/icons';
// 组件
import MyMenu from '@/pages/SI/components/MyMenu';
import HeadTabs from '@/pages/GT/components/HeadTabs';
import FlightTask from '@/pages/SI/components/flightTask';
import DynamicBackgroundMask from '@/components/DynamicBackgroundMask';

/** 以下为子页面，引用的其他页面 */
// 使用React.lazy进行懒加载
// 飞行调度
const FlyDispatch = lazy(() => import('@/pages/DJI/OrgPage/JiaoTongHome'));
// 巡飞任务 （智能巡检）
// const RecordsPage = lazy(() => import('@/pages/GT/components/RecordsPage'));
const RecordsPage = lazy(() => import('@/pages/SI/components/flightTask'));
// 航线管理（航线列表）
const WayLineListPage = lazy(() => import('@/pages/DJI/WayLine/index'));
// 飞行记录
const TaskListPage = lazy(() => import('@/pages/DJI/TaskListPage/index2'));
// 航拍图片
const MediaDataPage = lazy(() => import('@/pages/DJI/MediaDataPage/index'));
// 航拍视频（航拍影像）
const MediaDataPage2 = lazy(() => import('@/pages/DJI/MediaDataPage/index2'));
// 航拍正射（二维正射图）
const Orthophoto = lazy(() => import('@/pages/GT/ZZCH/Pages/VideoManagement/Pages/2DOrthophoto'));
// 飞行区域（电子围栏）
const NfzPage = lazy(() => import('@/pages/DJI/NfzPage'));
// 直播大屏（视频九宫格）
const RtmpPage = lazy(() => import('@/pages/DJI/RtmpPage'));

const FlyPlan = lazy(() => import('@/pages/GT/components/RecordsPage'));
// 测试组件
const TestCommon = lazy(() => import('@/pages/SI/components/TestCommon'));

import styles from './index.module.less';

// 子页面映射表
const subPageMap = {
  flyDispatch: <FlyDispatch className={styles.mapForSI} isMapFullScreen={true} fromSI={true}/>,
  flyTask: <RecordsPage doNotShowLastButton={true}/>,
  flyPlan: <FlyPlan title="巡检计划" />,
  airline: <WayLineListPage doNotShowLastButton={true} />,
  taskList: <TaskListPage doNotShowLastButton={true} />,
  photos: <MediaDataPage doNotShowLastButton={true} />,
  videos: <MediaDataPage2 doNotShowLastButton={true} />,
  orthophoto: <Orthophoto />,
  fence: <NfzPage doNotShowLastButton={true}/>,
  rtmp: <RtmpPage />,
  TestCommon: <TestCommon />,
};

function getItem(label, key, icon, children, type) {
  return {
    label,
    key,
    icon,
    children,
    type,
  };
}
// 配置菜单项
// 导入自定义SVG图标
import flyDispatchSvg from '@/pages/SI/assets/image/flyDispatch.svg';
import flyTaskSvg from '@/pages/SI/assets/image/flyTask.svg';
import airlineSvg from '@/pages/SI/assets/image/airline.svg';
import flyAreaSvg from '@/pages/SI/assets/image/flyArea.svg';
import planSvg from '@/pages/SI/assets/image/plan.svg';
import liveBroadcastSvg from '@/pages/SI/assets/image/liveBroadcast.svg';

const menuItems = [
  getItem('调度', 'flyDispatch', <img src={flyDispatchSvg} alt="飞行调度" style={{ width: '16px', height: '16px' }} />),
  // getItem('巡飞管理', 'flyManage', null),
  getItem('任务', 'flyTask', <img src={flyTaskSvg} alt="巡飞任务" style={{ width: '16px', height: '16px' }} />),
  getItem('计划', 'flyPlan', <img src={planSvg} alt="巡检计划" style={{ width: '16px', height: '16px' }} />),
  getItem('航线', 'airline', <img src={airlineSvg} alt="航线管理" style={{ width: '20px', height: '20px' }} />),
  getItem('成果', 'taskList', <img src={airlineSvg} alt="飞行记录" style={{ width: '20px', height: '20px' }} />),
  // getItem('影像记录', 'imageRecords', null),
  // getItem('航拍图片', 'photos', null),
  // getItem('航拍视频', 'videos', null),
  // getItem('航拍正射', 'orthophoto', null),
  getItem('区域', 'fence', <img src={flyAreaSvg} alt="飞行区域" style={{ width: '16px', height: '13px' }} />),
  getItem('直播', 'rtmp', <img src={liveBroadcastSvg} alt="直播大屏" style={{ width: '20px', height: '20px' }} />),
  // getItem('测试', 'TestCommon', <img src={flyAreaSvg} alt="测试" style={{ width: '16px', height: '16px' }} />),
];

const ControlCenter = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { setPage, page } = useModel('pageModel');

  // 初始化页面
  useEffect(() => {
    // 检查是否是从驾驶舱页面进入的遥控模式
    const fromCockpit = localStorage.getItem('fromCockpit') === 'true';
    if (!fromCockpit) {
      setPage(subPageMap['flyDispatch']);
    }
    localStorage.removeItem('fromCockpit');
  }, []);

  // 处理菜单点击
  const handleMenuSelect = key => {
    // 设置当前页面
    setPage(subPageMap[key]);
  };

  // 处理标签页切换
  const handleTabChange = key => {
    if (key) {
      setPage(subPageMap[key]);
    } else {
      console.warn('[ControlCenter] 未找到标签页key对应的标题:', key);
    }
  };

  return (
    <div style={{ height: '100%', width: '100vw', overflow: 'hidden', position: 'relative' }}>
      {/* 使用原有的MyMenu组件 */}
      <MyMenu
        className={styles.menuContainer}
        handlePageChange={handleMenuSelect}
        setCollapsed={setCollapsed}
        collapsed={collapsed}
        menuItems={menuItems}
      />

      {/* 主内容区域 */}
      <div
        style={{
          marginLeft: collapsed ? 55 : 55, // 这儿的宽度需要根据MyMenu的width来调整
          transition: 'margin-left 0.2s',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <ConfigProvider locale={locale}>
          {/* <HeadTabs handletabChange={handleTabChange} menuItems={menuItems} /> */}

          <div style={{ height: '100%', width: '100%', overflow: 'hidden' }}>{page}</div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default ControlCenter;
