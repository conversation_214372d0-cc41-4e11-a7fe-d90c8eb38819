// CoordinateTransformer.js
// 坐标转换工具类 - 从HTML中提取的计算方法

// 注意：仅需安装 proj4
// npm install proj4

import  proj4 from 'proj4';
import nj from 'numjs';  // 或 const nj = require('numjs');
import * as math from 'mathjs'; // 或 const math = require('mathjs');

// OpenCV将通过公共目录下的静态文件加载
// 需要初始化和检测OpenCV
let cvReady = false;
let cv = null;

// 初始化OpenCV
export function initializeOpenCV() {
  cv = window.cv;
  cvReady = true;
  //resolve(cv);
 
}

// 检查OpenCV是否已加载
export function isOpenCVReady() {
  return cvReady;
}

// 初始化坐标系统定义
function initializeCoordinateSystems() {
  if (!proj4.defs['EPSG:4326']) {
    proj4.defs('EPSG:4326', '+proj=longlat +datum=WGS84 +no_defs');
  }
  
  if (!proj4.defs['EPSG:4544']) {
    proj4.defs('EPSG:4544', '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs');
  }
}
  // 坐标转换函数
  export function transformCoordinateSingle(point) {
    //log("transformCoordinateSingle 输入", point);
    
    // 定义坐标系统
    // WGS84地理坐标系
    if (!proj4.defs['EPSG:4326']) {
        proj4.defs('EPSG:4326', '+proj=longlat +datum=WGS84 +no_defs');
    }
    
    // 常用的投影坐标系
    // CGCS2000 / 3-degree Gauss-Kruger CM 114E - 中国2000坐标系高斯克吕格投影，中央经线114度
    if (!proj4.defs['EPSG:4544']) {
        proj4.defs('EPSG:4544', '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs');
    }
    
    // 新增UTM投影坐标系 (UTM 48N - 适用于中国西部地区)
    if (!proj4.defs['EPSG:32648']) {
        proj4.defs('EPSG:32648', '+proj=utm +zone=48 +datum=WGS84 +units=m +no_defs');
    }
    
    // 新增Web墨卡托投影 - 适用于Web地图显示
    if (!proj4.defs['EPSG:3857']) {
        proj4.defs('EPSG:3857', '+proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +wktext +no_defs');
    }
    
    // 自动选择合适的投影坐标系
    // 根据经度确定使用哪个投影带
    const lon = point[1];
    let targetEPSG = 'EPSG:4544'; // 默认使用CGCS2000 / 114E
    
    // 根据经度选择合适的高斯克吕格投影带
    // 中国地区通常使用3度带投影
    // 计算中央经线：向下取整为3的倍数，然后加1.5
    if (lon >= 73.5 && lon <= 135) {
        const zoneNum = Math.floor(lon / 3) * 3 + 1.5;
        // 中国2000坐标系高斯克吕格投影的中央经线动态计算
        const customProjStr = '+proj=tmerc +lat_0=0 +lon_0=' + zoneNum + 
                             ' +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs';
        const customEPSG = 'CUSTOM:GK' + zoneNum;
        if (!proj4.defs[customEPSG]) {
            proj4.defs(customEPSG, customProjStr);
        }
        targetEPSG = customEPSG;
      //  log("使用自定义高斯克吕格投影", {中央经线: zoneNum, 投影名称: customEPSG});
    } 
    // 国际地区可以使用UTM投影或Web墨卡托
    else if (lon < 73.5 || lon > 135) {
        // 计算UTM带号
        const utmZone = Math.floor((lon + 180) / 6) + 1;
        const utmEPSG = 'EPSG:' + (32600 + utmZone);
        
        // 使用Web墨卡托或UTM投影
        targetEPSG = 'EPSG:3857'; // 默认使用Web墨卡托
        
        // 如果已定义了对应的UTM投影，则使用UTM
        if (utmZone >= 1 && utmZone <= 60) {
            if (!proj4.defs[utmEPSG]) {
                proj4.defs(utmEPSG, '+proj=utm +zone=' + utmZone + ' +datum=WGS84 +units=m +no_defs');
            }
            targetEPSG = utmEPSG;
          //  log("使用UTM投影", {UTM带号: utmZone, 投影名称: utmEPSG});
        } else {
          //  log("使用Web墨卡托投影", {投影名称: 'EPSG:3857'});
        }
    }
    
    try {
        // 执行坐标转换 WGS84 [lon, lat, height] -> 投影坐标系 [x, y]
        // 注意：proj4输入为[lon, lat]，而我们的输入是[lat, lon]
        const result = proj4('EPSG:4326', targetEPSG, [point[1], point[0]]);
        //log("proj4 转换结果", {结果: result, 源经纬度: [point[1], point[0]], 源坐标系: 'EPSG:4326', 目标坐标系: targetEPSG});
        
        // 返回投影坐标 [x, y, h]
        const finalResult = [result[0], result[1], point[2]];
        //log("transformCoordinateSingle 最终结果", finalResult);
        
        return finalResult;
    } catch (error) {
        //log("坐标转换错误", {错误: error.message, 输入点: [point[1], point[0], point[2]]});
        // 发生错误时返回原始坐标的近似投影，避免程序崩溃
        return [point[1] * 111000, point[0] * 111000, point[2]]; // 简单的度到米的粗略转换
    }
}

// 转换多个坐标
export function transformCoordinate(points) {
   
    
    const result = [];
    try {
        for (let i = 0; i < points.length; i++) {
            const point = points[i];
            
            // 验证点的格式
            if (!Array.isArray(point) || point.length < 3) {
             
                // 使用默认值替代无效点
                result.push([0, 0, 0]);
                continue;
            }
            
            // 确保输入点格式正确，期望的输入格式是 [lon, lat, height]
            // transformCoordinateSingle期望的输入格式是 [lat, lon, height]
            const transformed = transformCoordinateSingle([point[1], point[0], point[2]]);
            result.push(transformed);
        }
    } catch (error) {
     
    }
    
   
    return result;
}

// 计算欧几里得距离
export function distance(point1, point2) {
  return Math.sqrt(Math.pow(point1[0] - point2[0], 2) + Math.pow(point1[1] - point2[1], 2));
}

// 矩阵转置
export function transposeMatrix(m) {
  const result = [];
  for (let i = 0; i < m[0].length; i++) {
    result[i] = [];
    for (let j = 0; j < m.length; j++) {
      result[i][j] = m[j][i];
    }
  }
  return result;
}

// 绕任意轴旋转点
export function rotatePointsAroundAxis(points, axis, angle) {
  if (!cv) return console.log("OpenCV未初始化");
  
  // 将轴转换为单位向量
  const axisVec = new cv.Mat(3, 1, cv.CV_32F);
  axisVec.data32F.set(axis);
  const norm = Math.sqrt(axis[0]*axis[0] + axis[1]*axis[1] + axis[2]*axis[2]);
  for (let i = 0; i < 3; i++) {
    axisVec.data32F[i] /= norm;
  }
  
  // 将角度转换为弧度
  const theta = angle * Math.PI / 180;
  
  // 计算旋转矩阵
  const K = new cv.Mat(3, 3, cv.CV_32F);
  K.data32F[0] = 0;
  K.data32F[1] = -axisVec.data32F[2];
  K.data32F[2] = axisVec.data32F[1];
  K.data32F[3] = axisVec.data32F[2];
  K.data32F[4] = 0;
  K.data32F[5] = -axisVec.data32F[0];
  K.data32F[6] = -axisVec.data32F[1];
  K.data32F[7] = axisVec.data32F[0];
  K.data32F[8] = 0;
  
  const I = cv.Mat.eye(3, 3, cv.CV_32F);
  const KK = new cv.Mat();
  cv.gemm(K, K, 1, new cv.Mat(), 0, KK, 0);
  
  const R = new cv.Mat();
  
  // sin(theta) * K
  const sinK = new cv.Mat();
  const emptyMat = new cv.Mat(3, 3, cv.CV_32F, new cv.Scalar(0));
  cv.addWeighted(K, Math.sin(theta), emptyMat, 0, 0, sinK);
  
  // (1 - cos(theta)) * K*K
  const cosKK = new cv.Mat();
  cv.addWeighted(KK, 1 - Math.cos(theta), emptyMat, 0, 0, cosKK);
  
  // I + sin(theta) * K + (1 - cos(theta)) * K*K
  cv.add(I, sinK, R);
  cv.add(R, cosKK, R);
  
  // 旋转多个点
  const pointsMat = cv.matFromArray(points.length, 3, cv.CV_32F, points.flat());
  const pointsT = new cv.Mat();
  cv.transpose(pointsMat, pointsT);
  
  const RT = new cv.Mat();
  cv.transpose(R, RT);
  
  const rotatedPoints = new cv.Mat();
  cv.gemm(RT, pointsT, 1, new cv.Mat(), 0, rotatedPoints, 0);
  
  // 转换结果为数组
  const result = [];
  const cols = rotatedPoints.cols;
  for (let i = 0; i < cols; i++) {
    result.push([
      rotatedPoints.data32F[i],
      rotatedPoints.data32F[i + cols],
      rotatedPoints.data32F[i + 2 * cols]
    ]);
  }
  
  // 释放内存
  axisVec.delete();
  K.delete();
  I.delete();
  KK.delete();
  R.delete();
  sinK.delete();
  cosKK.delete();
  pointsMat.delete();
  pointsT.delete();
  rotatedPoints.delete();
  RT.delete();
  emptyMat.delete();
  
  return result;
}

// 修改矩阵乘法函数
export function matrixMultiply(a, b) {
  // 使用numjs代替手动实现
  return nj.array(a).dot(nj.array(b)).tolist();
}

// 计算旋转矩阵
export function computeRotationMatrix(pitch, roll, yaw) {
  // X轴旋转矩阵 (pitch)
  const c2w_x = [
    [1, 0, 0],
    [0, math.cos(pitch), -math.sin(pitch)],
    [0, math.sin(pitch), math.cos(pitch)]
  ];
  
  // Y轴旋转矩阵 (roll)
  const c2w_y = [
    [math.cos(roll), 0, math.sin(roll)],
    [0, 1, 0],
    [-math.sin(roll), 0, math.cos(roll)]
  ];
  
  // Z轴旋转矩阵 (yaw)
  const c2w_z = [
    [math.cos(yaw), -math.sin(yaw), 0],
    [math.sin(yaw), math.cos(yaw), 0],
    [0, 0, 1]
  ];
  
  // 矩阵乘法: c2w = c2w_x * c2w_y * c2w_z
  const c2w = nj.array(c2w_x).dot(nj.array(c2w_y)).dot(nj.array(c2w_z)).tolist();
  
  // 创建齐次变换矩阵
  const c2w1 = [
    [c2w[0][0], c2w[0][1], c2w[0][2], 0],
    [c2w[1][0], c2w[1][1], c2w[1][2], 0],
    [c2w[2][0], c2w[2][1], c2w[2][2], 0],
    [0, 0, 0, 1]
  ];
  
  return { c2w, c2w1 };
}

// 创建平移变换矩阵
export function createTransformationMatrix(tvec) {
  return [
    [1, 0, 0, tvec[0]],
    [0, 1, 0, tvec[1]],
    [0, 0, 1, tvec[2]],
    [0, 0, 0, 1]
  ];
}

// 计算投影所需的基本参数
export function computeProjectionParams() {
  // 预设内参参数
  const fx = 3725;
  const fy = 3725;
  const cx = 2640;
  const cy = 1978;
  
  const cameraMatrix = [
    [fx, 0, cx],
    [0, fy, cy],
    [0, 0, 1]
  ];
  
  // 畸变系数
  const distortionCoeffs = [
    -0.11257524,
    0.01487443,
    -0.02706411,
    -0.00008572,
    -0.0000007
  ];
  
  return { cameraMatrix, distortionCoeffs };
}

// 应用坐标变换
export function applyCoordinateTransformation(objectPoints, tvec, c2w) {
  if (!cv) return console.log("OpenCV未初始化");
  
  // 准备点数据 - 创建齐次坐标
  const numPoints = objectPoints.length;
  const homogeneousPoints = new cv.Mat(numPoints, 4, cv.CV_32F);
  for (let i = 0; i < numPoints; i++) {
    homogeneousPoints.data32F[i * 4] = objectPoints[i][0];
    homogeneousPoints.data32F[i * 4 + 1] = objectPoints[i][1];
    homogeneousPoints.data32F[i * 4 + 2] = objectPoints[i][2];
    homogeneousPoints.data32F[i * 4 + 3] = 1;
  }
  
  // 创建平移变换矩阵
  const transformMatrix = new cv.Mat(4, 4, cv.CV_32F);
  transformMatrix.setTo(new cv.Scalar(0));
  transformMatrix.data32F[0] = 1;
  transformMatrix.data32F[5] = 1;
  transformMatrix.data32F[10] = 1;
  transformMatrix.data32F[15] = 1;
  transformMatrix.data32F[3] = tvec[0];
  transformMatrix.data32F[7] = tvec[1];
  transformMatrix.data32F[11] = tvec[2];
  
  // 将c2w扩展为4x4矩阵
  const c2w_4x4 = new cv.Mat(4, 4, cv.CV_32F);
  c2w_4x4.setTo(new cv.Scalar(0));
  for (let i = 0; i < 3; i++) {
    for (let j = 0; j < 3; j++) {
      c2w_4x4.data32F[i * 4 + j] = c2w[i][j];
    }
  }
  c2w_4x4.data32F[15] = 1;
  
  // 执行变换
  const homogeneousPointsT = new cv.Mat();
  cv.transpose(homogeneousPoints, homogeneousPointsT);
  
  const transformed1 = new cv.Mat();
  cv.gemm(transformMatrix, homogeneousPointsT, 1, new cv.Mat(), 0, transformed1, 0);
  
  const c2w_4x4T = new cv.Mat();
  cv.transpose(c2w_4x4, c2w_4x4T);
  
  const transformedPoints = new cv.Mat();
  cv.gemm(c2w_4x4T, transformed1, 1, new cv.Mat(), 0, transformedPoints, 0);
  
  // 提取前三行结果 (非齐次坐标)
  const result = [];
  for (let i = 0; i < numPoints; i++) {
    result.push([
      transformedPoints.data32F[i],
      transformedPoints.data32F[i + numPoints],
      transformedPoints.data32F[i + 2 * numPoints]
    ]);
  }
  
  // 释放内存
  homogeneousPoints.delete();
  transformMatrix.delete();
  c2w_4x4.delete();
  homogeneousPointsT.delete();
  transformed1.delete();
  c2w_4x4T.delete();
  transformedPoints.delete();
  
  return result;
}

// 模拟OpenCV的projectPoints函数
export function projectPoints(objectPoints, rvec, tvec, cameraMatrix, distCoeffs) {
  const imagePoints = [];
  
  for (const point of objectPoints) {
    // Z值处理：避免除以零错误
    if (Math.abs(point[2]) < 1e-10) {
      point[2] = 1e-10;
    }
    
    // 透视投影
    const x = point[0] / point[2];
    const y = point[1] / point[2];
    
    // 考虑畸变
    const r2 = x*x + y*y;
    const r4 = r2*r2;
    const r6 = r4*r2;
    
    // 径向畸变
    const k1 = distCoeffs[0];
    const k2 = distCoeffs[1];
    const k3 = distCoeffs[4];
    
    const radialDistortion = 1 + k1*r2 + k2*r4 + k3*r6;
    
    // 切向畸变参数
    const p1 = distCoeffs[2];
    const p2 = distCoeffs[3];
    
    // 切向畸变
    const dx = 2*p1*x*y + p2*(r2 + 2*x*x);
    const dy = p1*(r2 + 2*y*y) + 2*p2*x*y;
    
    // 应用畸变
    const xDistorted = x*radialDistortion + dx;
    const yDistorted = y*radialDistortion + dy;
    
    // 应用内参矩阵
    const u = cameraMatrix[0][0]*xDistorted + cameraMatrix[0][2];
    const v = cameraMatrix[1][1]*yDistorted + cameraMatrix[1][2];
    
    imagePoints.push([u, v]);
  }
  
  return imagePoints;
}

// 将三维点投影到二维图像平面
export function projectPointsToImage(objectPoints, cameraPos, rotationAngles) {
  if (!cv) return console.log("OpenCV未初始化");
  
  // 1. 获取相机内参和畸变系数
  const { cameraMatrix, distortionCoeffs } = computeProjectionParams();
  
  // 2. 计算旋转矩阵
  const { c2w, c2w1 } = computeRotationMatrix(
    Math.PI - 0.1/180*Math.PI,
    -1/180*Math.PI,
    1/180*Math.PI
  );
  
  // 3. 应用坐标变换
  const negCameraPos = [-cameraPos[0], -cameraPos[1], -cameraPos[2]];
  
  const transformedPoints = applyCoordinateTransformation(objectPoints, negCameraPos, c2w);
  
  // 4. 计算并应用旋转轴变换
  const c2wT = transposeMatrix(c2w);
  const zAxis = [0, 0, 1];
  
  // 计算旋转轴
  const axis = [0, 0, 0];
  for (let i = 0; i < 3; i++) {
    for (let j = 0; j < 3; j++) {
      axis[i] += c2wT[i][j] * zAxis[j];
    }
  }
  
  const rotatedPoints = rotatePointsAroundAxis(
    transformedPoints,
    axis,
    -rotationAngles[1]
  );
  
  // 5. 应用第二次旋转
  const finalPoints = rotatePointsAroundAxis(
    rotatedPoints,
    [0.9999, -0.00174533, -0.0148],
    (rotationAngles[0] + 90)
  );
  
  // 6. 使用模拟投影
  const zeroVec = [0, 0, 0];
  const imagePoints = projectPoints(
    finalPoints,
    zeroVec,
    zeroVec,
    cameraMatrix,
    distortionCoeffs
  );
  
  return imagePoints;
}

// 主函数 - 获取投影点
export function getPoints(data) {

  if(!cvReady){
    initializeOpenCV();
  }

  if (!cv) return console.log("OpenCV未初始化");
  
  // 获取相机旋转向量
  const rotationAngles = [
    data.GPitch,
    data.FYaw,
    data.FRoll
  ];
  
  // 获取相机位置并转换到CGCS2000坐标系
  const cameraPos = transformCoordinateSingle([
    data.Lat,
    data.Lon, 
    data.H
  ]);
  
  // 转换目标点坐标
  const points = transformCoordinate(data.point);
  
  // 执行投影计算
  const result = projectPointsToImage(points, cameraPos, rotationAngles);
  
  return result;
}


export function getPoint(drcData,lat,lon,h) {

    // 定义相机参数和位置信息
    const cameraData = {
      GPitch:drcData.gimbal_pitch,
      FYaw: drcData.gimbal_yaw,
      FRoll: drcData.gimbal_roll,
      Lat: drcData.latitude,
      Lon: drcData.longitude,
      H: drcData.height,
      "point": [
        [lon, lat, h],
    ]
    };
  
    const calculationResult = getPoints(cameraData);
  
    return calculationResult[0];
 
}