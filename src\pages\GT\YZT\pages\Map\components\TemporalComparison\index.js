import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react'
import { Spin } from 'antd'
import { CloseOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons'
import styles from './index.module.less'

/**
 * 多时相对比组件
 * 提供水平时间轴选择功能，支持年份和月份的两级选择
 */
const TemporalComparison = ({
  data = [],
  selectedNode = null,
  loading = false,
  onNodeSelect,
  onClose,
  className = ''
}) => {
  const [expandedYears, setExpandedYears] = useState(new Set())
  const [scrollState, setScrollState] = useState({
    hasOverflow: false,
    canScrollLeft: false,
    canScrollRight: false
  })

  const hoverTimeoutRef = useRef(null)
  const scrollWrapperRef = useRef(null)
  const timelineContainerRef = useRef(null)
  const debounceTimeoutRef = useRef(null)

  // 滚动配置
  const SCROLL_AMOUNT = 240
  const SCROLL_TOLERANCE = 5
  const DEBOUNCE_DELAY = 100

  // 检查滚动状态
  const checkScrollability = useCallback(() => {
    const wrapperEl = scrollWrapperRef.current
    const contentEl = timelineContainerRef.current

    if (!wrapperEl || !contentEl || loading || data.length === 0) {
      setScrollState({ hasOverflow: false, canScrollLeft: false, canScrollRight: false })
      return
    }

    const contentWidth = contentEl.scrollWidth
    const { clientWidth, scrollLeft } = wrapperEl

    const isOverflowing = contentWidth > clientWidth + SCROLL_TOLERANCE

    setScrollState({
      hasOverflow: isOverflowing,
      canScrollLeft: isOverflowing && scrollLeft > SCROLL_TOLERANCE,
      canScrollRight: isOverflowing && scrollLeft + clientWidth < contentWidth - SCROLL_TOLERANCE
    })
  }, [loading, data.length])

  // 滚动事件防抖处理
  const debouncedCheckScrollability = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    debounceTimeoutRef.current = setTimeout(checkScrollability, DEBOUNCE_DELAY)
  }, [checkScrollability])

  // 监听滚动事件
  const handleOnScroll = useCallback(() => {
    debouncedCheckScrollability()
  }, [debouncedCheckScrollability])

  // 处理按钮点击滚动
  const handleScroll = useCallback((direction) => {
    const el = scrollWrapperRef.current
    if (!el) return

    el.scrollBy({
      left: direction === 'right' ? SCROLL_AMOUNT : -SCROLL_AMOUNT,
      behavior: 'smooth'
    })
  }, [])

  // 使用 ResizeObserver 监听尺寸变化
  useEffect(() => {
    checkScrollability()

    const wrapperEl = scrollWrapperRef.current
    const contentEl = timelineContainerRef.current
    if (!wrapperEl || !contentEl) return

    const resizeObserver = new ResizeObserver(debouncedCheckScrollability)
    resizeObserver.observe(wrapperEl)
    resizeObserver.observe(contentEl)

    return () => {
      resizeObserver.disconnect()
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [data, loading, debouncedCheckScrollability, checkScrollability])

  // 清理 timeout
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  // 处理年份展开（鼠标悬停）
  const handleYearHover = useCallback((yearRid) => {
    // 清除之前的延迟
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }

    // 设置展开延迟
    hoverTimeoutRef.current = setTimeout(() => {
      setExpandedYears(prev => {
        const newSet = new Set(prev)
        newSet.add(yearRid)
        return newSet
      })
    }, 200) // 200ms延迟
  }, [])

  // 处理年份收起（鼠标离开）
  const handleYearLeave = useCallback((yearRid) => {
    // 清除展开延迟
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }

    // 延迟收起，给用户时间移动到月份菜单
    hoverTimeoutRef.current = setTimeout(() => {
      setExpandedYears(prev => {
        const newSet = new Set(prev)
        newSet.delete(yearRid)
        return newSet
      })
    }, 300) // 300ms延迟收起
  }, [])

  // 处理月份菜单悬停（保持展开）
  const handleMonthsHover = useCallback(() => {
    // 清除收起延迟
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
  }, [])

  // 处理月份菜单离开（收起）
  const handleMonthsLeave = useCallback((yearRid) => {
    // 延迟收起
    hoverTimeoutRef.current = setTimeout(() => {
      setExpandedYears(prev => {
        const newSet = new Set(prev)
        newSet.delete(yearRid)
        return newSet
      })
    }, 100) // 较短延迟
  }, [])

  // 处理时间点选择
  const handleTimePointClick = useCallback((node) => {
    if (node.Attribute?.servicePath && onNodeSelect) {
      onNodeSelect(node)
    }
  }, [onNodeSelect])

  // 处理年份点击（如果年份本身有服务路径）
  const handleYearClick = useCallback((yearNode) => {
    if (yearNode.Attribute?.servicePath && onNodeSelect) {
      onNodeSelect(yearNode)
    }
  }, [onNodeSelect])

  // 按年份排序数据
  const sortedData = useMemo(() => {
    if (!Array.isArray(data)) {
      console.warn('TemporalComparison: data is not an array', data)
      return []
    }

    try {
      return [...data].sort((a, b) => {
        const yearA = parseInt(a?.Name?.match(/\d{4}/)?.[0] || '0', 10)
        const yearB = parseInt(b?.Name?.match(/\d{4}/)?.[0] || '0', 10)

        if (isNaN(yearA) || isNaN(yearB)) {
          console.warn('TemporalComparison: Invalid year format', { a: a?.Name, b: b?.Name })
          return 0
        }

        return yearA - yearB
      })
    } catch (error) {
      console.error('TemporalComparison: Error sorting data', error)
      return []
    }
  }, [data])

  // 获取选中月份对应的年份
  const getSelectedYearRid = useMemo(() => {
    if (!selectedNode) return null

    // 如果选中的是月份节点，找到其父年份
    for (const yearNode of sortedData) {
      if (yearNode.Rid === selectedNode.Rid) {
        return yearNode.Rid // 选中的就是年份
      }
      if (yearNode.Children) {
        for (const monthNode of yearNode.Children) {
          if (monthNode.Rid === selectedNode.Rid) {
            return yearNode.Rid // 选中的是月份，返回父年份
          }
        }
      }
    }
    return null
  }, [selectedNode, sortedData])

  // 滚动按钮组件
  const ScrollButton = ({ direction, isVisible, isDisabled, onClick }) => {
    const isLeft = direction === 'left'
    const Icon = isLeft ? LeftOutlined : RightOutlined
    const className = `${isLeft ? styles.scrollButtonLeft : styles.scrollButtonRight} ${isVisible ? styles.visible : ''}`

    return (
      <button
        type="button"
        className={className}
        tabIndex={isVisible && !isDisabled ? 0 : -1}
        onClick={onClick}
        aria-label={isLeft ? '向左滚动时间轴' : '向右滚动时间轴'}
        aria-hidden={!isVisible}
        disabled={!isVisible || isDisabled}
        title={isLeft ? '向左滚动查看更早的时间' : '向右滚动查看更晚的时间'}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            onClick()
          }
        }}
      >
        <Icon aria-hidden="true" />
      </button>
    )
  }

  return (
    <div className={`${styles.temporalComparison} ${className}`}>
      <div className={styles.temporalContent}>
        {loading ? (
          <div className={styles.temporalLoading}>
            <Spin size="small" />
            <span>加载中...</span>
          </div>
        ) : (
          <div className={styles.temporalTimeline}>
            {/* 滚动容器 */}
            <div
              ref={scrollWrapperRef}
              className={styles.scrollWrapper}
              onScroll={handleOnScroll}
            >
              <div ref={timelineContainerRef} className={styles.timelineContainer}>
                {sortedData.map((yearNode) => {
                  const isYearExpanded = expandedYears.has(yearNode.Rid)
                  const isYearHighlighted = getSelectedYearRid === yearNode.Rid // 年份高亮（包括选中月份时）
                  const hasYearService = yearNode.Attribute?.servicePath
                  const hasChildren = yearNode.Children && yearNode.Children.length > 0

                return (
                  <div
                    key={yearNode.Rid}
                    className={`${styles.timelineYearGroup} ${isYearExpanded ? styles.expanded : ''}`}
                    onMouseEnter={() => hasChildren && handleYearHover(yearNode.Rid)}
                    onMouseLeave={() => hasChildren && handleYearLeave(yearNode.Rid)}
                  >
                    {/* 年份节点 */}
                    <div
                      className={`${styles.timelineYear} ${isYearHighlighted ? styles.selected : ''} ${hasYearService ? styles.clickable : ''}`}
                      onClick={() => handleYearClick(yearNode)}
                    >
                      <div className={styles.yearContent}>
                        <div className={styles.yearLabel}>{yearNode.Name}</div>
                      </div>
                    </div>

                    {/* 月份节点 */}
                    {hasChildren && isYearExpanded && (
                      <div
                        className={styles.timelineMonths}
                        onMouseEnter={() => handleMonthsHover()}
                        onMouseLeave={() => handleMonthsLeave(yearNode.Rid)}
                      >
                        {yearNode.Children.map((monthNode) => {
                          const isMonthSelected = selectedNode && selectedNode.Rid === monthNode.Rid
                          const hasMonthService = monthNode.Attribute?.servicePath

                          return (
                            <div
                              key={monthNode.Rid}
                              className={`${styles.timelineMonth} ${isMonthSelected ? styles.selected : ''} ${hasMonthService ? styles.clickable : ''}`}
                              onClick={() => handleTimePointClick(monthNode)}
                            >
                              <div className={styles.monthContent}>
                                <div className={styles.monthLabel}>{monthNode.Name}</div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )}
                  </div>
                )
                })}
              </div>
            </div>
          </div>
        )}

        {/* 滚动按钮 - 放在 temporal-content 同级 */}
        {!loading && (
          <>
            {/* 左滚动按钮 */}
            <ScrollButton
              direction="left"
              isVisible={scrollState.hasOverflow}
              isDisabled={!scrollState.canScrollLeft}
              onClick={() => handleScroll('left')}
            />

            {/* 右滚动按钮 */}
            <ScrollButton
              direction="right"
              isVisible={scrollState.hasOverflow}
              isDisabled={!scrollState.canScrollRight}
              onClick={() => handleScroll('right')}
            />
          </>
        )}
      </div>
    </div>
  )
}

export default TemporalComparison
