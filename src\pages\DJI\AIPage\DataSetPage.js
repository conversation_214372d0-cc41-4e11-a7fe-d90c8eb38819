import { Get2, Post2 } from "@/services/general";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  List,
  Row,
  Select,
  Radio,
  Upload,
  Pagination,
  message,
} from "antd";
import { useRef, useState } from "react";
import { useEffect } from "react";
import AIMediaPanel from "./DatasetImgPanel";
import { getImgUrl2, getImgUrlByAli } from "@/utils/utils";
import { UpProps } from "./helper";
import ShowDeleteConfirm from "@/components/ShowDeleteConfirm";
import VirtualList from "rc-virtual-list";
import { isEmpty } from "@/utils/utils";
import { useCallback } from "react";
import useConfigStore from "@/stores/configStore";

const DatasetPage = ({ model }) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [total, setTotal] = useState(100); // 总数据量
  const [pageSize, setPageSize] = useState(10); // 每页显示数据量
  const [data, setData] = useState([]);
  const [dCount, setDCount] = useState(0);
  const [IsMarked, setIsMarked] = useState(''); // 是否已经标注 空：查询所有；1：查询已标注；2：查询未标注
  const [page, setPage] = useState(1);
  const [selectIndex, setSelectIndex] = useState(0);
  const [index2, setIndex2] = useState(0);
  const [ifDraw, setIfDraw] = useState(false);
  const [labelID, setLabelID] = useState(0);
  const List1 = useRef([]);
  const AList = model.AList.split(",");
  const onScroll = useCallback(
    async (e) => {
      const { scrollTop, clientHeight, scrollHeight } = e.target;
      // 检测是否滚动到底部
      if (scrollHeight - scrollTop <= clientHeight + 50) {
        // 加载下一页
        if (data && data.length < dCount) {
          setPage((prevPage) => prevPage + 1);
        }
      }
    },
    [data?.length, dCount]
  );

  // useEffect(() => {
  //   const getData = async () => {
  //     const pst = await Get2("/api/v1/AIDataset/GetCount?id=" + model.Guid, {});
  //     setDCount(pst);
  //     setPage(1);
  //   };
  //   getData();
  // }, []);

  useEffect(() => {
    const getData = async () => {
      const pst = await Get2("/api/v1/AIDataset/GetCountByLable?id=" + model.Guid, {});

      if (pst) {
        switch (IsMarked) {
          case "":
            // 计算所有 sl 的总和
            const total = pst.reduce((acc, item) => acc + Number(item.sl), 0);
            setDCount(total);
            break;
          case "1":
            // 查找 bzzt="1" 的项
            const marked = pst.find(item => item.bzzt === "1");
            setDCount(marked ? Number(marked.sl) : 0);
            break;
          case "2":
            // 查找 bzzt="0" 的项
            const unmarked = pst.find(item => item.bzzt === "0");
            setDCount(unmarked ? Number(unmarked.sl) : 0);
            break;
        }
      }else{
        setDCount(0);
      }

      setPage(1);
    };
    getData();
  }, [IsMarked]);



  const getData = async () => {
    const pst = await Get2('/api/v1/AIDataset/GetList?id=' + model.Guid + "&p1=" + page + "&p2=6" + "&zt=" + IsMarked, {});
    setData(pst);
    if (isEmpty(pst)) return;

    if (pst.length > 1) {
      setSelectIndex(0)
    }

  }
  // const getData = async () => {
  //   const pst = await Get2(
  //     "/api/v1/AIDataset/GetList?id=" + model.Guid + "&p1=" + page + "&p2=5",
  //     {}
  //   );
  //   if (pst && pst.length > 1) {
  //     setData((prevData) => [...prevData, ...pst]);
  //     if (page === 0) {
  //       setSelectIndex(0);
  //     }
  //   }
  // };
  const refrush = async () => {
    const pst = await Get2(
      "/api/v1/AIDataset/GetList?id=" + model.Guid + "&p1=" + page + "&p2=6",
      {}
    );

    setData(pst);
    if (pst && pst.length <= 1) {
      //如果刷新时只有一条数据时，索引值为0，跳到第一个按钮
      setSelectIndex(0);
    } else if (selectIndex >= pst?.length - 1) {
      //如果刷新时在最后一个按钮位置时，索引为数据长度，跳到最后一个按钮
      setSelectIndex((prevent) => (prevent = pst?.length - 1));
    }
  };
  useEffect(() => {
    getData();
  }, [page, IsMarked]);

  const deleteData = async (id) => {
    const pst = await Get2("/api/v1/AIDataset/Delete?id=" + id, {});
    if (isEmpty(pst)) {
      message.success("删除成功");
      refrush();
    }
  };

  const getType = (x1, x2) => {
    if (x1 == x2) {
      return "primary";
    }
    return "text";
  };

  const onClick = (index) => {
    setSelectIndex(index);
  };

  const onLabelClick = (e) => {
    setIndex2(e);
  };

  const ChangeData = () => {
    let st = "";
    List1.current.forEach((e) => {
      st = st + e.join(" ");
      st = st + "\n";
    });
    const item = getItem(selectIndex);
    item.Label = st;
  };
  const onLabelTypeChange = (e) => {
    List1.current[index2][0] = e.target.value;
    ChangeData();
  };

  const getAData = (item) => {
    const arr = item.Label.split("\n");
    const list = [];
    arr.forEach((e) => {
      const xx = e.split(" ").map(Number);
      if (xx.length == 5) {
        list.push(xx);
      }
    });
    List1.current = list;
    return list;
  };

  const getItem = (xx) => {
    const item = data.find((instance, index) => index == xx);
    return item;
  };
  const getImgPanel = () => {
    if (isEmpty(data)) return;
    const item = getItem(selectIndex);
    if (isEmpty(item)) return;
    return (
      <Card
        style={{ marginLeft: 12.0 }}
        title={"样本照片"}
        extra={
          <Button
            onClick={() => {
              ShowDeleteConfirm("样本", () => deleteData(item.ID));
            }}
          >
            删除该样本
          </Button>
        }
      >
        <AIMediaPanel
          key={selectIndex}
          DrawOK={DrawOK}
          img1={getImgUrlByAli(item["ObjectName"])}
          ifDraw={ifDraw}
          aList={AList}
          data={getAData(item)}
          data2={List1.current[index2]}
          labelID={labelID}
        ></AIMediaPanel>
      </Card>
    );
  };

  const labelTypeBtn = () => {
    const list = [];
    if (isEmpty(List1.current[index2])) return;
    let nn = 0;
    AList.forEach((e) => {
      list.push(
        <Radio.Button value={nn} style={{ margin: 4.0 }}>
          {e}
        </Radio.Button>
      );
      nn++;
    });
    return (
      <Radio.Group
        key={index2}
        onChange={onLabelTypeChange}
        buttonStyle="solid"
        value={List1.current[index2][0]}
      >
        {list}
      </Radio.Group>
    );
  };

  const selectData = () => {
    const list = [];
    let nn = 0;
    List1.current.forEach((e) => {
      list.push({
        value: nn,
        label: AList[e[0]] + "-" + nn,
      });
      nn++;
    });
    return list;
  };

  const onDeleteLabel = () => {
    List1.current.splice(index2, 1);
    ChangeData();
    setIndex2(0);
    const item = getItem(selectIndex);
    if (item) {
      item.Label = item.Label + " ";
      setData([...data]);
    }
  };

  const DrawOK = (xx) => {
    List1.current.push(xx);
    ChangeData();
    setIfDraw(false);
    const item = getItem(selectIndex);
    if (item) {
      setData([...data]);
    }
  };

  const SaveBZ = () => {
    const item = getItem(selectIndex);
    Post2("/api/v1/AIDataset/Update", { ...item });
  };

  const getLabelIDSelectButton = () => {
    const list = [];
    let nn = 0;
    AList.forEach((e) => {
      list.push({
        value: nn,
        label: e,
      });
      nn++;
    });
    return (
      <Select
        style={{
          width: 120,
        }}
        options={list}
        onChange={(e) => {
          setLabelID(e);
          setIfDraw(true);
        }}
      />
    );
  };

  //样本标注
  const getLabelPanel = () => {
    if (isEmpty(data)) return;
    const item = getItem(selectIndex);
    const sBtn = (
      <Select
        style={{
          width: 120,
        }}
        value={labelID}
        options={selectData()}
        onChange={onLabelClick}
      />
    );
    if (isEmpty(item)) return;
    return (
      <Card title="样本标注" style={{ marginLeft: 12.0 }} extra={sBtn}>
        <div>{labelTypeBtn()}</div>
        <Divider></Divider>
        <div style={{ marginTop: 24.0 }}>
          <Button style={{ marginRight: 8.0 }} onClick={onDeleteLabel}>
            删除选中标注
          </Button>{" "}
          <Button
            onClick={() => {
              List1.current = [];
              ChangeData();
            }}
          >
            清空标注
          </Button>
        </div>
        <Divider></Divider>

        <div style={{ marginTop: 24.0 }}>
          <span style={{ marginRight: 8.0 }}>{getLabelIDSelectButton()}</span>
          <Button
            type={ifDraw ? "primary" : "dashed"}
            onClick={() => setIfDraw(true)}
          >
            绘制新标注
          </Button>
        </div>
        <Divider></Divider>
        <div style={{ marginTop: 24.0 }}>
          <Button onClick={() => SaveBZ()}>保存修改</Button>
        </div>
      </Card>
    );
  };

  const YBList = () => {
    const handleChange = (info) => {
      if (info.file.status === "done") {
        message.success(`${info.file.name} 文件上传成功`);
        refrush();
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} 文件上传失败`);
      }
    };

    const handleSelectIsMarked = (e) => {
      setIsMarked(e);
    };


    const extra = (
      <>
        <Select
          value={IsMarked}
          style={{ width: 120 }}
          onChange={handleSelectIsMarked}
          options={[
            { value: '', label: '全部' },
            { value: '1', label: '已标注' },
            { value: '2', label: '未标注' },
          ]}
        />
        <Upload
          {...UpProps(
            "/api/v1/AIDataset/UploadFile",
            { m1: model.Guid },
            null,
            true
          )}
          onChange={handleChange}
        >
          <Button>上传样本</Button>
        </Upload>
      </>

    );

    useEffect(() => {
      const handleKeyDown = (event) => {
        event.preventDefault();
        event.stopPropagation();

        let rightKey = event.key === "ArrowRight" || event.key === "d";
        let leftKey = event.key === "ArrowLeft" || event.key === "a";

        setSelectIndex((prevIndex) => {
          //如果当前索引值小于最后一个数据，索引值加1
          if (rightKey && prevIndex < data?.length - 1) {
            return prevIndex + 1;
          } else if (leftKey && prevIndex > 0) {
            //如果当前索引值大于第一个数据，索引-1
            return prevIndex - 1;
          }
          if (prevIndex >= data?.length - 1) {
            setPage((prevPage) => prevPage + 1);
          }
          return prevIndex;
        });
      };
      // window.addEventListener("keydown", handleKeyDown);

      return () => {
        window.removeEventListener("keydown", handleKeyDown);
      };
    }, [data]);

    return (
      <Card title="样本列表" extra={extra}>
        <List
          dataSource={data}
          renderItem={(item, index) => (
            <List.Item key={item.ID}>
              <Button
                key={item.ID}
                style={{ width: "100%", margin: "2px 0", borderColor: "#ccc" }}
                type={getType(index, selectIndex)}
                onClick={() => onClick(index)}
              >
                {item.ID + ".jpg"}
              </Button>
            </List.Item>
          )}
        ></List>
        <Pagination
          style={{ width: "100%" }}
          pageSize={6}
          simple
          showSizeChanger={false}
          size={"small"}
          align={"center"}
          current={page}
          total={dCount}
          onChange={(e) => {
            setPage(e);
          }}
        />
      </Card>
    );
  };

  //   <Card title="样本列表" extra={extra}>
  //   <VirtualList
  //     data={data}
  //     height={370}
  //     itemHeight={47}
  //     itemKey="ID"
  //     onScroll={onScroll}
  //   >
  //     {(item, index) => (
  //       <List.Item key={item.ID}>
  //         <Button
  //           key={index.ID}
  //           style={{ width: "100%", margin: "2px 0", borderColor: "#ccc" }}
  //           type={getType(index, selectIndex)}
  //           onClick={() => onClick(index)}
  //         >
  //           {item.ID + ".jpg"}
  //         </Button>
  //       </List.Item>
  //     )}
  //   </VirtualList>
  //   {data && data.length > 0 && (
  //     <div style={{ textAlign: "right" }}>
  //       总共：{dCount}条
  //     </div>
  //   )}
  // </Card>

  const getBody = () => {
    return (
      <Row>
        {" "}
        <Col span={5}> {YBList()}</Col>
        <Col span={13}>{getImgPanel()}</Col>
        <Col span={6}>{getLabelPanel()}</Col>
      </Row>
    );
  };

  return <div title="样本库">{getBody()}</div>;
};

export default DatasetPage;
