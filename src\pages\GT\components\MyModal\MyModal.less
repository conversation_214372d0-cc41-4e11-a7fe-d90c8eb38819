.myModal {
    width: 400px;
    padding: 15px;
    background: rgba(#37313d, 0.8);
    border: 1px solid #1abc9c;
    border-radius: 5px;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    transform: translateY(50%);
    z-index: 99;
}
.myModal_head{
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.myModal_title {
    font-weight: bold;
}
.myModal_status{
    background: #d9001b;
    font-size: 12px;
    padding: 4px 15px;
    margin-left: 10px;
}

.myModal_content {
    padding: 15px;
    font-size: 13px;
}

.myModal_foot {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-size: 13px;
}

.myModal_cancel,
.myModal_close {
    padding: 5px 14px;
    border: 1px solid #1abc9c;
    border-radius: 2px;
    cursor: pointer;
}

.myModal_cancel {
    background: #1abc9c;
    color: #fff;
}
.myModal_cancel:hover{
    transition: all 0.3s ease;
    opacity: 0.7;
}

.myModal_close {
    color: #1abc9c;
}