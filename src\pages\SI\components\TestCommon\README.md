# 测试页面

这是基于通用组件的测试页面，展示了如何使用通用表单和表格组件构建业务页面。

## 📁 新的文件结构

```
src/pages/SI/components/
├── Common/                          # 通用组件库
│   ├── Form/                       # 通用表单组件
│   │   └── index.jsx              # DynamicSearchForm
│   ├── Table/                      # 通用表格组件
│   │   └── index.jsx              # DynamicDataTable
│   └── index.js                   # 统一导出文件
└── TestCommon/                     # 业务模块
    ├── index.jsx                  # 测试页面（主要文件）
    ├── config.js                  # 业务配置
    └── README.md                  # 说明文档
```

## 🎯 重构说明

### 组件抽离
- **通用表单组件** (`DynamicSearchForm`) 已移动到 `src/pages/SI/components/Common/Form/`
- **通用表格组件** (`DynamicDataTable`) 已移动到 `src/pages/SI/components/Common/Table/`
- **业务配置** 独立到 `config.js` 文件

### 测试页面
`TestCommon/index.jsx` 现在作为完整的测试页面，演示：
- 如何引入通用组件
- 如何配置业务数据
- 如何处理交互逻辑
- 如何自定义渲染

## 🚀 使用方式

### 1. 导入通用组件

```jsx
import { DynamicSearchForm, DynamicDataTable } from '../Common';
import { TestCommonFormConfig, TestCommonTableConfig } from './config';
```

### 2. 使用表单组件

```jsx
<DynamicSearchForm
  formConfig={TestCommonFormConfig}
  onSearch={handleSearch}
  onReset={handleReset}
  loading={loading}
  cols={4}
/>
```

### 3. 使用表格组件

```jsx
<DynamicDataTable
  {...TestCommonTableConfig}
  data={tableData}
  loading={loading}
  showToolbar={true}
  toolbarButtons={toolbarButtons}
  toolbarAlign="space-between"
  showSelectionInfo={true}
  showTotal={true}
  customRender={customRender}
  pagination={pagination}
  onPageChange={handlePageChange}
/>
```

## 📋 功能特性

### 表单功能
- ✅ 7个搜索字段（任务编号、任务名称、计划编号、航线名称、机场、时间范围、检查结果）
- ✅ 表单验证
- ✅ 重置功能
- ✅ 加载状态

### 表格功能  
- ✅ 完整的列定义（10列数据）
- ✅ 行选择功能
- ✅ 工具栏（批量导出按钮）
- ✅ 分页功能
- ✅ 自定义渲染（状态标签、操作按钮）
- ✅ 响应式滚动

### 交互功能
- ✅ 搜索功能
- ✅ 分页变化
- ✅ 批量导出
- ✅ 行级操作（查看、检查、巡检报告）

## 🎨 样式主题

使用统一的主题配置 (`src/pages/SI/style/theme.js`)：
- 深色科技风格
- 青色主题色调
- 统一的组件样式

## 📝 配置说明

### 表单配置 (`TestCommonFormConfig`)
包含7个字段的完整表单配置，支持输入框、选择器、日期范围等控件。

### 表格配置 (`TestCommonTableConfig`)
包含10列的完整表格配置，支持固定列、自定义渲染等功能。

## 🔄 运行测试

直接访问 `TestCommon/index.jsx` 即可看到完整的测试页面，包含：
- 表单搜索区域
- 数据表格区域
- 完整的交互功能

## ⚡ 优势

### 1. **代码结构清晰**
- 通用组件独立管理
- 业务逻辑分离
- 配置数据独立

### 2. **高度复用**
- 通用组件可在其他业务模块中直接使用
- 配置化开发，减少重复代码

### 3. **易于维护**
- 组件职责单一
- 配置集中管理
- 测试页面独立

### 4. **完全可配置**
- 表单字段完全可配置
- 表格列完全可配置
- 交互行为完全可配置 