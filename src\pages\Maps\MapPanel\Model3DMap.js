import React, { useEffect } from "react";
import { Cesium } from "umi";
import { Camera } from "cesium";
import { isEmpty, getBodyH } from "@/utils/utils";
import { useRef } from "react";
import { useState } from "react";
import { Row, Col, Button, List, message, Card } from "antd";
import { allClear } from "@/pages/Cesium/helper";
import { Get2, Post2 } from "@/services/general";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import VirtualList from "rc-virtual-list";
// 标注hooks
import useMark3DHooks from '@/hooks/Mark3DHooks'
import MapControl from '@/hooks/mapControl';
import LastPageButton from "@/components/LastPageButton";


const Map3D = ({doNotShowLastButton}) => {
  const viewer = useRef(null);
  let [viewerData, setviewerData] = useState(null)
  const [layer1, setLayer1] = useState({});
  const layers = useRef({});
  const [mapList, setMapList] = useState([]);
  const [ifLoad, setIfLoad] = useState(true);
  const ContainerHeight = getBodyH(166);
  // 标注功能
  let { Mark3D, getViewer } = useMark3DHooks({ idName: "cesiumContainer" });

  Camera.DEFAULT_VIEW_FACTOR = 0.0001;

  const getMList = async () => {
    let pst = await Get2("/api/v1/MapData/GetAllList");
    if (pst && pst.length > 0) {
      setIfLoad(false);
      setMapList(pst);
    }
  };
  useEffect(() => {
    getMList();
  }, []);

  const onScroll = (e) => {
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          ContainerHeight
      ) <= 1
    ) {
      getMList();
    }
  };
  useEffect(() => {
    viewer.current = getViewer()
    setviewerData(viewer.current)
    return () => {
      allClear(viewer.current);
    };
  }, []);

  const DDD = (url) => {
    if (isEmpty(viewer.current)) return;
    const option = {
      dynamicScreenSpaceError: true,
      dynamicScreenSpaceErrorDensity: 2.0e-4,
      dynamicScreenSpaceErrorFactor: 24.0,
      dynamicScreenSpaceErrorHeightFalloff: 0.1,
      maximumScreenSpaceError: 1.0,
    };
    const xxx = async (url) => {
      try {
        layers.current = await Cesium.Cesium3DTileset.fromUrl(url, option);
        viewer.current.scene.primitives.add(layers.current);
        viewer.current.flyTo(layers.current);
      } catch (error) {
        console.error(`Error creating tileset: ${error}`);
      }
    };
    xxx(url);
  };

  const onClick = (item) => {
    setLayer1(item);
    console.log('item',item)
    DDD(item.layer);
  };

  const getType = (x1) => {
    if (layer1.name == x1) {
      return "primary";
    } else {
      return "text";
    }
  };
  function fc() {
    // 定义目标位置和视角
    var targetPosition = Cesium.Cartesian3.fromDegrees(
      -75.62898254394531,
      40.02804946899414,
      1500
    );
    var heading = Cesium.Math.toRadians(90.0);
    var pitch = Cesium.Math.toRadians(-45.0);
    var roll = Cesium.Math.toRadians(0.0);

    // 相机飞到目标点
    viewer.current.camera.flyTo({
      destination: targetPosition,
      orientation: {
        heading: heading,
        pitch: pitch,
        roll: roll,
      },
      duration: 3, // 飞行动画时长
    });
  }

  const data = [];
  const getList = () => {
    mapList?.forEach((e) => {
      if (e.MapType === 11) {
        data.push({
          name: e.MapName,
          layer: e.Url,
          b3dm: true,
        });
      }
    });

    useEffect(() => {
      if (data?.length > 0) {
        // 组件加载后自动选择第一个地图
        const firstMap = data[0];
        onClick(firstMap);
      }
    }, [mapList]);
    //data.push({name:'8标模型',layer: "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/15c167c49b554749baa01d9941c74071/tileset.json"})
    // data.push({name:'局部区域-2023年8月拍摄',layer: "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/bd6c5b018dd046b59bcccf42e7ce64d5/tileset.json"})
    //  data.push({name:'2024年5月拍摄',layer:  "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/667edb33915b45218945367bbef08b2e/tileset.json"})
    return (
      <Card title={doNotShowLastButton ? '三维模型列表' : <LastPageButton title="三维模型列表" />}>
        <List>
          <VirtualList
            data={data}
            height={ContainerHeight}
            itemHeight={47}
            itemKey="i"
            onScroll={onScroll}
          >
            {(item, i) => (
              <List.Item key={item}>
                <Button
                  type={getType(item.name)}
                  onClick={() => onClick(item, i)}
                  style={{
                    width: "100%",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    borderColor:'#ccc'
                  }}
                >
                  {item.name}
                </Button>
              </List.Item>
            )}
          </VirtualList>
        </List>
      </Card>
    );
  };

  return (
    <div>
      <Row>
        <Col span={4}>{getList()}</Col>
        <Col span={20}>
          {" "}
          <div
            style={{
              height: "calc(100vh - 47px)",
              width: "100%",
              overflow: "auto",
              position: 'relative'
            }}
            id="cesiumContainer"
          >
            {/* 标注控件 */}
            <div style={{ position: 'absolute', right: 30, top: 20, zIndex: 1 }}>
              <Mark3D />
            </div>
            <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
              <MapControl viewerData={viewerData} />
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default Map3D;
