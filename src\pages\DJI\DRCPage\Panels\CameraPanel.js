import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import camera4 from "@/assets/drcImgs/camera4.png";
import light1 from "@/assets/drcImgs/light1.png";

import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import video4 from "@/assets/drcImgs/video4.png";
import { HGet2 } from "@/utils/request";
import { Get2 } from "@/services/general";
import { isEmpty } from "@/utils/utils";
import { useEffect, useState, useRef } from "react";
import { useModel } from "umi";
import { getMinstr } from "./helper";
import {
  Modal,
  Tooltip,
  Input,
  InputNumber,
  message,
  Spin,
  Slider,
  Popover,
  Radio,
  Row,
  Col,
  Space,
  Button,
} from "antd";
import { SyncOutlined } from "@ant-design/icons";
import HanHuaPage from "../../DevicePage/Panels/HanHuaQi";

const CameraPanel = () => {
  const w1 = 60;
  const h1 = 240;
  const { fj, fjData } = useModel("droneModel");
  const { lightPSDKIndex, lightData } = useModel("drcModel");
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const [isDanBinPlus2, setIsDanbBinPlus2] = useState(
    device.BindCode === "pilot" && device.Model === "RC_PLUS_2"
  );
  const [ifVideo, setIfVideo] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLightModalOpen, setIsLightModalOpen] = useState(false);
  const [lightBright, setlightBright] = useState(50);
  const [lightMode, setLightMode] = useState(2);

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);

    // if (v == 1) {
    //   VideoSave();
    // } else {
    //   CameraSave();
    // }
  };

  const photoCamera = (v) => {
    const data = { payload_index: device.Camera2 };
    if (fj?.data?.cameras[0]?.camera_mode == 0) {
      if (isDanBinPlus2) {
        let s = {};
        s.method = "drc_camera_photo_take";
        s.data = data;
        DoCMD2(`thing/product/${device.SN}/drc/down`, s);
      } else {
        DoCMD(device.SN, "camera_photo_take", data);
      }
    } else {
      changeCamera(0);
      setTimeout(() => {
        if (isDanBinPlus2) {
          let s = {};
          s.method = "drc_camera_photo_take";
          s.data = data;
          DoCMD2(`thing/product/${device.SN}/drc/down`, s);
        } else {
          DoCMD(device.SN, "camera_photo_take", data);
        }
      }, 1000);
    }
  };

  const VideoStart = () => {
    const data = { payload_index: device.Camera2 };
    if (isDanBinPlus2) {
      let s = {};
      s.method = "drc_camera_recording_start";
      s.data = data;
      DoCMD2(`thing/product/${device.SN}/drc/down`, s);
    } else {
      DoCMD(device.SN, "camera_recording_start", data);
    }
  };

  const VideoStop = () => {
    const data = { payload_index: device.Camera2 };
    if (isDanBinPlus2) {
      let s = {};
      s.method = "drc_camera_recording_stop";
      s.data = data;
      DoCMD2(`thing/product/${device.SN}/drc/down`, s);
    } else {
      DoCMD(device.SN, "camera_recording_stop", data);
    }
  };

  const VideoClick = () => {
    if (fj?.data?.cameras[0]?.recording_state == 1) {
      VideoStop();
      setIfVideo(false);
    } else {
      changeCamera(1);
      setTimeout(() => {
        VideoStart();
        setIfVideo(true);
      }, 1000);
    }
  };

  const CameraSave = () => {
    const data = {
      payload_index: device.Camera2,
      photo_storage_settings: ["current"],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const VideoSave = () => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["current"],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const getVideoImg = () => {
    if (ifVideo) return video3;
    return video4;
  };

  useEffect(() => {
    //
    if (isEmpty(fjData.current)) return;
    if (isEmpty(fjData.current?.cameras)) return;
    if (fjData.current?.cameras[0]?.recording_state == 1) {
      setIfVideo(false);
    } else {
      setIfVideo(true);
    }
  }, [fjData.current]);

  const onHanHuaQi = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const onLight = () => {
    // 赋初始值
    setLightMode(lightData.work_mode == 0 ? 2 : lightData.work_mode + 1);
    setlightBright(lightData.brightness || 50);
    //  console.log("状态上报-打开探照灯111",lightData)

    if (lightData.work_mode == 0) {
      // 第一次打开探照灯
      setLight();
      // console.log("状态上报-打开探照灯222")
    }
    setIsLightModalOpen(true);
  };

  const handleLightCancel = () => {
    setIsLightModalOpen(false);
  };

  const setLight = () => {
    // console.log('探照灯',lightPSDKIndex)
    if (lightPSDKIndex == 0) {
      message.info("未获取到探照灯psdk_index");
      return;
    }
    onLightModeChange(2);

    onLightBrightChange(50);
  };

  const getCameraName = () => {
    if (isEmpty(fj.data)) return "录像";
    if (isEmpty(fj.data.cameras)) return "录像";

    if (fj.data?.cameras[0]?.recording_state == 0) return "开始录像";
    return "结束录像";
  };
  // if(isEmpty(fj)) return;
  // if(fj.data.cameras[0].camera_mode==0){

  const onLightBrightChange = (newValue) => {
    setlightBright(newValue);
    let s1 = {};
    s1.method = "drc_light_brightness_set";
    s1.data = {
      psdk_index: lightPSDKIndex,
      group: 0,
      brightness: newValue,
    };
    DoCMD2(`thing/product/${device.SN}/drc/down`, s1);
  };

  const onLightModeChange = (e) => {
    // console.log('状态上报---onLightModeChange111111',e)
    setLightMode(typeof e == "number" ? e : e?.target?.value);
    let s = {};
    let mode = typeof e == "number" ? e : e?.target?.value;
    s.method = "drc_light_mode_set";
    s.data = {
      psdk_index: lightPSDKIndex,
      group: 0,
      mode: mode - 1,
    };
    // console.log('状态上报---onLightModeChange',s)
    DoCMD2(`thing/product/${device.SN}/drc/down`, s);
  };

  const onLightTurnSet = (which, action) => {
    let s = {};
    s.method = "drc_light_fine_tuning_set";
    s.data = {
      psdk_index: lightPSDKIndex,
      position: which == "right" ? 1 : 0,
      saved: false,
      value: action == "add" ? 1 : -1,
    };
    DoCMD2(`thing/product/${device.SN}/drc/down`, s);
  };

  return (
    <>
      <div>
        <Modal
          open={isModalOpen}
          onCancel={handleCancel}
          destroyOnClose={true}
          footer={null}
        >
          <HanHuaPage device={device} setOpen={setIsModalOpen} />
        </Modal>

        <Modal
          open={isLightModalOpen}
          onCancel={handleLightCancel}
          destroyOnClose={true}
          footer={null}
        >
          <div>
            <h3>亮度设置:</h3>
            <Slider
              min={1}
              max={100}
              onChange={onLightBrightChange}
              value={typeof lightBright === "number" ? lightBright : 0}
            />
            <h3>模式设置:</h3>
            <Radio.Group
              onChange={onLightModeChange}
              value={lightMode}
              options={[
                {
                  value: 1,
                  label: "关闭",
                },
                {
                  value: 2,
                  label: "常量",
                },
                {
                  value: 3,
                  label: "爆闪",
                },
                {
                  value: 4,
                  label: "快速爆闪",
                },
                {
                  value: 5,
                  label: "交替爆闪",
                },
              ]}
            />
            <h3 style={{ marginTop: "10px" }}>角度微调:</h3>
            <Row>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => onLightTurnSet("left", "add")}
                  >
                    左灯向右
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => onLightTurnSet("left", "reduce")}
                  >
                    左灯向左
                  </Button>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => onLightTurnSet("right", "add")}
                  >
                    右灯向右
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => onLightTurnSet("right", "reduce")}
                  >
                    右灯向左
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>
        </Modal>
      </div>

      <div
        draggable={false}
        style={{
          userSelect: "none",
          position: "absolute",
          cursor:"pointer",
          opacity: 0.6,
          top: 240,
          right: 40,
          height: h1,
          width: w1,
          zIndex: 1010,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          gap: 20,
        }}
      >
        <Tooltip placement="left" title="探照灯">
          <div>
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={45}
              width={45}
              src={light1}
              onClick={() => onLight()}
            ></img>
          </div>
        </Tooltip>

        {isDanBinPlus2 ? null : (
          <div>
            <Tooltip placement="left" title="喊话器drc">
              <img
                style={{ cursor: "pointer", userSelect: "none" }}
                draggable={false}
                height={45}
                width={45}
                src={camera4}
                onClick={() => onHanHuaQi()}
              ></img>
            </Tooltip>
          </div>
        )}
        <div>
          <Tooltip placement="left" title="拍照">
            <img
              draggable={false}
              height={42}
              width={42}
              src={camera3}
              onClick={() => {
                photoCamera();
              }}
            ></img>
          </Tooltip>
        </div>

        <div>
          <Tooltip placement="left" title={getCameraName()}>
            <img
              draggable={false}
              height={45}
              width={45}
              src={getVideoImg()}
              onClick={() => VideoClick()}
            ></img>
          </Tooltip>
        </div>
        <div
          style={{
            fontFamily: "MiSan",
            color: "red",
          }}
        >
          {getMinstr(fj?.data?.cameras[0]?.record_time)}
        </div>
      </div>
    </>
  );
};

export default CameraPanel;
