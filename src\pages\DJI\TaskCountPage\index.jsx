import React, {useState, useEffect, useRef } from 'react';
import { DatePicker, Card,List, Descriptions, Row, Col, Button, Form, message} from 'antd';
import { downloadFile2, getDeviceName, getImgUrl, isEmpty } from '@/utils/utils';
import { HGet2, HPost2 } from '@/utils/request';
import { Get2, Post2 } from '@/services/general';
import LastPageButton from '@/components/LastPageButton';
import dayjs from 'dayjs';
import LoadPanel from '@/components/IfShowPanel/load_panel';
import ComStyles from '@/pages/common.less';
import AddButton from '@/components/AddButton';
import commonStyles from '@/pages/common.less';
const { RangePicker } = DatePicker;
const TaskCountPage = () => {

  let para1={
    sDate:[dayjs('2024/1/1'),dayjs()],
    t1:dayjs('2024/1/1'),
    t2:dayjs(),
  }

  const localPara=localStorage.getItem('PageParams');
  if(!isEmpty(localPara)){
    para1=JSON.parse( localPara);
  }
  const [ifLoad,setIfLoad]=useState(true);
  const [tList, setTList] = useState([]);
  const [params,setParams]=useState(para1)
  const [xList, setXList] = useState([]);
  const [ifNew,setIfNew]=useState(false);
  const [dL, setDL] = useState([]);
  const cData=useRef([]);

  const ifPush = (e) => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')

    if (!isEmpty(params.t1)) {
      t1 = dayjs(params.t1);
    }
    if (!isEmpty(params.t2)) {
      t2 = dayjs(params.t2);
    }

   
      const t3 = dayjs(e.CreateTime);
      if (t1.isAfter(t3) || (t2.isBefore(t3))) {
        return false;
      }
   

    return true;
  }

  const getXL=(data)=>{
    const xL = []
      data.forEach(e => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
    return xL;
  }

  useEffect(() => {
    const xx = () => {
      const xL =getXL(tList);
      setXList([...xL]);
      cData.current=[];
    }
    localStorage.setItem('PageParams',JSON.stringify (params));
    xx();
  }, [ifNew]);

  useEffect( () => {
    const yy =async()=>{
      let xx= await HGet2(`/api/v1/Task/GetAllList`)
      setIfLoad(false);
      if(isEmpty(xx)) xx=[];

      if(!isEmpty(xx.err)) {
        message.error(xx.err);
        xx=[];
      }

      setTList(xx);
      const xL=getXL(xx);
      setXList([...xL]);
     
      const dL2 = await Get2('/api/v1/Device/GetAllList')
      setDL(dL2);
    }

    yy();
  }, []);


  const refrush = async () => {
    const  pst= await Get2( '/api/v1/Task/GetAllList',{});
    if(isEmpty(pst)) return;

    setTList(pst);
    setIfNew(!ifNew);
  };

  const exr = <div><Button type="primary" onClick={() => refrush()}>刷新</Button> </div>

const getExr2 = () => {
  return <div  className={ComStyles.searchpanel}>
  <span> <RangePicker  onChange={(e)=> setParams({...params,sDate: e})} /></span>

</div>
}

const downExcel=async()=>{
   const obj=await HPost2 ("/api/v2/ExcelOut/FlyCount",cData.current)
   if(!isEmpty(obj)){
     downloadFile2(getImgUrl(obj))
   }
}

const getExr = () => {
  return <Descriptions layout='vercital' labelStyle={{paddingTop:4.0}} style={{marginTop:12.0,width:720}} >
      <Descriptions.Item span={1} label='开始时间'> <DatePicker defaultValue={params.t1} onChange={(e)=>setParams({...params,t1: e})}> </DatePicker></Descriptions.Item>
      <Descriptions.Item span={1} label='结束时间'> <DatePicker defaultValue={params.t2} onChange={(e)=>setParams({...params,t2: e})}> </DatePicker></Descriptions.Item>
      <Descriptions.Item span={1} >
      <span><AddButton onClick={()=>setIfNew(!ifNew)}>统计</AddButton></span> 
      <span style={{marginLeft:24.0}}><AddButton onClick={()=>downExcel()}>成果导出</AddButton></span> 
      </Descriptions.Item>
</Descriptions>
}

const getPanel=(title,unit,xList,zd,fix,xs)=>{
    const data=[]
    let sum=0;
    dL.forEach(e => {
        const vL=xList.filter(p=>p.DeviceSN==e.SN);
        let v1=0
        vL.forEach(x => {
            v1=v1+x[zd]*xs
        });
        const a={DName:getDeviceName(e.SN),SN:e.SN,Val:v1.toFixed(fix),Title:title}
        sum=sum+v1;
        data.push(a);
        cData.current.push(a);
    });

    return <Card title={title} extra={sum.toFixed(fix)+unit}>
        <List
      dataSource={data}
      title={title} extra={'100公里'}
      renderItem={(item) => <List.Item><div style={{width:'100%'}}><span>{item.DName}</span><span style={{float:'right'}}>{item.Val}</span></div></List.Item>}
    />
    </Card>
}


if(ifLoad) return <LoadPanel></LoadPanel>
  return (
    <div style={{ margin: 0, height:'100%' }}>
      <Card title={<LastPageButton title= "飞行统计"></LastPageButton>} bordered={false} extra={getExr()} >
      <div className={commonStyles.my_scroll_y}>
        <Row>
            <Col span={8}  style={{padding:8.0}}>{getPanel("飞行架次","架次",xList,'TaskState',0,1)}</Col>
            <Col span={8} style={{padding:8.0}}>{getPanel("飞行距离","公里",xList,'Distance',1,1)}</Col>
            <Col span={8}  style={{padding:8.0}}>{getPanel("飞行时间","小时",xList,'FlyTM',1,1/60/60)}</Col>
        </Row>
        </div>
      </Card>
    </div>
  )
};

export default TaskCountPage;
