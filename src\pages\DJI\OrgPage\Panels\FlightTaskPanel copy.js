
import { MainColor } from '@/utils/colorHelper';
import Chart from './../../../../utils/chart';
import BlockTitle from '../../../../components/BlockPanel/BlockTitle';
import { BorderBox7 } from '@jiaminghi/data-view-react';
import { Badge } from 'antd';


const option = {
  tooltip: {
    trigger: 'item'
  },
  legend: null,
  backgroundColor: '',
  xAxis: {
    type: 'category',
    data: ['一键起飞', '现场巡查', '定时任务', '绘制航线']
  },
  yAxis: {
    type: 'value'
  },
  grid: { x: 25, y: 25, x2: 30, y2: 30 },
  series: [
    {
      name: '飞行记录统计',
      type: 'bar',
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold',
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 25, name: '一键起飞' },
        { value: 113, name: '现场巡查' },
        { value: 120, name: '定时任务' },
        { value: 50, name: '绘制航线' },
      ]
    }
  ]
};

const getItem = () => {
  return <div><div style={{ color: 'white', marginTop: 12.0, marginLeft: 24.0 }}>
    
    {/* <span style={{ fontSize: 12.0, marginLeft: 8.0 }}>
      飞行记录 520次
    </span> */}
    
    <span>
      <Badge color="green" />
    </span>
    <span style={{ fontSize: 13.0, marginLeft: 8.0 }}>
      航线飞行 240次
    </span>
    <span>
      <Badge color="green"  style={{ fontSize: 14.0, marginLeft: 24.0 }}/>
    </span>
    <span style={{ fontSize: 13.0, marginLeft: 8.0 }}>
      飞行距离 420公里
    </span>
  </div>
  <div style={{ color: 'white', marginTop: 12.0, marginLeft: 24.0 }}>
    
    {/* <span style={{ fontSize: 12.0, marginLeft: 8.0 }}>
      飞行记录 520次
    </span> */}
    
    <span>
      <Badge color="green" />
    </span>
    <span style={{ fontSize: 13.0, marginLeft: 8.0 }}>
      拍摄照片 1280张
    </span>
    <span>
      <Badge color="green"  style={{ fontSize: 14.0, marginLeft: 24.0 }}/>
    </span>
    <span style={{ fontSize: 13.0, marginLeft: 8.0 }}>
      录制视频 380G
    </span>
  </div>
  </div>
}
const FlightTaskPanel = () => {
  return <div style={{ height: '45%', width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="飞行记录统计"></BlockTitle>
      <div style={{ height: '240px', width: '100%'}}>
        {getItem()}
        <div style={{height:180}}><Chart style={{marginBottom:24.0}}  option={option} /></div>
      </div>
    </BorderBox7>

  </div>
}
export default FlightTaskPanel;