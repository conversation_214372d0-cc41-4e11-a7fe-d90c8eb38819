import { Space, Tag, message, Modal } from "antd";
import { isEmpty } from "@/utils/utils";
import "./table.css";
import { HGet2 } from "@/utils/request";
import { Post2 } from "@/services/general";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const { confirm } = Modal;
const updateCron = async (record, refrush) => {
  if (isEmpty(record)) return;
  const xx = await HGet2("/api/v1/AIVideoJob/ChangeState?id=" + record.ID);
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("更新成功！");
    refrush();
  }
};

const deleteCronJob = async (record, refrush) => {
  const xx = await Post2("/api/v1/AIVideoJob/Delete", record);
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("删除成功！");
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除任务",
    content: "确定删除该任务吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deleteCronJob(record, refrush);
    },
    onCancel() {},
  });
};

const TableCols = (refrush) => {
  return [
    {
      title: getTableTitle("任务名称"),
      dataIndex: "JobName",
      key: "JobName",
      align: "center",
    },

    {
      title: getTableTitle("航线名称"),
      dataIndex: "WayLineName",
      key: "WayLineName",
      align: "center",
    },

    {
      title: getTableTitle("识别内容"),
      dataIndex: "AClsNM",
      key: "AClsNM",
      align: "center",
    },

    {
      title: getTableTitle("告警标题"),
      dataIndex: "DangerType",
      key: "DangerType",
      align: "center",
    },
    {
      title: getTableTitle("告警内容"),
      dataIndex: "DangerContent",
      key: "DangerContent",
      align: "center",
    },

    {
      title: getTableTitle("告警电话"),
      dataIndex: "Phone",
      key: "Phone",
      align: "center",
    },
    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <Tag>
            <a onClick={() => showDeleteConfirm(record, refrush)}>删除</a>
          </Tag>
        </Space>
      ),
    },
  ];
};

export default TableCols;
