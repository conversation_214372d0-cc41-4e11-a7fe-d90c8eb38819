.LeftPage2{
    width: inherit;
    height: 100vh;
    // position: absolute;
    // top:20;
    // left: 0;
    // z-index: 2;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}
.inspection_statistics{
    margin-top: -20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.inspection_statistics_item{
    text-align: center;
}
.icon_box{
    width: 140px;
    height: 100px;
    background: url("../../../assets/image/u133.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
}
.type_num{
    color: #1abc9c;
    margin:10px 0;
    font-weight: bold;
    font-size: 21px;
}
.type_name{
    color: #ffffff;
    font-size: 12px;
}
// .myEcharts{
//     height: 210px!important;
//     width: 100%!important;
// }
