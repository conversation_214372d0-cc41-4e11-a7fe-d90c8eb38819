import React, { Fragment, useState, useEffect } from "react";
import { Card, Button, Table, Tag } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2, Post2 } from "@/services/general";
import LastPageButton from "@/components/LastPageButton";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import HanHuaPage from "@/pages/DJI/DevicePage/Panels/HanHuaQi";

import CronAddForm from "./form_add";
import CronEditForm from "./form_edit";
import TableCols from "./table";
import ComStyles from "@/pages/common.less";
import { useModel } from "umi";
import Detial from "./detail";
import { getGuid } from "@/utils/helper";

const ReportGenerationTasks = (props) => {
  const [wayList, setWayList] = useState({});
  const [cronList, setCronList] = useState({});
  const { setModal, setOpen, setPage, lastPage } = useModel("pageModel");
  const [modelList, setModelList] = useState([]);
  const [ifLoad, setIfLoad] = useState(true);
  const [deviceLists, setdeviceLists] = useState();
  let show = false;
  useEffect(() => {
    const getLineData = async () => {
      const pst = await Get2("/api/v1/WayLine/GetAllList", {});
      if (isEmpty(pst)) return;
      setWayList(pst);
      setIfLoad(false);
    };

    const getModelData = async () => {
      const pst = await Get2("/api/v1/AIModel/GetList", {});
      setModelList(pst);
    };

    const getCronData = async () => {
      const pst = await Get2("/api/v1/AIDocJob/GetAllList", {});
      if (isEmpty(pst)) return;
      setCronList(pst);
    };
    async function getDevice() {
      const res = await Get2("/api/open/Device/GetAllList");
      if (!!res) {
        setdeviceLists(res);
      }
    }
    getDevice();
    getLineData();
    getCronData();
    getModelData();
  }, []);

  const showMap = (values) => {
    setPage(<Detial record={cronList}></Detial>);
  };

  const power = (record) => {
    setModal(
      <CronEditForm
        key={getGuid()}
        wayList={wayList}
        modelList={modelList}
        refrush={refrush}
        record={record}
        cronList={cronList}
      />
    );
    setOpen(true);
  };

  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/AIDocJob/GetAllList", {});
    console.log(pst,'pst');
    
    if (isEmpty(pst)) return;
    setCronList(pst);
  };

  const exr = (
    <>
      <Button
        type="primary"
        className={ComStyles.addButton}
        onClick={() => {
          setModal(
            <CronAddForm
              wayList={wayList}
              modelList={modelList}
              cronList={cronList}
              refrush={refrush}
            />
          );
          setOpen(true);
        }}
      >
        新建任务
      </Button>
      {/* <Tag>
        <a
          onClick={() => {
            setModal(<HanHuaPage setOpen={setOpen} />);
            setOpen(true);
          }}
        >
          喊话器实验
        </a>
      </Tag> */}
    </>
  );
  return (
    <div style={{ margin: 0, height: getBodyH(56), background: "#F5F5FF" }}>
      <Card
        title={<LastPageButton title="报告生成任务" />}
        bordered={false}
        extra={exr}
      >
        <div>
          {isEmpty(cronList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                defaultPageSize: cronList?.length,
                defaultCurrent: 1,
                showQuickJumper: true,
                pageSizeOptions: [5, 10, 15, 20, 30],
                showSizeChanger: true,
                locale: {
                  items_per_page: "条/页",
                  jump_tp: "跳至",
                  page: "页",
                },
              }}
              loading={cronList ? false : true}
              bordered
              dataSource={cronList}
              columns={TableCols(refrush, showMap,setPage, power, deviceLists)}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default ReportGenerationTasks;
