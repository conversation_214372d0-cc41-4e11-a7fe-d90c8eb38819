import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import TargetRecognition2 from "@/pages/GT/ZZCH/Pages/TaskManagement/Pages/TargetRecognition2";
import { useModel } from "umi";

const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (handlePageChange, handlePageChange2, getDefaultData, taskType) => {

  const { setPage } = useModel("pageModel");

  const handleDelete = async (id) => {
    try {
      const res = await axiosApi(`/api/v1/AIAlgorithmTask/Delete?id=${id}`, 'DELETE', {});
      if (res.code === 1) {
        message.success('删除成功');
        getDefaultData(); // 重新获取数据
      } else if (res.code === 0) {
        message.error(res.msg);
      }
    } catch (error) {
      message.error("删除失败");
      console.log(error);
    }
  };

  return [
    {
      title: getTableTitle("任务名称"),
      dataIndex: "TaskName",
      key: "TaskName",
      align: "center",
    },
    {
      title: getTableTitle("资源类别"),
      key: "ResourceCategory",
      align: "center",
      render: (record) => (
        <>
          {record.ResourceCategory === "图片"
            ? <Tag color="blue">图片</Tag>
            : <Tag color="gold">视频</Tag>
          }
        </>
      ),
    },
    {
      title: getTableTitle("场景"),
      dataIndex: "SceneRelated",
      key: "SceneRelated",
      align: "center",
    },
    {
      title: getTableTitle("识别项"),
      dataIndex: "SceneFunction",
      key: "SceneFunction",
      align: "center",
    },
    {
      title: getTableTitle("任务状态"),
      //dataIndex: 'TaskState',
      // key: 'TaskState',
      align: "center",
      render: (record) => {
        // 状态映射表
        const statusMap = {
          0: { text: '待执行', color: '#999' },
          1: { text: '进行中', color: 'orange' },
          2: { text: '已完成', color: 'green' },
          3: { text: '失败', color: 'red' },
          4: { text: '已取消', color: '#999' }
        };

        const statusInfo = statusMap[record.Status] || { text: '未知状态', color: '#666' };

        return (
          <span style={{ color: statusInfo.color, width: "100%" }}>
            {statusInfo.text}
          </span>
        );
      },
    },
    {
      title: getTableTitle("创建时间"),
      // dataIndex: "CreateTM",
      key: "CreatedAt",
      align: "center",
      render: (_, record) => (
        <span>
          {isEmpty(record.CreatedAt)
            ? "-"
            : timeFormat(record.CreatedAt)
          }
        </span>
      )
    },
    {
      title: getTableTitle("结束时间"),
      // dataIndex: "CreateTM",
      key: "CompletedAt",
      align: "center",
      render: (_, record) => (
        <span>
          {isEmpty(record.CompletedAt)
            ? "-"
            : timeFormat(record.CompletedAt)
          }
        </span>
      )
    },
    {
      title: getTableTitle("操作"),
      align: "center",
      width: 300,
      render: (_, record) => (
        <Space size="middle">
          {record.Status === 2 && (
            <MyButton
              style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
              onClick={() => {
                localStorage.setItem('record', JSON.stringify(record));
                // if (isDJPage) {
                //   handlePageChange2('识别详情');
                // } else {
                //   handlePageChange('识别详情');
                // }
                setPage({
                  title: "识别详情",
                  key: "/gt/ZZCH/rwgl/sbxq",
                  children: <TargetRecognition2 taskType={taskType} />,
                });
              }}
            >
              详情
            </MyButton>
          )}
          <MyButton
            style={{ padding: "2px 8px", color: 'red', background: 'none' }}
            onClick={() => {
              handleDelete(record.ID);
            }}
          >
            删除
          </MyButton>
        </Space>
      ),
    }
  ];
};

export default TableCols;
