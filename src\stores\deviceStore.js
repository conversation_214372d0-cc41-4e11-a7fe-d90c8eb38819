import { create } from "zustand";
import { Get2 } from "@/services/general";

const useDeviceStore = create((set, get) => ({
  // 状态
  deviceList: [],
  loading: false,
  error: null,
  lastFetchTime: null,
  
  // Actions
  setDeviceList: (devices) => set({ deviceList: devices }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  
  // 获取设备列表的核心方法
  fetchDeviceList: async (forceRefresh = false) => {
    const { deviceList, lastFetchTime, loading } = get();

    // 如果正在加载中，直接返回
    if (loading) return deviceList;

    // 缓存策略：如果不是强制刷新且距离上次获取不到30秒，直接返回缓存数据
    const now = Date.now();
    const cacheTimeout = 30 * 1000; // 30秒缓存
    if (!forceRefresh && lastFetchTime && (now - lastFetchTime) < cacheTimeout && deviceList.length > 0) {
      return deviceList;
    }

    try {
      set({ loading: true, error: null });

      const deviceData = await Get2("/api/open/Device/GetAllList");

      // 按在线状态排序
      const sortedData = [...deviceData].sort((a, b) => b.IfOnLine - a.IfOnLine);

      set({
        deviceList: sortedData,
        loading: false,
        lastFetchTime: now,
        error: null
      });

      return sortedData;
    } catch (error) {
      console.error('Error fetching device list:', error);
      set({
        loading: false,
        error: error.message || '获取设备列表失败',
        lastFetchTime: now
      });
      return [];
    }
  },
  
  // 启动定时刷新
  startAutoRefresh: (interval = 60000) => {
    const { stopAutoRefresh } = get();
    
    // 先清除已有的定时器
    stopAutoRefresh();
    
    // 立即获取一次数据
    get().fetchDeviceList();
    
    // 设置定时器
    const timer = setInterval(() => {
      get().fetchDeviceList();
    }, interval);
    
    set({ refreshTimer: timer });
    
    return timer;
  },
  
  // 停止定时刷新
  stopAutoRefresh: () => {
    const { refreshTimer } = get();
    if (refreshTimer) {
      clearInterval(refreshTimer);
      set({ refreshTimer: null });
    }
  },
  
  // 获取设备统计信息
  getDeviceStats: (bindCodes) => {
    const { deviceList } = get();
    const devices = deviceList.filter(d =>
      Array.isArray(bindCodes)
        ? bindCodes.includes(d.BindCode)
        : d.BindCode === bindCodes
    );
    return {
      online: devices.filter(d => d.IfOnLine).length,
      total: devices.length
    };
  },
  
  // 根据SN获取特定设备
  getDeviceBySN: (sn) => {
    const { deviceList } = get();
    return deviceList.find(device => device.SN === sn);
  },
  
  // 清理方法
  cleanup: () => {
    get().stopAutoRefresh();
    set({ 
      deviceList: [], 
      loading: false, 
      error: null, 
      lastFetchTime: null,
      refreshTimer: null 
    });
  }
}));

export default useDeviceStore;