# PanelBox 背景板组件

一个用于显示背景装饰效果的容器组件，包含背景图片和左右下角装饰图标。

## 功能特性

- 🎨 使用 `panel_box_bg.png` 作为背景图片
- 🔲 左下角显示 `panel_box_L.png` 装饰图标
- 🔳 右下角显示 `panel_box_R.png` 装饰图标
- 📱 支持响应式设计
- ⚙️ 支持自定义尺寸和样式

## 基本用法

```jsx
import PanelBox from '@/pages/SI/components/Styles/PanelBox';

// 基本使用
<PanelBox>
  <div>这里放置你的内容</div>
</PanelBox>

// 指定尺寸
<PanelBox width={400} height={300}>
  <div>指定尺寸的内容</div>
</PanelBox>

// 添加自定义样式
<PanelBox 
  className="my-custom-class" 
  style={{ margin: '20px' }}
>
  <div>自定义样式的内容</div>
</PanelBox>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| children | ReactNode | - | 子元素内容 |
| className | string | '' | 自定义样式类名 |
| style | Object | {} | 自定义行内样式 |
| width | number | - | 组件宽度（像素） |
| height | number | - | 组件高度（像素） |

## 预设尺寸

组件提供了三种预设尺寸样式：

```jsx
// 小尺寸（角落图标 16x16px，内边距 8px）
<PanelBox className="small">
  <div>小尺寸内容</div>
</PanelBox>

// 默认尺寸（角落图标 24x24px，内边距 16px）
<PanelBox>
  <div>默认尺寸内容</div>
</PanelBox>

// 大尺寸（角落图标 32x32px，内边距 24px）
<PanelBox className="large">
  <div>大尺寸内容</div>
</PanelBox>
```

## 样式层级

组件内部元素的 z-index 层级关系：

- 背景层：z-index: 1
- 内容区域：z-index: 2  
- 角落装饰图标：z-index: 3

## 注意事项

1. 组件会自动为内容区域添加底部内边距，确保内容不被角落图标遮挡
2. 背景图片使用 `background-size: cover` 保持比例缩放
3. 角落图标使用 `background-size: contain` 保持原始比例
4. 在移动端（屏幕宽度 ≤ 768px）会自动调整图标尺寸和内边距 