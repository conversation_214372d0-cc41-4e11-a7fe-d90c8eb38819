import React, { useEffect, useState, useRef } from "react";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { Button, Menu } from "antd";
import SelectNav from "@/pages/GT/DZJC/components/SelectNav";
const App = ({ menuItems, handlePageChange }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [items, setItems] = useState([]);
  const menuRef = useRef(null);
  const [menuWidth, setMenuWidth] = useState(96);
  const toggleCollapsed = () => {
    if (collapsed) {
      setMenuWidth(96);
    } else {
      setMenuWidth(72);
    }
    setCollapsed(!collapsed);
  };
  const initItems = () => {
    if (menuItems && menuItems.length > 0) {
      setItems(menuItems);
    }
  }
  useEffect(() => {
    initItems();
  }, []);

  return (
    <>
      <div 
        style={{
              background:
                "linear-gradient(to bottom, #06393f 200px, #1e2531 50%)",
            }}
      >
        {/* <Button
          type="primary"
          onClick={toggleCollapsed}
          style={{ marginBottom: 16 }}
        >
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </Button> */}
        <SelectNav width={menuWidth}></SelectNav>
        <div ref={menuRef}>
          <Menu
            defaultSelectedKeys={["1"]}
            defaultOpenKeys={["sub1"]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={(e) => {
              handlePageChange(e.key)
            }}
          />
        </div>
      </div>
    </>
  );
};
export default App;
