/**
 * Cesium 禁飞区渲染器
 * 在 Cesium 地图上渲染禁飞区数据
 */

import { Cesium } from 'umi';

/**
 * Cesium 禁飞区渲染器类
 */
export class CesiumNFZRenderer {
  constructor(viewer) {
    this.viewer = viewer;
    this.nfzEntities = new Map(); // 存储禁飞区实体
    this.nfzDataSource = null;    // 禁飞区数据源
    this.isVisible = true;        // 是否可见
    this.clickHandler = null;     // 点击事件处理器
    
    this.init();
  }

  /**
   * 初始化渲染器
   */
  init() {
    if (!this.viewer || this.viewer.isDestroyed()) {
      throw new Error('CesiumNFZRenderer: Invalid viewer instance');
    }

    // 创建专用数据源
    this.nfzDataSource = new Cesium.CustomDataSource('NFZ_DataSource');
    this.viewer.dataSources.add(this.nfzDataSource);

    // 设置点击事件处理
    this.setupClickHandler();
  }

  /**
   * 设置点击事件处理器
   */
  setupClickHandler() {
    this.clickHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    
    this.clickHandler.setInputAction((event) => {
      const pickedObject = this.viewer.scene.pick(event.position);
      
      if (Cesium.defined(pickedObject) && 
          pickedObject.id && 
          pickedObject.id.nfzData) {
        this.handleNFZClick(pickedObject.id, event.position);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  /**
   * 处理禁飞区点击事件
   * @param {Object} entity - 被点击的实体
   * @param {Object} position - 点击位置
   */
  handleNFZClick(entity, position) {
    const nfzData = entity.nfzData;
    
    // 显示信息窗口
    this.viewer.selectedEntity = entity;
    
    // 触发自定义事件
    if (this.onNFZClick) {
      this.onNFZClick(nfzData, position);
    }
  }

  /**
   * 创建信息窗口内容
   * @param {Object} nfzData - 禁飞区数据
   * @returns {string} HTML内容
   */
  createInfoWindowContent(nfzData) {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h4 style="margin: 0 0 10px 0; color: #1890ff;">${nfzData.name}</h4>
        <p><strong>等级:</strong> ${nfzData.levelConfig.name}</p>
        <p><strong>类型:</strong> ${nfzData.geometry?.type === 'circle' ? '圆形区域' : '多边形区域'}</p>
        ${nfzData.height ? `<p><strong>高度限制:</strong> ${nfzData.height}m</p>` : ''}
        ${nfzData.description ? `<p><strong>描述:</strong> ${nfzData.description}</p>` : ''}
        ${nfzData.address ? `<p><strong>地址:</strong> ${nfzData.address}</p>` : ''}
      </div>
    `;
  }

  /**
   * 渲染禁飞区数据
   * @param {Array} nfzAreas - 处理后的禁飞区数据
   * @param {Object} options - 渲染选项
   */
  renderNFZAreas(nfzAreas, options = {}) {
    if (!Array.isArray(nfzAreas)) {
      console.warn('CesiumNFZRenderer: Invalid nfzAreas data');
      return;
    }

    // 清除现有数据
    this.clearNFZAreas();

    const renderOptions = {
      showLabels: true,
      enableClick: true,
      maxDisplayCount: 100,
      ...options
    };

    // 限制显示数量
    const areasToRender = nfzAreas.slice(0, renderOptions.maxDisplayCount);
    
    areasToRender.forEach((area, index) => {
      try {
        this.renderSingleArea(area, renderOptions);
        
        // 渲染子区域（倒序渲染，确保后添加的在上层）
        if (area.subAreas && area.subAreas.length > 0) {
          area.subAreas.slice().reverse().forEach(subArea => {
            this.renderSingleArea(subArea, { ...renderOptions, isSubArea: true });
          });
        }
      } catch (error) {
        console.error('CesiumNFZRenderer: Error rendering area:', area, error);
      }
    });

    console.log(`CesiumNFZRenderer: Rendered ${this.nfzEntities.size} NFZ entities`);
  }

  /**
   * 渲染单个禁飞区域
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   */
  renderSingleArea(area, options) {
    if (!area.geometry) {
      console.warn('CesiumNFZRenderer: Area has no geometry:', area);
      return;
    }

    let entity = null;

    if (area.geometry.type === 'circle') {
      entity = this.renderCircleArea(area, options);
    } else if (area.geometry.type === 'polygon') {
      entity = this.renderPolygonArea(area, options);
    }

    if (entity) {
      // 添加禁飞区数据到实体
      entity.nfzData = area;
      
      // 设置描述信息
      entity.description = this.createInfoWindowContent(area);
      
      // 存储实体引用
      this.nfzEntities.set(area.id, entity);
      
      // 添加到数据源
      this.nfzDataSource.entities.add(entity);
    }
  }

  /**
   * 渲染圆形禁飞区
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   * @returns {Object} Cesium实体
   */
  renderCircleArea(area, options) {
    const geometry = area.geometry;
    const center = Cesium.Cartesian3.fromDegrees(geometry.center[0], geometry.center[1]);
    
    const entity = {
      id: area.id,
      name: area.name,
      position: center,
      ellipse: {
        semiMajorAxis: geometry.radius,
        semiMinorAxis: geometry.radius,
        material: Cesium.Color.fromCssColorString(geometry.color).withAlpha(geometry.opacity),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(geometry.color),
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        classificationType: Cesium.ClassificationType.TERRAIN
      }
    };

    // 添加标签（只有父级区域显示标签）
    if (options.showLabels && !options.isSubArea) {
      entity.label = {
        text: area.name,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -20),
        scale: 0.8,
        show: true
      };
    }

    return entity;
  }

  /**
   * 渲染多边形禁飞区
   * @param {Object} area - 区域数据
   * @param {Object} options - 渲染选项
   * @returns {Object} Cesium实体
   */
  renderPolygonArea(area, options) {
    const geometry = area.geometry;
    
    if (!geometry.coordinates || !geometry.coordinates[0]) {
      console.warn('CesiumNFZRenderer: Invalid polygon coordinates:', geometry);
      return null;
    }

    // 转换坐标为Cesium格式
    const positions = geometry.coordinates[0].map(coord => 
      Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
    );

    // 计算多边形中心点（用于标签位置）
    const center = this.calculatePolygonCenter(geometry.coordinates[0]);

    const entity = {
      id: area.id,
      name: area.name,
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString(geometry.color).withAlpha(geometry.opacity),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(geometry.color),
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        classificationType: Cesium.ClassificationType.TERRAIN
      }
    };

    // 添加标签（只有父级区域显示标签）
    if (options.showLabels && !options.isSubArea && center) {
      entity.position = Cesium.Cartesian3.fromDegrees(center[0], center[1]);
      entity.label = {
        text: area.name,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -20),
        scale: 0.8,
        show: true
      };
    }

    return entity;
  }

  /**
   * 计算多边形中心点
   * @param {Array} coordinates - 坐标数组
   * @returns {Array} 中心点坐标 [lng, lat]
   */
  calculatePolygonCenter(coordinates) {
    if (!coordinates || coordinates.length === 0) return null;

    let totalLng = 0;
    let totalLat = 0;
    let count = 0;

    coordinates.forEach(coord => {
      if (coord && coord.length >= 2) {
        totalLng += coord[0];
        totalLat += coord[1];
        count++;
      }
    });

    if (count === 0) return null;

    return [totalLng / count, totalLat / count];
  }

  /**
   * 清除所有禁飞区
   */
  clearNFZAreas() {
    if (this.nfzDataSource) {
      this.nfzDataSource.entities.removeAll();
    }
    this.nfzEntities.clear();
  }

  /**
   * 设置禁飞区可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.isVisible = visible;
    if (this.nfzDataSource) {
      this.nfzDataSource.show = visible;
    }
  }

  /**
   * 获取禁飞区可见性
   * @returns {boolean} 是否可见
   */
  getVisible() {
    return this.isVisible;
  }

  /**
   * 设置点击回调函数
   * @param {Function} callback - 回调函数
   */
  setClickCallback(callback) {
    this.onNFZClick = callback;
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    // 清除数据
    this.clearNFZAreas();
    
    // 移除数据源
    if (this.nfzDataSource && this.viewer && !this.viewer.isDestroyed()) {
      this.viewer.dataSources.remove(this.nfzDataSource);
    }
    
    // 清除点击事件处理器
    if (this.clickHandler) {
      this.clickHandler.destroy();
      this.clickHandler = null;
    }
    
    this.nfzDataSource = null;
    this.viewer = null;
    this.nfzEntities.clear();
  }
}

/**
 * 创建Cesium禁飞区渲染器实例
 * @param {Object} viewer - Cesium Viewer实例
 * @returns {CesiumNFZRenderer} 渲染器实例
 */
export function createCesiumNFZRenderer(viewer) {
  return new CesiumNFZRenderer(viewer);
}

export default {
  CesiumNFZRenderer,
  createCesiumNFZRenderer
};
