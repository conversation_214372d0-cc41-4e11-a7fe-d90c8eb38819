
import { getGuid } from '@/utils/helper';


import VideoManagement from '@/pages/GT/GDBH/Pages/VideoManagement';
import ModelListPage3 from "@/pages/DJI/AIPage/ModelListPage3";
import FlightTask from '@/pages/SI/components/flightTask';
import TaskListPage from "@/pages/DJI/TaskListPage/index2";


export const SIRouter = [
    {
        title: "影像管理",
        key: "/SI/GDBH/txgl",
        children: <VideoManagement key={getGuid()} />,
    },
    {
        title: "AI算法仓",
        key: "/SI/GDBH/AIsfc",
        children: <ModelListPage3 isSipage={true} key={getGuid()} />,
    },
    {
        title: "巡飞任务",
        key: "/SI/GDBH/xunfei",
        children: <FlightTask doNotShowLastButton={true}/>,
    },
    {
        title: "GDBH数据管理",
        key: "/SI/GDBH/sjgl",
        children: <TaskListPage doNotShowLastButton={true}/>,
    }
    
];
