.notipage {
    padding: 40px 20px 20px 20px;
    border-radius: 12px;
    box-shadow: rgb(0 0 0 / 5%) 0 0 20px;
    background: #fff;
}


.notipage :global(.ant-col-14.ant-form-item-control) {
    max-width: 100%;
    padding-right: 10%;
}

.addButton {
    background: url('@/assets/img/line.png') bottom center no-repeat, linear-gradient(-135deg, #00aeff, #0a3cb9);
    border: #0a3cb9 solid 1px !important;
    box-shadow: #6bb3e1 0 0 0 1px inset;
}


.page {
    background: linear-gradient(0deg, #ddd, #fff 30%);
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.page::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('@/assets/img/noticebg.png') no-repeat;
    background-size: 100% auto;
    z-index: -1;
 opacity: 0.5;
}