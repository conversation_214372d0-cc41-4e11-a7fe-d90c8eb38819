.map-scale-container {
  background-color: rgba(255, 255, 255, 0.85);
  padding: 8px 15px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: 'Arial', sans-serif;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  z-index: 1000;
  min-width: 80px;
  text-align: center;
}

.map-scale-bar-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
}

.map-scale-bar {
  height: 2px;
  background-color: #2c3e50;
  position: relative;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.map-scale-bar::before,
.map-scale-bar::after {
  content: '';
  position: absolute;
  top: -4px;
  width: 1px;
  height: 8px;
  background-color: #2c3e50;
}

.map-scale-bar::before {
  left: 0;
}

.map-scale-bar::after {
  right: 0;
}

.map-scale-label {
  font-size: 11px;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1;
}

@media (max-width: 768px) {
  .map-scale-container {
    bottom: 20px;
    left: 15px;
    padding: 8px 12px;
    min-width: 70px;
  }

  .map-scale-label {
    font-size: 10px;
  }

  .map-scale-bar::before,
  .map-scale-bar::after {
    top: -3px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .map-scale-container {
    bottom: 15px;
    left: 10px;
    padding: 6px 10px;
    border-radius: 4px;
    min-width: 60px;
  }
  .map-scale-bar-wrapper {
    margin-bottom: 4px;
  }
}