.selected-row {
  &>td {
    background-color: rgba(255, 255, 255, 0.08);
  }

  &:hover>td {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

.filter-section {
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 16px;

  .filter-label {
    margin-bottom: 0;
    font-weight: 500;
    white-space: nowrap;
  }
}

.model-table {
  padding: 8px;
  margin-top: 8px;

  .ant-radio-wrapper {
    margin-right: 8px;
  }

  .ant-radio-inner {
    border-color: @primary-color;
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: @primary-color;
    background-color: @primary-color;
  }
}

.selection-content {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .model-item {
    display: flex !important;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 0;
    border-bottom: 1px solid #f0f0f0;

    .model-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-btn {
      flex-shrink: 0;
      margin-left: 8px;
    }
  }
}

.model-detail-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  .model-tag {
    flex-shrink: 0;
    margin-right: 8px;
  }

  .model-description {
    flex: 1;
    word-break: break-word;
  }
}