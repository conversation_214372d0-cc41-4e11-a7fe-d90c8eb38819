import React, { useState, useEffect } from "react";
import {  <PERSON>, Button, message, Table} from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2} from "@/services/general";
import ComStyles from "@/pages/common.less";
import UserAddForm from "./form_add";
import TableCols from "./table";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import AdminDiv from "@/components/AdminDiv";
import { getGuid } from '@/utils/helper';
const UserListPage = ({doNotShowLastButton}) => {
  const [wayList, setWayList] = useState({});
  const [UserList, setUserList] = useState({});
  const { setModal, setOpen, setPage, lastPage } = useModel("pageModel");

  const getUserData = async () => {
    const pst = await Get2("/api/v1/UserInfo/GetAllList", {});
    if(isEmpty(pst)) {
      setUserList([]);
      return;
    }
    if(!isEmpty(pst.err)) {
      message.error(pst.err);
      setUserList([]);
      return;
    }
    setUserList(pst);
  };

  useEffect(() => {
    

    getUserData();
  }, []);

  const showMap = (values) => {
    const record = wayList.find((item) => {
      return item.WanLineId === values.FLightId;
    });
    setPage(
      <Card
        title="航线信息"
        extra={<Button onClick={() => lastPage()}>返回</Button>}
      >
        {WayLineMap(getBodyH(180), record)}
      </Card>
    );
  };

  const refrush = async () => {
    setOpen(false);
    getUserData();
  };

  const exr = (
    <AdminDiv>
      <Button
        className={ComStyles.addButton}
        type="primary"
        onClick={() => {
          setModal(<UserAddForm key={getGuid()} UserList={UserList} wayList={wayList} refrush={refrush} />);
          setOpen(true);
        }}
      >
        添加用户
      </Button>
    </AdminDiv>
  );
  {
  }
  return (
    <div style={{ margin: 0, height: getBodyH(56)}}>
      <Card
        title={ doNotShowLastButton ? '用户管理' : <LastPageButton title="用户管理"/> }
        bordered={false}
        extra={exr}
      >
        <div>
          {isEmpty(UserList) ? (
            <div />
          ) : (
            <Table
              pagination={false}
              bordered
              dataSource={UserList}
              columns={TableCols(UserList,refrush, showMap)}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default UserListPage;
