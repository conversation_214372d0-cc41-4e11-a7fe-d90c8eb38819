import {
  Input,
  Card,
  Form,
  Button,
  message,
} from "antd";
import React, { useState, useEffect } from "react";
import { Get2} from "@/services/general";
import { isEmpty } from "@/utils/utils";
import { HPost2 } from "@/utils/request";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import styles from "./form_add.less";
import AdminDiv from "@/components/AdminDiv";

const { TextArea } = Input;
const NoticeAddForm = () => {
  const [data, setData] = useState({ Content: "" });
  const [ifLoad, setIfLoad] = useState(true);

  const getNotice = async () => {
    let pst = await Get2("/api/v1/Notice/GetNotice", {});
    if(!isEmpty(pst.err)) {
      message.error(pst.err);
      pst={};
    } 
    setData(pst);
    setIfLoad(false);
  };

  useEffect(() => {
    getNotice();
  }, []);

  const onSave = async (e) => {
    //
    // if (isEmpty(data["Content"])) {
    //   message.info("请输入公告内容");
    //   return;
    // }
    // const phone = data["UserPhone"];
    // const phoneRegex = /^1\d{10}$/;
    // if (!phoneRegex.test(phone)) {
    //   message.info("请输入有效电话号码!");
    //   return;
    // }
    const xx = await HPost2("/api/v1/Notice/Add", data);
    if (isEmpty(xx.err)) {
      message.info("创建成功！");
    }
    getNotice();
  };

  const EditForm = (
    <Form
      labelCol={{
        span: 4,
      }}
      wrapperCol={{
        span: 14,
      }}
      layout="horizontal"
      //disabled={componentDisabled}
      style={{
        width: 900,
        marginTop: 24.0,
      }}
    >
      <div className={styles.notipage}>
        <Form.Item label="公告信息">
          <TextArea
            rows={10}
            defaultValue={data.Content}
            onChange={(e) => setData({ ...data, Content: e.target.value })}
          />
        </Form.Item>

        <Form.Item label="发布单位">
          <Input
            defaultValue={data.UserName}
            onChange={(e) => setData({ ...data, UserName: e.target.value })}
          ></Input>
        </Form.Item>

        <Form.Item label="联系电话">
          <Input
            defaultValue={data.UserPhone}
            onChange={(e) => setData({ ...data, UserPhone: e.target.value })}
          ></Input>
        </Form.Item>

        <Form.Item label={" "} colon={false}>
          <AdminDiv>
            <Button type="primary" onClick={onSave}>
              发布公告
            </Button>
          </AdminDiv>
        </Form.Item>
      </div>
    </Form>
  );

  const ShowForm = <Card title={"公告"}>{data.Content}</Card>;

  if (ifLoad) {
    return <LoadPanel />;
  } else {
    return <div className={styles.page}>{EditForm}</div>;
  }
};

export default NoticeAddForm;
