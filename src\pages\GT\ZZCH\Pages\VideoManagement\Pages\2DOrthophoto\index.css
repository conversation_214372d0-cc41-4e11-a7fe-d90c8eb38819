.header {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
}

.header .search {
    display: flex;
    gap: 16px;
}

.header .btns {
    display: flex;
    gap: 16px;
    margin-left: 16px;
}

.Orthophoto_body {
    margin-top: 16px;
    height: 80vh;

    
    .placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(12, 19, 56, 0.4);
        border-radius: 5px;
        border: 1px dashed rgba(255, 255, 255, 0.15);
    }
    
    /* 修改CSS部分 */
    .list-item-container {
        width: 300px;
        height: 250px;
        display: flex;
        flex-direction: column;
        border-radius: 5px;
        overflow: hidden;
        background: rgba(12, 19, 56, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }
    
    .list-item-container:hover {
        transform: translateY(-2px);
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    
    .list-item-content {
        flex: 1;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .file-name {
        padding-left: 8px;
        background: transparent;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .file-rest {
        padding-left: 8px;
        background: transparent;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(255, 255, 255, 0.5) !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    }
}

