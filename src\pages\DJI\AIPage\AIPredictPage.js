import { Upload,Card, Button ,message,Descriptions, Row, Col, Badge} from "antd";
import { useEffect, useState } from "react";
import { getBodyH, isEmpty } from "@/utils/utils";
import AIImgPanel from "./AIImgPanel";
import { Get2 } from "@/services/general";

const AIPredictPage=({model})=>{

    const [fName,setFName]=useState('');
    const [aData,setAData]=useState([]);
    const [IfOk,setIfOk]=useState(false);
    const [dCount,setDCount]=useState(0);
    const AList = model.AList.split(",");
  useEffect(() => {
    const getDataCount = async () => {
      const  pst= await Get2( '/api/v1/AIDataset/GetCount?id='+model.Guid,{});
      setDCount(pst);
    };
    getDataCount();
  },[]);


    const upProps =()=> {
        const token=localStorage.getItem('token');
         return {
         name: 'file',
         action: '/api/v1/AI/UploadImg',
         //data:{id},
         showUploadList: false,
         //accept:'video/*',
         multiple: false,
         headers: {
           authorization: 'authorization-text',
           'auth':token, 
         },
         beforeUpload: file => {
           //console.log('112',file.type)
          // setFName('');
          // setAData([]);
          setIfOk(false);
          //  const isPNG = (file.type.indexOf("vnd.google-earth.kmz") == -1);
          //  if (isPNG) {
          //    message.error(`${file.name} 不是kmz文件`);
          //  }
          // return !isPNG || Upload.LIST_IGNORE;
          return true;
         },
         onChange(info) {
           if (info.file.status === 'done') {
             setFName(info.file.response);
             getAData(info.file.response)
           } else if (info.file.status === 'error') {
             message.error(`${info.file.name} 上传失败！`);
           }
         },
    
       }};
    const getAData=async (xx)=>{
        const yy=await Get2('api/v1/AI/WorkAI?id='+xx+'&m1='+model.ObjectName)
        if(isEmpty(yy)){
          message.error('运行失败！');
          return;
        }
        setAData(JSON.parse(yy));
        setIfOk(true);
    }
    

    const dPanel1 = () => {
      return<Card title={model.AName} ><Descriptions  style={{ textAlign: 'left',margin:12.0 }} column={2}>
           {/* <Descriptions.Item label="模型名称" >{model.AName}</Descriptions.Item> */}
             <Descriptions.Item label="模型编号" >{model.Guid}</Descriptions.Item>
           <Descriptions.Item label="样本总数">{dCount}</Descriptions.Item>
           <Descriptions.Item label="部署状态">{model.State==1?<Badge status="success" text="已部署" />:<Badge status="warning" text="未部署" />}</Descriptions.Item>
          {/* <Descriptions.Item label="新样本数">{'1800张'}</Descriptions.Item> */}
           <Descriptions.Item label="模型备注" span={2}>{model.Description}</Descriptions.Item>
          <Descriptions.Item label="模型识别" span={2}>{model.AList}</Descriptions.Item>
          
      </Descriptions></Card> }

  //计算结果显示
  const dPanel2 = () => {
    if(!IfOk) return <div></div>
    const xx={}
    if(isEmpty(aData)) return <Card style={{marginTop:24.0}} title={'识别结果'} >未识别到</Card>
    aData.forEach(e => {
      xx[e.class] = xx[e.class] || [];
      xx[e.class].push(e);
    });
    console.log('aList',xx);
    const list=[]

    const keys = Object.keys(xx); // 获取水果种类：["apple", "banana"]
    keys.forEach(nameItem => {
      //let count = 0;
      list.push( <Descriptions.Item label={AList[nameItem]}>{xx[nameItem].length}</Descriptions.Item>)
    });


    
    return<Card title={'识别结果'}  style={{marginTop:24.0}}><Descriptions  style={{ textAlign: 'left',margin:12.0 }} column={2}>
      {list}
      
  </Descriptions></Card> }

    const getExtra=()=>{
      return null
      if(model.State==1){
        return  <Upload {...upProps()}><Button>上传识别图片</Button></Upload>
      }else{
        return <Badge status="warning" text="模型未部署" />
      }
    }

    const getAIPanel=()=>{
       if(IfOk){
        return <AIImgPanel key={fName} aList={AList} img1={"/api/v2/AI/DownloadImg?id="+fName} data={aData} w1={0} h1={getBodyH(300)} />
       }
    }

    const getBody2=()=>{
     return <Card style={{marginLeft:24.0}} title="模型识别" extra={getExtra()}>{getAIPanel()}</Card> 
    }
    const getBody=()=>{
      return <Row>
        <Col span={10} >
          {dPanel1()}

          {dPanel2()}
        </Col>
      
      <Col span={14} >{getBody2()}</Col>
          
      </Row>
    }
    return   <div>{getBody()}</div>;
}

export default AIPredictPage;