import { useState, useEffect } from 'react';
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
  RocketOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  FileImageOutlined,
  VideoCameraOutlined,
  DeploymentUnitOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { Button, Menu } from 'antd';
import { useModel } from 'umi';
import { queryPage, queryPageByPath } from '@/utils/MyRoute';
import styles from './index.less';
import eventBus from '@/utils/EventBus';

const App = ({ handlePageChange, setCollapsed, collapsed, menuItems, className }) => {
  const [openKeys, setOpenKeys] = useState([]);
  const [selectedKey, setSelectedKey] = useState('');

  useEffect(() => {
    // 仅在初始加载或菜单项发生根本变化时更新选中状态
    const validKeys = menuItems?.map(i => i?.key).filter(Boolean);

    if (validKeys.length > 0) {
      // 当前选中key无效时才设置默认值
      if (!validKeys.includes(selectedKey)) {
        setSelectedKey(validKeys[0]);
        setOpenKeys([validKeys[0]]);
      }
    }
  }, [menuItems]);

  // 监听标签页切换事件
  useEffect(() => {
    const unsubscribe = eventBus.on('tab-active-changed', activeKey => {
      setSelectedKey(activeKey);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 处理菜单点击
  function handleMenu(e) {
    const menuInfoKey = e.key;

    // 添加防重复点击判断
    if (menuInfoKey && menuInfoKey !== selectedKey) {
      eventBus.emit('add-tab', menuInfoKey);
      setSelectedKey(menuInfoKey);

      // 保持父级菜单展开
      const parentKey = menuItems?.find(i => i.key === menuInfoKey)?.parentKey;
      if (parentKey) {
        setOpenKeys(prev => [...new Set([...prev, parentKey])]);
      }
    }

    handlePageChange(e.key);
  }

  // 处理菜单展开和收起
  const onOpenChange = keys => {
    setOpenKeys(keys);
  };

  return (
    <>
      {
        <div
          className={`${styles.sidebarContainer} ${className || ''}`}
          style={{
            position: 'fixed',
            left: 0,
            top: 56,
            bottom: 0,
            width: collapsed ? 75 : 75, // MyMenu的width调整，需要同步调整对应ControlCenter的marginLeft
            background: 'rgba(15, 27, 45, 1)',
            zIndex: 1000,
            transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',
          }}
        >
          {/* 折叠按钮 目前不需要 */}
          {/* <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{
              marginBottom: 16,
              marginLeft: collapsed ? 16 : 16,
              marginTop: 8,
              background: '#162c53',
              borderColor: '#162c53',
              transition: 'all 0.3s',
              transform: collapsed ? 'rotate(0deg)' : 'rotate(0deg)',
            }}
          >
            <span style={{ display: 'inline-block', transition: 'transform 0.3s' }}>
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </span>
          </Button> */}
          <Menu
            selectedKeys={[selectedKey]}
            // defaultOpenKeys={[selectedKey]}
            openKeys={!collapsed ? openKeys : []}
            onOpenChange={onOpenChange}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={menuItems}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 47px)', /* 减去头部高度 MyHead.less 47px*/
              overflowY: 'auto',
              backgroundColor: 'transparent',
              borderRight: 'none',
            }}
            className={styles.customMenu}
          />
        </div>
      }
    </>
  );
};

export default App;
