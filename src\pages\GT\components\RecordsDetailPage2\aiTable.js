import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, } from "antd";
import { timeFormat } from "@/utils/helper";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import { queryPage, } from '@/utils/MyRoute';

const getTableTitle = (title) => {
    return (
        <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
    );
};
const aiTableCols = () => {

    const { setPage, } = useModel("pageModel");

    const StatusMap = {
        0: { color: 'default', text: '待执行' },
        1: { color: 'processing', text: '进行中' },
        2: { color: 'success', text: '已完成' },
        3: { color: 'error', text: '失败' },
        4: { color: 'error', text: '已取消' }
    };

    return [
        {
            title: getTableTitle("任务名称"),
            dataIndex: "TaskName",
            key: "TaskName",
            align: "center",
        },
        {
            title: getTableTitle("算法类型"),
            dataIndex: "Classification",
            key: "Classification",
            align: "center",
        },
        {
            title: getTableTitle("任务状态"),
            key: "Status",
            align: "center",
            render: (_, record) => {
                const { color, text } = StatusMap[record.Status] || {};
                const statusElement = text ? (
                    <Tag color={color}>{text}</Tag>
                ) : (
                    <span>-</span>
                );
        
                // 当状态为失败时，用Popover包裹并显示Process
                return record.Status === 3 ? (
                    <Popover 
                        content={
                            <div style={{ maxWidth: 300 }}>
                                <Alert 
                                    message="失败详情"
                                    description={record.ExecutionFeedback || "无错误信息"}
                                    type="error"
                                    showIcon
                                />
                            </div>
                        }
                        title="任务执行失败"
                        trigger="hover"
                    >
                        {/* 保留原样式，增加指针样式 */}
                        <span style={{ cursor: 'help' }}>
                            {statusElement}
                        </span>
                    </Popover>
                ) : statusElement;
            }
        },
        {
            title: getTableTitle("识别结果"),
            dataIndex: "content",
            key: "content",
            align: "center",
        },
        {
            title: getTableTitle("操作"),
            align: "center",
            render: (_, record) => (
                <Space size="middle">

                    <MyButton
                        style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
                        onClick={() => {
                            localStorage.setItem('record', JSON.stringify(record));
                            setPage(queryPage('识别详情'))
                        }}
                    >
                        详情
                    </MyButton>

                </Space>
            ),
        }
    ];
};

export default aiTableCols;
