import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import camera4 from "@/assets/drcImgs/camera4.png";

import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import video4 from "@/assets/drcImgs/video4.png";
import { HGet2 } from "@/utils/request";
import { Get2 } from '@/services/general';
import { isEmpty } from "@/utils/utils";
import { useEffect, useState,useRef } from "react";
import { useModel } from "umi";
import { getMinstr } from "./helper";
import { Modal, Tooltip,Input,InputNumber,message,Spin } from "antd";
import { SyncOutlined  } from "@ant-design/icons";
const { TextArea } = Input;

const CameraPanel = () => {

  const w1 = 60;
  const h1 = 240;
  const { fj, fjData } = useModel("droneModel");
  const { DoCMD } = useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const [ifVideo, setIfVideo] = useState(false);


  const [isModalOpen, setIsModalOpen] = useState(false);

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);

    // if (v == 1) {
    //   VideoSave();
    // } else {
    //   CameraSave();
    // }
  };

  const photoCamera = (v) => {
    const data = { payload_index: device.Camera2 };
    if (fj?.data?.cameras[0]?.camera_mode == 0) {
      DoCMD(device.SN, "camera_photo_take", data);
    } else {
      changeCamera(0);
      setTimeout(() => {
        DoCMD(device.SN, "camera_photo_take", data);
      }, 1000);
    }
  };

  const VideoStart = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_start", data);
  };

  const VideoStop = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_stop", data);
  };

  const VideoClick = () => {
    if (fj?.data?.cameras[0]?.recording_state == 1) {
      VideoStop();
      setIfVideo(false);
    } else {
      changeCamera(1);
      setTimeout(() => {
        VideoStart();
        setIfVideo(true);
      }, 1000);
    }
  };

  const CameraSave = () => {
    const data = {
      payload_index: device.Camera2,
      photo_storage_settings: ["current"],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const VideoSave = () => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["current"],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const getVideoImg = () => {
    if (ifVideo) return video3;
    return video4;
  };



  useEffect(() => {
    //
    if (isEmpty(fjData.current)) return;
    if (isEmpty(fjData.current?.cameras)) return;
    if (fjData.current?.cameras[0]?.recording_state == 1) {
      setIfVideo(false);
    } else {
      setIfVideo(true);
    }
  }, [fjData.current]);

  const onHanHuaQi = () => {
    setIsModalOpen(true)
  };

  async function hanhua(value){
    await Get2("/api/v1/PSDK/SpeakerSetVolume?sn=" + device.SN)
     HGet2("/api/v1/PSDK/Speaker?sn=" + device.SN + "&val="+value)
  }

  let [x1, setX1] = useState();
  let [count, setCount] = useState();
  const [spinning, setSpinning] = useState(false);
  let [showCount,setShowCount] = useState(); 


  let myCounter = (function () {
    let value = x1;
    let intervalId = useRef;
    let step = count;

    if (!step) {
      step = 5;
    }
    function timeFc() {
      if (step < 1) {
        message.open({ type: "success", content: "已喊话完毕!" });
        clearInterval(intervalId.current);
        setSpinning(false);
      } else {
        hanhua(value);
        step--;
        setShowCount(step)
      }

    }

    function start() {
      if (!value) {
        message.open({ type: "warning", content: "喊话内容是空的哦!" });
        return;
      }
        if(intervalId){
          window.clearInterval(intervalId.current);
        }
        setSpinning(true);
        timeFc()
        intervalId.current = window.setInterval(()=>{
            timeFc()
          },x1?.length * 500)
      // setOpen(false);
    }
    function stop() {
      window.clearInterval(intervalId.current);
      intervalId = null;
      message.open({ type: "success", content: "已喊话完毕!" });
      setSpinning(false);
    }
    function reset() {
      stop();
      step = 0;
    }
    return { startCount: start, stopCount: stop, resetCount: reset };
  })();

  const handleCancel = () => {
    myCounter.stopCount()
    setIsModalOpen(false);
  };

  const getCameraName=()=>{
    if(isEmpty(fj.data)) return "";
    if(isEmpty(fj.data.cameras)) return "";

   if(fj.data?.cameras[0]?.recording_state == 0) return "开始录像" 
   return "结束录像"
  }
  // if(isEmpty(fj)) return;
  // if(fj.data.cameras[0].camera_mode==0){
  return (
    <>
        <div>
      <Modal
        title="无人机喊话"
        open={isModalOpen}
        onOk={myCounter.startCount}
        onCancel={handleCancel}
        cancelText='取消'
        okText='确认'
        destroyOnClose={true}
      >
       <div style={{display:'flex',flexDirection:'column',justifyContent: 'space-around',alignItems:'center',padding:'0 20px 0 20px'}}>
       <div style={{ marginBottom: 20, display: "flex", alignItems: "center",height:'20px' }}>
        
        <>
          <Spin
            indicator={<SyncOutlined  spin />}
            spinning={spinning}
            size="middle"
            style={{color:'#81ce27'}}
          />
          <span style={{ 
            marginLeft: 5,
            // color:'#81ce27'
            background: "linear-gradient(to right, #fffefe, #88ff00)",
           }}>{spinning?`正在喊话中...还剩${showCount}次。`:''}</span>
          {/* <span>
            <Button onClick={myCounter.stopCount}>中断喊话</Button>
          </span> */}
        </>
  
    </div>
            <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}>
            <span>设置喊话语句:</span>
            {/* <span > <Input placeholder='请输入喊话内容' allowClear onChange={(e) => setX1(e.target.value)} defaultValue={x1} style={{width:340}}  /></span> */}
            <span>
          {" "}
          <TextArea
            showCount
            maxLength={200}
            placeholder="请输入喊话内容"
            onChange={(e) => setX1(e.target.value)}
            defaultValue={x1}
            style={{ width: 340 }}
          />
        </span>
            </div>
            <div style={{margin:'20px 0',lineHeight:'32px'}}>
            <span>设置喊话次数:</span>
            <span > <InputNumber placeholder='若未设,默认为5次' addonAfter="次" defaultValue={count} onChange={(value) => setCount(value)} style={{width:340}}/></span>
            </div>
     </div>
      </Modal>
    </div>

      <div
        draggable={false}
        style={{
          userSelect: "none",
          position: "absolute",
          opacity: 0.6,
          top: 170,
          right: 40,
          height: h1,
          width: w1,
          zIndex: 1010,
        }}
      >
        <div style={{ userSelect: "none", padding: 6.0 }}>
          <Tooltip placement="left" title="喊话器drc">
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={42}
              width={42}
              src={camera4}
              onClick={() => onHanHuaQi()}
            ></img>
          </Tooltip>
        </div>
        <div style={{ userSelect: "none", marginTop: 32.0,marginLeft:6 }}>
          <Tooltip placement="left" title="拍照">
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={45}
              width={45}
              src={camera3}
              onClick={() => {
                photoCamera();
              }}
            ></img>
          </Tooltip>
        </div>
     
        <div style={{ userSelect: "none", marginTop: 32.0,marginLeft:6 }}>
          <Tooltip
            placement="left"
            title={
              getCameraName()
            }
          >
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={45}
              width={45}
              src={getVideoImg()}
              onClick={() => VideoClick()}
            ></img>
          </Tooltip>
        </div>
        <div
          style={{
            userSelect: "none",
            // marginTop: 5.0,
            marginLeft: 13.0,
            fontFamily: "MiSan",
            color: "red",
          }}
        >
          {getMinstr(fj.data?fj.data?.cameras[0].record_time:'')}
        </div>
      </div>
    </>
  );
};

export default CameraPanel;
