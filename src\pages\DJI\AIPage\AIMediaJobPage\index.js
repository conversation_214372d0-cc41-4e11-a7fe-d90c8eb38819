import { useState, useEffect } from 'react';

import { Card, Button, Table,  } from 'antd';

import { getBodyH, isEmpty } from '@/utils/utils';

import { Get2, Post2 } from '@/services/general';

import ComStyles from '@/pages/common.less';
import CronAddForm from './form_add';
import TableCols from './table';
import LastPageButton from '@/components/LastPageButton';

import { useModel } from 'umi';

const AIMediaJobListPage = (props) => {

  const [modelList, setModelList] = useState([]);
  const [wayList, setWayList] = useState([]);
  const [cronList, setCronList] = useState([]);
  const { setModal, setOpen, } = useModel('pageModel')

  useEffect(() => {
    const getLineData = async () => {
      const pst = await Get2('/api/v1/WayLine/GetAllList', {});
      if (isEmpty(pst)) return;
      setWayList(pst);
    };

    const getModelData = async () => {
      const pst = await Get2('/api/v1/AIModel/GetList', {});
      setModelList(pst);
    };

    const getCronData = async () => {
      const pst = await Get2('/api/v1/AIMediaJob/GetAllList', {});
      if (isEmpty(pst)) return;
      setCronList(pst);
    };

    getLineData();
    getCronData();
    getModelData();
  }, []);


  const refrush = async () => {
    setOpen(false);
    const pst = await Get2('/api/v1/AIMediaJob/GetAllList', {});
    if (isEmpty(pst)) return;
    setCronList(pst);
  }


  const exr = <Button type="primary" className={ComStyles.addButton} onClick={() => { setModal(<CronAddForm wayList={wayList} refrush={refrush} modelList={modelList} />); setOpen(true) }}>新建任务</Button>
  return (
    <div style={{ margin: 0, height: getBodyH(56)}}>
      <Card title={<LastPageButton title= "图像识别任务"/>} bordered={false} extra={exr} >
        <div>
          {isEmpty(cronList) ? <div /> : <Table pagination={false}
            bordered dataSource={cronList} columns={TableCols(refrush)} size='small' />}
        </div>
      </Card>
    </div>
  )
};

export default AIMediaJobListPage;
