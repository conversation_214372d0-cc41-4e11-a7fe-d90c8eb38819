import React, { useState } from 'react';
import { CarryOutOutlined, CheckOutlined, FormOutlined } from '@ant-design/icons';
import { Select, Switch, Tree } from 'antd';
const treeData = [
  {
    title: '土地资源',
    key: '0-0',
    icon: <CarryOutOutlined />,
    children: [
      {
        title: '土地资源 1-0',
        key: '0-0-0',
        icon: <CarryOutOutlined />,
        children: [
          {
            title: 'leaf',
            key: '0-0-0-0',
            icon: <CarryOutOutlined />,
          },
          {
            title: (
              <>
                <div>multiple line title</div>
                <div>multiple line title</div>
              </>
            ),
            key: '0-0-0-1',
            icon: <CarryOutOutlined />,
          },
          {
            title: 'leaf',
            key: '0-0-0-2',
            icon: <CarryOutOutlined />,
          },
        ],
      },
      {
        title: '土地资源 1-1',
        key: '0-0-1',
        icon: <CarryOutOutlined />,
        children: [
          {
            title: 'leaf',
            key: '0-0-1-0',
            icon: <CarryOutOutlined />,
          },
        ],
      },
      {
        title: '土地资源 1-2',
        key: '0-0-2',
        icon: <CarryOutOutlined />,
        children: [
          {
            title: 'leaf',
            key: '0-0-2-0',
            icon: <CarryOutOutlined />,
          },
          {
            title: 'leaf',
            key: '0-0-2-1',
            icon: <CarryOutOutlined />,
            switcherIcon: <FormOutlined />,
          },
        ],
      },
    ],
  },
  {
    title: '耕地资源',
    key: '0-1',
    icon: <CarryOutOutlined />,
    children: [
      {
        title: '耕地资源 2-0',
        key: '0-1-0',
        icon: <CarryOutOutlined />,
        children: [
          {
            title: 'leaf',
            key: '0-1-0-0',
            icon: <CarryOutOutlined />,
          },
          {
            title: 'leaf',
            key: '0-1-0-1',
            icon: <CarryOutOutlined />,
          },
        ],
      },
    ],
  },
  {
    title: '正射影像',
    key: '0-2',
    icon: <CarryOutOutlined />,
    children: [
      {
        title: '正射影像 2-0',
        key: '0-2-0',
        icon: <CarryOutOutlined />,
        children: [
          {
            title: 'leaf',
            key: 'http://125.70.229.68:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/WenJiangZhenFu/{z}/{x}/{y}.png',
            icon: <CarryOutOutlined />,
          },
          {
            title: '温江',
            key: 'http://112.44.103.230:9000/300bdf2b-a150-406e-be63-d28bd29b409f/cdkcy/WenJiangZhenFu/{z}/{x}/{y}.png',
            icon: <CarryOutOutlined />,
          },
        ],
      },
    ],
  },
];
const App = ({updateZSMapUrl}) => {
  const onSelect = (selectedKeys, info) => {
    console.log('selected', selectedKeys, info);
    updateZSMapUrl(selectedKeys)
  };

  return (
    <div>

      <Tree
        showLine={true}
        showIcon={false}
        defaultExpandedKeys={['0-0-0']}
        onSelect={onSelect}
        treeData={treeData}
      />
    </div>
  );
};
export default App;