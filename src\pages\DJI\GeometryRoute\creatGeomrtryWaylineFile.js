// 生成wpml
export function CreatWaylineFile(waylineFileInfo) {
    let wpmlString = CreatWpml(waylineFileInfo)
    let kmlString = CreatKml(waylineFileInfo)
    console.log(wpmlString);
    console.log(kmlString);

    return { wpmlString, kmlString }
}
// 生成wpml
function CreatWpml(wpmlObject) {
    return `<?xml version="1.0" encoding="UTF-8"?>
    <kml xmlns="http://www.opengis.net/kml/2.2" xmlns:wpml="http://www.dji.com/wpmz/1.0.6">
        <Document>
            <wpml:missionConfig>
                <wpml:flyToWaylineMode>${wpmlObject.missionConfig.flyToWaylineMode}</wpml:flyToWaylineMode>
                <wpml:finishAction>${wpmlObject.missionConfig.finishAction}</wpml:finishAction>
                <wpml:exitOnRCLost>${wpmlObject.missionConfig.exitOnRCLost}</wpml:exitOnRCLost>
                <wpml:executeRCLostAction>${wpmlObject.missionConfig.executeRCLostAction}</wpml:executeRCLostAction>
                <wpml:takeOffSecurityHeight>${wpmlObject.missionConfig.takeOffSecurityHeight}</wpml:takeOffSecurityHeight>
                <wpml:globalTransitionalSpeed>${wpmlObject.missionConfig.globalTransitionalSpeed}</wpml:globalTransitionalSpeed>
                <wpml:globalRTHHeight>${wpmlObject.missionConfig.globalRTHHeight}</wpml:globalRTHHeight>
                <wpml:droneInfo>
                    <wpml:droneEnumValue>${wpmlObject.missionConfig.droneInfo.droneEnumValue}</wpml:droneEnumValue>
                    <wpml:droneSubEnumValue>${wpmlObject.missionConfig.droneInfo.droneSubEnumValue}</wpml:droneSubEnumValue>
                </wpml:droneInfo>
                <wpml:waylineAvoidLimitAreaMode>${wpmlObject.missionConfig.waylineAvoidLimitAreaMode}</wpml:waylineAvoidLimitAreaMode>
                <wpml:payloadInfo>
                    <wpml:payloadEnumValue>${wpmlObject.missionConfig.payloadInfo.payloadEnumValue}</wpml:payloadEnumValue>
                    <wpml:payloadSubEnumValue>${wpmlObject.missionConfig.payloadInfo.payloadSubEnumValue}</wpml:payloadSubEnumValue>
                    <wpml:payloadPositionIndex>${wpmlObject.missionConfig.payloadInfo.payloadPositionIndex}</wpml:payloadPositionIndex>
                </wpml:payloadInfo>
            </wpml:missionConfig>
            <Folder>
                <wpml:templateId>${wpmlObject.Folder.templateId}</wpml:templateId>
                <wpml:executeHeightMode>${wpmlObject.Folder.executeHeightMode}</wpml:executeHeightMode>
                <wpml:waylineId>${wpmlObject.Folder.waylineId}</wpml:waylineId>
                <wpml:distance>${wpmlObject.Folder.distance}</wpml:distance>
                <wpml:duration>${wpmlObject.Folder.duration}</wpml:duration>
                <wpml:autoFlightSpeed>${wpmlObject.Folder.autoFlightSpeed}</wpml:autoFlightSpeed>${wpmlObject.Folder.startActionGroup ? `<wpml:startActionGroup>${wpmlObject.Folder.startActionGroup.actionList.map((ite, ind) => {
        return `
                    <wpml:action>
                        <wpml:actionId>${ite.actionId}</wpml:actionId>
                        <wpml:actionActuatorFunc>${ite.actionActuatorFunc}</wpml:actionActuatorFunc>
                        <wpml:actionActuatorFuncParam>${actionActuatorFuncParamFun(ite.actionActuatorFuncParam).map(([key, value]) => {
            return `
                            <wpml:${key}>${value}</wpml:${key}>`
        })}
                        </wpml:actionActuatorFuncParam>
                    </wpml:action>`})}
                </wpml:startActionGroup>`: ''}${wpmlObject.Folder.PlacemarkList.map((item, index) => {
            return `
                <Placemark>
                    <Point>
                        <coordinates>${item.Point.coordinates.longitude},${item.Point.coordinates.latitude}</coordinates>
                    </Point>
                    <wpml:index>${item.index}</wpml:index>
                    <wpml:executeHeight>${item.executeHeight}</wpml:executeHeight>
                    <wpml:waypointSpeed>${item.waypointSpeed}</wpml:waypointSpeed>
                    <wpml:waypointHeadingParam>
                        <wpml:waypointHeadingMode>${item.waypointHeadingParam.waypointHeadingMode}</wpml:waypointHeadingMode>
                        <wpml:waypointHeadingAngle>${item.waypointHeadingParam.waypointHeadingAngle}</wpml:waypointHeadingAngle>
                        <wpml:waypointPoiPoint>${item.waypointHeadingParam.waypointPoiPoint}</wpml:waypointPoiPoint>
                        <wpml:waypointHeadingAngleEnable>${item.waypointHeadingParam.waypointHeadingAngleEnable}</wpml:waypointHeadingAngleEnable>
                        <wpml:waypointHeadingPathMode>${item.waypointHeadingParam.waypointHeadingPathMode}</wpml:waypointHeadingPathMode>
                        <wpml:waypointHeadingPoiIndex>${item.waypointHeadingParam.waypointHeadingPoiIndex}</wpml:waypointHeadingPoiIndex>
                    </wpml:waypointHeadingParam>
                    <wpml:waypointTurnParam>
                        <wpml:waypointTurnMode>${item.waypointTurnParam.waypointTurnMode}</wpml:waypointTurnMode>
                        <wpml:waypointTurnDampingDist>${item.waypointTurnParam.waypointTurnDampingDist}</wpml:waypointTurnDampingDist>
                    </wpml:waypointTurnParam>
                    <wpml:useStraightLine>${item.useStraightLine}</wpml:useStraightLine>${item.actionGroup && actionGroupFun(item.actionGroup)}
                    <wpml:waypointGimbalHeadingParam>
                        <wpml:waypointGimbalPitchAngle>${item.waypointGimbalHeadingParam.waypointGimbalPitchAngle}</wpml:waypointGimbalPitchAngle>
                        <wpml:waypointGimbalYawAngle>${item.waypointGimbalHeadingParam.waypointGimbalYawAngle}</wpml:waypointGimbalYawAngle>
                    </wpml:waypointGimbalHeadingParam>
                    <wpml:isRisky>${item.isRisky}</wpml:isRisky>
                    <wpml:waypointWorkType>${item.waypointWorkType}</wpml:waypointWorkType>
                </Placemark>`})}
            </Folder>
        </Document>
    </kml>`
}
// 生成kml
function CreatKml(kmlObject) {
    return `<?xml version="1.0" encoding="UTF-8"?>
    <kml xmlns="http://www.opengis.net/kml/2.2" xmlns:wpml="http://www.dji.com/wpmz/1.0.6">
        <Document>
            <wpml:author>${kmlObject.author}</wpml:author>
            <wpml:createTime>${kmlObject.createTime}</wpml:createTime>
            <wpml:updateTime>${kmlObject.updateTime}</wpml:updateTime>
            <wpml:missionConfig>
                <wpml:flyToWaylineMode>${kmlObject.missionConfig.flyToWaylineMode}</wpml:flyToWaylineMode>
                <wpml:finishAction>${kmlObject.missionConfig.finishAction}</wpml:finishAction>
                <wpml:exitOnRCLost>${kmlObject.missionConfig.exitOnRCLost}</wpml:exitOnRCLost>
                <wpml:executeRCLostAction>${kmlObject.missionConfig.executeRCLostAction}</wpml:executeRCLostAction>
                <wpml:takeOffRefPoint>${kmlObject.missionConfig.takeOffRefPoint.longitude},${kmlObject.missionConfig.takeOffRefPoint.latitude},${kmlObject.missionConfig.takeOffRefPoint.height}</wpml:takeOffRefPoint>
                <wpml:takeOffRefPointAGLHeight>${kmlObject.missionConfig.takeOffRefPointAGLHeight}</wpml:takeOffRefPointAGLHeight>
                <wpml:globalTransitionalSpeed>${kmlObject.missionConfig.globalTransitionalSpeed}</wpml:globalTransitionalSpeed>
                <wpml:globalRTHHeight>${kmlObject.missionConfig.globalRTHHeight}</wpml:globalRTHHeight>
                <wpml:droneInfo>
                    <wpml:droneEnumValue>${kmlObject.missionConfig.droneInfo.droneEnumValue}</wpml:droneEnumValue>
                    <wpml:droneSubEnumValue>${kmlObject.missionConfig.droneInfo.droneSubEnumValue}</wpml:droneSubEnumValue>
                </wpml:droneInfo>
                <wpml:waylineAvoidLimitAreaMode>${kmlObject.missionConfig.waylineAvoidLimitAreaMode}</wpml:waylineAvoidLimitAreaMode>
                <wpml:payloadInfo>
                    <wpml:payloadEnumValue>${kmlObject.missionConfig.payloadInfo.payloadEnumValue}</wpml:payloadEnumValue>
                    <wpml:payloadSubEnumValue>${kmlObject.missionConfig.payloadInfo.payloadSubEnumValue}</wpml:payloadSubEnumValue>
                    <wpml:payloadPositionIndex>${kmlObject.missionConfig.payloadInfo.payloadPositionIndex}</wpml:payloadPositionIndex>
                </wpml:payloadInfo>
            </wpml:missionConfig>
            <Folder>
                <wpml:templateType>${kmlObject.Folder.templateType}</wpml:templateType>
                <wpml:templateId>${kmlObject.Folder.templateId}</wpml:templateId>
                <wpml:waylineCoordinateSysParam>
                    <wpml:coordinateMode>${kmlObject.Folder.waylineCoordinateSysParam.coordinateMode}</wpml:coordinateMode>
                    <wpml:heightMode>${kmlObject.Folder.waylineCoordinateSysParam.heightMode}</wpml:heightMode>
                    <wpml:globalShootHeight>${kmlObject.Folder.waylineCoordinateSysParam.globalShootHeight}</wpml:globalShootHeight>
                </wpml:waylineCoordinateSysParam>
                <wpml:autoFlightSpeed>${kmlObject.Folder.autoFlightSpeed}</wpml:autoFlightSpeed>
                <Placemark>
                    <wpml:shootTopFaceEnable>${kmlObject.Folder.Placemark.shootTopFaceEnable}</wpml:shootTopFaceEnable>
                    <wpml:shootType>${kmlObject.Folder.Placemark.shootType}</wpml:shootType>
                    <wpml:trajectoryType>${kmlObject.Folder.Placemark.trajectoryType}</wpml:trajectoryType>
                    <wpml:startPositionType>${kmlObject.Folder.Placemark.startPositionType}</wpml:startPositionType>
                    <wpml:overlap>
                        <wpml:orthoCameraOverlapH>${kmlObject.Folder.Placemark.overlap.orthoCameraOverlapH}</wpml:orthoCameraOverlapH>
                        <wpml:orthoCameraOverlapW>${kmlObject.Folder.Placemark.overlap.orthoCameraOverlapW}</wpml:orthoCameraOverlapW>
                    </wpml:overlap>
                    <wpml:Polygon>
                        <outerBoundaryIs>
                            <LinearRing>
                                <coordinates>
                                    ${creatPolygonCoordinates(kmlObject.Folder.Placemark.Polygon.outerBoundaryIs.LinearRing.coordinates)}
                                </coordinates>
                            </LinearRing>
                        </outerBoundaryIs>
                    </wpml:Polygon>
                    <wpml:height>${kmlObject.Folder.Placemark.height}</wpml:height>
                    <wpml:topFaceFlightSpeed>${kmlObject.Folder.Placemark.topFaceFlightSpeed}</wpml:topFaceFlightSpeed>
                    <wpml:scanExtent>${kmlObject.Folder.Placemark.scanExtent}</wpml:scanExtent>
                    <wpml:scanOffset>${kmlObject.Folder.Placemark.scanOffset}</wpml:scanOffset>
                </Placemark>
                <wpml:payloadParam>
                    <wpml:payloadPositionIndex>${kmlObject.Folder.payloadParam.payloadPositionIndex}</wpml:payloadPositionIndex>
                    <wpml:focusMode>${kmlObject.Folder.payloadParam.focusMode}</wpml:focusMode>
                    <wpml:meteringMode>${kmlObject.Folder.payloadParam.meteringMode}</wpml:meteringMode>
                    <wpml:returnMode>${kmlObject.Folder.payloadParam.returnMode}</wpml:returnMode>
                    <wpml:samplingRate>${kmlObject.Folder.payloadParam.samplingRate}</wpml:samplingRate>
                    <wpml:scanningMode>${kmlObject.Folder.payloadParam.scanningMode}</wpml:scanningMode>
                    <wpml:imageFormat>${kmlObject.Folder.payloadParam.imageFormat.toString()}</wpml:imageFormat>
                </wpml:payloadParam>
            </Folder>
        </Document>
    </kml>`
}
function actionGroupFun(actionGroup) {
    return actionGroup ? `
                    <wpml:actionGroup>
                        <wpml:actionGroupId>${actionGroup.actionGroupId}</wpml:actionGroupId>
                        <wpml:actionGroupStartIndex>${actionGroup.actionGroupStartIndex}</wpml:actionGroupStartIndex>
                        <wpml:actionGroupEndIndex>${actionGroup.actionGroupEndIndex}</wpml:actionGroupEndIndex>
                        <wpml:actionGroupMode>${actionGroup.actionGroupMode}</wpml:actionGroupMode>
                        <wpml:actionTrigger>
                            <wpml:actionTriggerType>${actionGroup.actionTrigger.actionTriggerType}</wpml:actionTriggerType>
                        </wpml:actionTrigger>${actionGroup.actionList.map((ite, ind) => {
        return `
                        <wpml:action>
                            <wpml:actionId>${ite.actionId}</wpml:actionId>
                            <wpml:actionActuatorFunc>${ite.actionActuatorFunc}</wpml:actionActuatorFunc>
                            <wpml:actionActuatorFuncParam>${actionActuatorFuncParamFun(ite.actionActuatorFuncParam).map(([key, value]) => {
            return `
                                <wpml:${key}>${value.constructor === Array ? value.toString() : value}</wpml:${key}>`
        })}
                            </wpml:actionActuatorFuncParam>
                        </wpml:action>`})}
                    </wpml:actionGroup>`: ''
}
function actionActuatorFuncParamFun(actionActuatorFuncParam) {
    return Object.entries(actionActuatorFuncParam)
}
function creatPolygonCoordinates(coordinates) {
    let string=''
    coordinates.forEach((item, index) => {
        string = string + `${item[0]}` + `${item[1]}`+`${ item[2]}\n`
    })
    return string
}