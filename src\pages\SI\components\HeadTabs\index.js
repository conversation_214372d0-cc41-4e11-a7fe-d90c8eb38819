import React, { useState, useEffect, useRef } from 'react';
import { message, Tabs } from 'antd';
import { useModel } from 'umi';
import { getGuid, getRouteLevel } from '@/utils/helper';
import { queryPage, queryPageByPath } from '@/utils/MyRoute';
import '@/pages/SI/style/antd-common.less';
import eventBus from '@/utils/EventBus'; // 导入事件总线

const MyTabs = ({ handletabChange, menuItems }) => {
  // 获取默认页面信息
  const { label, key } = menuItems[0];
  const defaultItems = [
    {
      label: label,
      key: key,
      closable: false,
    },
  ];
  const [activeKey, setActiveKey] = useState();
  const [items, setItems] = useState(defaultItems);

  const initTabs = () => {
    setItems(defaultItems);
  };

  // 初始化清空标签
  useEffect(() => {
    initTabs();
  }, []);

  // 添加监听来自MyMenu的事件
  useEffect(() => {
    // 监听"add-tab"事件，由MyMenu触发
    const unsubscribe = eventBus.on('add-tab', tabInfo => {
      addTab(tabInfo);
    });

    // 清理函数
    return () => {
      unsubscribe(); // 取消订阅
    };
  }, []);

  useEffect(() => {
    if (activeKey) {
      // 只通知菜单更新，不调用handletabChange (已在onChange中调用)
      eventBus.emit('tab-active-changed', activeKey);
    }
  }, [activeKey]);

  // 新的addTab函数，由事件总线触发
  const addTab = menuInfoKey => {
    if (!menuInfoKey) return;

    // 这儿的menuItems可能有子菜单，需要递归查找
    const findMenuItemRecursively = (itemsToSearch, keyToFind) => {
      for (const currentItem of itemsToSearch) {
        if (currentItem.key === keyToFind) {
          return currentItem;
        }
        if (currentItem.children && currentItem.children.length > 0) {
          const foundInChildren = findMenuItemRecursively(currentItem.children, keyToFind);
          if (foundInChildren) {
            return foundInChildren;
          }
        }
      }
      return null;
    };

    const item = findMenuItemRecursively(menuItems, menuInfoKey);

    if (!item) {
      console.warn('[addTab] 未找到对应的item:', menuInfoKey);
      return;
    }

    const { label, key } = item;

    // 使用函数式更新，在更新函数内部完成所有逻辑
    setItems(prevItems => {
      // 在这里检查是否重复，使用最新的prevItems
      const isDuplicate = prevItems.some(item => String(item.key) === String(key) || item.label === label);

      if (isDuplicate) {
        // 不更新items，只返回原来的
        return prevItems;
      }

      // 不重复时添加新tab
      return [
        ...prevItems,
        {
          label: label,
          key: key,
        },
      ];
    });

    // 无论如何都设置activeKey
    setActiveKey(key);
  };

  const onChange = newActiveKey => {
    setActiveKey(newActiveKey);

    // 直接在这里调用handletabChange
    if (handletabChange && typeof handletabChange === 'function') {
      handletabChange(newActiveKey);
    } else {
      console.warn('[HeadTabs] handletabChange 不是函数或未定义');
    }
  };

  const remove = targetKey => {
    const newItems = items.filter(item => item.key !== targetKey);
    let newActiveKey = activeKey;

    if (newItems.length && newActiveKey === targetKey) {
      const lastIndex = items.findIndex(item => item.key === targetKey) - 1;
      newActiveKey = lastIndex >= 0 ? newItems[lastIndex].key : newItems[0].key;
    } else if (newItems.length === 0) {
      // 没有tab时，清空activeKey
      newActiveKey = undefined;
    }

    setItems(newItems);
    setActiveKey(newActiveKey);
    // 直接在这里调用handletabChange
    if (handletabChange && typeof handletabChange === 'function') {
      handletabChange(newActiveKey);
    } else {
      console.warn('[HeadTabs] handletabChange 不是函数或未定义');
    }
  };

  const onEdit = (targetKey, action) => {
    if (action === 'add') {
      // 不使用内置的add按钮，应由MyMenu触发
      console.log('[onEdit] add操作应由MyMenu触发');
    } else {
      remove(targetKey);
    }
  };

  return (
    <div>
      <Tabs hideAdd onChange={onChange} activeKey={activeKey} type="editable-card" size="small" onEdit={onEdit} items={items} tabBarGutter={10} />
    </div>
  );
};

export default MyTabs;
