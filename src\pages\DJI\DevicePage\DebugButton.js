import { HGet2 } from "@/utils/request";
import { Dropdown, <PERSON><PERSON>, message } from "antd";
import { DownOutlined, SmileOutlined } from "@ant-design/icons";
import { useModel } from "umi";
import { CheckIfCanFly } from "@/pages/DJI/FlyToPage/helper";
import { checkIfFlyer } from "@/utils/utils";
import  "@/pages/common.less";
//import WaylinePanel from "../WayLine/WaylinePanel";

const TakeOff = async (sn) => {
  const pb1 = await CheckIfCanFly(sn);
  if (!pb1) return;
  HGet2("/api/v1/FlyTo/TakeOff?sn=" + sn);
};

const items = (sn, onOpen, doCMD, doCMD2) => {
  return [
    {
      key: "2",
      label: (
        <a
          onClick={() => {
            if (checkIfFlyer()) {
              TakeOff(sn);
            }
          }}
        >
          一键起飞
        </a>
      ),
    },

    {
      key: "4",
      label: (
        <a
          onClick={() => {
            if (checkIfFlyer()) {
              HGet2("/api/v1/WayLine/ToHome?sn=" + sn);
            }
          }}
        >
          一键返航
        </a>
      ),
    },

    {
      key: "1",
      label: <a>调试</a>,
      children: [
        {
          key: "1-1",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) {
                  doCMD("debug_mode_open");
                }
              }}
            >
              开启调试模式
            </a>
          ),
        },
        {
          key: "1-2",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("debug_mode_close");
              }}
            >
              关闭调试模式
            </a>
          ),
        },
        {
          key: "1-3",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("device_reboot");
              }}
            >
              机场重启
            </a>
          ),
        },

        {
          key: "1-4",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("drone_open");
              }}
            >
              飞行器开机
            </a>
          ),
        },
        {
          key: "1-5",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("drone_close");
              }}
            >
              飞行器关机
            </a>
          ),
        },
        {
          key: "1-6",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("cover_open");
              }}
            >
              打开舱盖
            </a>
          ),
        },

        {
          key: "1-7",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("cover_close");
              }}
            >
              关闭舱盖
            </a>
          ),
        },

        {
          key: "1-8",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("device_format");
              }}
            >
              机场数据格式化
            </a>
          ),
        },
        {
          key: "1-9",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("charge_open");
              }}
            >
              打开充电
            </a>
          ),
        },

        {
          key: "1-10",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("charge_close");
              }}
            >
              关闭充电
            </a>
          ),
        },
        {
          key: "1-11",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("supplement_light_open");
              }}
            >
              打开补光灯
            </a>
          ),
        },
        {
          key: "1-12",
          label: (
            <a
              onClick={() => {
                if (checkIfFlyer()) doCMD("supplement_light_close");
              }}
            >
              关闭补光灯
            </a>
          ),
        },
        {
          key: "1-13",
          label: (
            <a
              onClick={() => {
                message.success("请保证飞行器处于开机状态");
                let data = {
                  link_workmode: 1,
                };
                doCMD2("sdr_workmode_switch", data);
              }}
            >
              打开4G图传增强
            </a>
          ),
        },
        {
          key: "1-14",
          label: (
            <a
              onClick={() => {
                message.success("请保证飞行器处于开机状态");
                let data = {
                  link_workmode: 0,
                };
                doCMD2("sdr_workmode_switch", data);
              }}
            >
              关闭4G图传增强
            </a>
          ),
        },
        {
          key: "1-15",
          label: (
            <a
              onClick={() => {
                let data = {
                  action: 0,
                };
                doCMD2("air_conditioner_mode_switch", data);
              }}
            >
              调整为空闲模式
            </a>
          ),
        },
        {
          key: "1-16",
          label: (
            <a
              onClick={() => {
                let data = {
                  action: 1,
                };
                doCMD2("air_conditioner_mode_switch", data);
              }}
            >
              调整为制冷模式
            </a>
          ),
        },
        {
          key: "1-17",
          label: (
            <a
              onClick={() => {
                let data = {
                  action: 2,
                };
                doCMD2("air_conditioner_mode_switch", data);
              }}
            >
              调整为制热模式
            </a>
          ),
        },
        {
          key: "1-18",
          label: (
            <a
              onClick={() => {
                let data = {
                  action: 1,
                };
                doCMD2("battery_store_mode_switch", data);
              }}
            >
              电池:计划模式(60%)
            </a>
          ),
        },
        {
          key: "1-19",
          label: (
            <a
              onClick={() => {
                let data = {
                  action: 2,
                };
                doCMD2("battery_store_mode_switch", data);
              }}
            >
              电池:待命模式(90%)
            </a>
          ),
        },
      ],
    },
  ];
};

const DeBugButton = (props) => {
  const { setModal, setOpen } = useModel("pageModel");
  const { DoCMD } = useModel("cmdModel");

  const { sn } = props;
  const doCMD = (cmd) => {
    DoCMD(sn, cmd, null);
  };
  const doCMD2 = (cmd, data) => {
    DoCMD(sn, cmd, data);
  };
  return (
    <Dropdown
      menu={{
        items: items(
          sn,
          () => {
            // setModal(<WaylinePanel />); setOpen(true)
          },
          doCMD,
          doCMD2
        ),
      }}
      placement="bottomRight"
      arrow
    >
      <Button type="text">
        {" "}
        <DownOutlined style={{ color: "white" }} />
      </Button>
    </Dropdown>
  );
};

export default DeBugButton;
