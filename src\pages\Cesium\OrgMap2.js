
import React, { useEffect, useRef } from "react";
import { Cesium, useModel } from "umi";
import {
  Camera,
} from "cesium";
import "./index.css";
import { isEmpty } from "@/utils/utils";
import FeiJi from "@/assets/Cesium_Air.glb";
import { Col, Row } from "antd";

const DDD = (viewer) => {
  if (isEmpty(viewer)) return;
  const xxx = async () => {
    try {
      const tileset = await Cesium.Cesium3DTileset.fromUrl(
        "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/667edb33915b45218945367bbef08b2e/tileset.json",
        //   "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/bd6c5b018dd046b59bcccf42e7ce64d5/tileset.json"

        // "https://cce95e19-1f9d-42e1-b6c3-7e0825651a20.oss-cn-chengdu.aliyuncs.com/3d/tileset.json"
        //"http://192.168.0.132:1281/osgb2/tileset.json", //数据地址
        // "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/ShuYuanDaDao/shuyuandadao/tileset.json"
        {
          dynamicScreenSpaceError: true,
          dynamicScreenSpaceErrorDensity: 2.0e-4,
          dynamicScreenSpaceErrorFactor: 24.0,
          dynamicScreenSpaceErrorHeightFalloff: 0.1,
          maximumScreenSpaceError: 1.0,
        }
      );
      viewer.scene.primitives.add(tileset);
      viewer.flyTo(tileset);
    } catch (error) {
      console.error(`Error creating tileset: ${error}`);
    }
  };
  xxx();
};

const gbPList = [
  [32.15164983, 105.5380648, 806.843, 60, "庞统柏"],
  [32.15184261, 105.5383173, 796.547, 80, "羽扇柏"],
  [32.15231467, 105.5387422, 802.727, 90, "汉砖柏"],
  [32.15243525, 105.5388007, 800.925, 110, "帅大柏"],
  [32.15267175, 105.538787, 799.249, 130, "阿斗柏"],
  [32.1530735, 105.5390088, 801.565, 140, "三国鼎立柏"],
  [32.15312128, 105.5390473, 800.778, 180, "剑阁柏"],
  [32.154077, 105.5393033, 801.117, 140, "隆中对柏"],
  [32.15439503, 105.5395044, 799.103, 130, "夫妻柏"],
  [32.15460806, 105.5395194, 798.908, 120, "结义柏"],
  [32.15494061, 105.5396271, 801.673, 100, "张飞柏"],
  [32.15510275, 105.5396425, 800.562, 80, "宋柏"],
  [32.15537361, 105.5396747, 800.613, 60, "张飞植柏"],
];

const Map3D = ({ h1 }) => {
  // const ref = React.createRef();
  const device = JSON.parse(localStorage.getItem("device"));
  const { pList } = useModel("gpsModel");
  let viewer;
  let FJEntity;
  const pX = Cesium.Cartesian3.fromDegrees(
    device.Lng,
    device.Lat,
    device.Height
  );
  let pL = [];
  const canvasRef = useRef(null);
  //pL.push(pX)

  // Camera.DEFAULT_VIEW_RECTANGLE = Rectangle.fromDegrees(103.55, 31.02, 103.6, 31.05)
  Camera.DEFAULT_VIEW_FACTOR = 0.0001;

  const getJD = (p1, p2) => {
    //
    console.log(p1);
    const x1 = p2.x;
    const y1 = p2.y;
    const x2 = p1.x;
    const y2 = p1.y;
    const jd = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;
    // console.log('getJD',jd);
    return jd;
  };

  const getJD2 = () => {
    //  console.log(p1);
    if (pList.current.length < 3) return 0;
    const p1 = getPoint(pList.current[pList.current.length - 1]);
    const p2 = getPoint(pList.current[pList.current.length - 2]);
    return getJD(p1, p2);
  };

  const getPoint = (p) => {
    return Cesium.Cartesian3.fromDegrees(p[1], p[0], p[2]);
  };

  const getGps2 = () => {
    if (pList.current.length == 0)
      return Cesium.Cartesian3.fromDegrees(
        device.Lng,
        device.Lat,
        device.Height
      );
    return getPoint(pList.current[pList.current.length - 1]);
  };

  const getPList = () => {
    //  console.log('getPList',pList.current);
    if (isEmpty(pList.current))
      return [
        Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, device.Height),
      ];
    const list = [];
    pList.current.forEach((e) => {
      list.push(getPoint(e));
    });
    pL = list;
    return list;
  };

  const FJIcon = (viewer) => {
    if (isEmpty(viewer)) return;

    FJEntity = viewer.entities.add({
      name: "gltf",
      position: new Cesium.CallbackProperty(() => {
        const p1 = getGps2();
        return p1;
      }, false),

      orientation: new Cesium.CallbackProperty(() => {
        const jd = getJD2();
        const p1 = getGps2();
        return Cesium.Transforms.headingPitchRollQuaternion(
          p1,
          new Cesium.HeadingPitchRoll(
            Cesium.Math.toRadians(200 - jd),
            Cesium.Math.toRadians(0),
            Cesium.Math.toRadians(0)
          )
        );
      }, false),

      model: {
        uri: FeiJi, // 模型路径
        // show:true,
        scale: 0.5,
        // color:new  Cesium.Color(255, 255, 255, 0.6),
      },
    });
    // viewer.flyTo(FJEntity);
    //viewer.trackedEntity = FJEntity;
  };

  const poiIconLabelAdd = (viewer, lon, lat, height, h2, name, color, url) => {
    viewer.entities.add({
      name: name,
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height + h2),
      // 图标
      billboard: {
        image: url,
        width: 45,
        height: 45,
      },
      label: {
        //文字标签
        text: name,
        font: "20px sans-serif",
        style: Cesium.LabelStyle.FILL,
        // 对齐方式(水平和竖直)
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(30, -2),
        showBackground: true,
        backgroundColor: new Cesium.Color.fromBytes(0, 70, 24),
      },
    });

    // 先画线后画点，防止线压盖点
    let linePositions = [];
    linePositions.push(new Cesium.Cartesian3.fromDegrees(lon, lat, height));
    linePositions.push(
      new Cesium.Cartesian3.fromDegrees(lon, lat, height + h2)
    );
    viewer.entities.add({
      polyline: {
        positions: linePositions,
        width: 1.5,
        material: new Cesium.PolylineDashMaterialProperty({
          //虚线
          color: Cesium.Color.GREEN,
          dashLength: 20, //短划线长度pixel
          gapColor: Cesium.Color.TRANSPARENT, //空隙颜色
        }),
      },
    });

    // 画点
    const xx = viewer.entities.add({
      // 给初始点位设置一定的离地高度，否者会被压盖
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height),
      point: {
        color: color,
        pixelSize: 15,
      },
    });

    viewer.flyTo(xx);
  };

  const BiaoZhu = (viewer) => {
    if (isEmpty(viewer)) return;
    gbPList.forEach((e) => {
      poiIconLabelAdd(
        viewer,
        e[1],
        e[0],
        e[2],
        e[3],
        e[4],
        "green",
        require("@/assets/mapImgs/tree.png")
      );
    });
  };

  const FJLine = (viewer) => {
    if (isEmpty(viewer)) return;

    viewer.entities.add({
      // 实体的唯一标识
      id: "myLine",
      // 线条属性
      polyline: {
        // 定义线条的位置
        positions: new Cesium.CallbackProperty(() => {
          const pp = getPList();
          return pp;
        }, false),
        // 线条的宽度
        width: 2,
        // 线条的颜色
        material: Cesium.Color.RED,
      },
    });
  };

  useEffect(() => {
    const layerOption = {
      show: true, // 图像层是否可见
      alpha: 0.2, // 透明度
      nightAlpha: 1, // 地球夜晚一侧的透明度
      dayAlpha: 1, // 地球白天一侧的透明度
      brightness: 1, // 亮度
      contrast: 1, // 对比度
      hue: 0, // 色调
      saturation: 1, // 饱和度
      gamma: 0.5, // 伽马校正
    };

    const viewer2 = new Cesium.Viewer("cesiumContainer", {
      terrain: Cesium.Terrain.fromWorldTerrain(),
      infoBox: false,
      //imageryProvider:null,
      baseLayerPicker: false,
      sceneModePicker: false,
      homeButton: true,
      fullscreenButton: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      animation: false,
      geocoder: false,
      shouldAnimate: true,
      sceneMode: 3,
    });
    viewer = viewer2;
    // viewer.imageryLayers.get(0).show = false;
    // const layer = new Cesium.ImageryLayer(
    //   new Cesium.UrlTemplateImageryProvider({ url: 'https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', }),
    //   {...layerOption}
    // )
    // viewer.imageryLayers.add(layer)
    //return layer

    const scene = new Cesium.Scene({
      canvas: canvasRef.current,
      contextOptions: {
        allowTextureFilterAnisotropic: false,
        requestRenderMode: true,
      },
    });

    DDD(viewer);
    //  DDD(viewer);

    //  StartEvent(viewer2);
    FJIcon(viewer2);
    FJLine(viewer2);
    BiaoZhu(viewer2);
  }, []);

  return (
    <div>
      <Row>
        <Col span={12}>
          <div style={{ height: h1, width: "100%" }} id="cesiumContainer"></div>
        </Col>
        <Col span={12}>
          {/* <div style={{ height: h1, width: '100%' }} id="cesiumContainer2"></div> */}
          <canvas style={{ height: h1, width: "100%" }} ref={canvasRef} />
        </Col>
      </Row>
    </div>
  );
};

export default Map3D;
