import React, { useRef, useEffect, useState } from "react";
import Srs from "./srs.sdk";
import { getRtmpWebrtcUrl } from "@/utils/config";
import { useModel } from "umi";
import CanvasHelper from './canvansHelper2'
import { HPost2 } from "@/utils/request";
// import HongXianPage from '@/pages/DJI/HongXianPage/index.js'
// import { getPoint } from '@/pages/DJI/HongXianPage/no_pic.js'

const setfjPositiondata = (fj,WanLineId) => {
  let fjPositionData = {};
  let fjCamara = ''
  if(!fj || Object.keys(fj).length === 0 || !fj.data){
    return "未获取到飞行姿态";
  }else{
    fjCamara = fj.data.cameras[0].payload_index,
    // console.log("fjCamara:",fjCamara);
    fjPositionData = {
      WanLineId:WanLineId,
      height:fj.data.height,
      latitude:fj.data.latitude,
      longitude:fj.data.longitude,
      timestamp:fj.timestamp,
      gimbal_pitch:fj.data[fjCamara].gimbal_pitch,
      gimbal_roll:fj.data[fjCamara].gimbal_roll,
      gimbal_yaw:fj.data[fjCamara].gimbal_yaw,
    }
  }
  return fjPositionData;
}

const VideoViewer2 = ({ sn3, ctl, height, width, isDrc }) => {
  const RtmpUrl = getRtmpWebrtcUrl();
  const url = RtmpUrl + sn3;
  const guid = "fj_player";
  const canvasRef = useRef(null);
  const { fj } = useModel("droneModel");
  const { drc } = useModel("drcModel");
  //是否加载画布
  const { showCanvas,setShowCanvas } = useModel("pageModel");
  let { WanLineId } = useModel("pageModel");
  if(WanLineId === ""){
    WanLineId = localStorage.getItem("WanLineId");
  }
  // console.log("WanLineId:",WanLineId);

  // 渲染数据
  const [pointListData, setPointListData] = useState(null);
  var timer = null;

  // 点击清除画布 在下一次fj返回的时候 重新渲染画布
  const handleClearCanvas = () => {
    const canvas = canvasRef.current;
    // console.log("清除画布");
    if (canvas) {
      const ctx = canvas.getContext('2d');
      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // 关掉预测渲染的定时器
      clearInterval(timer);
    }
  };

  // 加载播放器
  useEffect(() => {
    const player = document.getElementById(guid);
    const rtcPlayer = new Srs.SrsRtcPlayerAsync();
    rtcPlayer.play(url);
    player.srcObject = rtcPlayer.stream;

    // console.log("@@@device: ",localStorage.getItem("device"))
    return () => rtcPlayer.close();
  }, [url]);

  // 动态同步尺寸
  useEffect(() => {
    const video = document.getElementById(guid);
    const canvas = canvasRef.current;
    if (!video || !canvas) return;

    const updateSize = () => {
      canvas.width = video.clientWidth;
      canvas.height = video.clientHeight;
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [guid, height, width,showCanvas]);

  // 通过飞行姿态数据获取渲染信息
  useEffect(() => {
    if (!showCanvas || !fj) {
      // console.log("未获取到飞行姿态或未显示画布");
      return;
    };

    // 飞机高度低于50米,处于返航模式mode_code == 9,有垂直速度 不绘制 
    if(fj?.data?.elevation && fj?.data?.mode_code && fj?.data?.vertical_speed){
      if(fj.data.elevation < 50 || fj.data.mode_code == 9 || fj.data.vertical_speed > 1){
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布
        if(fj.data.mode_code == 9){
          // console.log("返航模式 关闭画布渲染");
          setShowCanvas(false); // 关闭画布
        }
        return;
      }
    }

    
    const fetchData = async () => {
      try {
        const positionData = setfjPositiondata(fj, WanLineId);
        const response = await HPost2("/ai/WayLine/GetArea", positionData);
        setPointListData(response); // 存储到state
      } catch (error) {
        console.error("获取区域数据失败:", error);
      }
    };
    fetchData();
    // getData();
  }, [showCanvas, WanLineId, fj, drc]); // 飞行姿态变化时 航线信息变化时 重新获取渲染数据



  // 绘制逻辑
  useEffect(() => {
    // 如果画布未打开 或者渲染数据是空的 则不绘制
    if (!showCanvas || !pointListData) {
      return;
    };
    const fjCamara = fj.data.cameras[0].payload_index;
    // console.log("云台偏航角@@:",fj.data[fjCamara].gimbal_yaw);
    // console.log("飞机偏航角@@",fj.data.attitude_head)

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // 调用绘制方法 并且拿到定时器的引用
      timer =  CanvasHelper.draw(pointListData,canvas,fj);
    };
    const animationFrame = requestAnimationFrame(draw);
    return () => cancelAnimationFrame(animationFrame);
  }, [pointListData, showCanvas]); // 当渲染数据变化时重绘

  return (
    <div style={{ 
      background: "black", 
      position: 'relative',
      width: width || "100%",
      height: height 
    }}>
      <video
        id={guid}
        autoPlay
        style={{ 
          objectFit: "cover",
          width: "100%",
          height: "100%",
          position: 'absolute',
          left: 0,
          top: 0
        }}
      ></video>
      {/* <HongXianPage/> */}
      {showCanvas && (
        <canvas
          ref={canvasRef}
          onMouseUp={handleClearCanvas} // 鼠标抬起时 暂时清除画布和定时器
          style={{ 
            position: 'absolute',
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "auto"
          }}
        /> 
      )}
    </div>
  );
};

export default VideoViewer2;  