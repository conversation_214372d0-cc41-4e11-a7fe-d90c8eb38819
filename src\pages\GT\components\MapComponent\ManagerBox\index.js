import { useEffect, useRef,useState } from "react";
import styles from "./ManagerBox.less";
import {
  CloseCircleOutlined,
  PictureOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import TreeCompent from "./Panels/TreeCompent"

export default function ManagerBox({ isOpen, onClose,updateZSMapUrl,isToggleRight }) {
  if (!isOpen) return null;
  let ManagerBox_right_list = [
    "地图标注",
    "基础图层",
    "线状数据",
    "管理数据",
    "无人机影像",
    "当前图层",
  ];
  let ManagerBox_left_list = ["土地资源", "耕地资源"];
  const [positionStyle, setPositionStyle] = useState({});
  const boxRef = useRef(null); // 用于存储盒子的引用
  const toggleFc = () => {
    if (isToggleRight) {
      setPositionStyle({ left: '-500px' });
    } else {
      setPositionStyle({ right: 0 });
    }
  }
  useEffect(() => {
    toggleFc();
  }, [isToggleRight]);  
  return (
    <>
      <div className={styles.ManagerBox}>
        <div className={styles.ManagerBox_flex}  ref={boxRef} style={positionStyle}>
          <div className={styles.ManagerBox_title}>
            <div>图层管理</div>
            <CloseCircleOutlined onClick={onClose} />
          </div>

          <div className={styles.ManagerBox_content}>
            <div className={styles.ManagerBox_left}>
                <TreeCompent updateZSMapUrl={updateZSMapUrl}></TreeCompent>
              {/* {ManagerBox_left_list.map((item) => {
                return (
                  <div key={item} className={styles.ManagerBox_left_item}>
                    <PlusCircleOutlined />
                    <div style={{marginLeft:'5px'}}>{item}</div>
                  </div>
                );
              })} */}
            </div>
            <div className={styles.ManagerBox_right}>
              {ManagerBox_right_list.map((item) => {
                return (
                  <div className={styles.ManagerBox_right_item} key={item}>
                    <PictureOutlined />
                    <div>{item}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
