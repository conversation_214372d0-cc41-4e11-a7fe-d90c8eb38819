// 坐标查询组件样式
.coordinateQueryContainer {
  position: relative;
  z-index: 1000;

  .coordinateQueryButton {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #333;

    &:hover {
      border-color: #40a9ff;
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    &.active {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      
      &:hover {
        border-color: #40a9ff;
        background: rgba(24, 144, 255, 0.15);
      }
    }

    :global(.anticon) {
      font-size: 16px;
    }
  }
}

.coordinateQueryPanel {
  width: 320px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border: 1px solid #f0f0f0;

  .section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .sectionTitle {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #333;

      :global(.anticon) {
        font-size: 16px;
        color: #1890ff;
      }
    }
  }

  .locationForm {
    :global(.ant-form-item) {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :global(.ant-form-item-label) {
      padding-bottom: 4px;

      > label {
        font-size: 13px;
        font-weight: 500;
        color: #666;
      }
    }

    :global(.ant-input) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  .locationButton {
    width: 100%;
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }

  .pickSection {
    .pickButton {
      width: 100%;
      height: 36px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      margin-bottom: 12px;
      color: #22AF93;

      &:global(.ant-btn-primary) {
        background: #52c41a;
        border-color: #52c41a;

        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
    }

    .pickedCoordinate {
      .coordinateDisplay {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: rgba(24, 144, 255, 0.05);
        border: 1px solid rgba(24, 144, 255, 0.2);
        border-radius: 6px;

        .coordinateText {
          font-size: 13px;
          font-weight: 500;
          color: #1890ff;
          font-family: 'Consolas', 'Monaco', monospace;
        }

        .copyButton {
          padding: 4px;
          height: auto;
          min-width: auto;
          border: none;
          box-shadow: none;

          &:hover {
            background: rgba(24, 144, 255, 0.1);
          }

          :global(.anticon) {
            font-size: 14px;
            color: #1890ff;
          }
        }
      }
    }
  }

  .clearButton {
    width: 100%;
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }
}

// 下拉菜单样式
:global(.coordinateQueryDropdown) {
  :global(.ant-dropdown) {
    padding: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .coordinateQueryContainer {
    .coordinateQueryButton {
      padding: 6px 10px;
      height: 32px;
      font-size: 13px;
    }
  }

  .coordinateQueryPanel {
    width: 280px;
    padding: 12px;

    .section {
      margin-bottom: 16px;

      .sectionTitle {
        font-size: 13px;
        margin-bottom: 10px;
      }
    }

    .locationButton,
    .pickButton,
    .clearButton {
      height: 32px;
      font-size: 13px;
    }
  }
}
