import { useEffect, useRef, useState } from "react";
import LastPagePanel from "@/components/LastPagePanel";
import OrgPage from "@/pages/DJI/OrgPage";

export default function Page() {
  const [page, setP] = useState(<div />); // 默认状态由OrgPage修改为空div
  const [modal, setModal] = useState(<div />);
  const [open, setOpen] = useState(false);
  const [pageTitle, setTitle] = useState();
  const [user, setUser] = useState({});
  const [ifAI, setIfAI] = useState(false);
  const [pNM1, setPNM1] = useState("飞行地图");
  const [pNM2, setPNM2] = useState("机场镜头");
  const [showCanvas, setShowCanvas] = useState(false); // 画布是否展示
  const [WanLineId, setWanLineId] = useState(""); // 航线ID
  const [listPage, setListPage] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nearWRJlist, setNearWRJlist] = useState([]);
  const [DZZHPoint, setDZZHPoint] = useState({});
  const pList = useRef([]);
  const [pageData, setPageData] = useState([]);
  const [currentPage, setCurrentPage] = useState("");
  const pData = useRef({
    ifAI: false,
    pNM1: "飞行地图",
    pNM2: "机场镜头",
    SN: "",
    LiveJT: "",
  });

  const setPage = (child) => {
    if (child && child.children) {
      pList.current.push(child);
      setP(child.children);
      // console.log('@@@child',child);
      setPageData(child);
    } else {
      pList.current.push(child);
      setP(child);
      setPageData(null); // 如果是DJ的跳转逻辑 需要将pageData置空 防止影响到其他页面
    }
  };

  const PDataInit = (sn) => {
    if (sn != pData.current.SN) {
      pData.current = {
        ifAI: false,
        pNM1: "飞行地图",
        pNM2: "机场镜头",
        SN: sn,
      };
    }
  };

  const setPage2 = (child2) => {
    const child = <LastPagePanel child={child2}></LastPagePanel>;
    setPage(child);
  };

  const lastPage = () => {
    const len = pList.current.length;
    if (len < 2) {
      // 没有上一页了，重置为空页面
      setP(<div />);
      return;
    }
    const child = pList.current[len - 2]; // 获取倒数第二个元素
    if (child === undefined) {
      setP(<div />); // 修改为空div
    } else {
      if (child && child.children) {
        pList.current.pop();
        setP(child.children);
      } else {
        pList.current.pop();
        setP(child);
      }
    }
  };

  return {
    page,
    setPage,
    setPage2,
    lastPage,
    pageData,
    setPageData,
    modal,
    setModal,
    open,
    setOpen,
    pageTitle,
    setTitle,
    user,
    setUser,
    ifAI,
    setIfAI,
    pNM1,
    setPNM1,
    pNM2,
    setPNM2,
    PDataInit,
    pData,
    showCanvas,
    setShowCanvas,
    WanLineId,
    setWanLineId,
    listPage,
    setListPage,
    isModalOpen,
    setIsModalOpen,
    nearWRJlist,
    setNearWRJlist,
    DZZHPoint,
    setDZZHPoint,
    currentPage,
    setCurrentPage,
  };
}
