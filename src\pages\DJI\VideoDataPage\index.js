import React, { Fragment, useState, useEffect } from 'react';

import { <PERSON>readcrumb, Card, Input, Select, Tag, DatePicker, Descriptions, Row, Col, Button, Form, message, Table, Modal } from 'antd';

import { getBodyH, getDeviceName, isEmpty } from '@/utils/utils';

import { Get2, Post, Post2, Post3 } from '@/services/general';


import WayLineAddForm from './form_add';
import TableCols from './table';


import { useModel } from 'umi';
import { HGet2, HPost2 } from '@/utils/request';
import { ArrowLeftOutlined } from '@ant-design/icons';
import LastPageButton from '@/components/LastPageButton';
import dayjs from 'dayjs';


const { RangePicker } = DatePicker;
const { Description } = Descriptions;
const FormItem = Form.Item;


const VideoDataListPage = (props) => {


  const [vList, setVList] = useState([]);
  const [sJC, setSjc] = useState('');
  const [xList, setXList] = useState([]);
  const [sDate, setSDate] = useState({});

  const { setPage, lastPage } = useModel('pageModel')

  const ifPush = (e) => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')
    if (!isEmpty(sDate)) {
      t1 = dayjs(sDate[0]);
      t2 = dayjs(sDate[1]);
    }

    if (!isEmpty(sDate)) {
      const t3 = dayjs(e.CreateTime);
      if (t1.isAfter(t3) || (t2.isBefore(t3))) {
        return false;
      }
    }

    if (!isEmpty(sJC)) {
      if (e.SN != sJC) {
        return false;
      }
    }
    return true;

  }

  useEffect(() => {
    const xx = () => {
      const xL = []
      // 
      vList.forEach(e => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
      setXList([...xL]);
    }
    xx();

  }, [sJC, sDate]);



  useEffect(() => {
    const getLineData = async () => {
      let pst = await Get2('/api/v1/VideoData/GetAllList', {});
      if(isEmpty(pst)) pst=[];
      setVList([...pst]);
      setXList([...pst]);
    };
    getLineData();
  }, []);


  const refrush = async () => {
    const pst = await HGet2('/api/v1/VideoData/GetAllList', {});
    setVList(pst);
  };

  const fhBtn = <Button icon={<ArrowLeftOutlined />} onClick={lastPage}>返回</Button>

  const showMap = (record) => {
    setPage(<Card title={fhBtn}>{WayLineMap(getBodyH(180), record)}</Card>)
  };


  const getWaySelect = (wayList, getLabel) => {

    const list = []
    wayList.forEach(e => {
      list.push(<Select.Option key={e} data={e}>{getLabel(e)}</Select.Option>)
    });
    // console.log('CronAddForm', list);

    return list;
  }



  const getExr = () => {

    const dList = []


    vList.forEach(e => {

      if (!dList.includes(e.SN)) {
        dList.push(e.SN);
      }

    });

    return <Row>

      <Col style={{ marginLeft: 6.0 }}>   <Select allowClear={true} style={{ width: 200 }} onClear={() => setSjc('')}
        placeholder={'选择机场'}
        onSelect={(e) => setSjc(e)}>
        {getWaySelect(dList, (e) => getDeviceName(e))}
      </Select></Col>

      <Col style={{ marginLeft: 6.0 }}> <RangePicker onChange={(e) => setSDate(e)} /></Col>
      <Col style={{ marginLeft: 24.0 }}><WayLineAddForm data={refrush} /></Col>
    </Row>
  }


  return (

    <div style={{ margin: 0, height: getBodyH(56), background: '#F5F5FF' }}>

      <Card title={<LastPageButton title='视频列表' />} bordered={false} extra={getExr()} >
        <div>
          {isEmpty(vList) ? <div /> : <Table pagination={{ pageSize: 8 }}
            bordered dataSource={xList} columns={TableCols(refrush, showMap, setPage)} size='small' />}
        </div>
      </Card>


    </div>
  )
};

export default VideoDataListPage;
