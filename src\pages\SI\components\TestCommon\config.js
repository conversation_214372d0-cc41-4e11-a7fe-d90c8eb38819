// 巡飞任务配置文件

// 巡飞任务表单的默认配置
export const TestCommonFormConfig = [
  {
    name: 'taskNumber',
    label: '任务编号',
    type: 'input',
    placeholder: '请输入任务编号'
  },
  {
    name: 'taskName',
    label: '任务名称',
    type: 'input',
    placeholder: '请输入任务名称'
  },
  {
    name: 'planNumber',
    label: '计划编号',
    type: 'input',
    placeholder: '请输入计划编号'
  },
  {
    name: 'routeName',
    label: '航线名称',
    type: 'select',
    placeholder: '请选择航线',
    options: [
      { value: 'LSXJJH2025004', label: 'LSXJJH2025004' },
      { value: 'LSXJJH2025005', label: 'LSXJJH2025005' },
      { value: 'LSXJJH2025006', label: 'LSXJJH2025006' }
    ]
  },
  {
    name: 'airport',
    label: '所属机场',
    type: 'select',
    placeholder: '请选择机场',
    options: [
      { value: '首都机场', label: '首都机场' },
      { value: '浦东机场', label: '浦东机场' },
      { value: '白云机场', label: '白云机场' }
    ]
  },
  {
    name: 'dateRange',
    label: '配置时间',
    type: 'dateRange',
    placeholder: ['开始时间', '结束时间']
  },
  {
    name: 'checkResult',
    label: '检查结果',
    type: 'select',
    placeholder: '请选择检查结果',
    options: [
      { value: '已完成', label: '已完成' },
      { value: '正常', label: '正常' },
      { value: '异常', label: '异常' },
      { value: '未检查', label: '未检查' }
    ]
  }
];

// 巡飞任务表格的默认配置
export const TestCommonTableConfig = {
  columns: [
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      key: 'taskNumber',
      width: 120,
      ellipsis: true
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
      ellipsis: true
    },
    {
      title: '计划编号',
      dataIndex: 'planNumber',
      key: 'planNumber',
      width: 120,
      ellipsis: true
    },
    {
      title: '航线名称',
      dataIndex: 'routeName',
      key: 'routeName',
      width: 120,
      ellipsis: true
    },
    {
      title: '所属机场',
      dataIndex: 'airport',
      key: 'airport',
      width: 120,
      ellipsis: true
    },
    {
      title: '配置时间',
      dataIndex: 'configTime',
      key: 'configTime',
      width: 180,
      ellipsis: true
    },
    {
      title: '飞行距离',
      dataIndex: 'flyDistance',
      key: 'flyDistance',
      width: 100,
      ellipsis: true
    },
    {
      title: '飞行时间',
      dataIndex: 'flyTime',
      key: 'flyTime',
      width: 100,
      ellipsis: true
    },
    {
      title: '检查结果',
      dataIndex: 'checkResult',
      key: 'checkResult',
      width: 100,
      ellipsis: true,
      customRender: 'statusTag' // 使用自定义渲染
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      customRender: 'actionButtons' // 使用自定义渲染
    }
  ],
  
  toolbarButtons: [
    {
      key: 'export',
      text: '批量导出',
      type: 'primary',
      needsSelection: true,
      selectionMessage: '请选择要导出的数据'
    }
  ],
  
  rowSelection: {
    type: 'checkbox'
  },
  
  pagination: {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true
  }
}; 