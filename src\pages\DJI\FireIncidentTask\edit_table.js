import { useEffect, useState } from "react";
import {
  Button,
  Radio,
  message,
  Modal,
  Descriptions,
  Input,
  InputNumber,
  Select
} from "antd";
import { axiosApi } from "@/services/general";


const EditTable = ({ mapList, record, refrush }) => {
  const [canSee, setCanSee] = useState(false);
  const [formData, setFormData] = useState(record);
  const [selectStatus, setSelectStatus] = useState(record.status);

  let statusList = [
    { value: 0, label: "不可用" },
    { value: 1, label: "初始化" },
    { value: 2, label: "已接收" },
    { value: 3, label: "执行中" },
    { value: 4, label: "已完成" },
    { value: 5, label: "已取消" },
  ];
  const handleChange = (key, value) => {
    setFormData((prevState) => ({ ...prevState, [key]: value }));
  };
  async function updateStatus(record, status) {
    console.log(record, status);
    
    // 更新状态
    if (!record.id || !status) return;
    let res = await axiosApi(`api/v1/ThirdPartyObject/UpdateStatus?id=${record.id}&status=${status}`, "POST");
    console.log(res, "UpdateStatus");
    if (res.code === 1) {
      refrush();
    } else {
      message.error(res.msg);
    }
  }
  const submit = async () => {
    updateStatus(record, selectStatus);
  };

  let inputStyle = {
    margin: 0,
    width: "100%",
  };
  return (
    <>
      <a onClick={() => setCanSee(true)}>编辑</a>
      <Modal
        title={null}
        onOk={submit}
        open={canSee}
        onCancel={() => setCanSee(false)}
        okText="提交"
        cancelText="取消"
        width={900}
      >
        <Descriptions title="编辑" bordered column={2}>
          <Descriptions.Item label="状态" span={24}>
            <Select
              allowClear
              style={{ width: 250 }}
              onClear={() => setSelectStatus(null)}
              placeholder={"选择状态"}
              onSelect={(e) => setSelectStatus(e)}
            >
              {statusList.map((item) => {
                return (
                  <Select.Option key={item.value} data={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
};

export default EditTable;
