import React, { useState, useRef, useEffect } from "react";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import geojsonData from "@/pages/GT/GTPages/HomePage/Maps/geojsonData";
import MapComponent from "@/pages/GT/components/MapComponent/MapComponent";
import useConfigStore from "@/stores/configStore";
import { createFeaturePopup } from "@/pages/GT/GTPages/HomePage/Maps/mypopup.js";
const LeafletMap = () => {
  let dt_bz2 =
    "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f"; // 路网
  let satellite =
    "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
  const position = [30.659835174171207, 104.0633797645569]; // 地图中心点坐标

  let mapRef = useRef();
  const [currentLayer, setCurrentLayer] = useState(null); // 使用状态来管理当前图层
  const [wmsLayer, setWmsLayer] = useState(null); // 用于管理WMS图层的状态
  const { MapUrl, ZSMapUrl, setMapSelf } = useConfigStore();
  const [tileLayers, setTileLayers] = useState([
    {
      name: "德昌光伏正射",
      url: "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/map2D/DeChangGuangFu/jg1/{z}/{x}/{y}.png",
      tms: true,
    },
  ]); // 用于管理底图图层的状态
  // 初始化地图
  let wmsUrl = "http://47.108.71.9:38081/geoserver/bdzl/wms";
  useEffect(() => {
    mapRef.current = L.map("mapDiv", {
      maxZoom: 18,
      minZoom: 3,
      zoomControl: false,
      attributionControl: false,
    });
    mapRef.current.setView(position, 9);

    updateMapUrl(satellite);
    updateWMSTileLayer(wmsUrl);
    L.tileLayer(dt_bz2, { maxZoom: 20 }).addTo(mapRef.current); //路网图层
    tileLayers.forEach(value => {
      L.tileLayer(value.url, { tms: value.tms, maxZoom: 20, minZoom: 1, opacity: 1.0, attribution: "" })
        .addTo(mapRef.current);
    });
    setMapSelf(mapRef.current);

    // 地图点击事件
    const handleMapClick = (latlng, map) => {
      // 获取点击位置的像素坐标
      const point = map.latLngToContainerPoint(latlng);
      const size = map.getSize();

      // 构造GetFeatureInfo请求的URL
      const wmsUrl = "http://47.108.71.9:38081/geoserver/bdzl/wms";
      const params = {
        service: "WMS",
        version: "1.1.0",
        request: "GetFeatureInfo",
        layers: "bdzl:dzzhd",
        query_layers: "bdzl:dzzhd",
        info_format: "application/json",
        feature_count: 10,
        x: Math.round(point.x),
        y: Math.round(point.y),
        width: size.x,
        height: size.y,
        srs: "EPSG:4326",
        bbox: map.getBounds().toBBoxString(),
      };

      // 发送GetFeatureInfo请求
      /* 这里有跨域问题 使用谷歌浏览器插件暂时解决 */
      fetch(`${wmsUrl}?${new URLSearchParams(params).toString()}`)
        .then((response) => response.json())
        .then((data) => {
          // console.log("GetFeatureInfo 返回的数据：", data);
          // 在这里处理返回的数据，显示在弹出窗口中
          if (data.features && data.features.length > 0) {
            const popupContent = createFeaturePopup(data);
            L.popup({
              // 弹出窗口的样式设置
              maxWidth: 800,
              minWidth: 500,
              autoPanPadding: [20, 20],
            })
              .setLatLng(latlng)
              .setContent(popupContent) // 调用自定义的弹出窗口函数
              .openOn(map);
          } else {
            L.popup()
              .setLatLng(latlng)
              .setContent("未找到相关数据")
              .openOn(map);
          }
        })
        .catch((error) => {
          console.error("GetFeatureInfo 请求失败：", error);
        });
    };
    // 暴露函数
    useConfigStore.setState({ handleMapClick });

    // 添加地图点击事件
    mapRef.current.on("click", function (e) {
      handleMapClick(e.latlng, mapRef.current);
    });

    // 清理函数 移除点击事件 避免内存泄漏
    return () => {
      if (mapRef.current) {
        mapRef.current.off("click"); // 移除点击事件
        mapRef.current.remove(); // 销毁地图
      }
    };
  }, []);
  const updateWMSTileLayer = (url) => {
    if (!mapRef.current) return;

    // 如果目前的 WMS 图层和新图层相同，则不需要再次添加
    if (wmsLayer) {
      mapRef.current.eachLayer((layer) => {
        if (layer.options && layer.options.layerType === "wms") {
          mapRef.current.removeLayer(layer);
        }
      });
    }

    // 创建新的 WMS 图层并添加到地图
    const layer = L.tileLayer
      .wms(url, {
        layers: "bdzl:dzzhd",
        version: "1.1.0",
        format: "image/png",
        transparent: true,
        opacity: 0.8,
        attribution: null,
      })
      .addTo(mapRef.current);

    setMapSelf(mapRef.current);
    // 更新状态以存储当前的 WMS 图层
    setWmsLayer(url);
  };
  const updateMapUrl = (url) => {
    if (!mapRef.current) return;
    if (currentLayer === url) {
      return;
    }
    if (currentLayer) {
      mapRef.current.eachLayer((layer) => {
        if (layer.options && layer.options.layerType === "tile") {
          mapRef.current.removeLayer(layer);
        }
      });
    }
    const tileLayer = L.tileLayer(url, {
      maxZoom: 20,
      layerType: "tile",
    }).addTo(mapRef.current);
    setCurrentLayer(url); // 更新当前图层状态
    updateWMSTileLayer(wmsUrl);
  };

  const updateZSMapUrl = (zs_map_url) => {
    const layers = [];
    // 移除旧图层
    mapRef.current.eachLayer((layer) => {
      if (layer.getAttribution && layer.getAttribution()) {
        mapRef.current.removeLayer(layer);
      }
    });
    // 添加新图层
    zs_map_url.forEach((url) => {
      const tileLayer = L.tileLayer(url, { maxZoom: 20 });
      // 检查 layers 数组中是否已存在该图层
      if (!layers.includes(tileLayer)) {
        tileLayer.addTo(mapRef.current);
        layers.push(tileLayer);
      }
    });
  };

  // 监听 MapUrl 和 ZSMapUrl 的变化
  useEffect(() => {
    if (MapUrl !== satellite) {
      dt_bz2 = null;
    }
    if (MapUrl) {
      updateMapUrl(MapUrl);
    }
  }, [MapUrl]);

  useEffect(() => {
    if (ZSMapUrl) {
      updateZSMapUrl(ZSMapUrl);
    }
  }, [ZSMapUrl]);

  // 监听窗口大小变化，调整地图尺寸
  let currentPage = localStorage.getItem("currentPage");
  useEffect(() => {
    const handleResize = () => {
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [currentPage]);

  return (
    <div
      id="mapDiv"
      style={{ height: "calc(100vh - 56px)", width: "100%" }}
    ></div>
  );
};

export default LeafletMap;
