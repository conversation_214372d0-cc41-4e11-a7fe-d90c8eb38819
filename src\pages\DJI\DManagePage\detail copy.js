import { bytesToSize, getBodyH, isEmpty } from "@/utils/utils";
import {
  Row,
  Col,
  Card,
  Image,
  Descriptions,
  Button,
  Divider,
  Input,
  message,
  Progress,
} from "antd";
import RecordItemListPage from "./../RecordItem/index";
import { HPost2, HGet2 } from "@/utils/request";
import { useEffect, useState, useRef } from "react";
import img1 from "@/assets/icons/device.png";
import img2 from "@/assets/icons/device2.png";
import LastPageButton from "@/components/LastPageButton";
import AddButton from "@/components/AddButton";
import { EditOutlined } from "@ant-design/icons";
import { useModel } from "umi";
import { Get2, Post2, axiosApi } from "@/services/general";
import ShowDeleteConfirm from "@/components/ShowDeleteConfirm";
import WebsocketPanel from "../DevicePage/Panels/WebSocketPanel";
import {
  ChangeDNamePanel,
  ChangeDNamePanel2,
  ChangeDNamePanel3,
} from "./helper";
import dayjs from "dayjs";
import DeBugButton from "./DebugButton";
import { color } from "echarts";
import { getGuid } from "@/utils/helper";
import useMqttStore from "@/stores/useMqttStore";

const FJModeCodeJson = {
  0: "待机",
  1: "起飞准备",
  2: "起飞准备完毕",
  3: "手动飞行",
  4: "自动起飞",
  5: "航线飞行",
  6: "全景拍照",
  7: "智能跟随",
  8: "ADS-B 躲避",
  9: "自动返航",
  10: "自动降落",
  11: "强制降落",
  12: "三桨叶降落",
  13: "升级中",
  14: "未连接",
  15: "APAS",
  16: "虚拟摇杆状态",
  17: "指令飞行",
};
const ModeCodeJson = {
  0: "空闲中",
  1: "现场调试",
  2: "远程调试",
  3: "固件升级中",
  4: "作业中",
};
const CoverStateJson = { 0: "关闭", 1: "打开", 2: "半开", 3: "舱盖状态异常" };
const KongTiao = {
  0: "空闲模式(无制冷、制热、除湿等)",
  1: "制冷模式",
  2: "制热模式",
  3: "除湿模式",
  4: "制冷退出模式",
  5: "制热退出模式",
  6: "除湿退出模式",
  7: "制冷准备模式",
  8: "制热准备模式",
  9: "除湿准备模式",
};
const GetDroneName = (x1) => {
  if (x1 == "0-91-0") return "Matrice 3D";
  if (x1 == "0-91-1") return "Matrice 3TD";
  if (x1 == "0-67-0") return "Matrice 30";
  if (x1 == "0-67-1") return "Matrice 30T";
  return x1;
};

const GetDroneJT = (x1) => {
  if (x1 == "0-91-0") return "80-0-0";
  if (x1 == "0-91-1") return "81-0-0";
  if (x1 == "0-67-0") return "52-0-0";
  if (x1 == "0-67-1") return "53-0-0";
  return "81-0-0";
};

const DManagePage = ({ device2 }) => {
  //const  device = JSON.parse(localStorage.getItem('device'))
  const { setModal, setOpen } = useModel("pageModel");
  const { jcData, jcData2, jcData3 } = useModel("dockModel");
  const { fjData } = useModel("droneModel");
  const { DoCMD2, DoCMD3 } = useModel("cmdModel");
  const { MqttConnect } = useModel("mqttModel");
  const {EventMqttConn} = useModel("eventModel");
  const h1 = getBodyH(190);
  const [device, setDevice] = useState(device2);
  const [gujian, setGuJian] = useState(""); //机场固件版本
  const [gujian2, setGuJian2] = useState("");
  const [rtcHeight, setRtcHeight] = useState(50);
  const [dockNeedupdata, setDockNeedupdata] = useState(false); //机场是否需要更新
  const [flyNeedupdata, setFlyNeedupdata] = useState(false); //飞行器是否需要更新
  const [DockUpdateUrl, setDockUpdateUrl] = useState([]); //机场固件更新地址
  const [FlyUpdateUrl, setFlyUpdateUrl] = useState([]); //飞行器固件更新地址
  const [upgrade, setUpgrade] = useState(); //机场固件是否正在升级
  const intervalRef = useRef(null); // 机场固件升级定时器 id
  const isMounted = useRef(true); //踪组件是否已经挂载
  const [upgrade2, setUpgrade2] = useState(); //飞行器固件是否正在升级
  const intervalRef2 = useRef(null); // 飞行器固件升级定时器 id
  const { DoCMD } = useModel("cmdModel");

  const refrush = async () => {
    const d1 = await Get2("/api/v1/Device/GetBySN?sn=" + device.SN);
    setDevice(d1);
  };

  const getGuJian = async () => {
    const d1 = await Get2(
      "/api/v1/Property/Get?sn=" + device.SN + "&p1=FirmwareVersion"
    );
    if (!isEmpty(d1)) setGuJian(d1.PValue);
    const d2 = await Get2(
      "/api/v1/Property/Get?sn=" + device.SN2 + "&p1=FirmwareVersion"
    );
    if (!isEmpty(d2)) setGuJian2(d2.PValue);
    const d3 = await Get2("/api/v1/Property/GetRtcHeight?sn=" + device.SN);
    setRtcHeight(d3);
  };


  useEffect(() => {
    return () => {
      isMounted.current = false; // 组件卸载时设置 false
      clearInterval(intervalRef.current);
      clearInterval(intervalRef2.current);
    };
  }, []);

  const GetOtaStatusJc = async () => {
    // 获取机场固件升级 OTA 状态
    try {
      let res = await axiosApi(`/api/v2/OTA/GetOtaStatus`, "GET", {
        SN: device.SN,
      });
      if (res && res.data.Status === "in_progress") {
        // 正在升级
        if (isMounted.current) {
          setUpgrade(res.data.Percent);
        }
      } else {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        if (isMounted.current) {
          setUpgrade(null);
        }
      }
    } catch (error) {
      console.error("获取机场 OTA 状态失败:", error);
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };
  const GetOtaStatusFj = async () => {
    // 获取机场固件升级 OTA 状态
    try {
      let res = await axiosApi(`/api/v2/OTA/GetOtaStatus`, "GET", {
        SN: device.SN2,
      });
      if (res && res.data.Status === "in_progress") {
        // 正在升级
        if (isMounted.current) {
          setUpgrade2(res.data.Percent);
        }
      } else {
        clearInterval(intervalRef2.current);
        intervalRef2.current = null;
        if (isMounted.current) {
          setUpgrade2(null);
        }
      }
    } catch (error) {
      console.error("获取飞行器 OTA 状态失败:", error);
      clearInterval(intervalRef2.current);
      intervalRef2.current = null;
    }
  };

  const getDock2 = async () => {
    //更新机场固件
    try {
      let res = await axiosApi(`/api/v2/OTA/GetUpdateInfo`, "GET", {
        SN: device.SN,
        deviceType: device.Model,
        version: gujian,
      });
      if (res && res.code === 1) {
        message.info(res.data);
          // if (intervalRef.current) return; // 防止重复设置 interval
          // intervalRef.current = setInterval(() => {
          //   GetOtaStatusJc();
          // }, 2000);
      }
    } catch (error) {
      console.error("获取更新信息失败:", error);
    }
  };

  //发送更新机场固件的命令
  const updateDock = async () => {
    // const data ={
    //     firmware_upgrade_type : DockUpdateUrl.firmware_upgrade_type,
    //     product_version : DockUpdateUrl.product_version,
    //     sn : device.SN,
    // }
    let data = {
      device: [],
    };
    data.device = DockUpdateUrl;
    data.device[0].sn = device.SN;
    DoCMD(device.SN, "ota_create", data);
  };

  //检查飞行器固件版本的代码
  const getFly = async () => {
    try {
      let res = await axiosApi(`/api/v2/OTA/GetUpdateInfo`, "GET", {
        SN: device.SN2,
        deviceType: device.Model2,
        version: gujian2,
      });
      if (res && res.data) {
        message.info(res.data);

        if (res.data.includes("查询到新版本，正在为您升级！")) {
          if (intervalRef2.current) return; // 防止重复设置 interval
          intervalRef2.current = setInterval(() => {
            GetOtaStatusFj();
          }, 2000);
        }
      }
    } catch (error) {
      console.error("获取更新信息失败:", error);
    }
  };

  const updateFly = async () => {
    let data = {
      device: [],
    };
    data.device = FlyUpdateUrl;
    data.device[0].sn = device.SN2;
    DoCMD(device.SN, "ota_create", data);
    return;
  };

  //输入dock/fly的固件版本号，返回固件版本号字符串
  const GuJianString = (NumberString, deviceString) => {
    if (deviceString == "fly") {
      return "M3TD_10.01.15.07_pro.zip"; //用于测试，上传要删掉
      //return "M3D_10.01.15.07_pro.zip"; //用于测试，上传要删掉
      const device = JSON.parse(localStorage.getItem("device")); //获取设备信息,根据Camera2判断飞行器型号
      let Camera2 = device.Camera2; //获取飞行器型号,80-0-0是M3D，81-0-0是M3TD
      let FlyCamera;
      if (Camera2 == "80-0-0") {
        FlyCamera = "M3D";
      } else if (Camera2 == "81-0-0") {
        FlyCamera = "M3TD";
      } else {
        message.error("未支持的飞机型号");
      }
      let flystring = FlyCamera + "_" + NumberString + "_pro.zip";
      return flystring;
    } else if (deviceString == "dock") {
      return "DOCK2_10.01.15.07_pro.zip"; //用于测试，上传要删掉
      let Dock2string = "DOCK2_" + NumberString + "_pro.zip";
      return Dock2string;
    } else {
      message.error("设备类型错误");
    }
  };

  useEffect(() => {
    refrush();
    getGuJian();
    setDockNeedupdata(false); //进入设备详情页默认设置机场、飞行器不需要更新
    setFlyNeedupdata(false);
    MqttConnect(device2);
  }, []);

  const editButton = (val, onClick) => {
    return (
      <div style={{ marginBottom: 8.0 }}>
        <span>{val}</span>
        <span onClick={onClick} style={{ cursor: "pointer", marginLeft: 8.0 }}>
          <EditOutlined style={{ fontSize: 16.0 }} />
        </span>
      </div>
    );
  };
  const changeJcCMD = (data) => {
    DoCMD3(device.SN, `thing/product/${device.SN}/property/set`, data);
    setOpen(false);
  };

  const editRtcHeight = async (d1) => {
    const d2 = await Post2(
      "/api/v1/Property/AddRtcHeight?sn=" + device.SN + "&h1=" + d1
    );
    const d3 = await Get2("/api/v1/Property/GetRtcHeight?sn=" + device.SN);
    setRtcHeight(d3);
    setOpen(false);
    // if(!isEmpty(d2)) setGuJian2(d2.PValue);
  };

  const dPanel = () => {
    // const jc1=JSON.parse(localStorage.getItem('jcdata1'));
    // const jc2=JSON.parse(localStorage.getItem('jcdata2'));
    // const jc3=JSON.parse(localStorage.getItem('jcdata3'));
    if (
      isEmpty(jcData.current) ||
      isEmpty(jcData2.current) ||
      isEmpty(jcData3.current)
    ) {
      return "设备不在线";
    }

    const jt1 = GetDroneJT(jcData.current.sub_device.device_model_key);
    if (jt1 != "" && jt1 != device.Camera2) {
      onSaveDevice({ Camera2: jt1 });
    }

    // const sn2=jcData.current.sub_device.device_sn;
    // if(sn2!="" && sn2!=device.SN2){
    //     onSaveDevice({SN2:sn2})
    //  }
    //

    return (
      <Descriptions
        style={{ textAlign: "left" }}
        title="机场信息"
        column={2}
        extra={
          <DeBugButton
            label="远程调试"
            color="black"
            sn={device.SN}
          ></DeBugButton>
        }
      >
        <Descriptions.Item label="机场名称">
          {editButton(device.DName, editDName)}
        </Descriptions.Item>
        <Descriptions.Item label="机场状态">
          {ModeCodeJson[jcData.current.mode_code]}
        </Descriptions.Item>

        {/* <Descriptions.Item label="机场位置"> {editButton(device.Location,()=>editDevice("机场位置",device.Location,(d1)=>onSaveDevice({Location:d1})))}</Descriptions.Item> */}
        <Descriptions.Item label="设备型号">{device.Model}</Descriptions.Item>
        {/* <Descriptions.Item label="飞机摄像头"><div><span>{device.Camera2}</span><span style={{marginLeft:4.0}}><Button type="text" icon={<EditOutlined />} onClick={()=>editDevice((e)=>onSaveDevice({...device,'Camera2':e}))}></Button></span></div></Descriptions.Item> */}

        <Descriptions.Item label="序列号">{device.SN}</Descriptions.Item>
        <Descriptions.Item label="固件版本">
          {gujian}{" "}
          {dockNeedupdata ? (
            <button
              onClick={updateDock}
              style={{
                marginLeft: "10px", // 与前面的间距
                border: "none", // 去掉边框
                backgroundColor: "#007bff", // 按钮背景色
                color: "white", // 按钮文字颜色
                //width: "20px", // 按钮宽度
                //height: "20px", // 按钮高度
                cursor: "pointer", // 鼠标样式
                //fontSize: "15px", // 调整字体大小
              }}
            >
              更新
            </button>
          ) : (
            <>
              <button
                onClick={getDock2}
                style={{
                  marginLeft: "10px", // 与前面的间距
                  border: "none", // 去掉边框
                  backgroundColor: "#007bff",
                  color: "white",
                  whiteSpace: "nowrap",
                  cursor: "pointer", // 鼠标样式
                }}
              >
                {upgrade ? "升级中" : "检查"}
              </button>
              {upgrade && (
                <Progress
                  percent={upgrade}
                  status="active"
                  strokeColor={{
                    from: "#108ee9",
                    to: "#87d068",
                  }}
                  style={{
                    marginLeft: 10,
                    width: 100,
                  }}
                />
              )}
            </>
          )}
        </Descriptions.Item>
        {getItem(
          "无人机状态",
          jcData.current.sub_device.device_online_status == "0"
            ? "关机"
            : "开机",
          ""
        )}
        {/* <Descriptions.Item label="固件版本">{'***********'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="固件升级">{device.Level}</Descriptions.Item> */}
        <Descriptions.Item label="飞行架次">
          {jcData3.current.job_number}
        </Descriptions.Item>
        <Descriptions.Item label="运行时间">
          {Number(jcData3.current.acc_time / 3600).toFixed(0) + "小时"}
        </Descriptions.Item>
        {/* <Descriptions.Item label="状态保养">{'已保养'}</Descriptions.Item> */}

        {getItem(
          "首次激活时间",
          dayjs(jcData3.current.activation_time * 1000).format("YYYY-MM-DD")
        )}
        {/* {getItem('静音模式',jcData.current.silent_mode==1?'开启':'关闭')} */}
        <Descriptions.Item label="静音模式">
          {" "}
          {editButton(jcData.current.silent_mode == 1 ? "开启" : "关闭", () =>
            editDevice2(
              "静音模式",
              jcData.current.silent_mode,
              "0:关闭，1：开启",
              (d1) => changeJcCMD({ silent_mode: Number(d1) })
            )
          )}
        </Descriptions.Item>

        {getItem(
          "空调工作状态",
          KongTiao[jcData.current.air_conditioner.air_conditioner_state]
        )}
        {/* {getItem('电池运行模式',jcData.current.battery_store_mode==1?'计划模式':'待命模式')} */}
        {/* {getItem('工作电压',(jcData3.current.working_voltage/1000).toFixed(1)+'V')} */}
        {getItem(
          "备用电源",
          jcData3.current.backup_battery.switch == 1 ? "开启" : "关闭"
        )}
        {getItem(
          "安全返航高度",
          editButton(rtcHeight + "米", () =>
            editDevice2("安全返航高度", rtcHeight, "", editRtcHeight)
          )
        )}

        {/* <Descriptions.Item label="上次保养时间">{'2023年12月'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="是否需要保养">{'不需要'}</Descriptions.Item> */}

        {/* <Descriptions.Item label="主要内容">{device.Content}</Descriptions.Item> */}
      </Descriptions>
    );
  };

  const getItem = (label, val) => {
    return <Descriptions.Item label={label}>{val}</Descriptions.Item>;
  };

  const dPanel2 = () => {
    //
    if (isEmpty(fjData.current)) {
      return (
        <div style={{ paddingLeft: 48.0, marginTop: 8.0 }}>
          无人机不在线,可通过调试指令开启无人机
        </div>
      );
    }
    if (
      isEmpty(jcData.current) ||
      isEmpty(jcData2.current) ||
      isEmpty(jcData3.current)
    ) {
      return "设备不在线";
    }

    if (jcData.current.sub_device.device_online_status == "0") {
      return (
        <div style={{ paddingLeft: 48.0, marginTop: 8.0 }}>
          无人机不在线,可通过调试指令开启无人机
        </div>
      );
    }

    const RCLostActionList = ["悬停", "着陆(降落)", "返航"];
    //
    return (
      <Descriptions style={{ textAlign: "left" }} title="无人机信息" column={2}>
        {/* <Descriptions.Item label="飞机名称"><div><span>{device.DName2}</span><span style={{marginLeft:4.0}}><Button type="text" icon={<EditOutlined />} onClick={editDName}></Button></span></div></Descriptions.Item> */}
        <Descriptions.Item label="无人机名称">
          {" "}
          {editButton(device.DName2, () =>
            editDevice("无人机名称", device.DName2, (d1) =>
              onSaveDevice({ DName2: d1 })
            )
          )}
        </Descriptions.Item>
        <Descriptions.Item label="无人机状态">
          {FJModeCodeJson[fjData.current.mode_code]}
        </Descriptions.Item>

        <Descriptions.Item label="无人机型号">
          {GetDroneName(jcData.current.sub_device.device_model_key)}
        </Descriptions.Item>
        <Descriptions.Item label="序列号">{device.SN2}</Descriptions.Item>
        <Descriptions.Item label="固件版本">
          {gujian2}
          {flyNeedupdata ? (
            <button
              onClick={updateFly}
              style={{
                marginLeft: "10px", // 与前面的间距
                border: "none", // 去掉边框
                backgroundColor: "#007bff", // 按钮背景色
                color: "white", // 按钮文字颜色
                cursor: "pointer", // 鼠标样式
              }}
            >
              更新
            </button>
          ) : (
            <>
              <button
                onClick={getFly}
                style={{
                  marginLeft: "10px", // 与前面的间距
                  border: "none", // 去掉边框
                  backgroundColor: "#007bff", // 按钮背景色
                  color: "white", // 按钮文字颜色
                  cursor: "pointer", // 鼠标样式
                  whiteSpace: "nowrap",
                }}
              >
                {upgrade2 ? "升级中" : "检查"}
              </button>
              {upgrade2 && (
                <Progress
                  percent={upgrade2}
                  status="active"
                  strokeColor={{
                    from: "#108ee9",
                    to: "#87d068",
                  }}
                  style={{
                    marginLeft: 10,
                    width: 100,
                  }}
                />
              )}
            </>
          )}
        </Descriptions.Item>

        {/* <Descriptions.Item label="固件版本">{'***********'}</Descriptions.Item>
            <Descriptions.Item label="搭配镜头">{'广角、变焦、红外'}</Descriptions.Item>
            <Descriptions.Item label="满电续航时间">{'50分钟'}</Descriptions.Item>
            <Descriptions.Item label="满电巡检距离">{'8公里'}</Descriptions.Item> */}

        <Descriptions.Item label="无人机电量">
          {fjData.current.battery.capacity_percent + "%"}
        </Descriptions.Item>
        <Descriptions.Item label="累计飞行架次">
          {fjData.current.total_flight_sorties}
        </Descriptions.Item>

        {/* <Descriptions.Item label="累计飞行时间">{'2.4h'}</Descriptions.Item> */}
        <Descriptions.Item label="累计飞行时间">
          {(fjData.current.total_flight_time / 3600).toFixed(1) + "小时"}
        </Descriptions.Item>

        <Descriptions.Item label="累计飞行距离">
          {(fjData.current.total_flight_distance / 1000).toFixed(1) + "公里"}
        </Descriptions.Item>
        {/* <Descriptions.Item label="状态保养">{'已保养'}</Descriptions.Item> */}
        {/* {getItem('限制飞行距离',fjData.current.distance_limit_status.distance_limit+'米')} */}
        {getItem(
          "限制飞行距离",
          editButton(
            fjData.current.distance_limit_status.distance_limit + "米",
            () =>
              editDevice2(
                "限制飞行距离",
                fjData.current.distance_limit_status.distance_limit,
                "",
                (d1) =>
                  changeJcCMD({
                    distance_limit_status: { distance_limit: Number(d1) },
                  })
              )
          )
        )}
        {getItem(
          "限制飞行高度",
          editButton(fjData.current.height_limit + "米", () =>
            editDevice2("限制飞行高度", fjData.current.height_limit, "", (d1) =>
              changeJcCMD({ height_limit: Number(d1) })
            )
          )
        )}

        {getItem(
          "无人机夜航灯状态",
          editButton(
            fjData.current.night_lights_state == 0 ? "关闭" : "打开",
            () =>
              editDevice2(
                "夜航灯",
                fjData.current.night_lights_state,
                "0:关闭，1：开启",
                (d1) => changeJcCMD({ night_lights_state: Number(d1) })
              )
          )
        )}

        {/* {getItem('飞行器夜航灯状态',fjData.current.night_lights_state==0?'关闭':'打开')} */}

        {/* {getItem('水平避障状态',fjData.current.obstacle_avoidance.horizon==0?'关闭':'打开')}
            {getItem('上方避障状态',fjData.current.obstacle_avoidance.upside==0?'关闭':'打开')}
            {getItem('下方避障状态',fjData.current.obstacle_avoidance.downside==0?'关闭':'打开')} */}
        {/* {getItem('失联后动作',RCLostActionList[fjData.current.rc_lost_action])} */}

        {/* {getItem('无人机返航高度',fjData.current.rth_altitude+'米')} */}
        {/* {getItem('无人机返航高度',editButton(fjData.current.rth_altitude+'米',()=>editDevice2("无人机返航高度",fjData.current.rth_altitude,"", (d1)=>changeJcCMD({"rth_altitude": Number(d1),}))))} */}

        {getItem(
          "无人机激活时间",
          dayjs(fjData.current.activation_time * 1000).format("YYYY-MM-DD")
        )}
        {getItem(
          "存储空间",
          bytesToSize(fjData.current.storage.used * 1000) +
            "/" +
            bytesToSize(fjData.current.storage.total * 1000)
        )}
      </Descriptions>
    );
  };

  const onSaveDevice = async (d1) => {
    // device.DName=d1;
    const xx = await HPost2("/api/v1/Device/Update", { ...device, ...d1 });
    await refrush();
    setOpen(false);
  };

  const editDName = () => {
    setModal(
      <ChangeDNamePanel
        key={getGuid()}
        nm1={device.DName}
        flyTo={(d1) => onSaveDevice({ DName: d1 })}
        setOpen={setOpen}
      ></ChangeDNamePanel>
    );
    setOpen(true);
  };

  const editDevice = (label, val, onSave) => {
    setModal(
      <ChangeDNamePanel2
        key={getGuid()}
        label={label}
        nm1={val}
        flyTo={onSave}
        setOpen={setOpen}
      ></ChangeDNamePanel2>
    );
    setOpen(true);
  };
  const editDevice2 = (label, val, tooltip, onSave) => {
    setModal(
      <ChangeDNamePanel3
        key={getGuid()}
        label={label}
        nm1={val}
        tooltip={tooltip}
        flyTo={onSave}
        setOpen={setOpen}
      ></ChangeDNamePanel3>
    );
    setOpen(true);
  };

  const era = (
    <AddButton
      type="primary"
      onClick={() =>
        ShowDeleteConfirm("解绑机场", () => {
          message.info("权限不足");
        })
      }
    >
      机场解绑
    </AddButton>
  );

  return isEmpty(device) ? (
    <div>未获取设备</div>
  ) : (
    <Card title={<LastPageButton title="设备管理" />} extra={era}>
      <Row>
        <Col span={12} style={{ paddingRight: 24.0 }}>
          <Row style={{ margin: 12.0 }} justify="center">
            <div
              style={{
                height: 140,
                width: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image
                style={{
                  height: 120,
                  width: 140,
                  marginLeft: 4.0,
                  marginTop: 4,
                  marginBottom: 16.0,
                }}
                src={img1}
              />
            </div>
            <div style={{ marginLeft: 24.0 }}> {dPanel()}</div>
          </Row>
        </Col>
        <Col span={12}>
          <Row style={{ margin: 12.0 }}>
            <div
              style={{
                height: 140,
                width: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image style={{ height: 140, width: 180 }} src={img2} />
            </div>

            <div style={{ marginLeft: 24.0 }}> {dPanel2()}</div>
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default DManagePage;
