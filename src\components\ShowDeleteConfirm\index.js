import {Modal} from 'antd';

const ShowDeleteConfirm = (title,callback) => {
  const { confirm } = Modal;
    confirm({
      title: '删除'+title,
      //icon: <ExclamationCircleFilled />,
      content: '确定删除该'+title+'吗？',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        callback();
      },
      onCancel() {
       // console.log('Cancel');
      },
    });
  };
export default ShowDeleteConfirm;