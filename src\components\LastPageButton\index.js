import { Button,} from 'antd';
import { useModel } from 'umi';
import { ArrowLeftOutlined } from '@ant-design/icons';

const LastPageButton=({title})=>{
    const { lastPage, setCurrentPage } = useModel('pageModel')
    const handleLastPage = () => {
        lastPage();
    };
    return  <div><Button type='text' icon={<ArrowLeftOutlined />} onClick={handleLastPage}></Button> <span>{title}</span></div>
   //return <Row style={{fontStretch:'black'}} onClick={lastPage}><Col> <ArrowLeftOutlined style={{ fontSize: '16px', color: '#08c' }} /> </Col> <Col style={{color:'black'}}>{title}</Col></Row>
}

export default LastPageButton;