import React, { useState, useRef } from "react";
import styles from "./DraggableModal.less";

const DraggableModal = ({ title, child, isOpen, onClose, map,style }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 300, y: 100 });
  const initialMousePos = useRef({ x: 0, y: 0 });
  let draggableRef = useRef(null);
  const handleMouseDown = (e) => {
    if(map){
      map.dragging.disable(); // 禁用地图拖动
    }
    e.stopPropagation(); // 阻止事件冒泡
    setIsDragging(true);
    initialMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const dx = e.clientX - initialMousePos.current.x;
      const dy = e.clientY - initialMousePos.current.y;

      // 计算新的位置
      const newX = position.x + dx;
      const newY = position.y + dy;

      // 获取窗口的宽度和高度
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight-47;

      // 计算弹窗的宽度和高度
      const modalWidth = draggableRef.current.offsetWidth; 
      const modalHeight = draggableRef.current.offsetHeight; 

      // 边界检查
      const boundedX = Math.min(Math.max(newX, 0), windowWidth - modalWidth);
      const boundedY = Math.min(Math.max(newY, 0), windowHeight - modalHeight);

      setPosition({ x: boundedX, y: boundedY });
      initialMousePos.current = { x: e.clientX, y: e.clientY };
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseEnter = () => {
    if(map){
      map.dragging.disable(); // 禁用地图拖动
      map.scrollWheelZoom.disable(); // 禁用地图缩放
    }
  };

  const handleMouseLeave = () => {
    if(map){
      map.dragging.enable(); // 启用地图拖动
      map.scrollWheelZoom.enable(); // 启用地图缩放
    }
  };

  if (!isOpen) return null; // 如果没有打开，则不渲染弹窗

  return (
    <div
      id={styles.DraggableModal}
      ref={draggableRef}
      style={{ left: position.x, top: position.y,...style}}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
    >
      <div className={styles.modalClose} onClick={onClose?onClose:null}>
        ×
      </div>
      <div className={styles.modalTitle}>{title?title:null}</div>
      <div className={styles.modalContent}>{child?child:null}</div>
    </div>
  );
};

export default DraggableModal;
