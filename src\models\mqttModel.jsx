import { useRef } from "react";
import { useModel } from "umi";
export default function mqttModel() {
    const { NewDroneConn } = useModel('droneModel');
    const { EventMqttConn } = useModel('eventModel');
    const {StateMqttConn} =useModel('stateModel');
    const { CmdMqttConn } =useModel('cmdModel');
    const { DrcMqttConn } =useModel('drcModel');
    const { DockMqttConn } = useModel('dockModel');
    const SN=useRef('');
    const MqttConnect = (device) => {
        if(device.SN!=SN.current){
            SN.current=device.SN;
            NewDroneConn(device);
            EventMqttConn(device.SN);
            DockMqttConn(device.SN);
            StateMqttConn(device.SN);
            CmdMqttConn(device.SN);
            DrcMqttConn(device.SN);
        }
    }
    return { MqttConnect };
};