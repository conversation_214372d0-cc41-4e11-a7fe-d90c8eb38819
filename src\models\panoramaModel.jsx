import { isEmpty } from "@/utils/utils";
import { useState, useRef, useEffect, useCallback } from "react";
import { message } from "antd";
import { useModel } from "umi";

export default function panoramaModel() {
  // 引入命令模型，用于发送命令
  const { DoCMD, DoCMD2 } = useModel('cmdModel');
  // 引入事件模型，用于统一的MQTT事件处理
  const { registerPanoramaCallback } = useModel('eventModel');
  
  const timeoutRef = useRef(null);
  const retryCountRef = useRef(0);
  const autoHideTimeoutRef = useRef(null); // 自动隐藏弹窗的定时器
  const retryTimeoutRef = useRef(null); // 重试的定时器
  const delayTimeoutRef = useRef(null); // 延迟发送命令的定时器
  const MAX_RETRIES = 3;
  const TIMEOUT_DURATION = 30000; // 30秒超时
  const PROGRESS_UPDATE_THROTTLE = 100; // 进度更新节流，100ms
  const lastProgressUpdateTime = useRef(0); // 上次进度更新时间
  
  // 全景拍照状态
  const [panoramaState, setPanoramaState] = useState({
    isActive: false,           // 是否正在进行全景拍照
    visible: false,            // 进度弹窗是否显示
    progress: {
      percent: 0,              // 进度百分比 0-100
      current_step: 3000,      // 当前步骤：3000-未开始/已结束，3002-拍摄中，3005-合成中
      status: 'idle'           // 状态：idle-准备中，in_progress-执行中，ok-完成，fail-失败
    },
    error: null,               // 错误信息
    startTime: null,           // 开始时间
    endTime: null,             // 结束时间
    device: null,              // 当前设备信息
    mode: 3                    // 全景拍照模式 (camera_mode: 3)
  });

  // 注册全景拍照事件回调（替代原来的MQTT连接）
  const PanoramaMqttConn = useCallback((sn) => {
    console.log('注册全景拍照事件回调, 设备SN:', sn);
    // 不再需要直接连接MQTT，而是通过eventModel的回调机制
    // eventModel已经处理了MQTT连接，我们只需要注册回调即可
  }, []);

  // 处理拍照进度数据
  const handlePhotoProgress = (data) => {
    const { output } = data;
    if (!output) return;

    const { status, progress, ext } = output;
    
    // 检查是否是全景拍照模式
    if (ext?.camera_mode !== 3) {
      return; // 不是全景拍照模式，忽略
    }

    // 进度更新节流，避免过于频繁的状态更新
    const now = Date.now();
    const shouldThrottle = status === 'in_progress' && 
                          (now - lastProgressUpdateTime.current) < PROGRESS_UPDATE_THROTTLE;
    
    if (shouldThrottle) {
      return; // 跳过这次更新，避免过于频繁的渲染
    }
    
    lastProgressUpdateTime.current = now;

    console.log('全景拍照进度更新:', {
      status,
      percent: progress?.percent,
      currentStep: progress?.current_step
    });

    // 清除超时定时器（收到进度说明命令已经开始执行）
    clearTimeoutTimer();

    // 更新状态
    setPanoramaState(prev => ({
      ...prev,
      progress: {
        percent: progress?.percent || 0, // MQTT返回的percent已经是整数
        current_step: progress?.current_step || 3000,
        status: mapStatusToLocal(status)
      },
      endTime: status === 'ok' || status === 'fail' ? new Date() : prev.endTime
    }));

    // 处理不同状态
    if (status === 'ok') {
      // 拍照完成
      handlePanoramaComplete();
    } else if (status === 'fail') {
      // 拍照失败
      handlePanoramaFailed(data.result || '未知错误');
    } else if (status === 'in_progress') {
      // 拍照进行中，重新设置超时定时器
      setTimeoutTimer();
    }
  };
  
  // 处理全景拍照完成
  const handlePanoramaComplete = () => {
    console.log('全景拍照完成');
    
    // 清除超时定时器
    clearTimeoutTimer();
    
    // 更新状态
    setPanoramaState(prev => ({
      ...prev,
      isActive: false,
      progress: {
        ...prev.progress,
        status: 'ok'
      },
      endTime: new Date()
    }));
    
    message.success('全景拍照完成');
    
    // 清除之前的自动隐藏定时器
    if (autoHideTimeoutRef.current) {
      clearTimeout(autoHideTimeoutRef.current);
    }
    
    // 3秒后自动隐藏进度弹窗
    autoHideTimeoutRef.current = setTimeout(() => {
      setPanoramaState(prev => ({
        ...prev,
        visible: false
      }));
      autoHideTimeoutRef.current = null;
    }, 3000);
  };
  
  // 处理全景拍照失败
  const handlePanoramaFailed = (errorCode) => {
    console.error('全景拍照失败:', errorCode);
    
    // 清除超时定时器
    clearTimeoutTimer();
    
    // 尝试重试
    if (retryCountRef.current < MAX_RETRIES) {
      retryCountRef.current++;
      message.warning(`全景拍照失败，正在重试 (${retryCountRef.current}/${MAX_RETRIES})...`);
      
      // 获取当前设备
      const { device } = panoramaState;
      
      // 清除之前的重试定时器
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      // 延迟重试
      retryTimeoutRef.current = setTimeout(() => {
        // 重新发送拍照命令
        if (device) {
          sendStartPanoramaCommand(device);
          setTimeoutTimer();
        } else {
          setPanoramaError('重试失败: 设备信息为空');
        }
        retryTimeoutRef.current = null;
      }, 2000);
    } else {
      // 重试次数用完，标记为失败
      setPanoramaError(`全景拍照失败 (错误码: ${errorCode})`);
      
      // 清除之前的自动隐藏定时器
      if (autoHideTimeoutRef.current) {
        clearTimeout(autoHideTimeoutRef.current);
      }
      
      // 3秒后自动隐藏进度弹窗
      autoHideTimeoutRef.current = setTimeout(() => {
        setPanoramaState(prev => ({
          ...prev,
          visible: false
        }));
        autoHideTimeoutRef.current = null;
      }, 3000);
    }
  };

  // 映射服务器状态到本地状态
  const mapStatusToLocal = (serverStatus) => {
    switch (serverStatus) {
      case 'in_progress':
        return 'in_progress';
      case 'ok':
        return 'ok';
      case 'fail':
        return 'fail';
      default:
        return 'idle';
    }
  };

  // 设置当前设备
  const setCurrentDevice = useCallback((device) => {
    if (!device) return;
    console.log('设置全景拍照当前设备:', device);
    setPanoramaState(prev => ({
      ...prev,
      device: device
    }));
  }, []); // 空依赖数组，因为只使用了 setPanoramaState

  // 清除所有定时器
  const clearAllTimers = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (autoHideTimeoutRef.current) {
      clearTimeout(autoHideTimeoutRef.current);
      autoHideTimeoutRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    if (delayTimeoutRef.current) {
      clearTimeout(delayTimeoutRef.current);
      delayTimeoutRef.current = null;
    }
  };

  // 清除超时定时器
  const clearTimeoutTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  // 设置超时定时器
  const setTimeoutTimer = () => {
    clearTimeoutTimer();
    timeoutRef.current = setTimeout(() => {
      console.error('全景拍照超时');
      setPanoramaError('操作超时，请重试');
      stopPanoramaCapture();
    }, TIMEOUT_DURATION);
  };

  // 切换到全景拍照模式
  const switchToPanoramaMode = (device) => {
    if (!device) {
      console.error('切换全景拍照模式失败: 设备信息为空');
      return false;
    }

    try {
      const data = {
        camera_mode: panoramaState.mode, // 全景拍照模式 (3)
        payload_index: device.Camera2,
      };
      console.log('切换到全景拍照模式:', data, '设备类型:', device.BindCode, device.Model);
      
      // 根据设备类型选择不同的命令发送方式
      if (device.BindCode === 'pilot' && device.Model === "RC_PLUS_2") {
        // RC_PLUS_2设备使用DRC协议
        let s = {};
        s.method = "drc_camera_mode_switch";
        s.data = data;
        console.log('使用DRC协议切换模式:', s);
        DoCMD2(`thing/product/${device.SN}/drc/down`, s);
      } else {
        // 标准设备使用services协议
        console.log('使用标准协议切换模式');
        DoCMD(device.SN, "camera_mode_switch", data);
      }
      return true;
    } catch (error) {
      console.error('切换全景拍照模式失败:', error);
      return false;
    }
  };

  // 发送开始全景拍照命令
  const sendStartPanoramaCommand = (device) => {
    if (!device) {
      console.error('开始全景拍照失败: 设备信息为空');
      return false;
    }

    try {
      const data = { payload_index: device.Camera2 };
      console.log('发送全景拍照命令:', data);
      
      // 根据设备类型选择不同的命令发送方式
      if (device.BindCode === 'pilot' && device.Model === "RC_PLUS_2") {
        let s = {};
        s.method = "drc_camera_photo_take";
        s.data = data;
        DoCMD2(`thing/product/${device.SN}/drc/down`, s);
      } else {
        DoCMD(device.SN, "camera_photo_take", data);
      }
      return true;
    } catch (error) {
      console.error('发送全景拍照命令失败:', error);
      return false;
    }
  };

  // 发送停止全景拍照命令
  const sendStopPanoramaCommand = (device) => {
    if (!device) {
      console.error('停止全景拍照失败: 设备信息为空');
      return false;
    }

    try {
      const data = { payload_index: device.Camera2 };
      console.log('发送停止全景拍照命令:', data);
      
      // 根据设备类型选择不同的命令发送方式
      if (device.BindCode === 'pilot' && device.Model === "RC_PLUS_2") {
        let s = {};
        s.method = "drc_camera_photo_stop";
        s.data = data;
        DoCMD2(`thing/product/${device.SN}/drc/down`, s);
      } else {
        DoCMD(device.SN, "camera_photo_stop", data);
      }
      return true;
    } catch (error) {
      console.error('发送停止全景拍照命令失败:', error);
      return false;
    }
  };

  // 开始全景拍照流程
  const startPanorama = useCallback((device) => {
    // 使用传入的设备或状态中的设备
    const currentDevice = device || panoramaState.device;
    
    if (!currentDevice) {
      message.error('无法开始全景拍照: 设备信息为空');
      return false;
    }
    
    console.log('开始全景拍照流程, 设备:', currentDevice);
    
    // 重置重试计数
    retryCountRef.current = 0;
    
    // 更新状态
    setPanoramaState(prev => ({
      ...prev,
      isActive: true,
      visible: true,
      progress: {
        percent: 0,
        current_step: 3000,
        status: 'in_progress'
      },
      error: null,
      startTime: new Date(),
      endTime: null,
      device: currentDevice
    }));
    
    // 切换到全景拍照模式
    const modeSwitchSuccess = switchToPanoramaMode(currentDevice);
    if (!modeSwitchSuccess) {
      setPanoramaError('切换全景拍照模式失败');
      return false;
    }
    
    // 清除之前的延迟定时器
    if (delayTimeoutRef.current) {
      clearTimeout(delayTimeoutRef.current);
    }
    
    // 设置延时，等待模式切换完成后发送拍照命令
    delayTimeoutRef.current = setTimeout(() => {
      const commandSuccess = sendStartPanoramaCommand(currentDevice);
      if (!commandSuccess) {
        setPanoramaError('发送全景拍照命令失败');
        return false;
      }
      
      // 设置超时定时器
      setTimeoutTimer();
      
      message.info('全景拍照已开始');
      delayTimeoutRef.current = null;
    }, 1000);
    
    return true;
  }, [panoramaState.device]); // 依赖 panoramaState.device

  // 停止全景拍照
  const stopPanorama = useCallback(() => {
    const { device } = panoramaState;
    
    console.log('停止全景拍照');
    
    // 清除所有定时器
    clearAllTimers();
    
    // 发送停止命令
    if (device) {
      sendStopPanoramaCommand(device);
    }
    
    // 更新状态并隐藏进度弹窗
    setPanoramaState(prev => ({
      ...prev,
      isActive: false,
      visible: false, // 立即隐藏进度弹窗
      progress: {
        ...prev.progress,
        status: 'idle'
      },
      endTime: new Date()
    }));
    
    message.info('全景拍照已停止');
    
    return true;
  }, [panoramaState.device]); // 依赖 panoramaState.device
  
  // 停止拍照捕获（内部方法，不发送停止命令）
  const stopPanoramaCapture = () => {
    console.log('停止全景拍照捕获');
    
    // 清除所有定时器
    clearAllTimers();
    
    // 更新状态并隐藏进度弹窗
    setPanoramaState(prev => ({
      ...prev,
      isActive: false,
      visible: false, // 隐藏进度弹窗
      endTime: new Date()
    }));
  };

  // 显示全景拍照进度
  const showPanoramaProgress = useCallback(() => {
    setPanoramaState(prev => ({
      ...prev,
      visible: true
    }));
  }, []);

  // 隐藏全景拍照进度
  const hidePanoramaProgress = useCallback(() => {
    setPanoramaState(prev => ({
      ...prev,
      visible: false,
      isActive: false
    }));
  }, []);

  // 重置全景拍照状态
  const resetPanoramaState = useCallback(() => {
    console.log('重置全景拍照状态');
    
    // 清除所有定时器
    clearAllTimers();
    
    // 重置重试计数
    retryCountRef.current = 0;
    
    setPanoramaState({
      isActive: false,
      visible: false,
      progress: {
        percent: 0,
        current_step: 3000,
        status: 'idle'
      },
      error: null,
      startTime: null,
      endTime: null,
      device: null,
      mode: 3
    });
  }, []);

  // 设置错误状态
  const setPanoramaError = (error) => {
    console.error('全景拍照错误:', error);
    
    // 清除所有定时器
    clearAllTimers();
    
    setPanoramaState(prev => ({
      ...prev,
      isActive: false,
      progress: {
        ...prev.progress,
        status: 'fail'
      },
      error: error,
      endTime: new Date()
    }));
    message.error(`全景拍照错误: ${error}`);
  };

  // 手动更新进度（用于测试）
  const updateProgress = useCallback((percent, currentStep, status) => {
    setPanoramaState(prev => ({
      ...prev,
      progress: {
        percent: percent || prev.progress.percent,
        current_step: currentStep || prev.progress.current_step,
        status: status || prev.progress.status
      }
    }));
  }, []);

  // 获取当前状态描述
  const getStatusDescription = useCallback(() => {
    const { progress } = panoramaState;
    
    switch (progress.current_step) {
      case 3002:
        return '正在拍摄全景图片...';
      case 3005:
        return '正在合成全景图片...';
      case 3000:
      default:
        if (progress.status === 'ok') {
          return '全景拍照完成';
        } else if (progress.status === 'fail') {
          return '全景拍照失败';
        }
        return '准备全景拍照...';
    }
  }, [panoramaState.progress]);

  // 检查是否可以开始新的全景拍照
  const canStartPanorama = useCallback(() => {
    return !panoramaState.isActive && panoramaState.progress.status !== 'in_progress';
  }, [panoramaState.isActive, panoramaState.progress.status]);

  // 获取拍照持续时间
  const getPanoramaDuration = useCallback(() => {
    const { startTime, endTime } = panoramaState;
    if (!startTime) return 0;
    const end = endTime || new Date();
    return Math.floor((end - startTime) / 1000); // 返回秒数
  }, [panoramaState.startTime, panoramaState.endTime]);

  // 获取重试次数
  const getRetryCount = useCallback(() => {
    return retryCountRef.current;
  }, []);

  // 获取最大重试次数
  const getMaxRetries = useCallback(() => {
    return MAX_RETRIES;
  }, []);

  // 注册全景拍照事件回调
  useEffect(() => {
    if (registerPanoramaCallback) {
      console.log('注册全景拍照事件回调');
      const unregister = registerPanoramaCallback(handlePhotoProgress);
      
      return () => {
        console.log('注销全景拍照事件回调');
        if (unregister) {
          unregister();
        }
      };
    }
  }, [registerPanoramaCallback]);

  // 组件卸载或页面切换时清理资源
  useEffect(() => {
    return () => {
      // 清除所有定时器，防止内存泄漏
      clearAllTimers();
      
      // 重置进度更新时间
      lastProgressUpdateTime.current = 0;
      
      // 不再需要关闭独立的MQTT连接，因为已经使用eventModel统一管理
      // if (!isEmpty(mqttC.current)) {
      //   mqttC.current.end();
      // }
    };
  }, []);

  return {
    // 状态
    panoramaState,
    
    // 连接方法
    PanoramaMqttConn,
    
    // 设备管理
    setCurrentDevice,
    
    // 控制方法
    startPanorama,
    stopPanorama,
    showPanoramaProgress,
    hidePanoramaProgress,
    resetPanoramaState,
    setPanoramaError,
    updateProgress,
    
    // 工具方法
    getStatusDescription,
    canStartPanorama,
    getPanoramaDuration,
    getRetryCount,
    getMaxRetries,
    
    // 内部方法（用于测试）
    switchToPanoramaMode,
    sendStartPanoramaCommand,
    sendStopPanoramaCommand,
    handlePhotoProgress
  };
}