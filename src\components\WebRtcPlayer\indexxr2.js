import React, { useRef, useEffect, useState } from "react";
import Srs from "./srs.sdk";
import { getRtmpWebrtcUrl } from "@/utils/config";

import { useModel } from "umi";
import CanvasHelper from './canvansHelper2'
import { HPost2 } from "@/utils/request";
// import HongXianPage from '@/pages/DJI/HongXianPage/index.js'
// import { getPoint } from '@/pages/DJI/HongXianPage/no_pic.js'
import CesoiumTest from '@/pages/DJI/cesiumTest/index.js'
import InfraredThermometer from "@/pages/DJI/FlyToPage/Panels/InfraredThermometer/index";



const VideoViewer2 = ({ sn3, ctl, height, width, isDrc }) => {
  const rtcPlayerRef = useRef(null);
  const RtmpUrl = getRtmpWebrtcUrl();
  const url = RtmpUrl + sn3;
  const guid = "fj_player";
  const canvasRef = useRef(null);
  const { fj } = useModel("droneModel");
  const { drc } = useModel("drcModel");
  //是否加载画布
  const { showCanvas, setShowCanvas } = useModel("pageModel");
  let { WanLineId } = useModel("pageModel");
  if (WanLineId === "") {
    WanLineId = localStorage.getItem("WanLineId");
  }

  useEffect(() => {
    // 初始化播放器
    const player = document.getElementById("fj_player");
    rtcPlayerRef.current = new Srs.SrsRtcPlayerAsync();
    rtcPlayerRef.current.play(url);
    player.srcObject = rtcPlayerRef.current.stream;

    return () => {
      // 清理播放器
      if (rtcPlayerRef.current) {
        rtcPlayerRef.current.close();
        player.srcObject = null;
      }
    };
  }, [url]); // 保持对 url 的依赖
  // console.log("WanLineId:",WanLineId);

  // useEffect(() => {
  //   console.log('@@@drc', drc);
  //   console.log('@@@sn3', sn3);
  // }, [drc]);

  return (
    <div style={{
      background: "black",
      position: 'relative',
      width: width || "100%",
      height: "100%"
    }}>
      <video
        id="fj_player"
        autoPlay
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          background: 'black',
          transform: 'translate(-50%, -50%)',
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          zIndex: 2
        }}
      ></video>
      <CesoiumTest height={height} showCanvas={showCanvas} setShowCanvas={setShowCanvas} sn={sn3} drc={drc} fj={fj} />
      {/* 红外相机下的点测温和区域测温组件 */}
      <InfraredThermometer />
    </div>
  );
};

export default VideoViewer2;  