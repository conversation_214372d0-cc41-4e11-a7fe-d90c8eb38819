import React, { useEffect, useState } from "react";
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { Divider, Radio, Table, ConfigProvider, Select, Input, CloseCircleOutlined } from "antd";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import img from "@/assets/17bg.jpg";
import TableCols from "./table";
import { axiosApi } from "@/services/general";
import { SearchOutlined } from "@ant-design/icons";
import UpdateTable from "./update_table";
import EditTable from "./edit_table";
import DetialTable from "./detial_table";
import { timeFormat, getGuid } from "@/utils/helper";
import useConfigStore from "@/stores/configStore";
const { Search } = Input;
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";

const suffix = (
  <SearchOutlined
    style={{
      fontSize: 16,
      color: "#1abc9c",
    }}
  />
);
const App = () => {
  const { MapSelf } = useConfigStore();
  const [list, setList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  let selectArr = [
    {
      placeholder: "处理状态",
      options: [
        {
          value: "已处理",
          label: "已处理",
        },
        {
          value: "未处理",
          label: "未处理",
        },
      ],
    },
  ];
  const handleChange = (value) => {
    console.log(`selected ${value}`);
  };
  // 搜索框方法
  const onSearch = (value) => {
    if (value) {
      GetAllList(1, `"室内编号"='${value}'`);
    } else {
      // 清空查询条件
      GetAllList(1);
    }
  };
  const GetAllList = async (page = 1, filter = '') => {
    const res = await axiosApi('/api/v1/Danger/GetAllListByTable', 'GET', {
      tableName: 'GDBH',
      state: 0
    })
    console.log("GetAllListByTable", res);
    console.log("res.data",getImgUrl(res.data[0].ImgObjectName));
    if (res && res.code === 1) {
      setList(res.data);

    }

  };

  const handleExport = () => {
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(list);
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '数据');

    // 导出Excel文件
    const wbout = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    
    saveAs(blob, '导出数据.xlsx');
  };

  // 分页变化处理
  const handleTableChange = (pagination) => {
    setCurrentPage(pagination.current);
    GetAllList(pagination.current);
  };

  useEffect(() => {
    GetAllList(1);
  }, []);
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.name === 'Disabled User',
      // Column configuration not to be checked
      name: record.name,
    }),
  };
  return (
    <div>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            margin: "10px 0",
            whiteSpace: 'nowrap'
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '10px' }}>
            <Input
              style={{ width: 180 }}
              suffix={suffix}
              placeholder="请输入编号,方阵编号"
              allowClear
              onChange={(e) => {
                // 当清空输入时自动刷新列表
                if (!e.target.value) {
                  onSearch('')
                }
              }}
              onPressEnter={(e) => onSearch(e.target.value)}
            />
            {selectArr.map((item, index) => {
              return (
                <Select
                  key={index}
                  placeholder={item.placeholder}
                  style={{
                    width: 120,
                  }}
                  onChange={handleChange}
                  options={item.options}
                />
              );
            })}
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-start', gap: '10px' }}>
            <MyButton type="primary" onClick={handleExport}>导出</MyButton>
            <MyButton type="primary">更多</MyButton>
          </div>
        </div>
      <Table
        rowSelection={{
          type: "checkbox",
          ...rowSelection,
        }}
        pagination={{
          defaultPageSize: 10,
          current: currentPage,
          total: total,
          showSizeChanger: true,
        }}
        onChange={handleTableChange}
        columns={TableCols(GetAllList)}
        dataSource={list}
        bordered
        size="small"
        scroll={{
          scrollToFirstRowOnChange: true,
          y: 340,
        }}
        onRow={(record) => {
          return {
            onClick: (event) => {
              // const template = [record.geometry[1], record.geometry[0]]
              // MapSelf.setView(template, 15);
            }
          }
        }
        }
      />
    </div>
  );
};
export default App;