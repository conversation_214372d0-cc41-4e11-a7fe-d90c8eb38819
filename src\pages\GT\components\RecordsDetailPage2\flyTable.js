import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, } from "antd";
import { timeFormat } from "@/utils/helper";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import TaskDetails from "@/pages/SI/components/flightTask/details"

const getTableTitle = (title) => {
    return (
        <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
    );
};
const flyTableCols = () => {

    const { setPage, } = useModel("pageModel");

    const statusMap = {
        0: { color: 'default', text: '未执行' },
        1: { color: 'processing', text: '执行中' },
        2: { color: 'success', text: '执行成功' },
        '-1': { color: 'error', text: '执行失败' }
    };

    return [
        {
            title: getTableTitle("任务名称"),
            dataIndex: "surveyTaskDesc",
            key: "surveyTaskDesc",
            align: "center",
        },
        {
            title: getTableTitle("航线名称"),
            dataIndex: "waylineName",
            key: "waylineName",
            align: "center",
        },
        {
            title: getTableTitle("状态"),
            // dataIndex: "Status",
            key: "status",
            align: "center",
            render: (_, record) => {
                const { color, text } = statusMap[record.status] || {};
                return text ? (
                    <Tag color={color}>{text}</Tag>
                ) : (
                    <span>-</span>
                );
            }
        },
        {
            title: getTableTitle("计划执行时间"),
            // dataIndex: "ExecTime",
            key: "execTime",
            align: "center",
            render: (_, record) => (
                <span>
                    {isEmpty(record.execTime)
                        ? "-"
                        : record.execTime
                    }
                </span>
            )
        },
        {
            title: getTableTitle("结束时间"),
            // dataIndex: "EndTime",
            key: "endTime",
            align: "center",
            render: (_, record) => (
                <span>
                    {isEmpty(record.endTime)
                        ? "-"
                        : record.endTime
                    }
                </span>
            )
        },
        {
            title: getTableTitle("执行反馈"),
            dataIndex: "execFeedback",
            key: "execFeedback",
            align: "center",
        },
        {
            title: getTableTitle("操作"),
            align: "center",
            render: (_, record) => (
                <Space size="middle">

                    <MyButton
                        style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
                        onClick={() => {
                            setPage(<TaskDetails baseInfo={record}></TaskDetails>);
                        }}
                    >
                        详情
                    </MyButton>

                </Space>
            ),
        }
    ];
};

export default flyTableCols;
