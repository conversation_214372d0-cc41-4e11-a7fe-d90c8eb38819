import { Get2 } from "@/services/general";
import { List, Card, Button } from "antd";
import { useState } from "react";
import { useEffect } from "react";
import { useModel } from "umi";
import ModelInfoPage from "./ModelInfoPage";
import ModelAddPage from "./ModelAddPage";
import LastPageButton from "@/components/LastPageButton";
import commonStyles from "@/pages/common.less"
const ModelListPage = () => {
  const [MList, setModelList] = useState([]);
  const { setPage, setModal, open, setOpen } = useModel("pageModel");
  useEffect(() => {
    const getModelData = async () => {
      const pst = await Get2("/api/v1/AI/GetModelList", {});
      setModelList(pst);
    };
    getModelData();
  }, []);
  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/AI/GetModelList", {});
    setModelList(pst);
  };

  const addForm = () => {
    setModal(<ModelAddPage refrush={refrush} />);
    setOpen(true);
  };
  const exr = (
    <Button
      className={commonStyles.addButton}
      type="primary"
      onClick={() => addForm()}
    >
      新建模型
    </Button>
  );
  return (
    <Card title={<LastPageButton title="AI模型" />}>
      <div className={commonStyles.my_scroll_y}>
        <List
          grid={{ gutter: 16, column: 4 }}
          dataSource={MList}
          renderItem={(item) => (
            <List.Item>
              <Card
                bodyStyle={{ height: 120.0 }}
                hoverable={true}
                title={item.AName}
                onClick={() =>
                  setPage(<ModelInfoPage model={item}></ModelInfoPage>)
                }
              >
                {item.Description}
              </Card>
            </List.Item>
          )}
        />
      </div>
    </Card>
  );
};

export default ModelListPage;
