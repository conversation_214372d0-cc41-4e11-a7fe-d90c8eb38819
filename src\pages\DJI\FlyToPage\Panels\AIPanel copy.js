
import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Form, DatePicker, Radio, Descriptions, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';

import locale from 'antd/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { HPost2 } from '@/utils/request';
import { useModel } from 'umi';
import { FJStart } from '../../DRCPage/Panels/RtmpChange';

const { TextArea } = Input;
const WayLineTypeList = ["库区巡查", "工程安全", "电厂线路", "一键起飞"]
const { RangePicker } = DatePicker;

const AIStartPanel = ({setOpen }) => {

  const [model, setModel] = useState({})
  const [cls, setCls] = useState([])
  const [modelList, setModelList] = useState({})
  const { DoCMD2 } = useModel('cmdModel');
  const {ifAI,setIfAI,setPNM1,pData}=useModel('pageModel');


  useEffect(() => {
    const getModelData = async () => {
      const pst = await Get2('/api/v1/AIModel/GetList', {});
      setModelList(pst);
    };
    getModelData();
  }, []);




  const getModelSelect = (modelList) => {
    const list = []
    if (isEmpty(modelList)) return list;

    modelList.forEach(e => {
      list.push(<Select.Option key={e.Guid} data={e} value={e.Guid}>{e.AName}</Select.Option>)
    });
    console.log('AIStartPanel', list);

    return list;
  }

  function sleep(time) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, time);
    });
  }

  const onSave = async (e) => {
    const device = JSON.parse(localStorage.getItem('device'))
    if (isEmpty(model)) {
      message.info("请先选择模型！");
      return;
    }

    if (isEmpty(cls)) {
      message.info("请先选择识别内容！");
      return;
    }

    //FJStart(device);
    
    const data = {
      method: 'StartVideo',
      data: {
        'sn': device.SN2,
        'm1': model.ObjectName,
        'cls': cls
      }
    }
    DoCMD2('thing/'+device.SN+'/ai', data)
    //FJStart(device);
    setOpen(false);
   await sleep(1000)
   pData.current.ifAI=true;
    //setIfAI(true);
  };


  const onChange2 = (values) => {
    const xx = modelList.find((item) => {
      return item.Guid === values;
    })
    console.log('onChange values of form: ', xx);
    setModel(xx);
  };


  const getAList = (m1) => {
    if (isEmpty(m1)) return <div></div>
    const list = []
    const arr = m1.AList.split(",");
    let nn = 0;
    arr.forEach(e => {
      list.push({
        label: e,
        value: nn,
        disabled: false,
      },);
      nn++;
    });
    return <Checkbox.Group options={list} onChange={(e) => setCls(e)} />
  }

  return <Form
    labelCol={{
      span: 4,
    }}

    wrapperCol={{
      span: 14,
    }}
    layout="horizontal"
    //disabled={componentDisabled}
    style={{
      maxWidth: 600,
    }}
  >

    <Form.Item label="选择模型">
      <Select
        onSelect={onChange2}>
        {getModelSelect(modelList)}
      </Select>
    </Form.Item>

    <Form.Item label="识别内容">
      {getAList(model)}
    </Form.Item>

    <Form.Item label={" "} colon={false}>
      <Button type="primary" onClick={onSave}>
        开始识别
      </Button>
    </Form.Item>
  </Form>

}



export default AIStartPanel;
