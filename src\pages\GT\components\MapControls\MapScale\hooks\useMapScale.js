import { useState, useRef, useCallback, useMemo } from 'react';
import { Cartesian2, EllipsoidGeodesic, defined, getTimestamp } from 'cesium';
import L from 'leaflet';
import { PREDEFINED_DISTANCES_METERS, MAX_BAR_WIDTH_PIXELS, UPDATE_THROTTLE_MS } from '../constants';

// 计算Cesium比例尺
const calculateCesiumScale = (viewer, geodesic) => {
  const scene = viewer.scene;
  const camera = viewer.camera;
  const canvas = viewer.canvas;
  const globe = scene.globe;

  if (!globe || !geodesic) {
    return { label: '', barWidth: 0 };
  }

  const canvasWidth = canvas.clientWidth;
  const canvasHeight = canvas.clientHeight;

  const pickRay1 = camera.getPickRay(
    new Cartesian2(Math.floor(canvasWidth / 2), canvasHeight - 1)
  );
  const pickRay2 = camera.getPickRay(
    new Cartesian2(1 + Math.floor(canvasWidth / 2), canvasHeight - 1)
  );

  const point1_3D = globe.pick(pickRay1, scene);
  const point2_3D = globe.pick(pickRay2, scene);

  if (!defined(point1_3D) || !defined(point2_3D)) {
    return { label: '', barWidth: 0 };
  }

  const point1_Cartographic = globe.ellipsoid.cartesianToCartographic(point1_3D);
  const point2_Cartographic = globe.ellipsoid.cartesianToCartographic(point2_3D);

  geodesic.setEndPoints(point1_Cartographic, point2_Cartographic);
  const metersPerPixel = geodesic.surfaceDistance;

  return calculateScale(metersPerPixel);
};

// 计算Leaflet比例尺
const calculateLeafletScale = (map) => {
  if (!map) {
    return { label: '', barWidth: 0 };
  }

  const bounds = map.getBounds();
  const centerLat = bounds.getCenter().lat;
  const metersPerPixel = 40075016.686 * Math.abs(Math.cos(centerLat * Math.PI / 180)) / Math.pow(2, map.getZoom() + 8);

  return calculateScale(metersPerPixel);
};

// 通用比例尺计算
const calculateScale = (metersPerPixel) => {
  if (metersPerPixel === 0) {
    return { label: '', barWidth: 0 };
  }

  let bestDistanceInMeters;
  for (let i = PREDEFINED_DISTANCES_METERS.length - 1; i >= 0; --i) {
    const currentDistance = PREDEFINED_DISTANCES_METERS[i];
    if (currentDistance / metersPerPixel < MAX_BAR_WIDTH_PIXELS) {
      bestDistanceInMeters = currentDistance;
      break;
    }
  }

  if (!bestDistanceInMeters) {
    if (PREDEFINED_DISTANCES_METERS[0] / metersPerPixel >= MAX_BAR_WIDTH_PIXELS) {
      return { label: '', barWidth: 0 };
    }
    bestDistanceInMeters = PREDEFINED_DISTANCES_METERS[0];
  }

  const label = bestDistanceInMeters >= 1000
    ? `${bestDistanceInMeters / 1000} km`
    : `${bestDistanceInMeters} m`;

  const barWidth = Math.round(bestDistanceInMeters / metersPerPixel);

  return { label, barWidth };
};

// 自定义Hook: useMapScale
export const useMapScale = (viewer) => {
  const [distanceLabel, setDistanceLabel] = useState('');
  const [barPixelWidth, setBarPixelWidth] = useState(0);
  const lastUpdateTimeRef = useRef(0);
  const geodesicRef = useRef(null);

  // 使用useMemo缓存计算函数
  const calculateScaleForViewer = useMemo(() => {
    if (viewer instanceof L.Map) {
      return () => calculateLeafletScale(viewer);
    }
    return () => calculateCesiumScale(viewer, geodesicRef.current);
  }, [viewer]);

  // 使用useCallback包装更新函数
  const updateScale = useCallback(() => {
    const currentTime = getTimestamp();
    if (currentTime < lastUpdateTimeRef.current + UPDATE_THROTTLE_MS) {
      return;
    }
    lastUpdateTimeRef.current = currentTime;

    const { label, barWidth } = calculateScaleForViewer();
    setDistanceLabel(label);
    setBarPixelWidth(barWidth);
  }, [calculateScaleForViewer]);

  return {
    distanceLabel,
    barPixelWidth,
    updateScale,
    geodesicRef,
  };
};