import { useEffect } from "react";
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
} from "@ant-design/icons";
import { Button, Menu } from "antd";
import { useModel } from "umi";

const items = [
  {
    key: "",
    icon: <PieChartOutlined />,
    label: "任务管理",
  },
  {
    key: "影像管理",
    icon: <DesktopOutlined />,
    label: "影像管理",
  },
  {
    key: "异常事件",
    icon: <ContainerOutlined />,
    label: "异常事件",
  },
];
const App = ({setPage,collapsed,setCollapsed}) => {
  const { pageData, setPageData } = useModel("pageModel");
  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  // 处理菜单点击
  function handleMenu(e) {
    setPage(e.key);
  };
  // 挂载时 默认打开第一个菜单
  useEffect(() => {
    setPage(items[0].key);
  }, []);
  return (
    <>
      {
        <div 
        style={{
          position: 'fixed',
          left: 0,
          top: 56,
          bottom: 0,
          width: collapsed ? 80 : 160,
          background: '#001529',
          zIndex: 1000,
          transition: 'width 0.2s',
        }}
        >
          <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{ 
              marginBottom: 16 ,
            }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <Menu
            defaultSelectedKeys={[items[0].key]} 
            selectedKeys={[pageData?.title]}
            defaultOpenKeys={["sub1"]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 56px)', // 100%可视高度减去按钮区域高度
              overflowY: 'auto'
            }}
          />
        </div>
      }
    </>
  );
  
};
export default App;
