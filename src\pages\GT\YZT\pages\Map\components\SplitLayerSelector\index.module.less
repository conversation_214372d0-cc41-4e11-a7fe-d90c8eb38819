.splitLayerSelector {
  position: absolute;
  width: 320px;
  // background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 990;
  font-size: 14px;
  transition: all 0.3s ease;
  
  &.left {
    left: 20px;
  }
  
  &.right {
    right: 20px;
  }

  // 折叠状态样式
  &.collapsed {
    .selectorHeader {
      border-bottom: none;
      border-radius: 8px;
    }
  }

  .selectorHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(24, 144, 255, 0.05);
    border-radius: 8px 8px 0 0;
    transition: border-radius 0.3s ease;

    .headerTitle {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #1890ff;
      
      :global(.anticon) {
        font-size: 16px;
      }
    }

    .headerActions {
      display: flex;
      align-items: center;
      gap: 4px;

      .collapseBtn,
      .closeBtn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 16px;
        color: #666;
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
          color: #333;
        }
      }

      .collapseBtn {
        font-size: 12px;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .selectorContent {
    padding: 16px;
    max-height: 500px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;

    .searchInput {
      margin-bottom: 12px;
      border-radius: 6px;
      
      :global(.ant-input) {
        border-color: rgba(0, 0, 0, 0.1);
        
        &:focus,
        &:hover {
          border-color: #1890ff;
        }
      }
    }

    .treeContainer {
      flex: 1;
      overflow-y: auto;
      max-height: 400px;

      .noResults {
        text-align: center;
        padding: 40px 20px;
        color: #999;
        font-size: 13px;
      }

      .splitLayerTree {
        :global(.ant-tree-node-content-wrapper) {
          width: 100%;
          
          &:hover {
            background: rgba(24, 144, 255, 0.04);
          }
          
          &:global(.ant-tree-node-selected) {
            background: rgba(24, 144, 255, 0.08);
          }
        }

        .splitLayerNode {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          padding: 2px 0;

          .nodeContent {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;

            .nodeTitle {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .nodeActions {
            display: flex;
            align-items: center;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;

            :global(.ant-btn) {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              
              &:hover {
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;
              }
            }
          }

          &:hover .nodeActions {
            opacity: 1;
          }
        }

        :global(.ant-tree-checkbox) {
          margin-right: 8px;
        }

        :global(.ant-tree-switcher) {
          width: 20px;
          height: 20px;
          line-height: 20px;
          
          :global(.ant-tree-switcher-icon) {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 滚动条样式
  .treeContainer::-webkit-scrollbar {
    width: 6px;
  }

  .treeContainer::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.04);
    border-radius: 3px;
  }

  .treeContainer::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    width: 280px;
    
    &.left {
      left: 10px;
    }
    
    &.right {
      right: 10px;
    }
  }
}

// 动画效果
.splitLayerSelector {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 折叠动画
.selectorContent {
  animation: contentSlideIn 0.3s ease-out;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
    padding-top: 16px;
    padding-bottom: 16px;
  }
}
