import MyMenu from "@/pages/GT/components/MyMenu";
import pages from "./panels/DroneRouter";
import { useState, useEffect } from "react";
import { PageProvider, useDronePage } from './DronePageManger';
import '@/pages/GT/style/antd-common.less'
import commonStyle from '@/pages/GT/style/common.less'
function droneManage() {
  return (
    <PageProvider>
      <Page />
    </PageProvider>
  );
}

function Page() {
  const { currentPage, setCurrentPage } = useDronePage();
  const renderPage = () => {
    return pages[currentPage] || pages["home"] || currentPage;
  };

  return (
    <div
    className={commonStyle.gt_back}
      style={{
        display: "flex",
        justifyContent: "flex-start",
        position: "relative",
      }}
    >
      <div>
        <MyMenu setCurrentPage={setCurrentPage}></MyMenu>
      </div>
      <div style={{ width: "100%",height: "100%" }}>{renderPage()}</div>
    </div>
  );
}
export default droneManage