
import {irColorType} from '@/utils/colorHelper'
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";
import { useModel } from "umi";

const IrSetColor = ()=>{
    const  device = JSON.parse(localStorage.getItem('device'));
        const {DoCMD,DoCMD2}=useModel('cmdModel');



       const CameraIRType=(value = 0)=> {
            let data={}
            data[device.Camera2]={"thermal_current_palette_style": value}
            let s = {}
            s.bid = getGuid();
            s.tid = getGuid();
           // s.gateway = sn
            s.data = data
            s.timestamp = dayjs().valueOf();
            DoCMD2(`thing/product/${device.SN}/property/set`, s)  
        }

    return (
        <div style={{width: 300,
            backgroundColor: 'white',
            position: 'absolute',
            zIndex: 99999,
            top: '310px',
            right: '8px',
            display:'flex',flexWrap:'wrap',justifyContent:"space-between"}}>
              {irColorType.map(item=>
                (
                <div style={{cursor:'pointer'}} onClick={()=>{CameraIRType(item.value)}}>
                  <img src={item.img} alt="" style={{width:55}}/>
                  <div style={{width:40,textAlign:'center',fontSize:'15px'}}>
                      {item.name}
                  </div>
              </div>

                )
              )}
    
        </div>
    )

}

export default  IrSetColor