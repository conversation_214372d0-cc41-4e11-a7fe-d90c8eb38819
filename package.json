{"private": true, "author": "", "scripts": {"dev": "umi dev", "build": "cross-env ENV_MODE=production umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "analyze": "cross-env ANALYZE=1 ENV_MODE=production umi build", "preview": "umi preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@jiaminghi/data-view-react": "^1.2.5", "@turf/turf": "^7.2.0", "agora-rtc-react": "^1.1.3", "ali-oss": "^6.22.0", "antd": "^5.24.9", "antd-style": "^3.7.1", "axios": "^1.9.0", "cesium": "^1.128.0", "compression-webpack-plugin": "^11.1.0", "dayjs": "^1.11.13", "dva": "^2.4.1", "echarts": "^5.6.0", "esri-leaflet": "^3.0.15", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "hls.js": "^1.6.2", "jswebrtc": "^1.0.0", "jszip": "^3.10.1", "leaflet": "^1.9.4", "leaflet-compass": "^1.5.6", "leaflet-kml": "^1.0.1", "leaflet-side-by-side": "^2.2.0", "lodash": "^4.17.21", "mathjs": "^14.4.0", "moment": "^2.30.1", "mqtt": "^5.12.0", "numjs": "^0.16.1", "panolens": "^0.12.1", "proj4": "^2.15.0", "qrcodejs2": "^0.0.2", "rc-virtual-list": "^3.18.5", "react-amap": "^1.2.8", "react-compare-slider": "^3.1.0", "react-file-viewer": "^1.2.1", "react-helmet-async": "^1.3.0", "react-keyboard-event-handler": "^1.5.4", "react-leaflet": "^4.2.1", "react-player": "^2.16.0", "react-use-websocket": "^4.13.0", "react-zoom-pan-pinch": "^3.7.0", "sm4util": "^1.0.5", "umi": "^4.4.10", "umi-cesium-plugin": "^1.0.6", "umi-request": "^1.4.0", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@umijs/lint": "^4.4.10", "@umijs/plugins": "^4.4.10", "cross-env": "^7.0.3", "prettier": "3.5.3", "typescript": "^5.8.3"}}