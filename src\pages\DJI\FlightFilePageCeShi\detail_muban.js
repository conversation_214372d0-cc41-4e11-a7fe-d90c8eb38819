import { isEmpty } from "@/utils/utils";
import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Descriptions, DatePicker, Radio, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';
import { useState } from "react";


const DetailForm=({record})=>{
    const [data,setData]=useState(record);

    const getDataItem=(title,field,span)=>{
        if(isEmpty(span)) span=1;
      return  <Descriptions.Item label={title} span={span}>{data[field]}</Descriptions.Item>
    }
    
    const getPanel=()=>{
        const list=[]
        //list.push(getDataItem('单位','FDepartment'));
        {colStr}
        return <Card title={'数据详情'}><Descriptions style={{ textAlign: 'center', padding: 12.0  }} column={2}>
               {list} 
    </Descriptions></Card>
    }
       
    return getPanel();
}

export default DetailForm;