.map-data-directory {
    height: 100%;
    
    .directory-sider {
      // background: #fff;
      border-right: 1px solid #f0f0f0;
      
      .sider-header {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        
        h2 {
          margin: 0;
        }
      }
      
      .directory-tree {
        padding: 16px;
        overflow: auto;
        height: calc(100% - 64px);
        
        .tree-node-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          
          .tree-node-actions {
            display: none;
            margin-left: 8px;
          }
          
          &:hover .tree-node-actions {
            display: flex;
          }
        }
      }
    }
    
    .directory-content {
      padding: 24px;
      // background: #fff;
      
      .empty-content {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
        // color: #999;
        font-size: 16px;
      }
      
      .folder-detail, .service-detail {
        padding: 16px;
        // background: #fafafa;
        border-radius: 4px;
        
        h2 {
          margin-top: 0;
        }
        
        p {
          margin-bottom: 16px;
        }
      }
    }
  }