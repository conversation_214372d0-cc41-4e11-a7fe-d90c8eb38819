import { useState, useEffect, useRef } from 'react';
import { message, Button, Select } from 'antd';
import { Cesium } from "umi";
import { axiosApi } from '@/services/general';
import { useModel } from 'umi';
import { queryPage2 } from '@/utils/MyRoute';
import useSurroundingRouteHooks from './hooks/SurroundingRouteHooks';
import JSZip from "jszip";

const WayLine3DPage = ({ wayLineData }) => {
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    let { } = useSurroundingRouteHooks();
    // 页面载入
    useEffect(() => {

    }, []);
    return (
        <div style={{ height: '100%', padding: 12, background: '#ffffff', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <div style={{ width: '100%', height: '100%' }} >
                <div style={{ height: '100%', width: '100%', position: 'relative' }} id="cesisss"></div>
            </div>

        </div>
    )
};

export default WayLine3DPage;