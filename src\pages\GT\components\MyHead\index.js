import React, { useEffect, useState, useRef } from 'react';
import { UserOutlined, LogoutOutlined } from '@ant-design/icons';
import styles from './MyHead.less';
import { useModel, history } from 'umi';
import { DownOutlined } from '@ant-design/icons';
import useConfigStore from '@/stores/configStore';

const Nav = ({ headList, handlePageChange }) => {
  const headerRef = useRef(null);
  const { systemInfos, setHeaderHeight } = useConfigStore();
  const { currentPage, setCurrentPage, setPageData } = useModel('pageModel');
  const savedPage = localStorage.getItem('currentPage');
  const [headTitle, setheadTitle] = useState(savedPage);
  const IsSIJump = localStorage.getItem('IsSIJump');

  useEffect(() => {
    if (headerRef.current) {
      //获得并传送导航栏高度
      const observer = new ResizeObserver(entries => {
        for (let entry of entries) {
          setHeaderHeight(headerRef.current?.getBoundingClientRect()?.height);
        }
      });
      observer.observe(headerRef.current);
    }
  }, [headerRef.current]);

  const handleHeadClick = key => {
    handlePageChange(key);
    setCurrentPage(key);
  };
  useEffect(() => {
    if (headList && headList.length > 0) {
      handleHeadClick(headList[0]?.key);
    }
  }, []);

  function handleGoHome() {
    if (history.location.pathname.includes('SI')) {
      history.push('/SI/index');
    } else {
      history.push('/HOME/index');
    }
    setPageData(null);
  }
  function exit() {
    localStorage.clear();
    history.push('/HOME/login?system=', {});
    return;
  }

  return (
    <div ref={headerRef}>
      <div id={styles.MyHead}>
        <div className={styles.headIconBox}>
          <div className={styles.headIcon}></div>
          <div className={styles.headIcon2}></div>
        </div>
        <div className={styles.headTitle}>{headTitle ? headTitle : null}</div>
        <div className={styles.headContent}>
          {headList?.map(item => (
            <div key={item.key} className={styles.menuItemWrapper}>
              <div
                className={`${styles.headContentItem} ${currentPage === item.key ? styles.active : ''}`}
                onClick={() => handleHeadClick(item.key)}
                role="button"
                tabIndex={0}
              >
                {item.label}
              </div>
            </div>
          ))}
        </div>
        {IsSIJump !== 'true' && (
          <div className={styles.headSetting}>
            {/* home 按钮 */}
            <div className={styles.headSettingItem} title='回到首页'>
              <button onClick={handleGoHome} className={styles.headBtn}></button>
            </div>

            {/* 管理员 */}
            <div className={styles.headSettingItem}>
              <UserOutlined />
              <span className={styles.headSettingItem_text}>管理员</span>
            </div>

            {/* 退出 */}
            <div className={styles.headSettingItem} onClick={exit}>
              <LogoutOutlined />
              <span className={styles.headSettingItem_text}>退出</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Nav;
