import styles from "./index.less";
import React, { useState, useEffect, useRef, useMemo } from "react";
import { Badge, Progress, Row, Col, message } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import useConfigStore from "@/stores/configStore";
import { getBodyH, isEmpty } from "@/utils/utils";
import { getGuid, isIndividual, timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import { useLocation, history, useModel } from "umi";
import useContentStore from "@/pages/GT/DZJC/store";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import DevicePage from "@/pages/GT/DZJC/pages/dataAcquisition/DevicePage";
const AirportsListPanel = ({ data, route, onVideoClick, map }) => {
  const [airportsData, setAirportsData] = useState(data);
  const [deviceList, setDeviceList] = useState([]);
  const { waylineData } = useContentStore();

  useEffect(() => {
    if (airportsData) {
      const sortedData = [...airportsData].sort(
        (a, b) => b.IfOnLine - a.IfOnLine
      ); //根据设备是否在线排序
      setDeviceList(sortedData);
    }
  }, [airportsData]);

  const enrichedDeviceList = useMemo(() => {
    if (!deviceList.length || !waylineData?.wayline) return deviceList;

    const snMap = {};
    waylineData.wayline.forEach((item) => {
      if (!snMap[item.SN]) snMap[item.SN] = [];
      snMap[item.SN].push(item);
    });

    return deviceList.map((device) => ({
      ...device,
      waylineData: snMap[device.SN] || [], //将设备的sn与航线数据进行合并
    }));
  }, [deviceList, waylineData]);

  const handleMouseEnter = () => {
    if (map) {
      map.dragging.disable(); // 禁用地图拖动
      map.scrollWheelZoom.disable(); // 禁用地图缩放
    }
  };

  const handleMouseLeave = () => {
    if (map) {
      map.dragging.enable(); // 启用地图拖动
      map.scrollWheelZoom.enable(); // 启用地图缩放
    }
  };

  if (!enrichedDeviceList) return null;

  return (
    <div>
      <div
        className={styles["airports-list-panel"]}
        onMouseLeave={handleMouseLeave}
        onMouseEnter={handleMouseEnter}
      >
        <div className={styles["airports-head"]}></div>
        {enrichedDeviceList &&
          enrichedDeviceList.map((item, index) => (
            <AirportsListItem
              key={item.ID}
              item={item}
              styles={styles}
              onVideoClick={onVideoClick}
            />
          ))}
      </div>
    </div>
  );
};

const AirportsListItem = ({ item, styles, onVideoClick }) => {
  const { MapSelf } = useConfigStore();
  const { page, setPage } = useModel("pageModel");
  const [showPanel, setShowPanel] = useState(() => {
    const stored = sessionStorage.getItem(`showPanel_${item.ID}`);
    return stored === "true"; //为每个开关存储状态
  });

  const toggleShowPanel = () => {
    const newShow = !showPanel;
    setShowPanel(newShow);
    sessionStorage.setItem(`showPanel_${item.ID}`, newShow.toString());
  };

  const getDCColor = (dc) => {
    if (dc < 30) return "red";
    if (dc < 60) return "orange";
    return "#00ff00";
  };

  const getColor = (d1) => {
    if (!d1.OsdData) return "rgba(255, 0, 0, 1)"; // Red
    if (d1.OsdData.mode_code == 0) return "rgba(0, 255, 0, 1)"; // Green
    return "rgba(255, 165, 0, 1)"; // Orange
  };
  const changeAlpha = (color, alpha) => {
    const parts = color.split(",");
    const rgbValues = parts.slice(0, 3).join(",");
    return rgbValues + `, ${alpha})`;
  };

  function getStatus(value) {
    switch (value) {
      case 0:
        return "空闲中";
      case 1:
        return "现场调试";
      case 2:
        return "远程调试";
      case 3:
        return "固件升级中";
      case 4:
        return "作业中";
      case true:
        return "在线";
      case false:
        return "离线";
      default:
        return "设备未注册";
    }
  }

  const handleViewVideo = (e, value) => {
    if (onVideoClick) {
      onVideoClick(value);
    }
  };
  const handleSetCenter = (item) => {
    let latlng = [item?.Lat, item?.Lng];
    if (!latlng[0] || !latlng[1]) return;
    if (MapSelf) {
      MapSelf.setView(latlng, 18);
    }
  };

  const handleExecuteSmartSurveyTask = async (record) => {
    try {
      const res = await axiosApi(
        `/api/v1/Survey/ExecuteSmartSurveyTask`,
        "POST",
        { surveyTaskID: record.SurveyTaskId }
      );
      if (res.code === 1) {
        message.success(res.data.message);
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log("error", error);
      message.warning(error);
    }
  };

  const renderWayline = (item) => {
    if (!showPanel) return null;
    return (
      <div className={styles["airports-item-detail"]}>
        <div className={styles.routesList}>
          {item.waylineData && item.waylineData.length > 0 ? (
            item.waylineData.map((route, index) => (
              <div
                key={index}
                className={styles.routeItem}
                onClick={(e) => e.stopPropagation()}
              >
                <div className={styles.WayLineName_con}>
                  <span className={styles.WayLineName_icon}></span>
                  <span>{route.WayLineName}</span>
                </div>
                <div className={styles.routeDetail}>
                  <span>创建时间:</span>
                  <span>{timeFormat(route.CreateTime)}</span>
                </div>
                <div className={styles.operateBtns}>
                  <MyButton>查看航线</MyButton>
                  <MyButton onClick={() => handleExecuteSmartSurveyTask(route)}>
                    立即执行
                  </MyButton>
                </div>
              </div>
            ))
          ) : (
            <div className={styles.noRoutes}>
              <p>暂无航线任务</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className={styles["airports-item"]}>
        <div className={styles["airports-item-left"]}>
          <div className={styles["airports-self-icon"]}></div>
          <div
            className={styles["airports-name"]}
            onClick={() => {
              handleSetCenter(item);
            }}
          >
            {item.DName}
          </div>
        </div>

        <div className={styles["airports-item-right"]}>
          <div
            style={{
              color: getColor(item),
              background: changeAlpha(getColor(item), 0.2),
              boxShadow: `inset 0 0 4px 0px ${changeAlpha(getColor(item), 0.5)}`,
            }}
            className={styles["airports-status"]}
          >
            {item.OsdData ? getStatus(item.OsdData.mode_code) : "离线"}
          </div>
          {item.IfOnLine ? (
            // <div style={{color: getDCColor(item.OsdData?.drone_charge_state?.capacity_percent)}}>
            //   电量:
            //   {item.OsdData?.drone_charge_state?.capacity_percent}%
            // </div>
            <div>
              {isEmpty(item.OsdData) ? null : (
                <span style={{ marginLeft: 8.0 }}>
                  <Progress
                    strokeColor={getDCColor(
                      isIndividual(item)
                        ? item.OsdData.capacity_percent
                        : item.OsdData.drone_charge_state?.capacity_percent
                    )}
                    steps={6}
                    percent={
                      isIndividual(item)
                        ? item.OsdData.capacity_percent
                        : item.OsdData.drone_charge_state?.capacity_percent
                    }
                    format={(percent) => (
                      <span style={{ color: "white", fontSize: 12.0 }}>
                        {percent + "%"}
                      </span>
                    )}
                    size="small"
                  />
                </span>
              )}
            </div>
          ) : (
            <div className={styles["airports-null"]}>空白占位</div>
          )}
          <div
            className={styles["airports-location-icon"]}
            title="设备详情"
            onClick={() => {
              localStorage.setItem("device", JSON.stringify(item));
              setPage({
                title: "设备详情",
                path: "/gt/DZJC/devicePage",
                children: <DevicePage />,
              });
              //  history.push(`/gt/DZJC/devicePage`, { device: item });
            }}
          ></div>
          <div
            className={styles["airports-camera-icon"]}
            title="查看视频"
            onClick={(e) => {
              handleViewVideo(e, item);
            }}
          ></div>
          <div onClick={toggleShowPanel}>
            {showPanel ? <UpOutlined /> : <DownOutlined />}
          </div>
        </div>
      </div>
      {renderWayline(item)}
    </div>
  );
};

export default AirportsListPanel;
