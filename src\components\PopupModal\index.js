import React, { useRef, useEffect } from "react";
import "./Modal.less";

const Modal = ({ isOpen, onClose, children }) => {
  const modalRef = useRef(null);

  const handleOverlayClick = (event) => {
    if (modalRef.current && event.target === modalRef.current) {
      onClose();
    }
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.keyCode === 27) {
        onClose();
      }
    };

    if (isOpen) {
      window.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="Popup-modal-overlay" ref={modalRef} onClick={handleOverlayClick}>
      <div className="Popup-modal-content">
        <button className="Popup-close-button" onClick={onClose}>
          &times;
        </button>
        {children}
      </div>
    </div>
  );
};

export default Modal;
