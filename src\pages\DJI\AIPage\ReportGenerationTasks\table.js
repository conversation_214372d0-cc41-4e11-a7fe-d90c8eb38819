import { Space, Tag, message, Modal, Switch } from "antd";
import { downloadFile, isEmpty } from "@/utils/utils";
import "./table.css";
import { timeFormat, timeFormat2 } from "@/utils/helper";
import { HGet2, HPost2 } from "@/utils/request";
import { Post2, Get2 } from "@/services/general";
import Detial from "./detail";

const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};

const { confirm } = Modal;
const updateCron = async (record, refrush) => {
  if (isEmpty(record)) return;
  const xx = await HGet2("/api/v1/AIDocJob/ChangeState?id=" + record.ID);
  if (!isEmpty(xx.err)) {
    message.open({ type: "error", content: "错误:" + xx.err });
  } else {
    message.open({ type: "success", content: "更新成功!" });
    refrush();
  }
};

const deleteCronJob = async (record, refrush) => {
  const xx = await Post2("/api/v1/AIDocJob/Delete", record);
  if (!isEmpty(xx.err)) {
    message.open({ type: "error", content: "错误:" + xx.err });
  } else {
    message.open({ type: "success", content: "删除成功!" });
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除任务",
    content: "确定删除该任务吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deleteCronJob(record, refrush);
    },
    onCancel() {},
  });
};

const TableCols = (refrush, showMap, setPage, power, deviceLists) => {
  return [
    {
      title: getTableTitle("报告名称"),
      dataIndex: "JobName",
      key: "JobName",
      align: "center",
    },

    {
      title: getTableTitle("航线名称"),
      dataIndex: "WayLineName",
      key: "WayLineName",
      align: "center",
    },

    {
      title: getTableTitle("AI模型"),
      dataIndex: "ModelName",
      key: "ModelName",
      align: "center",
    },

    {
      title: getTableTitle("执行设备"),
      dataIndex: "SN",
      key: "SN",
      align: "center",
      render: (record) => (
        <Space size="middle">
          {deviceLists?.map((device) => {
            if (record === device.SN) {
              return device.DName;
            }
          })}
        </Space>
      ),
    },

    {
      title: getTableTitle("执行时间"),
      align: "center",
    },
    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <Tag>
            <a onClick={() => showDeleteConfirm(record, refrush)}>删除</a>
          </Tag>
          <Tag>
            <a onClick={() => power(record)}>编辑</a>
          </Tag>
          <Tag>
            <a
              onClick={() => {
                showMap();
                // if(record?.JobUrl){
                //   setPage(<Detial></Detial>)
                // }else{
                //   return
                // }
              }}
            >
              详情
            </a>
          </Tag>
        </Space>
      ),
    },
  ];
};

export default TableCols;
