import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";
import img1 from "./img/mmexport1612236920741.jpg";
import img2 from "./img/mmexport1612236924323.jpg";
import { useEffect, useRef } from "react";
export default function App() {
  let itemX = useRef();
  let itemX2 = useRef();

  let itemY = useRef();
  let itemY2 = useRef();
  let winHeight = useRef();
  winHeight.current =
    window.innerHeight ||
    document.documentElement.clientHeight ||
    document.body.clientHeight;
  useEffect(() => {
    itemX.current.addEventListener("wheel", (e) => {
      whellX(e);
      e.preventDefault();
    });

    let scale = 1;
    function whellX(e) {
      if (e.deltaY > 0) {
        scale += 0.05;
       // itemX.current.style.transform = `scale(${scale})`;
        itemX2.current.style.transform = `scale(${scale})`;
      } else {
        scale -= 0.05;
       // itemX.current.style.transform = `scale(${scale})`;
        itemX2.current.style.transform = `scale(${scale})`;
      }
    }
    function whellY(e) {
      if (e.deltaY > 0) {
        scale += 0.05;
        //itemY.current.style.transform = `scale(${scale})`;
        itemY2.current.style.transform = `scale(${scale})`;
      } else {
        scale -= 0.05;
        //itemY.current.style.transform = `scale(${scale})`;
        itemY2.current.style.transform = `scale(${scale})`;
      }
    }
  }, []);
  return (
    <div style={{ width: "100%", flexGrow: 2 }}>
      <ReactCompareSlider
        itemOne={
          <ReactCompareSliderImage ref={itemX} src={img1} alt="Image one" />
        }
        itemTwo={
          <ReactCompareSliderImage
            ref={itemX2}
            src={img2}
            alt="Image two"
            style={{
              backgroundColor: "white",
              backgroundImage: `
            linear-gradient(45deg, #ccc 25%, transparent 25%),
            linear-gradient(-45deg, #ccc 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, #ccc 75%),
            linear-gradient(-45deg, transparent 75%, #ccc 75%)`,
              backgroundSize: `20px 20px`,
              backgroundPosition: `0 0, 0 10px, 10px -10px, -10px 0px`,
            }}
          />
        }
        style={{ width: "100%", height: `${winHeight.current / 2.3}px` }}
      />
    
    </div>
  );
}
