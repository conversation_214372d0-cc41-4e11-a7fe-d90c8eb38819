// 滚动按钮配置
@scroll-button-width: 36px;
@scroll-button-padding: 8px;
@timeline-padding: 24px;
@item-gap: 16px;
// 月份菜单空间配置
@month-menu-space: 150px;
@month-menu-max-height: (@month-menu-space - 20px); // 月份菜单最大高度，预留边距

// 滚动按钮样式 mixin
.scrollButtonMixin() {
  position: absolute;
  width: @scroll-button-width;
  height: @scroll-button-width;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.8);
  border-radius: 50%;
  cursor: pointer;
  z-index: 5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease,
    background-color 0.2s ease,
    transform 0.2s ease;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  backdrop-filter: blur(4px);
  color: #475569;

  &:hover {
    background-color: rgba(239, 246, 255, 1);
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-50%) scale(1.05);
    color: #3b82f6;
  }

  &.visible {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  &[disabled] {
    opacity: 0.3 !important;
    cursor: not-allowed;
    pointer-events: none;
    &:hover {
      background-color: rgba(255, 255, 255, 0.9);
      border-color: rgba(203, 213, 225, 0.8);
      transform: translateY(-50%) scale(1);
      color: #475569;
    }
  }
}

.temporalComparison {
  position: fixed;
  bottom: 70px !important;
  left: 50% !important;
  transform: translateX(-50%);
  // background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
  // backdrop-filter: blur(12px);
  border-radius: 16px;
  // box-shadow:
  //   0 8px 32px rgba(0, 0, 0, 0.12),
  //   0 2px 8px rgba(0, 0, 0, 0.08),
  //   inset 0 1px 0 rgba(255, 255, 255, 0.8);
  // border: 1px solid rgba(255, 255, 255, 0.6);
  min-width: 100px;
  max-width: 75vw;
  z-index: 1000;
  overflow: visible;
  transition: padding 0.3s ease;
  position: relative;

  // 滚动按钮样式 - 相对于整个组件定位
  .scrollButtonLeft {
    .scrollButtonMixin();
    left: @scroll-button-padding;
    // 调整垂直位置，对齐时间轴中心
    top: 50%;
    transform: translateY(-50%);
  }

  .scrollButtonRight {
    .scrollButtonMixin();
    right: @scroll-button-padding;
    // 调整垂直位置，对齐时间轴中心
    top: 50%;
    transform: translateY(-50%);
  }

  .temporalContent {
    position: relative;
    z-index: 1;
    overflow: visible;
    // 为滚动按钮预留空间
    padding: 0 (@scroll-button-width + @scroll-button-padding);

    .temporalLoading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 32px (@timeline-padding * 2);
      color: #64748b;
      font-size: 15px;
      font-weight: 500;
      min-height: 60px;
    }
  }

  .temporalTimeline {
    position: relative;
    display: flex;
    align-items: center;
    overflow: visible; // 确保时间轴容器不裁剪内容

    // 主时间轴线
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(
        90deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(59, 130, 246, 0.6) 20%,
        rgba(59, 130, 246, 0.8) 50%,
        rgba(59, 130, 246, 0.6) 80%,
        rgba(59, 130, 246, 0.1) 100%
      );
      border-radius: 2px;
      transform: translateY(-50%);
      z-index: 1;
      box-shadow: 0 1px 3px rgba(59, 130, 246, 0.2);
    }

    // 滚动容器
    .scrollWrapper {
      overflow-x: auto;
      overflow-y: unset;
      scrollbar-width: none; // Firefox
      &::-webkit-scrollbar {
        display: none; // Chrome, Safari, Opera
      }
      scroll-behavior: smooth;
      position: relative;
      z-index: 2;
      display: flex;
      flex: 1;
      padding: 0 @timeline-padding;
      // 确保滚动容器不裁剪弹出内容
      clip-path: none;
      // 强制不裁剪任何内容
      contain: none;
      // 为月份菜单预留上方空间（与其他容器保持一致）
      padding-top: @month-menu-space;
      margin-top: -@month-menu-space;
    }

    .timelineContainer {
      display: flex;
      align-items: center;
      gap: @item-gap;
      position: relative;
      z-index: 3;
      min-height: 60px;
      width: max-content;
      padding: 0;
      overflow: visible;

      .timelineYearGroup {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex-shrink: 0;
        z-index: 4;
        // 确保年份组不被父容器裁剪
        overflow: visible;

        &.expanded {
          z-index: 100;
          overflow: visible;
        }

        .timelineYear {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 90px;
          height: 48px;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border: 2px solid rgba(226, 232, 240, 0.8);
          border-radius: 24px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);

          .yearContent {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 4px;

            .yearLabel {
              font-size: 14px;
              font-weight: 700;
              color: #334155;
              white-space: nowrap;
              letter-spacing: -0.025em;
              transition: all 0.3s ease;
            }
          }

          &:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%);
            border-color: rgba(59, 130, 246, 0.6);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
              0 8px 20px rgba(59, 130, 246, 0.15),
              0 3px 8px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.9);

            .yearContent {
              color: #3b82f6;
              transform: scale(1.1);
            }

            .yearContent .yearLabel {
              color: #1e40af;
            }
          }

          &.selected {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow:
              0 6px 16px rgba(59, 130, 246, 0.25),
              0 2px 6px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);

            .yearContent {
              color: #1e40af;
              transform: scale(1.1);
            }

            .yearContent .yearLabel {
              color: #1e40af;
              font-weight: 800;
            }
          }
        }

        .timelineMonths {
          position: absolute;
          bottom: 58px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          flex-direction: column;
          gap: 4px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
          backdrop-filter: blur(12px);
          border: 1px solid rgba(226, 232, 240, 0.8);
          border-radius: 12px;
          padding: 12px;
          box-shadow:
            0 8px 24px rgba(0, 0, 0, 0.12),
            0 4px 8px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
          min-width: 120px;
          max-height: @month-menu-max-height;
          z-index: 9999;
          animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          visibility: visible !important;
          opacity: 1 !important;
          overflow-y: auto;
          scroll-behavior: smooth;
          // 自定义滚动条样式
          scrollbar-width: thin;
          scrollbar-color: rgba(59, 130, 246, 0.3) transparent;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 2px;

            &:hover {
              background: rgba(59, 130, 246, 0.5);
            }
          }

          // 连接线（指向下方）
          &::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid rgba(226, 232, 240, 0.8);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid rgba(255, 255, 255, 0.98);
          }

          .timelineMonth {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 8px;
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
            border: 1px solid rgba(226, 232, 240, 0.6);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            flex-shrink: 0;

            // 悬停时的背景动画
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
              transition: left 0.5s ease;
            }

            .monthContent {
              display: flex;
              align-items: center;
              gap: 10px;
              width: 100%;
              position: relative;
              z-index: 1;

              .monthLabel {
                font-size: 13px;
                font-weight: 600;
                color: #475569;
                flex: 1;
                transition: all 0.3s ease;
                letter-spacing: -0.025em;
              }
            }

            &.clickable:hover {
              background: linear-gradient(135deg, rgba(239, 246, 255, 0.95), rgba(219, 234, 254, 0.9));
              border-color: rgba(59, 130, 246, 0.4);
              transform: translateX(2px) scale(1.02);
              box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.15),
                0 2px 4px rgba(0, 0, 0, 0.08);

              &::before {
                left: 100%;
              }

              .monthContent {
                color: #3b82f6;
                transform: scale(1.1);
              }

              .monthContent .monthLabel {
                color: #1e40af;
                font-weight: 700;
              }
            }

            &.selected {
              background: linear-gradient(135deg, rgba(219, 234, 254, 0.9), rgba(191, 219, 254, 0.8));
              border-color: #3b82f6;
              box-shadow:
                0 3px 8px rgba(59, 130, 246, 0.2),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);

              .monthContent {
                color: #1e40af;
                transform: scale(1.1);
              }

              .monthContent .monthLabel {
                color: #1e40af;
                font-weight: 700;
              }
            }
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .temporalComparison {
    left: 10px;
    right: 10px;
    transform: none;
    min-width: auto;
    max-width: none;

    .temporalTimeline {
      gap: 16px;

      .timelineYearGroup {
        min-width: 80px;

        .timelineYear {
          min-width: 80px;
          padding: 8px 12px;

          .yearContent .yearLabel {
            font-size: 12px;
          }
        }

        .timelineMonths .timelineMonth {
          min-width: 80px;
          padding: 6px 8px;

          .monthContent .monthLabel {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .temporalComparison {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);

    .temporalTimeline {
      .timelineYearGroup {
        .timelineYear {
          background: rgba(55, 65, 81, 0.8);
          border-color: rgba(75, 85, 99, 0.6);

          .yearContent {
            .yearLabel {
              color: #d1d5db;
            }
          }

          &.clickable:hover {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.4);

            .yearContent {
              color: #60a5fa;
            }

            .yearContent .yearLabel {
              color: #93c5fd;
            }
          }

          &.selected {
            background: rgba(59, 130, 246, 0.2);
            border-color: #60a5fa;

            .yearContent {
              color: #60a5fa;
            }

            .yearContent .yearLabel {
              color: #93c5fd;
            }
          }
        }

        .timelineMonths {
          border-top-color: rgba(75, 85, 99, 0.6);

          .timelineMonth {
            background: rgba(51, 65, 85, 0.8);
            border-color: rgba(71, 85, 105, 0.6);

            .monthContent {
              .monthLabel {
                color: #cbd5e1;
              }
            }

            &.clickable:hover {
              background: rgba(59, 130, 246, 0.15);
              border-color: rgba(59, 130, 246, 0.4);

              .monthContent {
                color: #60a5fa;
              }

              .monthContent .monthLabel {
                color: #93c5fd;
              }
            }

            &.selected {
              background: rgba(59, 130, 246, 0.2);
              border-color: #60a5fa;

              .monthContent {
                color: #60a5fa;
              }

              .monthContent .monthLabel {
                color: #93c5fd;
              }
            }
          }
        }
      }
    }
  }
}
