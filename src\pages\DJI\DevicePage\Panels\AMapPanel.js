import React, { useState } from 'react';
import { <PERSON><PERSON>,<PERSON><PERSON><PERSON> } from 'react-amap';
import icon2 from '@/assets/icons/feiji.png'
import icon1 from '@/assets/icons/device.png'
import icon3 from '@/assets/icons/red.png'
import { isEmpty } from '@/utils/utils';
import { useModel } from 'umi';
import DevicePage from '../../DevicePage';
import { Get2 } from '@/services/general';
import { HPost2 } from '@/utils/request';
import { Button, Popover } from 'antd';
import { IsAQQ } from '@/pages/Maps/helper';
import AMapContainer from '@/pages/Maps/AMap/MapContainer';
const DeviceAMapPanel = ({ h1, sn, device }) => {
  const { gps } = useModel('gpsModel');
  const [position, setPosition] = useState({ lat: 31.0352923126208, lng: 103.57032766578237 })
  const [ifP, setIfP] = useState(false)
  const [open, setOpen] = useState(false)

  const FlyToPoint = (sn, lat, lng, h1) => {
    const xx = IsAQQ(lat, lng)

    if (!xx) {
      message.info("该点不在安全区，禁止手动飞行！");
      return;
    }
    Get2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
  }

  const LookToPoint = (sn, lat, lng) => {
    const data = {
      "locked": true,
      "payload_index": "80-0-0",
      "latitude": lat,
      "longitude": lng,
      "height": 530
    }
    HPost2("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_look_at", data)
  }

  const content = <div> <Button type='primary' style={{ margin: 4.0 }} onClick={(e) => {
    FlyToPoint(sn, position.lat, position.lng, 530);
    setOpen(false);
    e.stopPropagation();
  }}>飞向该点</Button>
    <Button style={{ margin: 4.0 }} onClick={(e) => {
      LookToPoint(sn, position.lat, position.lng);
      setOpen(false);
      e.stopPropagation();
    }}>朝向该点</Button>
    <Button style={{ margin: 4.0 }} onClick={(e) => {
      setPosition({})
      setIfP(false);
      setOpen(false);
      e.stopPropagation();
    }}>重新选点</Button></div>

  const getPanel = () => {
   return <Marker visiable={true} position={position} >

      <Popover content={content} open={open} >
        <img onClick={() => { setOpen(true) }} height={36.0} width={36.0} src={icon3}></img>
      </Popover>
    </Marker>
  }

  const getLine = () => {
    const xx=[[device.Lng,device.Lat],[position.longitude,position.latitude]];
    return <Polyline  
      strokeColor={'red'} path={xx} />
  }

  const onPClick=(e)=>{
    if(ifP) return;
    setPosition({ latitude: e.lnglat.Q, longitude: e.lnglat.R });
    setIfP(true); 
  }
  const getMarker = (lat, lng, icon) => {
    return <Marker
      visiable={true}
      position={{ latitude: lat, longitude: lng }}
    >
      <img height={48.0} width={48.0} src={icon}></img>
    </Marker>
  }


  if (isEmpty(device)) return <div />

  const getBody=()=>{
    const list=[]
    list.push(getLine())
    list.push(getMarker(device.Lat, device.Lng, icon1))
    list.push(gps[2] > device.Height ? getMarker(gps[0], gps[1], icon2) : null)
    list.push(getPanel())
    return list;
  }
  return (
    <div style={{ height: h1, width: '100%' }}>
         <AMapContainer
      center={{latitude: device.Lat, longitude: device.Lng}}
      child={getBody()} 
      eve={{ 'click': onPClick}}
    />

     </div>
  );
}
export default DeviceAMapPanel;

