import React, { useEffect, useRef } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import useMapStore from "@/stores/useMapStore";

export default function WarningPopup({ map }) {
  // 用来弹出事件警告
  const { connectMapMqtt, disconnectMapMqtt, messages } = useMapStore();
  const popupsRef = useRef({}); // 存储所有弹窗实例
  const mapRef = useRef(map);

  useEffect(() => {
    connectMapMqtt();
    return () => {
      disconnectMapMqtt();
    };
  }, [connectMapMqtt, disconnectMapMqtt]);

  useEffect(() => {
    mapRef.current = map;
  }, [map]);

  useEffect(() => {
    if (messages && messages.length > 0 && mapRef.current) {
      messages.forEach((element) => {
        openPopup(element);
      });
    }
  }, [messages]);

  function openPopup(data) {
    
    let { id, latitude, longitude, statusText } = data;
    let latLng = [latitude, longitude];
    // 关闭旧弹窗（如果已存在）
    if (popupsRef.current[id]) {
      mapRef.current.removeLayer(popupsRef.current[id]);
    }

    let popupContent = `
      <div class="popup-content" style="display:flex;justify-content:center;align-items:center;cursor:pointer">
          <div class="fly-to-point" style="padding:5px;border:1px solid #ccc">飞到此处</div>
          <div class="clear-mark" style="padding:5px;border:1px solid #ccc;">移除标记</div>
          <div class="status-text" style="padding:5px;border:1px solid #ccc;">${statusText}</div>
      </div>`;

    // 创建弹出框
    if (latLng && Array.isArray(latLng) && latLng.length === 2) {
      const popup = L.popup().setLatLng(latLng).setContent(popupContent);
      popupsRef.current[id] = popup; // 存储 popup 实例
      popup.addTo(mapRef.current);
    }

    // 绑定事件
    setTimeout(() => {
      // 获取弹出框的 DOM 元素
      const popupElement = document.querySelector(".leaflet-popup-content");

      // 绑定飞至该点事件
      if (popupElement) {
        const flyToPointElement = popupElement.querySelector(".fly-to-point");
        if (flyToPointElement) {
          flyToPointElement.onclick = function () {
            // bindEvent(latLng[0], latLng[1]);
            mapRef.current.closePopup(); // 关闭弹出框
          };
        }

        // 绑定删除标记事件
        const clearMarkElement = popupElement.querySelector(".clear-mark");
        if (clearMarkElement) {
          clearMarkElement.onclick = function () {
            mapRef.current.closePopup(); // 关闭弹出框
          };
        }
      }
    }, 0);
  }

  return null;
}
