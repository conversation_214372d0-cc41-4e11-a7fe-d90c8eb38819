@baseColor: #0DD3CF;

.login-page {
    height: 100vh;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-image: url("@/pages/SI/assets/image/login/login-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .login-page-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 129px;
        backdrop-filter: blur(5px);

        .login-page-head {
            margin-bottom: 14px;
            font-size: 25px;
            font-weight: bold;
            font-family: "FZYaoti";
            color: transparent;
            background: linear-gradient(to bottom, #ffffff 20%, @baseColor);
            -webkit-background-clip: text;
            background-clip: text;
        }

        .login-page-body-form {
            padding: 55px 40px 40px;
            backdrop-filter: blur(5px);
            position: relative;
            background-image: url("@/pages/SI/assets/image/login/login-form-bg.png");
            background-size: 100% 100%;
            width: 350px;

            .basic-login {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
            }

            // .login-page-body-form-title {
            //    display: flex;
            //    justify-content: space-between;
            //    font-size: 24px;
            //    .titleZc{
            //     color: white;
            //    }
            //    .titleEn{
            //     color: #4d6e6e;
            //    }
            // }
            .login-bt {
                width: 100%;
                height: 32px;
                background: #09504c;
                box-shadow: inset 0px 0px 10px 0px @baseColor;
                border: 1px solid @baseColor;
                color: @baseColor;
                font-size: 13px;
            }

            .login-bt:hover {
                color: saturate(#fff, 100%);
                background-color: @baseColor;
            }

            input:-webkit-autofill,
            input:-webkit-autofill:hover,
            input:-webkit-autofill:focus,
            input:-webkit-autofill:active {
                -webkit-text-fill-color: white !important;
                transition: background-color 9999s ease-in-out 0s;
                -webkit-transition-delay: 99999s;
                transition-delay: 99999s;
            }


            .inputStyle {
                background-color: #0A2425;
                border: 1px solid #0E4E5F;
                color: white;
                font-size: 13px;
            }


        }

        .login-page-body-footer {
            position: absolute;
            bottom: 0;
            font-family: PingFangSC;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 20px;
            letter-spacing: 1px;
            text-align: left;
            font-style: normal;
        }
    }

    .login-page-foot {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 35px;
        background: #92c7fb;
        display: flex;
        justify-content: center;
        align-items: center;
    }


}

.remeberPW {
    color: white;
}


.ant-checkbox-inner {
    background-color: #0A2425 !important;
    border: 1px solid #0E4E5F !important;
}