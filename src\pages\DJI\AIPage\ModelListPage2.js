import { Get2 } from "@/services/general";
import { List, Card, Button, Tag, Image, Spin, Divider, Modal, Select } from "antd";
import { useState, useEffect, useRef } from "react";
import { useModel } from "umi";
import ModelAddPage from "./ModelAddPage";
import LastPageButton from "@/components/LastPageButton";
import commonStyles from "@/pages/common.less";
import ReactPlayer from 'react-player';
import ModelInfoPage from "./ModelInfoPage";
import { getImgUrl } from '@/utils/utils';
import { PlayCircleFilled } from "@ant-design/icons";

const ModelListPage2 = ({ isSipage, }) => {
  const [MList, setModelList] = useState([]);
  const [filtedMList, setFiltedMList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [playing, setPlaying] = useState(true);
  const [currentVideo, setCurrentVideo] = useState();
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]); // 选中的分类

  const playerRef = useRef(null);

  const { setPage, setModal, open, setOpen } = useModel("pageModel");
  const IsSIJump = localStorage.getItem("IsSIJump");

  // 获取模型数据
  const getModelData = async () => {
    const pst = await Get2("/api/v1/AI/GetModelList", {});
    const filteredData = pst.filter(item => item.Video !== '');
    setModelList(filteredData);
    setFiltedMList(filteredData); // 初始筛选条件
    if (pst && pst.length > 0) {
      const allCategories = [... new Set(
        pst.flatMap(item =>
          (item.UsrTips || '') //处理空值
            .split(',') // 按逗号分割
            .map(tag => tag.trim()) // 去除空格
            .filter(tag => tag) // 去除空值
        )
      )];
      setCategories(allCategories);
    }
  };

  // 获取默认数据
  useEffect(() => {
    getModelData();
  }, []);

  // 关闭页面的时候 重置 IsSIJump 值
  useEffect(() => {
    const handleUnload = () => {
      localStorage.setItem("IsSIJump", ""); // 重置为空值或你需要设置的值
    };
  
    window.addEventListener('beforeunload', handleUnload);
  
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, []);

  // 处理筛选
  const handleFillter = (selectedTags) => {
    if (!selectedTags || selectedTags.length === 0) {
      setFiltedMList(MList); // 没有筛选条件时显示全部
      return;
    }

    const filtered = MList.filter(item => {
      // 处理可能为空的标签数据
      const itemTags = (item.UsrTips || '')
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag);

      // 检查是否包含所有选中的标签
      return selectedTags.every(selectedTag =>
        itemTags.includes(selectedTag)
      );
    });

    setFiltedMList(filtered);
  };

  // // 处理点击筛选按钮筛选
  // const handleCategoryClick = (category) => {
  //   setSelectedCategories(prev => {
  //     const newCategories = prev.includes(category)
  //       ? prev.filter(c => c !== category)
  //       : [...prev, category];
  //     handleFillter(newCategories); // 触发筛选
  //     return newCategories;
  //   });
  // };

  // 处理点击筛选按钮筛选(单选)
  const handleCategoryClick = (category) => {
    setSelectedCategories(prev => {
      // 如果点击的已经是当前选中分类，则清空选择，否则设为当前分类
      const newCategories = prev.includes(category) ? [] : [category];
      handleFillter(newCategories); // 触发筛选
      return newCategories;
    });
  };

  const filterSection = (
    <div style={{
      height: 150,
      width: '100%',
      display: 'flex',
      alignItems: 'center',
      padding: 16,
    }}>
      {/* 固定文字部分 */}
      <span style={{
        marginRight: 16,
        fontSize: 14,
        flexShrink: 0,
      }}>
        行业筛选：
      </span>

      {/* 可滚动按钮容器 */}
      <div style={{
        flex: 1,
        height: '100%',
        flexWrap: 'wrap',
        overflow: 'auto',
        display: 'flex',
        gap: 8,
        alignItems: 'center',
      }}>
        {categories.map(category => (
          <Button
            key={category}
            type={selectedCategories.includes(category) ? 'primary' : 'default'}
            onClick={() => handleCategoryClick(category)}
            style={{
              borderRadius: 20,
              transition: 'all 0.3s',
              borderWidth: selectedCategories.includes(category) ? 0 : 1,
              flexShrink: 0, // 保持按钮不收缩
            }}
          >
            {category}
          </Button>
        ))}
      </div>
    </div>
  );

  const refrush = async () => {
    setOpen(false);
    getModelData();
  };

  const addForm = () => {
    setModal(<ModelAddPage refrush={refrush} />);
    setOpen(true);
  };

  // 视频点击处理
  const handleVideoClick = (item) => {
    setCurrentVideo(item);
    setVisible(true);
    setPlaying(true); // 默认自动播放
  };

  // 关闭视频弹窗
  const handleCancel = () => {
    setVisible(false);
    setPlaying(false);
  };

  // 弹窗内容
  const modalContent = (
    <div style={{
      position: 'relative',
      paddingTop: '56.25%',  // 保持宽高比为16:9
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}>
        <ReactPlayer
          ref={playerRef}
          url={getImgUrl(currentVideo?.Video)}
          playing={playing}
          controls={true}
          width="100%"
          height="100%"
          style={{
            objectFit: 'contain',
          }}
          config={{
            file: {
              attributes: {
                style: {
                  objectFit: 'contain', // 保持视频比例不变
                  width: '100%',
                  height: '100%'
                }
              }
            }
          }}
        />
      </div>
    </div>
  );


  const exr = (
    <Button className={commonStyles.addButton} type="primary" onClick={addForm}>
      新建模型
    </Button>
  );

  const exr2 = (
    <div style={{ width: 400, }}>
      <Select
        mode="multiple"
        style={{ width: '100%' }}
        allowClear
        placeholder="分类筛选"
        options={categories.map(item => ({ label: item, value: item }))}
        onChange={(value) => handleFillter(value)}
      />
    </div>
  );


  return (
    <Card
      title={!isSipage && <LastPageButton title="AI模型" />}
    >
      {/* 筛选部分 */}
      {filterSection}
      <div className={!isSipage ? commonStyles.my_scroll_y2 : commonStyles.my_scroll_y1}>
        <List
          dataSource={filtedMList}
          renderItem={(item) => (
            <List.Item>
              <Card
                style={{ width: '100%', }}
                styles={{ body: { padding: 16 } }}
                hoverable={true}
                onClick={() =>
                  setPage(<ModelInfoPage model={item}></ModelInfoPage>)
                }
              >
                <div style={{ display: 'flex', gap: 16 }}>
                  {/* 视频区域 */}
                  <div
                    onClick={e => {
                      e.stopPropagation();
                      handleVideoClick(item);
                    }}
                    style={{
                      width: 240,
                      height: 135,
                      borderRadius: 8,
                      backgroundColor: '#f0f0f0',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                      cursor: 'pointer',
                      transition: 'opacity 0.3s',
                      overflow: 'hidden', // 防止视频图片溢出
                    }}
                    // 鼠标悬停效果
                    onMouseEnter={(e) => {
                      e.currentTarget.style.opacity = 0.6;
                      e.currentTarget.querySelector('.play-icon').style.opacity = 1;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.opacity = 1;
                      e.currentTarget.querySelector('.play-icon').style.opacity = 0;
                    }}
                  >
                    {/* 播放图标 */}
                    <PlayCircleFilled
                      className="play-icon"
                      style={{
                        position: 'absolute',
                        fontSize: 48,
                        color: 'white',
                        opacity: 0, // 默认隐藏
                        transition: 'opacity 0.3s',
                        zIndex: 1
                      }}
                    />
                    <Spin spinning={item.Video === ''}>
                      <ReactPlayer
                        url={getImgUrl(item.Video)}
                        controls={false}
                        width="100%"
                        height="100%"
                        style={{
                          borderRadius: 8,
                          overflow: 'hidden',
                        }}
                      />
                    </Spin>
                  </div>

                  {/* 信息区域 */}
                  <div style={{ flex: 1 }}>
                    <div style={{
                      display: 'flex',
                      gap: 16,
                      height: '100%' // 确保容器高度填满
                    }}>
                      {/* 左半部分 */}
                      <div style={{ flex: 2 }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: 8
                        }}>
                          <h3 style={{ margin: 0, marginRight: 12 }}>{item.AName}</h3>
                          <Tag color={item.State === 1 ? 'green' : 'orange'}>
                            {item.State === 1 ? '已部署' : '未部署'}
                          </Tag>
                          {item.UsrTips?.split(',').map((tag, index) => (
                            <Tag key={index} color='gray' style={{ borderRadius: 4 }}>
                              {tag.trim()}
                            </Tag>
                          ))}
                        </div>

                        <div style={ {color: '#f0f0f0', marginBottom: 12} }>
                          {item.Description}
                        </div>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                          {item.AList?.split(',').map((tag, index) => (
                            <Tag key={index} color='gray' style={{ borderRadius: 4 }}>
                              {tag.trim()}
                            </Tag>
                          ))}
                        </div>
                      </div>

                      {/* 分割线 */}
                      <Divider type="vertical" style={{ height: 'auto', margin: 0 }} />

                      {/* 右半部分 */}
                      <div style={{
                        flex: 1,
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: 8
                        }}>
                          <h3 style={{ margin: 0, marginRight: 12 }}>注意事项</h3>
                        </div>
                        <div style={{ color: '#f0f0f0', marginBottom: 12 }}>
                          {item.Note || '无'}
                        </div>


                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </div>
      <Modal
        title={currentVideo?.AName || '视频播放'}
        open={visible}
        onCancel={handleCancel}
        footer={null}
        width={'70vw'}
        centered
      >
        {modalContent}
      </Modal>
    </Card>
  );
};

export default ModelListPage2;
