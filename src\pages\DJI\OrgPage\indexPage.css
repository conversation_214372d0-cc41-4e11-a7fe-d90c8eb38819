.IndexPageStyle {
  margin: 0;
  /* background: url('./../../../assets/mlbg.png') center center no-repeat; */
  background-size: cover;
  width: 100%;
}


.shadow {
  /* display:none; */
  /* box-shadow: #000 0 0 100px inset; */
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  z-index: 400;
  left: 0;
  pointer-events: none;
  /* background: url('./../../../assets/img/shadow2.png') center center no-repeat; */
  background-size: 100% 100%;
}

.shadow::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 38px;
  /* background: url('./../../../assets/img/bg-foot.png') top center no-repeat; */
  background-size: auto 100%;
  left: 0;
  bottom: 0;
}


.CradGroup :global(.dv-border-svg-container) {
  display: none;
}

.CradGroup :global(.dv-border-box-7) {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

.CradGroup :global(.block-title-bar>img) {
  display: none;
}


.CradGroup :global(.block-title-bar) {
  height: 32px;
  display: flex;
  align-items: center;
  padding:0 0 8px 32px;
  border: none;
  background: url('./../../../assets/img/bg-stitle.png') bottom left no-repeat;
  background-size:auto 100% ;
}

.CradGroup :global(.block-title-bar>span){
  background-image: linear-gradient(180deg, #149eff 0%, #fff 50%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  cursor: pointer;
  user-select: none;
  font-size: 15px;
}

.CradGroup :global(.block-title-bar+div){
  background: linear-gradient(135deg, #1e63a2, rgba(10, 28, 71, 0.6));
  border: 1px solid;
  border-image: linear-gradient(133deg, #0cebf7, #18416d 70%, #0071d2) 1 1;
  padding: 0 0 8px 0;
  backdrop-filter: blur(0px);
  margin-top: 0!important;
}