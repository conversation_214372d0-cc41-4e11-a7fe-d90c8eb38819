@font-face {
  font-family: "MiSan";
  src: url("../../assets/fonts/Roboto-Bold.9ece5b48.ttf");
  font-weight: normal;
  font-style: normal;
}
.IndexPageStyle {
  margin: 0;
  background: url("../../assets/images/head_bg.png") center center no-repeat;
  width: 100%;
}
.btn {
  color: white;
  font-size: 15px;
  font-weight: bold;
  float: right;
  margin-left: auto;
  margin-right: auto;
}
.headBar {
  width: 100%;
  background: url("../../assets/img/title-bg.png") center center no-repeat #0a1544;
  background-size: auto 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
  z-index: 401;
  padding: 0.5vw 0;
}
.headBar > div {
  height: 100%;
  display: flex;
  align-items: center;
  width: 100%;
}
.vintage {
  font-size: 0;
  margin-bottom: 10px;
  width: 100%;
}
.vintage > span {
  background: url("../../assets/img/title.svg") center center no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 32px;
  display: block;
}
.menuLeft .btn,
.menuRight .btn {
  height: 37px;
  display: flex;
  align-items: center;
  line-height: 1;
  font-size: 0.85vw !important;
  cursor: pointer;
  color: #fff;
  font-size: 15px;
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: left center;
}
.menuLeft .btn {
  justify-content: flex-start;
  padding-left: 30px;
  background-image: url("../../assets/img/img-muen1.png");
}
.menuRight .btn {
  justify-content: flex-end;
  padding-right: 30px;
  background-image: url("../../assets/img/img-muen2.png");
}
.headBar .btn:hover {
  filter: brightness(1.3);
}
.headBar .btn > span {
  background-image: linear-gradient(180deg, #149eff 0%, #fff 50%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  font-family: "MiSan";
}
.userInfo {
  position: absolute;
  right: 12px;
}
.menuLeft,
.menuRight,
.headCenter {
  display: flex;
  align-items: center;
}
.headCenter {
  justify-content: center;
  flex: 1 -1;
  text-align: center;
}
.menuLeft {
  justify-content: flex-end;
}
.menuRight {
  justify-content: flex-start;
}
.logo {
  width: 34px;
  height: 34px;
  background: url("../../assets/cdchy/cdchy-logo.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 0.5vw 0 -0.7vw;
}
.titleBox {
  background: repeating-linear-gradient(190deg, #ffffff 40px, #2db0df 100px), repeating-linear-gradient(-190deg, #ffffff 30px, rgba(130, 9, 236, 0.5) 60px), repeating-linear-gradient(23deg, #ffffff 50px, rgba(244, 14, 14, 0.506) 100px);
  text-shadow: 0px 0px 300px #2db0df;
  font-weight: bold;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  font-family: "Trebuchet MS", Helvetica, sans-serif;
}
.title {
  font-size: 1.4rem;
}
.smallTitle {
  font-size: 0.6rem;
}
@media screen and (min-width: 1280px) and (max-width: 1680px) {
  .title {
    font-size: 1.4rem;
  }
  .smallTitle {
    font-size: 0.5rem;
  }
}
@media screen and (max-width: 1280px) {
  .title {
    font-size: 1.2rem;
  }
  .smallTitle {
    font-size: 0.5rem;
  }
}
@media screen and (max-width: 1094px) {
  .title {
    font-size: 0.9rem;
  }
  .smallTitle {
    font-size: 0.4rem;
  }
}
@media screen and (max-width: 860px) {
  .title {
    font-size: 0.6rem;
  }
  .smallTitle {
    font-size: 0.3rem;
  }
}
@media screen and (max-width: 700px) {
  .title {
    font-size: 0.3rem;
  }
  .smallTitle {
    font-size: 0.1rem;
  }
}
.userInfo .btn {
  padding-right: 15px;
  font-size: 13px;
  background: url("../../assets/img/img-user.png") no-repeat center;
  background-size: 100% 70%;
  font-size: 0.75vw !important;
}
.userInfo .btn:hover {
  color: #fff !important;
  filter: brightness(1.5);
}
:where(.css-dev-only-do-not-override-1172axd).ant-dropdown .ant-dropdown-menu,
:where(.css-dev-only-do-not-override-1172axd).ant-dropdown-menu-submenu .ant-dropdown-menu {
  background: rgba(10, 21, 68, 0.781) !important;
}
