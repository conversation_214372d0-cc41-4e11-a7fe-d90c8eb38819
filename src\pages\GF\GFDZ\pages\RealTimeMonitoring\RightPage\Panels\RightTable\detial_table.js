import { useEffect, useState } from "react";
import {
  Button,
  Radio,
  message,
  Modal,
  Descriptions,
  Input,
  Table,
  InputNumber,
  Row,
  Col,
  Card,
  Image,
} from "antd";
import { HPost2 } from "@/utils/request";
import { downloadFile2, getBodyH, getImgUrl, isEmpty } from "@/utils/utils";
import LocationMap from "@/pages/Maps/LocationMap";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import RecordItemListPage from "@/pages/DJI/RecordItem/index";
import { FlyToPoint2 } from "@/pages/DJI/FlyToPage/helper";
import img from "@/assets/17bg.jpg";
import EditTable from "./edit_table";
const { TextArea } = Input;

const DetialTable = ({ danger2 }) => {
  const [danger, setDanger] = useState({ ...danger2 });
  const [canSee, setCanSee] = useState(false);

  const dPanel = () => {
    return (
      <Descriptions
        style={{ textAlign: "center", marginBottom: 18 }}
        column={2}
      >
        <Descriptions.Item label="事件类型">
          {danger.DangerType}
        </Descriptions.Item>
        <Descriptions.Item label="紧急程度">{danger.Level}</Descriptions.Item>
        <Descriptions.Item label="事件来源">{"AI识别"}</Descriptions.Item>
        <Descriptions.Item label="事件状态">
          {danger.State == 2 ? "已处理" : "处理中"}
        </Descriptions.Item>
        {/* <Descriptions.Item label="主要内容">{danger.Content}</Descriptions.Item> */}
      </Descriptions>
    );
  };
  const onOK = async () => {
    danger.State = 2;
    const xx = await HPost2("/api/v1/Danger/OK", danger);
    setDanger({ ...danger });
  };

  const btn = () => {
    if (isEmpty(danger)) return null;
    if (danger.State == 2) return null;
    return (
      <Button
        onClick={() => {
          onOK();
        }}
      >
        标记已处理
      </Button>
    );
  };

  const btn2 = () => {
    if (isEmpty(danger.Remarks)) return null;
    const L1 = danger.Remarks.split(",");

    return (
      <Button
        onClick={() => {
          FlyToPoint2(danger.SN, L1[1], L1[0], 100);
        }}
      >
        飞至拍摄点位
      </Button>
    );
  };

  const dMap = (str) => {
    const L1 = str.split(",");

    if (L1.length < 2) return <div></div>;
    return (
      <LocationMap
        lat={Number(L1[1])}
        lng={Number(L1[0])}
        h1={getBodyH(380)}
        title={"事件点位"}
      ></LocationMap>
    );
  };
  return (
    <>
      {/* <a onClick={() => setCanSee(true)}>编辑</a> */}
      <MyButton style={{ padding: "2px 5px" }} onClick={() => setCanSee(true)}>
        详情
      </MyButton>
      <Modal
        title={"详情"}
        // onOk={submit}
        open={canSee}
        onCancel={() => setCanSee(false)}
        okText="提交"
        cancelText="取消"
        width={900}
        footer={null}
      >
        <Row>
          <Col span={8}>
            <Card title={danger.Title} extra={btn2()}>
              <div style={{ textAlign: "left" }}>
                {dPanel()}
                {dMap(danger.Remarks)}
                {/* <Image style={{ height: getBodyH(360)}}
                src={getImgUrl(danger.ImgObjectName)}/> */}
              </div>
            </Card>
          </Col>
          <Col span={10}>
            <Card
              style={{ marginLeft: 12.0 }}
              title={danger.Title}
              extra={
                <Button
                  onClick={() => {
                    downloadFile2(
                      getImgUrl(danger.ImgObjectName, "现场照片.jpg")
                    );
                  }}
                >
                  下载图片
                </Button>
              }
            >
              <div style={{ textAlign: "left" }}>
                {/* {dPanel()} */}
                <Image
                  style={{ fill: "cover", maxHeight: getBodyH(300) }}
                  src={getImgUrl(danger.ImgObjectName)}
                />
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              title={"处理过程"}
              style={{ marginLeft: 12.0, height: getBodyH(200) }}
              extra={btn()}
            >
              <RecordItemListPage spaceID={danger.Guid}></RecordItemListPage>
            </Card>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default DetialTable;
