import {
  Input,
  Checkbox,
  Form,
  Select,
  Button,
  message,
} from "antd";
import { useState, } from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2} from "@/utils/request";
const CronEditForm = (props) => {
  const { wayList, refrush, modelList,record,cronList} = props;
  const [ID, setID] = useState(record.ID);
  const [way, setWay] = useState(
    wayList.find((item)=>{
      return item.WanLineId === record.WayLineID
    })
  );
  const [model, setModel] = useState(
    modelList.find((item)=>{
      return item.Guid === record.Model
    })
  );
  const [cls, setCls] = useState([]);
  const [name, setName] = useState(record.JobName);
  const [remarks, setRemarks] = useState(record.Remarks);
  const [PList,setPList] = useState(record.JobData)
  const getWaySelect = (wayList) => {
    const list = [];
    wayList.forEach((e) => {
      list.push(
        <Select.Option key={e.WanLineId} data={e} value={e.WanLineId}>
          {e.WayLineName}
        </Select.Option>
      );
    });

    return list;
  };
  
  const getModelSelect = (modelList) => {
    const list = [];
    modelList.forEach((e) => {
      list.push(
        <Select.Option key={e.Guid} data={e} value={e.Guid}>
          {e.AName}
        </Select.Option>
      );
    });

    return list;
  };
  
  const onSave = async (e) => {
    const data = {
      ID:ID,
      JobName:name,
      WayLineID:way.WanLineId,
      WayLineName:way.WayLineName,
      Model:model.Guid,
      ModelName:model.AName ,
      // ModelPath:model.ObjectName,
      JobData:PList,
      Remarks:remarks,
      SN:record.SN,
      // ACls:aCls,
      // AClsNM:aClsNM
    };
    const xx = await HPost2("/api/v1/AIDocJob/Update", data);
    
    if (isEmpty(xx.err)) {
      message.open({type:'success',content:"修改成功!"});
    }
    refrush();
  };

  const onChange = (values) => {
    const xx = wayList.find((item) => {
      return item.WanLineId === values;
    });
    setWay(xx);
  };

  const onChange2 = (values) => {
    
    const xx = modelList.find((item) => {
      return item.Guid === values;
    });
    setModel(xx);
  };

  const getAList = (m1) => {
    if (isEmpty(m1)) return <div></div>;
    const list = [];
    const arr = m1.AList.split(",");
    let nn = 0;
    arr.forEach((e) => {
      list.push({
        label: e,
        value: nn,
        disabled: false,
      });
      nn++;
    });
    return <Checkbox.Group options={list} onChange={(e) => setCls(e)} />;
  };

  return (
      <Form
        labelCol={{span: 4,}}
        wrapperCol={{span: 18}}
        layout="horizontal"
      >
        <Form.Item label="任务名称">
          <Input
            onChange={(e) => {
              setName(e.target.value);
            }}
            defaultValue={record.JobName}
            allowClear
          ></Input>
        </Form.Item>

        <Form.Item label="选择航线">
          <Select onSelect={onChange} defaultValue ={record.WayLineName}
           >{getWaySelect(wayList)}</Select>
        </Form.Item>

        <Form.Item label="选择模型">
          <Select onSelect={onChange2} defaultValue={record.ModelName}
         >{getModelSelect(modelList)}</Select>
        </Form.Item>

        <Form.Item label="照片点位">
          <Input
            defaultValue={record.JobData}
            allowClear
            onChange={(e) => {
              setPList(e.target.value);
            }}
          ></Input></Form.Item>

        <Form.Item label="备注">
          <Input
          defaultValue={record.Remarks}
          allowClear
            onChange={(e) => {
              setRemarks(e.target.value);
            }}
          ></Input>
        </Form.Item>

        <Form.Item label={" "} colon={false}>
          <Button type="primary" onClick={onSave}>
            保存
          </Button>
        </Form.Item>
      </Form>
  );
};

export default CronEditForm;
