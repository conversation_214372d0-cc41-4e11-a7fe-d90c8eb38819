import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, } from "antd";
import { timeFormat } from "@/utils/helper";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import { queryPage, } from '@/utils/MyRoute';

const getTableTitle = (title) => {
    return (
        <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
    );
};
const dzzhTableCols = () => {

    const { setPage, } = useModel("pageModel");

    const StatusMap = {
        'RUNNING': { color: 'processing', text: '进行中' },
        'COMPLETED': { color: 'success', text: '已完成' },
        'FAILED': { color: 'error', text: '失败' },
    };

    return [
        {
            title: getTableTitle("航线名称"),
            dataIndex: "WaylineName",
            key: "WaylineName",
            align: "center",
        },
        {
            title: getTableTitle("建模类型"),
            dataIndex: "Remarks",
            key: "Remarks",
            align: "center",
        },
        {
            title: getTableTitle("任务ID"),
            dataIndex: "TaskId",
            key: "TaskId",
            align: "center",
        },
        {
            title: getTableTitle("任务状态"),
            key: "Status",
            align: "center",
            render: (_, record) => {
                const { color, text } = StatusMap[record.Status] || {};
                const statusElement = text ? (
                    <Tag color={color}>{text}</Tag>
                ) : (
                    <span>-</span>
                );
        
                return statusElement;
            }
        },
        {
            title: getTableTitle("创建时间"),
            key: "CreateTM",
            align: "center",
            render: (_, record) => (
                <span>
                    {isEmpty(record.CreateTM)
                        ? "-"
                        : timeFormat(record.CreateTM)
                    }
                </span>
            )
        },
        {
            title: getTableTitle("任务进度"),
            dataIndex: "Taskstage",
            key: "Taskstage",
            align: "center",
        },
        // {
        //     title: getTableTitle("操作"),
        //     align: "center",
        //     render: (_, record) => (
        //         <Space size="middle">

        //             <MyButton
        //                 style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
        //                 onClick={() => {
        //                     localStorage.setItem('record', JSON.stringify(record));
        //                     setPage(queryPage('识别详情'))
        //                 }}
        //             >
        //                 详情
        //             </MyButton>

        //         </Space>
        //     ),
        // }
    ];
};

export default dzzhTableCols;
