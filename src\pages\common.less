.addButton{
    // background:var(--primaryColor) bottom center no-repeat, linear-gradient(-135deg, var(--primarySimpleColor), var(--primaryColor));
    box-shadow: var(--primarySimpleColor) 0 0 3px 1px inset;
}
.my_scroll_y{
    height: calc(100vh - 170px);
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.my_scroll_y1{
    height: calc(100vh - 240px);
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.my_scroll_y2{
    height: calc(100vh - 250px);
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.ant-dropdown-menu-submenu-popup {
    max-height: 500px; 
    overflow-y: auto;
}

.ant-dropdown-menu-submenu-popup::-webkit-scrollbar {
    width: 5px; /* 滚动条宽度 */
}

.ant-dropdown-menu-submenu-popup::-webkit-scrollbar-thumb {
    background-color: rgba(118, 220, 251, 0.5); /* 滚动条颜色 */
    border-radius: 34px; 
}

.ant-dropdown-menu-submenu-popup::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1); /* 滚动条轨道颜色 */
}
@media (max-width: 768px) {
    .ant-dropdown-menu-submenu-popup {
        max-height: 200px; /* 小屏幕时的最大高度 */
    }
}

@media (min-width: 769px) {
    .ant-dropdown-menu-submenu-popup {
        max-height: 400px; /* 大屏幕时的最大高度 */
    }
}

