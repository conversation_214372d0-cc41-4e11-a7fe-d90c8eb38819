
import { HGet2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import { useState } from "react";
import {IfTimeOver} from "@/utils/caches/info";
import dayjs from "dayjs";
export default function OsdModel() {
    const deBug=async(cmd)=>{
        HGet2("/api/v1/Debug/CMD?cmd="+cmd)
    }
    const [jc, setJc] = useState({});
    const [fj, setFj] = useState({});
    const deviceReBoot=async()=>{
        console.log('deviceReBoot',jc);
        const pb1=IfTimeOver('deviceReBoot')
        if(!pb1)return;
        localStorage.setItem('deviceReBoot',dayjs().unix())
        deBug("debug_mode_open");
        setTimeout(()=>deBug("device_reboot"), 2000);
    }
    const Reboot=()=>{
        console.log('mm4',jc);
        if(isEmpty(jc)) return;
        if (jc.mode_code==0) {deviceReBoot();}
    }

    return { jc, setJc,fj,setFj,Reboot};
};