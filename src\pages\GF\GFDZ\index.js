import React, { useEffect, useState } from "react";
import commonStyle from "@/pages/GF/style/common.less";
import "@/pages/GT/style/antd-common.less";
import MyHead from "@/pages/GT/components/MyHead";
import { queryPage } from "@/utils/MyRoute";
import { useModel } from "umi";
import HeadTabs from "@/pages/GT/components/HeadTabs";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import MyMenu from "@/pages/GF/GFDZ/pages/MyMenu";

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const { page, setPage, lastPage, currentPage } = useModel("pageModel");

  const isOperationPage = 
    currentPage === '运维管理'
  ;
  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };

  const headList = [
    { label: "实时监测", key: "实时监测" },
    { label: "缺陷管理", key: "缺陷管理" },
    { label: "运维管理", key: "运维管理" },
  ];

  return (
    <div className="gt-page">
      <div
        className={commonStyle.gt_back}
        style={{ position: "relative", overflow: "hidden", height: "100vh" }}
      >
        <MyHead headList={headList} handlePageChange={handlePageChange} />

        <div
          style={{
            display: "flex",
            flex: 1,
            position: "relative",
            overflow: "hidden",
            height:'calc(100vh - 56px)',
          }}
        >
          {isOperationPage && (
            <MyMenu
              handlePageChange={handlePageChange}
              setCollapsed={setCollapsed}
              collapsed={collapsed}
            />
          )}
          <div
            // className="blackBackground"
            style={{
              flex: 1,
              overflow: "hidden",
              marginLeft: isOperationPage ? (collapsed ? 80 : 160) : 0,
              transition: isOperationPage ? "margin-left 0.2s" : "none",
            }}
          >
            <ConfigProvider locale={locale}>
              <HeadTabs></HeadTabs>
              {page}
            </ConfigProvider>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
