import React, { useRef, useEffect } from 'react';
import flvjs from 'flv.js';

const FLVPlayer = ({ sn3,ctl,height ,width}) => {
  const videoRef = useRef(null);
  const flvPlayerRef = useRef(null);
  //const url="http://play-agora.schykj.net.cn/live/"+sn3+".flv"
  const url="http://112.44.103.230:1938/live/"+sn3+".flv"
  console.log('FLVPlayer',url)
  useEffect(() => {
    if (flvjs.isSupported()) {
      flvPlayerRef.current = flvjs.createPlayer({
        type: 'flv',
        url: url,
        isLive: true, // 是否为直播流
        hasAudio: false,				//数据源是否包含有音频
        hasVideo: true,					//数据源是否包含有视频
        enableStashBuffer: false,		//是否启用缓存区
        headers: {'Access-Control-Allow-Origin':'*'},
        cors:'*',
      });

      flvPlayerRef.current.attachMediaElement(videoRef.current);
      flvPlayerRef.current.load();
      flvPlayerRef.current.play();
    }

    flvPlayerRef.current.on(flvjs.Events.ERROR, () => {
           if (flvPlayerRef.current) {
            flvPlayerRef.current.pause();
            flvPlayerRef.current.unload();
            flvPlayerRef.current.detachMediaElement();
            flvPlayerRef.current.destroy();
            flvPlayerRef.current= null;
          }
        });

    return () => {
      if (flvPlayerRef.current) {
        flvPlayerRef.current.unload();
        flvPlayerRef.current.detachMediaElement();
        flvPlayerRef.current.destroy();
      }
    };
  }, [url]);

  return (
    <div>
      <video  style={{objectFit:'cover'}} playsInline={true} ref={videoRef} controls={ctl} width={width} height={height-4} />
    </div>
  );
};

export default FLVPlayer;
