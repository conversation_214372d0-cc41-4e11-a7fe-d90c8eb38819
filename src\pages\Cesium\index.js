
import React, { useEffect, useState, useRef } from "react";
import { Cesium, useModel } from "umi";
import {
  Camera,
} from "cesium";
import "./index.css";
import { isEmpty } from "@/utils/utils";
import FeiJi from "@/assets/air.glb";
import useMapManager from "@/utils/useMapManager";
import { getMapDataBySN } from "@/services/general";
import { GetCesiumViewer } from '@/utils/cesium_help';

// const DDD = (viewer) => {
//   if (isEmpty(viewer)) return;
//   const xxx = async () => {
//     try {
//       //桥墩模型一
//       const tileset = await Cesium.Cesium3DTileset.fromUrl(
//         "http://183.222.16.139:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/qiaodun_1/models/pc/0/terra_b3dms/tileset.json",
//         {
//           dynamicScreenSpaceError: true,
//           dynamicScreenSpaceErrorDensity: 2.0e-4,
//           dynamicScreenSpaceErrorFactor: 24.0,
//           dynamicScreenSpaceErrorHeightFalloff: 0.1,
//           maximumScreenSpaceError: 1.0,
//         }
//       );
//       //桥墩模型二
//       const tileset2 = await Cesium.Cesium3DTileset.fromUrl(
//         "http://183.222.16.139:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/qiaodun_2/models/pc/0/terra_b3dms/tileset.json",
//         {
//           dynamicScreenSpaceError: true,
//           dynamicScreenSpaceErrorDensity: 2.0e-4,
//           dynamicScreenSpaceErrorFactor: 24.0,
//           dynamicScreenSpaceErrorHeightFalloff: 0.1,
//           maximumScreenSpaceError: 1.0,
//         }
//       );
//       //正摄影像
//       var imageryProvider = new Cesium.TileMapServiceImageryProvider({
//         url: "http://183.222.16.139:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/tif20241128/{z}/{x}/{y}.png",
//       });
//       viewer.scene.primitives.add(tileset);
//       viewer.scene.primitives.add(tileset2);
//       viewer.imageryLayers.addImageryProvider(imageryProvider);

//       viewer.flyTo(tileset);
//     } catch (error) {
//       console.error(`Error creating tileset: ${error}`);
//     }
//   };
//   xxx();
// };
//加载geojson数据
// const loadGeoJson = async () => {
//   try {
//     const dataSource = await Cesium.GeoJsonDataSource.load(
//       "http://183.222.16.139:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/kml/Geo2.geojson", // 替换为你的 GeoJSON URL
//       {
//         // 可选样式配置
//         stroke: Cesium.Color.BLUE,
//         fill: Cesium.Color.BLUE.withAlpha(0.1),
//         strokeWidth: 2,
//         clampToGround: true, // 如果数据是二维面或线，贴地显示
//       }
//     );
//     viewer2.dataSources.add(dataSource);
//   } catch (error) {
//     console.error("加载 GeoJSON 失败:", error);
//   }
// };

const Map3D = ({ width, h1 }) => {
  const device = JSON.parse(localStorage.getItem("device"));
  let { addSWMapUrlArr } = useMapManager();
  let titleJsonArr = addSWMapUrlArr;
  const { pList } = useModel("gpsModel");
  const { mapUrl, baseMapLayers } = useModel("mapModel");
  const [GeoJsonData, setGeoJsonData] = useState([]); //Geojson数据
  const [TitleJsonData, setTitleJsonData] = useState([]); //三维模型数据
  let viewer;
  const viewerRef = useRef(null);
  const viewerInitialized = useRef(false);  
  let FJEntity;
  const pX = Cesium.Cartesian3.fromDegrees(
    device.Lng,
    device.Lat,
    device.Height
  );
  let pL = [];

  // "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/15c167c49b554749baa01d9941c74071/tileset.json"
  // "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/bd6c5b018dd046b59bcccf42e7ce64d5/tileset.json"
  //翠云廊
  //"https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/3dModels/667edb33915b45218945367bbef08b2e/tileset.json"
  // "https://cce95e19-1f9d-42e1-b6c3-7e0825651a20.oss-cn-chengdu.aliyuncs.com/3d/tileset.json"
  //"http://192.168.0.132:1281/osgb2/tileset.json", //数据地址

  Camera.DEFAULT_VIEW_FACTOR = 0.0001;
  const getJD = (p1, p2) => {
    console.log(p1);
    const x1 = p2.x;
    const y1 = p2.y;
    const x2 = p1.x;
    const y2 = p1.y;
    const jd = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;
    return jd;
  };

  const getJD2 = () => {
    if (pList.current.length < 3) return 0;
    const p1 = getPoint(pList.current[pList.current.length - 1]);
    const p2 = getPoint(pList.current[pList.current.length - 2]);
    return getJD(p1, p2);
  };

  const getPoint = (p) => {
    return Cesium.Cartesian3.fromDegrees(p[1], p[0], p[2]);
  };

  const getGps2 = () => {
    if (pList.current.length == 0)
      return Cesium.Cartesian3.fromDegrees(
        device.Lng,
        device.Lat,
        device.Height
      );
    return getPoint(pList.current[pList.current.length - 1]);
  };

  const getPList = () => {
    if (isEmpty(pList.current))
      return [
        Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, device.Height),
      ];
    const list = [];
    pList.current.forEach((e) => {
      list.push(getPoint(e));
    });
    pL = list;
    return list;
  };

  const FJIcon = (viewer) => {
    if (isEmpty(viewer)) return;

    FJEntity = viewer.entities.add({
      name: "gltf",
      position: new Cesium.CallbackProperty(() => {
        const p1 = getGps2();
        return p1;
      }, false),

      orientation: new Cesium.CallbackProperty(() => {
        const jd = getJD2();
        const p1 = getGps2();
        return Cesium.Transforms.headingPitchRollQuaternion(
          p1,
          new Cesium.HeadingPitchRoll(
            Cesium.Math.toRadians(200 - jd),
            Cesium.Math.toRadians(0),
            Cesium.Math.toRadians(0)
          )
        );
      }, false),

      model: {
        uri: FeiJi, // 模型路径
        // show:true,
        scale: 7,
        // color:new  Cesium.Color(255, 255, 255, 0.6),
      },
    });
    // viewer.flyTo(FJEntity);
    //viewer.trackedEntity = FJEntity;
  };

  const BiaoZhu = (viewer) => {
    if (isEmpty(viewer)) return;
    var entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, 573.5), // 文字标注的位置
      // point: {
      //     pixelSize: 10,
      //     color: Cesium.Color.RED
      // },
      billboard: {
        image: require("@/assets/icons/device.png"), // 广告牌图片URL
        scale: 0.1, // 广告牌缩放比例
        maxiumScale: 0.5,
        verticalOrigin: Cesium.VerticalOrigin.BASELINE, // 广告牌定位
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
    });
    const line = viewer.entities.add({
      name: "Red tube with rounded corners",
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, device.Height),
          Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, 400),
        ],
        width: 2,
        material: new Cesium.PolylineDashMaterialProperty({
          //虚线
          color: Cesium.Color.YELLOW,
          dashLength: 20, //短划线长度pixel
          gapColor: Cesium.Color.TRANSPARENT, //空隙颜色
        }),
      },
    });
    console.log(" device" + device);
    viewer.flyTo(entity);
  };

  const FJLine = (viewer) => {
    if (isEmpty(viewer)) return;

    viewer.entities.add({
      // 实体的唯一标识
      id: "myLine",
      // 线条属性
      polyline: {
        // 定义线条的位置
        positions: new Cesium.CallbackProperty(() => {
          const pp = getPList();
          return pp;
        }, false),
        // 线条的宽度
        width: 2,
        // 线条的颜色
        material: Cesium.Color.RED,
      },
    });
  };

   // 初始化 viewer
   useEffect(() => {
    if (!viewerInitialized.current) {
      viewerRef.current = GetCesiumViewer("cesiumContainer");
      viewerRef.current.bottomContainer.style.display = 'none'; //隐藏底部cuisum图标
      // 移除默认的底图图层
      viewerRef.current.imageryLayers.remove(viewerRef.current.imageryLayers.get(0));
      viewerInitialized.current = true;
    }
    
    return () => {
      if (viewerRef.current) {
        viewerRef.current.destroy();
        viewerRef.current = null;
        viewerInitialized.current = false;
      }
    };
  }, []);

  useEffect(() => {
    if (!viewerRef.current || !mapUrl) return;

    const imageryLayer = new Cesium.ImageryLayer(
      new Cesium.UrlTemplateImageryProvider({
        url: mapUrl,
        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
        minimumLevel: 0,
        maximumLevel: 18,
      })
    );
    viewerRef.current.imageryLayers.add(imageryLayer);
  }, [mapUrl]);

  useEffect(() => {
    if (!viewerRef.current) return;
    viewer = viewerRef.current;
    const layerOption = {
      show: true, // 图像层是否可见
      alpha: 0.2, // 透明度
      nightAlpha: 1, // 地球夜晚一侧的透明度
      dayAlpha: 1, // 地球白天一侧的透明度
      brightness: 1, // 亮度
      contrast: 1, // 对比度
      hue: 0, // 色调
      saturation: 1, // 饱和度
      gamma: 0.5, // 伽马校正
    };

    getData(viewer);
    FJIcon(viewer);
    FJLine(viewer);
    BiaoZhu(viewer);
  }, []);

  const getData = async (viewer) => {
    let res = await getMapDataBySN(device);
    if (res && res.length > 0) {
      let arr1 = res.filter((e) => e.MapType == 11);
      let arr2 = res.filter((e) => e.MapType == 2);
      setTitleJsonData(arr1);
      setGeoJsonData(arr2);
      DDD(viewer, res, arr1, arr2);
    }
  };

  const DDD = async (viewer, mapData) => {
    if (isEmpty(viewer)) return;
    const option = {
      dynamicScreenSpaceError: true,
      dynamicScreenSpaceErrorDensity: 2.0e-4,
      dynamicScreenSpaceErrorFactor: 24.0,
      dynamicScreenSpaceErrorHeightFalloff: 0.1,
      maximumScreenSpaceError: 1.0,
    };
    let tileset = [];

    try {
      if (mapData && mapData.length > 0) {
        const tilePromises = mapData.map(async (item) => {
          switch (item.MapType) {
            case 11: //三维模型
              const res2 = await Cesium.Cesium3DTileset.fromUrl(
                item.Url,
                option
              );
              viewer.scene.primitives.add(res2);
              tileset.push(res2);
              break;
            case 0:
            case 1: //正射影像
              const imageryProvider = new Cesium.TileMapServiceImageryProvider({
                url: item.Url,
              });
              viewer.imageryLayers.addImageryProvider(imageryProvider);
              break;
            case 2: //geojson矢量地图
              const dataSource = await Cesium.GeoJsonDataSource.load(item.Url, {
                stroke: Cesium.Color.BLUE,
                fill: Cesium.Color.BLUE.withAlpha(0.1),
                strokeWidth: 2,
                clampToGround: true,
              });
              viewer.dataSources.add(dataSource);
              break;
          }
        });
        await Promise.all(tilePromises);
      }
      if (tileset.length > 0) {
        viewer.flyTo(tileset[0]);
      }
    } catch (error) {
      console.error(`Error creating entities: ${error}`);
    }
  };

  return (
    <div
      style={{ height: h1, width: width ? width : "100%" }}
      id="cesiumContainer"
    ></div>
  );
};

export default Map3D;
