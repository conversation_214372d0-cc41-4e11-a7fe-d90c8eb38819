import React, { useState, useEffect, useMemo } from 'react';
import { Layout, Tree, Button, Form, Input, InputNumber, Select, Table, Modal, message, Tabs, Space, Popconfirm, Dropdown, Menu, Checkbox, Upload, Row, Col, Tooltip, TreeSelect } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined, GlobalOutlined, DownOutlined, QuestionCircleOutlined, UploadOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import './index.less';
import { serviceTypeList } from '@/pages/GT/components/Map/mapServiceConfig';
import api from '@/pages/GT/utils/api';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { TabPane } = Tabs;

// 服务类型名称映射
const getServiceTypeName = (type) => {
  if (type === 'folder') return '文件夹';
  const serviceType = serviceTypeList.find(item => item.value.toLowerCase() === type.toLowerCase());
  return serviceType ? serviceType.label : type;
};

// ArcGIS动态地图服务才能保存子图层
const arcgisTypes = ['ArcGISDynamicMapServiceLayer', 'ArcGISTiledMapServiceLayer', 'ArcGISFeatureMapServiceLayer'];

// 安全访问参数表单组件
const AccessParamsForm = ({ initialData, onChange }) => {
  const [formData, setFormData] = useState(initialData);
  
  useEffect(() => {
    onChange?.(formData);
  }, [formData, onChange]);
  
  return (
    <Form layout="vertical">
      <Form.Item label="验证类型">
        <Select
          value={formData.verifyType}
          style={{ width: '100%' }}
          onChange={(value) => setFormData({ ...formData, verifyType: value })}
        >
          <Option value="usrpwd">账号和密码</Option>
          <Option value="token">token</Option>
          <Option value="custom">自定义配置</Option>
        </Select>
      </Form.Item>
      {formData.verifyType === 'usrpwd' && (
        <>
          <Form.Item label="登录地址">
            <Input
              value={formData.verifyParams.url}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, url: e.target.value }
              })}
              placeholder="请输入登录地址"
            />
          </Form.Item>
          <Form.Item label="用户名">
            <Input
              value={formData.verifyParams.username}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, username: e.target.value }
              })}
              placeholder="请输入用户名"
            />
          </Form.Item>
          <Form.Item label="密码">
            <Input.Password
              value={formData.verifyParams.password}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, password: e.target.value }
              })}
              placeholder="请输入密码"
            />
          </Form.Item>
        </>
      )}
      {formData.verifyType === 'token' && (
        <>
          <Form.Item label="token_name">
            <Input
              value={formData.verifyParams.tokenName}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, tokenName: e.target.value }
              })}
              placeholder="请输入token_name"
            />
          </Form.Item>
          <Form.Item label="token_value">
            <Input
              value={formData.verifyParams.token}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, token: e.target.value }
              })}
              placeholder="请输入token_value"
            />
          </Form.Item>
        </>
      )}
      {formData.verifyType === 'custom' && (
        <>
          <Form.Item label="请求地址">
            <Input
              value={formData.verifyParams.url}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, url: e.target.value }
              })}
              placeholder="请输入请求地址"
            />
          </Form.Item>
          <Form.Item label="请求方式">
            <Select
              value={formData.verifyParams.requestType}
              onChange={(value) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, requestType: value }
              })}
              style={{ width: '100%' }}
            >
              <Option value="Get">Get</Option>
              <Option value="Post">Post</Option>
            </Select>
          </Form.Item>
          <Form.Item label="提取规则">
            <Input
              value={formData.verifyParams.rule}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, rule: e.target.value }
              })}
              placeholder="请输入提取规则"
            />
          </Form.Item>
          <Form.Item label="token_name">
            <Input
              value={formData.verifyParams.tokenName}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, tokenName: e.target.value }
              })}
              placeholder="请输入token_name"
            />
          </Form.Item>
          <Form.Item label="更新间隔">
            <Input
              value={formData.verifyParams.interval}
              onChange={(e) => setFormData({
                ...formData,
                verifyParams: { ...formData.verifyParams, interval: e.target.value }
              })}
              placeholder="请输入更新间隔"
              addonAfter="分钟"
            />
          </Form.Item>
        </>
      )}
    </Form>
  );
};


// 递归获取所有文件夹节点
const getAllFolders = (data, folders = []) => {
  data.forEach(item => {
    if (item.Attribute.dataType === "" || item.Attribute.dataType === "1") {
      folders.push({ value: item.Rid, label: item.Name });
      if (item.Children) {
        getAllFolders(item.Children, folders);
      }
    }
  });
  return folders;
};


const params = {
  pcatalog: 'DDT',
};

export default function BaseMapConfig() {
  const [serviceForm] = Form.useForm();
  const [loadParamsForm] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [catalogName, setCatalogName] = useState('');
  const [selectedParentId, setSelectedParentId] = useState('#');
  const [mapServices, setMapServices] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [currentService, setCurrentService] = useState(null);
  const [modalType, setModalType] = useState('view'); // add, edit, view

  // 获取数据
  const fetchData = async () => {
    try {
      const data = await api.queryDataDirectory(params);
      console.log('fetchData', data);
      setMapServices(data);
      // 默认展开第一级目录
      if(data.length > 0) {
        setExpandedKeys(data.map(item => item.Rid));
      }
    } catch (error) {
      message.error('获取数据目录失败: ' + error.message);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchData();
  }, []);
  
  // 提取所有文件夹用于下拉选择
  const folderOptions = useMemo(() => getAllFolders(mapServices), [mapServices]);

  // 初始化表单值
  useEffect(() => {
    if (currentService) {
      serviceForm.setFieldsValue({
        name: currentService.name,
        expandedNode: true,
        internalType: '2',
        serviceTypeName: currentService.serviceTypeName,
        servicePath: currentService.servicePath || '',
        secureAccess: false,
        useConfig: false,
        sortValue: currentService.sortValue || 1,
        status: ['运行状态未知'],
        expandParam: {
          data: currentService.loadParams?.data || [],
          editUrl: '',
          isUseExpandParam: currentService.loadParams?.isUseExpandParam || false,
          expandedNode: currentService.loadParams?.expandedNode || false,
        },
        isUseExpandParam: ['配置', '启用配置'],
        editUrl: '', 
        bootLoad: currentService.bootLoad, 
        imageSrc: currentService.thumbnail || '',
        dataType: currentService.dataType,
        parentId: currentService.parentId,
        parentIdLabel: folderOptions.find(item => item.value === String(currentService.parentId))?.label || '', // 设置对应的显示名称
      });
    }
  }, [currentService]);

  // 处理树节点展开/收起
  const onExpand = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
  };

  // 处理树节点选择
  const onSelect = (selectedKeysValue, info) => {
    setSelectedKeys(selectedKeysValue);
    if (selectedKeysValue.length > 0) {
      const nodeInfo = findNodeAndParent(mapServices, selectedKeysValue[0]);
      if (nodeInfo) {
        const node = nodeInfo.node;
        setCurrentService({
          id: node.Rid,
          name: node.Name,
          type: node.Attribute.dataType === "" || node.Attribute.dataType === "1" ? 'folder' : node.Attribute?.serviceTypeName,
          dataType: node.Attribute.dataType,
          parentId: node.Attribute.parentId,
          internalType: node.Attribute?.internalService === 2 ? '2' : '1',
          accessInfo: node.Attribute.accessInfo ? JSON.parse(node.Attribute.accessInfo) : { verifyType: 'usrpwd', verifyParams: {}, isUseVerification: false }, // 提供默认值
          bootLoad: node.Attribute.bootLoad === 1 ? true : false,
          sortValue: node.Attribute.sortValue || 1,
          loadParams: node.Attribute.expandParam ? JSON.parse(node.Attribute.expandParam) : {
            data: [],
            editUrl: '',
            isUseExpandParam: false,
            expandedNode: false,
          },
          ...node.Attribute
        });
        console.log('nodeInfo', nodeInfo);
      }
    } else {
      setCurrentService(null);
    }
  };

  useEffect(() => {
    console.log('currentService updated:', currentService);
  }, [currentService]);
  
  // 根据ID查找节点及其父节点
  const findNodeAndParent = (data, id, parent = null) => {
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.Rid === id) return { node: item, parent: parent, index: i };
      if (item.Children) {
        const found = findNodeAndParent(item.Children, id, item);
        if (found) return found;
      }
    }
    return null;
  };

  // 删除目录或服务
  const handleDelete = async (Rid) => {
    try {
      await api.directoryDelete(Rid);

      const newData = await api.queryDataDirectory(params);

      setMapServices(newData);

      if (currentService?.id === Rid) {
        setCurrentService(null);
        setSelectedKeys([]);
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败: ' + error.message);
    }
  };

  // 保存服务
  const handleSaveService = (type) => {
    serviceForm.validateFields().then(async values => {
      const { // 从 values 中解构出所有需要的字段
        name,
        internalType,
        serviceTypeName,
        servicePath,
        sortValue,
        bootLoad,
        imageSrc,
        expandParam,
        accessInfo,
        parentIdLabel, // 获取表单中的父级目录名称 (label)
      } = values;
      console.log('values', values);

      // 根据 label 查找对应的 ID
      const selectedFolder = folderOptions.find(item => item.label === parentIdLabel);
      const actualParentId = selectedFolder ? selectedFolder.value : null; // 获取实际的 parentId (value/Rid)
      console.log('actualParentId', parentIdLabel,selectedFolder)

      // 处理accessInfo，确保它是正确的对象格式
      const processedAccessInfo = typeof accessInfo === 'string'
        ? JSON.parse(accessInfo)
        : accessInfo || {
            verifyType: "usrpwd",
            tokenName: "token",
            isUseVerification: false,
            isUseToken: false,
            verifyParams: {
              url: "",
              username: "",
              password: "",
              token: "",
              tokenLoginUrl: "",
              requestType: "Get",
              requestParam: "",
              interval: 60,
              rule: "",
              tokenName: ""
            }
          };
      const data = {
        name,
        internalService: internalType === '2' ? 2 : 1,
        serviceTypeName,
        servicePath,
        sortValue,
        bootLoad: bootLoad ? 1 : 0,
        thumbnail: imageSrc,
        dataType: currentService.dataType,
        parentId: currentService.dataType === '2' ? actualParentId : currentService.parentId, // 使用查找到的实际 parentId
        expandParam: currentService.dataType === '2' ? JSON.stringify({
          ...currentService.loadParams,
          ...expandParam,
          data: expandParam.data || []
        }) : '',
        accessInfo: currentService.dataType === '2' ? JSON.stringify(processedAccessInfo) : '',
        pcatalog: 'DDT'
    };

      try {
        if (type === 'add') {
          // 调用API新增数据
          await api.addAndUpdate(data);
          
          if (data.dataType === 2 && data.internalType === '2' && arcgisTypes.includes(data.serviceTypeName)) {
            // 如果是ArcGIS动态地图服务，调用添加子图层接口
            await api.addMapChildLayer({
              ...data,
            });
          }
          
          // 刷新数据目录
          const newData = await api.queryDataDirectory(params);
          setMapServices(newData);
          
          message.success('新增成功');
        } else if (type === 'edit') {
          console.log('edit场景下保存服务', data);
          // 更新数据
          await api.addAndUpdate({
            ...data,
            rid: currentService.id
          });
          
          // 刷新数据目录
          const newData = await api.queryDataDirectory(params);
          setMapServices(newData);
          
          message.success('更新成功');
        }
        
        setModalType('view');
      } catch (error) {
        message.error('操作失败: ' + error.message);
      }
    });
  };

  // 渲染树节点
  const renderTreeNodes = (data) => {
    return data.map(item => {
      if (item.Attribute.dataType === "" || item.Attribute.dataType === "1") {
        return (
          <Tree.TreeNode 
            key={item.Rid} 
            title={
              <div className="tree-node-title">
                <FolderOutlined /> {item.Name}
                <div className="tree-node-actions">
                  <Popconfirm
                    title="确定要删除此文件夹及其所有服务吗？"
                    onConfirm={() => {
                      console.log('触发删除文件夹确认', item.Rid);
                      handleDelete(item.Rid);
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<DeleteOutlined />} 
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </div>
              </div>
            }
          >
            {item.Children && renderTreeNodes(item.Children)}
          </Tree.TreeNode>
        );
      }
      
      return (
        <Tree.TreeNode 
          key={item.Rid} 
          title={
            <div className="tree-node-title">
              <GlobalOutlined /> {item.Name}
              <div className="tree-node-actions">
                <Popconfirm
                  title="确定要删除此服务吗？"
                  onConfirm={(e) => {
                    e?.stopPropagation();
                    console.log('触发删除服务确认', item.Rid);
                    handleDelete(item.Rid);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Popconfirm>
              </div>
            </div>
          }
        />
      );
    });
  };

  // 渲染新增下拉菜单
  const addMenu = (
    <Menu
      items={[
        {
          key: 'folder',
          label: '新增目录',
          onClick: () => setIsModalVisible(true)
        },
        {
          key: 'service',
          label: '新增图层服务',
          onClick: () => {
            if (!selectedKeys.length || !findNodeAndParent(mapServices, selectedKeys[0])?.node.Attribute?.dataType?.match(/^[1]?$/)) {
              message.error('请先选择一个目录节点');
              return;
            }
            setModalType('add');
            serviceForm.resetFields();
            setCurrentService({
              type: 'service',
              name: '',
              dataType: '2',
              parentId: currentService.rid,
              sortValue: 1,
              loadParams: {
                data: [],
                editUrl: '',
                isUseExpandParam: false,
                expandedNode: false,
                mapUseRange: ['2D'],
              },
              isUseExpandParam: ['启用配置'],
              bootLoad: 0,
              imageSrc: '',
              internalType: '2',
              accessInfo: '',
              pcatalog: 'DDT',
            });
          }
        }
      ]}
    />
  );

  // 渲染新增目录Modal
  const renderAddCatalogModal = () => {
    return (
      <Modal
        title="新增目录"
        open={isModalVisible}
        onOk={() => {
          if (!catalogName) {
            message.error('请输入目录名称');
            return;
          }
          handleAddService(selectedParentId, 'folder');
        }}
        onCancel={() => setIsModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form layout="vertical">
          <Form.Item
            label="目录名称"
            required
            rules={[{ required: true, message: '请输入目录名称' }]}
          >
            <Input 
              placeholder="请输入目录名称"
              value={catalogName}
              onChange={(e) => setCatalogName(e.target.value)}
            />
          </Form.Item>
          <Form.Item label="目录分类">
            <TreeSelect
              placeholder="请选择父目录"
              value={selectedParentId}
              onChange={(value) => setSelectedParentId(value)}
              allowClear
              style={{ width: '100%' }}
              treeData={parentOptions}
              treeDefaultExpandAll
            />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  // 获取可选的父目录列表
  const parentOptions = useMemo(() => {
    const findFolderNodesRecursive = (data) => {
        const folderNodes = [];
        if (!Array.isArray(data)) {
            return folderNodes;
        }

        data.forEach(item => {
            if (item.Attribute.dataType === '' || item.Attribute.dataType === '1') {
                const childFolders = item.Children ? findFolderNodesRecursive(item.Children) : [];

                const node = {
                    title: item.Name,
                    value: item.Rid,
                    key: item.Rid,
                    children: childFolders
                };
                folderNodes.push(node);
            }
        });
        return folderNodes;
    };

    const topLevelFolders = findFolderNodesRecursive(mapServices);

    const rootNode = {
        title: '顶级目录',
        value: '#', 
        key: '#',
        children: topLevelFolders
    };

    console.log('Final parentOptions structure:', [rootNode]);
    
    return [rootNode];

}, [mapServices]);

  //新增服务函数，增加类型参数
  const handleAddService = async (parentId = null, type = null) => {
    if (type === 'folder') {
      if (!catalogName) {
        message.error('请输入目录名称');
        return;
      }
      
      // 调用addAndUpdate接口创建目录
      try {
        const data = {
          name: catalogName,
          dataType: '1',
          expandedNode: true,
          parentId: parentId,
          sortValue: 1,
          expandParam: '',
          pcatalog: 'DDT'
        };
        console.log('新增目录addAndUpdate', data);
        await api.addAndUpdate(data);
        message.success('新增目录成功');
        const newData = await api.queryDataDirectory(params);
        setMapServices(newData);
        setIsModalVisible(false);
        setCatalogName('');
        setSelectedParentId('#');
      } catch (error) {
        message.error('新增目录失败: ' + error.message);
      }
      return;
    }

    setModalType('add');
    serviceForm.resetFields();
      
    serviceForm.setFieldsValue({
        type: type === 'folder' ? 'folder' : 'ArcGISTiledMapServiceLayer',
        dataType: type === 'folder' ? '1' : '2',
        name: '',
        expandedNode: true
    });
    setSelectedKeys([]);
    };

  // 测试服务连接
  const handleTestService = () => {
    message.success('服务连接测试成功');
  };

  // 选择缩略图
  const handleSelectThumbnail = (file) => {
    // 处理缩略图选择逻辑
    return false; // 阻止自动上传
  };

  // 渲染配置表单
  const renderConfigForm = () => {
    if (!currentService) return <div className="empty-content">请选择一个服务查看配置</div>;
    
    // 根据服务类型显示不同的表单
    if (currentService.type === 'folder') {
      return (
        <Form
          form={serviceForm}
          layout="horizontal"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <h2 style={{ marginBottom: '20px' }}>
            {modalType === 'add' ? '新增目录' : '目录信息'}
          </h2>
          
          <Form.Item
            name="name"
            label="目录名称"
            rules={[{ required: true, message: '请输入目录名称' }]}
          >
            <Input placeholder="请输入目录名称" />
          </Form.Item>
          
          <Form.Item
            name="expandedNode"
            label="默认展开节点"
            valuePropName="checked"
          >
            <Checkbox />
          </Form.Item>

          
          <Form.Item
            name="bootLoad"
            label="启动加载"
            valuePropName="checked"
          >
            <Checkbox defaultChecked={false} />
          </Form.Item>

          <Form.Item
          name="sortValue"
          label="目录显示顺序"
          initialValue={1}
          >
            <InputNumber min={1} placeholder="请设置目录显示顺序" style={{ width: '70px' }} />
          </Form.Item>
          
          <Form.Item
            wrapperCol={{ offset: 4, span: 20 }}
          >
            <Space>
              <Button type="primary" onClick={() => {
                const formValues = serviceForm.getFieldsValue();
                console.log('formValues', formValues);
                console.log('currentService', currentService);
                const hasChanges = Object.keys(formValues).some(key => {
                  if (key in currentService) {
                    return formValues[key] !== currentService[key];
                  }
                });
                if (hasChanges) {
                  if (modalType !== 'add') {
                    setModalType('edit');
                    handleSaveService('edit');
                  }
                  else {
                    handleSaveService('add');
                  }
                } else {
                  message.info('没有修改内容');
                }
              }}>
                保存
              </Button>
              {modalType === 'add' && (
                <Button onClick={() => {
                  setModalType('view');
                  setCurrentService(null);
                  setSelectedKeys([]);
                }}>
                  取消
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      );
    }
    
    // 服务类型表单
    return (
      <Form
              form={serviceForm}
              layout="horizontal"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              initialValues={{
                expandParam: {
                  data: [],
                  editUrl: '',
                  isUseExpandParam: false,
                  expandedNode: false,
                  extrudedList: [],
                  layerFilters: {}
                },
                accessInfo: {
                  verifyType: 'usrpwd',
                  tokenName: 'token',
                  isUseVerification: false,
                  isUseToken: false,
                  verifyParams: {
                    url: '',
                    username: '',
                    password: '',
                    token: '',
                    tokenLoginUrl: '',
                    requestType: 'Get',
                    requestParam: '',
                    interval: 60,
                    rule: '',
                    tokenName: ''
                  }
                }
              }}
            >
        <h2 style={{ marginBottom: '20px' }}>
          {modalType === 'add' ? '新增图层服务' : '图层信息'}
        </h2>
        
        <Form.Item
          name="name"
          label="服务名称"
          rules={[{ required: true, message: '请输入服务名称' }]}
        >
          <Input placeholder="请输入服务名称" />
        </Form.Item>

        <Form.Item
          name="parentIdLabel"
          label="所属目录"
          rules={[{ required: true, message: '请选择所属目录!' }]} // 添加校验规则
        >
          <Select
            placeholder="请选择所属目录"
            onChange={(value) => serviceForm.setFieldsValue({ parentId: value })} // 更新表单中的 parentId
          >
            {folderOptions.map(folder => (
              <Option key={folder.value} value={folder.label}>
                {folder.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name={['expandParam', 'expandedNode']}
          label="默认展开节点"
          valuePropName="checked"
        >
          <Checkbox />
        </Form.Item>
        
        <Form.Item
          name="internalType"
          label="内部服务转换"
        >
          <Select defaultValue="2" placeholder="请选择是否使用内部服务转换">
            <Option value="1">是</Option>
            <Option value="2">否</Option>
          </Select>
        </Form.Item>
        
        {currentService?.internalType === '1' && (
          <Form.Item
            name="servicePath"
            label="转换接口地址"
            rules={[{ required: true, message: '请输入转换接口地址' }]}
          >
            <Input placeholder="请输入转换接口地址" />
          </Form.Item>
        )}
        
        <Form.Item
          name="serviceTypeName"
          label="服务类型"
          rules={[{ required: true, message: '请选择服务类型' }]}
        >
          <Select placeholder="请选择服务类型">
            {serviceTypeList.map(service => (
              <Option key={service.value} value={service.value}>{service.label}</Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="servicePath"
          label="服务地址"
          rules={[{ required: true, message: '请输入服务URL' }]}
        >
          <Input placeholder="请输入服务URL" />
        </Form.Item>
        
        {currentService?.serviceTypeName === 'WfsServiceLayer' && (
          <Form.Item
            name={['expandParam', 'editUrl']}
            label="编辑服务地址"
            rules={[{ required: true, message: '请输入编辑服务地址' }]}
          >
            <Input placeholder="请输入编辑服务地址" />
          </Form.Item>
        )}
        
        {currentService?.internalType === '2' && (
          <Form.Item
            name="accessInfo"
            label="服务安全访问"
          >
            <Space>
              <Button
                onClick={() => {
                  const accessInfo = JSON.parse(currentService.accessInfo || '{"verifyType":"usrpwd","tokenName":"token","isUseVerification":false,"isUseToken":false,"verifyParams":{"url":"","username":"","password":"","token":"","tokenLoginUrl":"","requestType":"Get","requestParam":"","interval":60,"rule":"","tokenName":""}}');
                  console.log('accessInfo', accessInfo);
                  
                  // 使用普通变量保存最新的表单数据
                  let latestFormData = accessInfo;
                  
                  Modal.confirm({
                    title: '安全访问参数信息',
                    width: 500,
                    content: <AccessParamsForm
                      initialData={accessInfo}
                      onChange={(data) => {
                        latestFormData = data;
                      }}
                    />,
                    onOk() {
                      // 使用最新的表单数据更新
                      const newAccessInfo = JSON.stringify(latestFormData);
                      currentService.accessInfo = newAccessInfo;
                      // 更新Form的值
                      serviceForm.setFieldsValue({
                        accessInfo: latestFormData
                      });
                      console.log('Updated accessInfo:', newAccessInfo);
                    }
                  });
                }}
              >
                配置
              </Button>
              <Form.Item
                  name={['accessInfo', 'isUseVerification']}
                  valuePropName="checked"
                  noStyle
                >
                <Checkbox>启用配置</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>
        )}
{/*         
        <Form.Item
          name="bootLoad"
          label="启动加载"
          valuePropName="checked"
        >
          <Checkbox defaultChecked={false} />
        </Form.Item> */}

        <Form.Item
          name="sortValue"
          label="服务加载顺序"
          initialValue={1}
        >
          <InputNumber min={1} max={99} placeholder="请设置服务加载顺序" style={{ width: '70px' }} />
        </Form.Item>
        
        <Form.Item
          name={['expandParam', 'isUseExpandParam']}
          label="加载参数"
          valuePropName="checked"
        >
          <Space>
            <Button
              onClick={() => {
                console.log('loadParams', currentService.loadParams);
                const loadParams = typeof currentService.loadParams === 'string' ?
                    JSON.parse(currentService.loadParams || '{"isUseExpandParam":false,"expandParam":[]}') :
                    currentService.loadParams;
                
                // 设置初始值
                loadParamsForm.setFieldsValue({
                  expandParam: loadParams.expandParam || []
                });

                const LoadParamsForm = () => {
                  return (
                    <Form form={loadParamsForm} layout="vertical">
                      <Form.List name="expandParam">
                        {(fields, { add, remove }) => (
                          <>
                            {fields.map(({ key, name, ...restField }) => (
                              <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                                <Form.Item
                                  {...restField}
                                  name={[name, 'paramKey']}
                                  rules={[{ required: true, message: '请输入参数名称' }]}
                                >
                                  <Input placeholder="参数名称" />
                                </Form.Item>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'paramValue']}
                                  rules={[{ required: true, message: '请输入参数值' }]}
                                >
                                  <Input placeholder="参数值" />
                                </Form.Item>
                                <DeleteOutlined onClick={() => remove(name)} />
                              </Space>
                            ))}
                            <Form.Item>
                              <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                                添加参数
                              </Button>
                            </Form.Item>
                          </>
                        )}
                      </Form.List>
                    </Form>
                  );
                };

                Modal.confirm({
                  title: '加载参数配置',
                  width: 600,
                  content: <LoadParamsForm />,
                  async onOk() {
                    try {
                      const values = await loadParamsForm.validateFields();
                      currentService.loadParams = {
                        ...loadParams,
                        expandParam: values.expandParam || []
                      };
                    } catch (error) {
                      console.error('验证表单失败:', error);
                      return Promise.reject();
                    }
                  }
                });
              }}
            >
              配置
            </Button>
            <Form.Item
              name={['expandParam', 'isUseExpandParam']}
              valuePropName="checked"
              noStyle
            >
              <Checkbox>启用配置</Checkbox>
            </Form.Item>
          </Space>
        </Form.Item>
        
        <Form.Item
          name="imageSrc"
          label="缩略图"
        >
          <Row>
            <Col span={6}>
              <div style={{ width: '100px', height: '100px', border: '1px dashed #d9d9d9', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {currentService.imageSrc ? (
                  <img src={currentService.imageSrc} alt="缩略图" style={{ maxWidth: '100%', maxHeight: '100%' }} />
                ) : (
                  <div>预览图</div>
                )}
              </div>
            </Col>
            <Col span={18}>
              <Button icon={<UploadOutlined />}>选择</Button>
            </Col>
          </Row>
        </Form.Item>
        
        <Form.Item
          wrapperCol={{ offset: 4, span: 20 }}
        >
          <Space>
              <Button type="primary" onClick={() => {
                const formValues = serviceForm.getFieldsValue();
                console.log('formValues', formValues);
                console.log('currentService', currentService);
                const hasChanges = Object.keys(formValues).some(key => {
                  if (key in currentService) {
                    return formValues[key] !== currentService[key];
                  }
                  if (key === 'imageSrc') {
                    return formValues[key] !== currentService['thumbnail'];
                  }
                });
                if (hasChanges) {
                  if (modalType !== 'add') {
                    setModalType('edit');
                    handleSaveService('edit');
                  }
                  else {
                    handleSaveService('add');
                  }
                } else {
                  message.info('没有修改内容');
                }
              }}>
                保存
              </Button>
            {modalType === 'add' && (
              <Button onClick={() => {
                setModalType('view');
                setCurrentService(null);
                setSelectedKeys([]);
              }}>
                取消
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    );
  };

  return (
    <Layout className="map-data-directory">
      <Sider width={300} className="directory-sider">
        <div className="sider-header">
          <Space>
            <Dropdown overlay={addMenu}>
              <Button type="primary">
                新增 <DownOutlined />
              </Button>
            </Dropdown>
            <Button 
              type="primary" 
              // icon={<ReloadOutlined />}
              onClick={() => {
                serviceForm.resetFields();
                setCurrentService(null);
                setSelectedKeys([]);
                setModalType('view');
                message.success('已刷新');
              }}
            >
              刷新 <ReloadOutlined />
            </Button>
          </Space>
        </div>
        {renderAddCatalogModal()}
        <div className="directory-tree">
          <Tree
            showLine
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={onExpand}
            onSelect={onSelect}
          >
            {renderTreeNodes(mapServices)}
          </Tree>
        </div>
      </Sider>
      <Layout className="directory-content">
        <Content>
          {renderConfigForm()}
        </Content>
      </Layout>
    </Layout>
  );
};