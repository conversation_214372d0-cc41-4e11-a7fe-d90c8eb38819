import { useState, useEffect } from "react";
import { Card, Button, Table } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2 } from "@/services/general";
import LastPageButton from "@/components/LastPageButton";
import CronAddForm from "./form_add";
import TableCols from "./table";
import ComStyles from "@/pages/common.less";
import { useModel } from "umi";
import useConfigStore from "@/stores/configStore";

const AIVideoJobListPage = (props) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [wayList, setWayList] = useState({});
  const [cronList, setCronList] = useState({});
  const { setModal, setOpen, setPage, lastPage } = useModel("pageModel");
  const [modelList, setModelList] = useState([]);
  useEffect(() => {
    const getLineData = async () => {
      const pst = await Get2("/api/v1/WayLine/GetAllList", {});
      if (isEmpty(pst)) return;
      setWayList(pst);
    };

    const getModelData = async () => {
      const pst = await Get2("/api/v1/AIModel/GetList", {});
      setModelList(pst);
    };

    const getCronData = async () => {
      const pst = await Get2("/api/v1/AIVideoJob/GetAllList", {});
      if (isEmpty(pst)) return;
      setCronList(pst);
    };

    getLineData();
    getCronData();
    getModelData();
  }, []);

  const showMap = (values) => {
    const record = wayList.find((item) => {
      return item.WanLineId === values.FLightId;
    });
    setPage(
      <Card
        title="航线信息"
        extra={<Button onClick={() => lastPage()}>返回</Button>}
      >
        {WayLineMap(getBodyH(180), record)}
      </Card>
    );
  };

  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/AIVideoJob/GetAllList", {});
    if (isEmpty(pst)) return;
    setCronList(pst);
  };

  const exr = (
    <Button
      type="primary"
      className={ComStyles.addButton}
      onClick={() => {
        setModal(
          <CronAddForm
            wayList={wayList}
            modelList={modelList}
            refrush={refrush}
          />
        );
        setOpen(true);
      }}
    >
      新建任务
    </Button>
  );
  return (
    <div style={{ margin: 0, height: getBodyH(56) }}>
      <Card
        title={<LastPageButton title="视频识别任务" />}
        bordered={false}
        extra={exr}
      >
        <div>
          {isEmpty(cronList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                current: tableCurrent,
                onChange: (e) => {
                  setTableCurrent(e);
                },
              }}
              bordered
              dataSource={cronList}
              columns={TableCols(refrush, showMap)}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default AIVideoJobListPage;
