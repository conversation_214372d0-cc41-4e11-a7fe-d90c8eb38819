import { useEffect, useState } from "react";
import { Card, Col, Row, DatePicker, Select, Space, message, Radio } from "antd";
import LastPageButton from "@/components/LastPageButton";
import { Get2 } from "@/services/general";
import dayjs from "dayjs";
import { isEmpty } from "@/utils/utils";

const { Option } = Select;

const groupLogsByOrgAndDate = (logs) => {
  const groupedLogs = {};

  logs.forEach((log) => {
    const date = new Date(log.CreateTM).toLocaleDateString();
    const key = `${log.OrgNM}-${date}`;

    if (!groupedLogs[key]) {
      groupedLogs[key] = {
        orgName: log.OrgNM,
        date,
        day: new Date(log.CreateTM).toLocaleDateString("zh-CN", {
          weekday: "long",
        }),
        records: [],
      };
    }

    groupedLogs[key].records.push(log);
  });

  return Object.values(groupedLogs);
};

const ActivityLog = () => {
  const [rawData, setRawData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [selectedUser, setSelectedUser] = useState(null);
  const [uniqueUsers, setUniqueUsers] = useState([]);
  const [ifLogin, setIfLogin] = useState(true);

  const getData2 = async (t0) => {
    const t1 = dayjs(t0).format("YYYY/MM/DD 00:00");
    const t2 = dayjs(t0).format("YYYY/MM/DD 23:59");
    let pst = await Get2(
      "/api/v1/SystemLog/GetListByDate?t1=" + t1 + "&t2=" + t2,
      {}
    );
    if (isEmpty(pst)) {
      message.info("没有数据");
      pst = [];
    }
    if (!isEmpty(pst.err)) {
      message.error(pst.err);
      pst = [];
    }

    const pst2 = []
    pst.forEach(log => {
      const inLogin = (ifLogin && log.Title.includes('登录')) || (!ifLogin && !log.Title.includes('登录'));
      if (inLogin) {
        if(!ifLogin && !(log.Title.includes('成功')||log.Title.includes('失败'))){
          log.Title=log.Title+"-成功";
        }
        pst2.push(log);
      }
    });



    setRawData(pst2);
    setFilteredData(pst2);
    setUniqueUsers([...new Set(pst2.map((log) => log.UserName))]);
  };

  useEffect(() => {
    getData2(selectedDate);
  }, [ifLogin]);

  const handleFilterChange = (user, date) => {
    const filteredLogs = rawData.filter((log) => {
      const inUser = !user || log.UserName === user;
      const inDate =
        !date ||
        new Date(log.CreateTM).toLocaleDateString() ===
        new Date(date).toLocaleDateString();

      return inUser && inDate;
    });
    setFilteredData(filteredLogs);
  };

  useEffect(() => {
    handleFilterChange(selectedUser, selectedDate);
  }, [selectedUser, selectedDate]);

  const groupedLogs = groupLogsByOrgAndDate(filteredData);

  const TypeBtn = <Radio.Group onChange={(e) => setIfLogin(e.target.value)} value={ifLogin}>
    <Radio value={true}>登录日志</Radio>
    <Radio value={false}>操作日志</Radio>
  </Radio.Group>

  return (
    <Card
      title={
        <Row align="middle" justify="space-between">
          <Col>
            <LastPageButton title={"操作日志"} />
          </Col>
          <Col>
            <Space>
              {TypeBtn}
              <Select
                placeholder="筛选用户"
                style={{ width: 120 }}
                onChange={(value) => {
                  setSelectedUser(value);
                  handleFilterChange(value, selectedDate);
                }}
                allowClear
              >
                {uniqueUsers.map((user) => (
                  <Option key={user} value={user}>
                    {user}
                  </Option>
                ))}
              </Select>
              <DatePicker
                defaultValue={selectedDate}
                onChange={(date) => {
                  setSelectedDate(date);
                  //handleFilterChange(selectedUser, date);
                  getData2(date);
                }}
                canClear={false}
                placeholder="选择日期"
              />
            </Space>
          </Col>
        </Row>
      }
    >
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {groupedLogs.map((log, index) => (
          <Row
            key={index}
            justify="center"
            gutter={[16, 16]}
            style={{ marginBottom: 16 }}
          >
            <Col span={4}>
              <Card>
                <p style={{ fontWeight: "bold" }}>{log.orgName}</p>
                <p style={{ fontWeight: "bold" }}>{log.date}</p>
                <p style={{ fontWeight: "bold" }}>{log.day}</p>
              </Card>
            </Col>
            <Col span={20}>
              <Card style={{ overflowY: "auto", maxHeight: "500px" }}>
                {log.records.map((record, idx) => (
                  <p key={idx}>{`${dayjs(record.CreateTM).add(-8,"hour").format("HH:mm")}  ${record.UserName} - ${record.Title}- ${record.TheUrl}`}</p>
                ))}
              </Card>
            </Col>
          </Row>
        ))}
      </Space>
    </Card>
  );
};

export default ActivityLog;
