import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (refrush) => {
  
  const { isModalOpen, setIsModalOpen } = useModel('pageModel');
  const { nearWRJlist, setNearWRJlist } = useModel('pageModel');
  const { DZZHPoint, setDZZHPoint } = useModel('pageModel');

  // 获取附近的无人机列表
  const getListByNearby = async (geometry) => {
      const url = `/api/v1/Device/GetListByNearby?lat=${geometry[1]}&lng=${geometry[0]}`
      const res = await axiosApi(url, "GET", null)
      if(res?.data){
        setNearWRJlist(res.data)
        //打开弹窗
        setIsModalOpen(true)
      }else{
        message.error("5公里范围内没有在线的可用机场")
      }
  };  

  return [
    {
      title: getTableTitle("灾害体类型"),
      dataIndex: "灾害体类型",
      key: "灾害体类型",
      align: "center",
    },

    {
      title: getTableTitle("灾害体名称"),
      dataIndex: "灾害体名称",
      key: "灾害体名称",
      align: "center",
    },

    {
      title: getTableTitle("室内编号"),
      dataIndex: "室内编号",
      key: "室内编号",
      align: "center",
    },

    {
      title: getTableTitle("地理位置"),
      dataIndex: "地理位置",
      key: "地理位置",
      align: "center",
    },

    {
      title: getTableTitle("监测建议"),
      dataIndex: "监测建议",
      key: "监测建议",
      align: "center",
    },

    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <MyButton
            style={{ padding: "2px 5px" }}
            onClick={() => {
              setDZZHPoint(record);
              getListByNearby(record.geometry);
            }}
          >
            一键飞至
          </MyButton>
        </Space>
      ),
    },
  ];
};

export default TableCols;
