import { getBodyH, getBodyW, getBodyW2, isEmpty } from '@/utils/utils';
import { useEffect, useRef, useState } from 'react';
import { Button, message,Card} from 'antd';
import styles from '@/pages/DJI/DevicePage/index.less';
import DJBaseMap from '@/pages/Maps/DJBaseMap';
import JCXX from '@/pages/DJI/DevicePage/JXSSPanel';
import IfShowPanel from '@/components/IfShowPanel';
import HmsPanel from '@/pages/DJI/HmsPage/HmsPanel';
import GetPlayer from '@/pages/DJI/DevicePage/PlayerPanel';
import JoyStick from './joy';
import PageButton from '@/pages/DJI/DevicePage/pageButton';
import RtmpButton from '@/pages/DJI/DevicePage/RtmpButton';
import FJInfoPanel from '@/pages/DJI/DevicePage/FJInfoPanel';
import FJCtrPanel from '@/pages/DJI/FlyToPage/FJCtrPanel';
import FlyButtonPanel from '@/pages/DJI/FlyToPage/FlyButtonPanel';
import OsdPanel from './OsdPanel';
import Map3D from '@/pages/Cesium';
import { useModel } from 'umi';
import { Get2 } from '@/services/general';
import JoyStickPanel from '@/pages/DJI/DevicePage/Panels/JoyStockPanel';


const DRCPage = ({ device }) => {

  const [url, setUrl] = useState(1);
  const [p1, setP1] = useState(<DJBaseMap device={device} h1={getBodyH(56)} />);
  const [p2, setP2] = useState(GetPlayer('100%', '100%', 1));
  const [ifX, setIfX] = useState(true);
  const [ifY, setIfY] = useState(true);
  const { sdata } = useModel('stateModel');
  const { ifJCRtmp, ifFJRtmp,ifCMDPanel,cmdPanel } = useModel('rtmpModel')
  const {DoCMD,DoCMD2}=useModel('cmdModel')
  const seq=useRef(0);

  // const sn="7CTDLCE00AC2J4";
  // const sn2="1581F6Q8D23CT00A5N49";
  if (isEmpty(device)) {
    device = JSON.parse(localStorage.getItem('device'))
  }

  const onClickYY = () => {
    console.log(ifY);
    setIfY(!ifY);
  };
  const getJData=(v1,min,max)=>{
       const v2=(v1-40)/80*(max-min)+min
       return v2;
  }
  const JoyHandle1=(d)=> {
   // const w1=getJData(d.xPosition,-5,5);
  //  const hh1=getJData(d.yPosition,-2,2)
  //  sendW(getJData(d.xPosition,-5,5));
    sendH(getJData(d.yPosition,-2,2));
   // sendH(hh1);
   // console.log('joy1',stickData);
  }
  const JoyHandle2=(stickData)=> {

    console.log('joy2',stickData);
 }
  useEffect(() => {
    setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />);
    setP2(GetPlayer('300px', '240px', url));
    const  Joy1 = new JoyStick('joyDiv1',{},  JoyHandle1);
    const  Joy2 = new JoyStick('joyDiv2',{},  JoyHandle2 );
    JCStart();
    return () => {
       //Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
      // Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
    };
  }, []);


  const onClickZHM = (e) => {
    console.log('onClickZHM', e)
    const tt = e.target.innerText;
    if (tt === "机场镜头") {

      setP1(GetPlayer('100%', getBodyH(56), 1, device.SN))
      JCStart();
    }
    if (tt === "飞机镜头") {
      setP1(GetPlayer('100%', getBodyH(56), 2, device.SN))
      FJStart();
    }

    if (tt === "飞行地图") {
      setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />)
    }

    if (tt === "三维场景") {
        setP1(<Map3D h1={getBodyH(56)}/>)
    }

    if (tt === "识别视频") {
     setP1(GetPlayer('100%', getBodyH(56), 5, device.SN))
    }
  }

  const JCStart = async () => {
    Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
  }

  const FJStart = async () => {
     await Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
  }


   const FJStart2 = async () => {
     await Get2(`/api/v1/Live/RtmpStart?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}&quality=0`)
   }

  const onClickCHM = (e) => {
    const tt = e.target.innerText;
    if (tt === "机场镜头2") {
      setP2(GetPlayer('300px', '240px', 1, device.SN))
      JCStart();
    }
    if (tt === "飞机镜头") {
      setP2(GetPlayer('300px', '240px', 2, device.SN))
      FJStart();
    }

    if (tt === "三维场景") {
      setP2(<Map3D h1={240}/>)
    }
    if (tt === "飞行地图") {
      setP2(<DJBaseMap device={device} sn={device.SN} h1={'240px'} />)
    }

    if (tt === "机场镜头") {
      setP2(GetPlayer('300px', '240px', 3, device.SN))
    }
    if (tt === "识别视频") {
      FJStart2();
      setP2(GetPlayer('300px', '240px', 4, device.SN))
    }
  }
  const drcPanel=()=>{
      <></>
  }

   const btnPanel = <div
    style={{
      position: 'absolute',
      zIndex: 1000,
      width: '100%',
      background: 'rgba(255,255,255,0.5)',
    }}
  >

   

    <RtmpButton sn={device.SN} />
    {PageButton('主画面', onClickZHM, sdata)}
    {PageButton('画中画', onClickCHM, sdata)}

    {/* {DeBugButton()} */}
    <Button type="text" onClick={() => onClickYY()}>
      {' '}
      {ifY ? '隐藏' : '显示'}{' '}
    </Button>
  </div>

  const xxx = <div style={{ height: 24.0, background: 'red' }}></div>


  const beginX=()=>{
    const data={
		"hsi_frequency": 1,
		"mqtt_broker": {
			"address": "***********:12503",
			"client_id": device.SN2,
			"enable_tls": false,
			"expire_time": 1672744922,
			"password": "",
			"username": ""
		},
		"osd_frequency": 10
	}
    const data2={
        "payload_index": device.Camera2
    }
    DoCMD(device.SN,'drc_mode_enter',data);
    DoCMD(device.SN,'payload_authority_grab',data2);

  }

  const sendW=(w)=>{
    seq.current=seq.current+1;
    const data={
        "data": {
            "h": 0,
            "seq": seq.current,
            "w": w,
            "x": 0,
            "y": 0
        },
        "method": "drone_control"
    }
    console.log('joy1', seq.current,w)
    DoCMD2(`thing/product/${device.SN}/drc/down`,data);
  }

  const sendH=(w)=>{
    seq.current=seq.current+1;
    const data={
        "data": {
            "h": w,
            "seq": seq.current,
            "w": 0,
            "x": 0,
            "y": 0
        },
        "method": "drone_control"
    }
    //console.log('joy1', seq.current,w,h)
    DoCMD2(`thing/product/${device.SN}/drc/down`,data);
  }

  return (
    <div className={styles.IndexPageStyle} style={{ height: 120 }}>

      <div
        className="container"
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
        }}
      >
        {p1}

        <div
          className={styles.XXX2}
          style={{
            zIndex: 1000,
          }}
        >
          {btnPanel}

          {ifY ? p2 : null}
        </div>
        {IfShowPanel(290, 260, 8, 8, null, null, FJInfoPanel(device.SN), ifY)}
        {IfShowPanel(240, 260, 320, 8, null, null, <OsdPanel></OsdPanel>, ifY)}

        {IfShowPanel(48, getBodyW2(200), getBodyH(136), 100, null, 0, <div style={{ height: 48, width: getBodyW2(200) }}><FlyButtonPanel device={device}></FlyButtonPanel></div>, true,'auto')}
       
        <div style={{ display: 'block',height:160,width:160,position:'absolute', zIndex: 1000,top:getBodyH(250),left:550}}><Button onClick={()=>beginX()}>开始</Button></div>
        <div id="joyDiv1" style={{ display: 'block',height:160,width:160,position:'absolute', zIndex: 1000,top:getBodyH(250),left:50}}></div>
        <div id="joyDiv2" style={{ display: 'block',height:160,width:160,position:'absolute', zIndex: 1000,top:getBodyH(250),right:50}}></div>

      </div>
    </div>
  );
};

export default DRCPage;
