import axios from "axios";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import <PERSON>S<PERSON> from "file-saver";
import {getImgUrl} from "./utils.js";

const getFile = (url) => {
    return new Promise((resolve, reject) => {
      axios({
        url: getImgUrl(url),
        method: "GET",
        responseType: "arraybuffer",
      })
        .then((data) => {
          resolve(data.data);
        })
        .catch((error) => {
          reject(error.toString());
        });
    });
  };

  export const handleBatchDownload = async (selectImgList) => {
    const zip = new JSZip();
    const cache = {};
    const promises = [];
    await selectImgList.forEach((item, index) => {
      const promise = getFile(item.ObjectName).then((data) => {
        const arr_name = item.ObjectName.split("/");
        let file_name = arr_name[arr_name.length - 1];
        file_name = `${item.WayLineNM}-航点${item.HangDianIndex}${item.FileName.slice(item.FileName.indexOf('.'),item.FileName.length)}`
        zip.file(file_name, data, {
          binary: true,
        }); 
        cache[file_name] = data;
      });
      promises.push(promise);
    });
    Promise.all(promises).then(() => {
      zip
        .generateAsync({
          type: "blob",
        })
        .then((content) => {
          FileSaver.saveAs(content, "航拍照片.zip");
        });
    });

  };

  const getImgType=(xx)=>{
      if(xx.includes(".mp4")) return "视频";
      return "照片";
  }

  export const handleBatchDownload2 = async (selectImgList,qianzhui) => {
    const zip = new JSZip();
    const cache = {};
    const promises = [];
    let n1=0;
    let n2=0;

    await selectImgList.forEach((item, index) => {
      const promise = getFile(item.ObjectName).then((data) => {
        const arr_name = item.ObjectName.split("/");
        let file_name = arr_name[arr_name.length - 1];
        const type1=getImgType(item.FileName);
        let qianzhui2="";
        if(type1=="照片") {
          n1=n1+1;
          qianzhui2=qianzhui+"-"+type1+n1;
        }
        if(type1=="视频") {
          n2=n2+1;
          qianzhui2=qianzhui+"-"+type1+n2;
        }
        file_name = qianzhui2+`${item.FileName.slice(item.FileName.indexOf('.'),item.FileName.length)}`
        zip.file(file_name, data, {
          binary: true,
        }); 
        cache[file_name] = data;
      });
      promises.push(promise);
    });
    Promise.all(promises).then(() => {
      zip
        .generateAsync({
          type: "blob",
        })
        .then((content) => {
          FileSaver.saveAs(content, qianzhui+".zip");
        });
    });

  };