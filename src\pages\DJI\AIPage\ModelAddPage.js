import { Input, Form, DatePicker, But<PERSON>, Modal, message } from "antd";
import { useState } from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";
const { TextArea } = Input;
const AIModelAddForm = ({ refrush }) => {
  const [nm, setNM] = useState("");
  const [bz, setBZ] = useState("");
  const [wt, setWT] = useState("");

  const onSave = async (e) => {
    if (isEmpty(nm)) {
      message.info("请先输入模型名称！");
      return;
    }

    if (isEmpty(wt)) {
      message.info("请输入识别物体名称！");
      return;
    }

    const data = {
      AName: nm,
      AList: wt,
      Description: bz,
    };

    const xx = await HPost2("/api/v1/AI/ModelAdd", data);
    if (isEmpty(xx.err)) {
      message.info("创建成功！");
    }
    refrush();
  };

  return (
    <div>
      <div
        style={{
          fontSize: 16.0,
          fontWeight: "bold",
          marginLeft: 12.0,
          marginBottom: 24.0,
        }}
      >
        新建模型
      </div>
      <Form
        labelCol={{
          span: 4,
        }}
        wrapperCol={{
          span: 14,
        }}
        layout="horizontal"
        style={{
          maxWidth: 600,
        }}
      >
        <Form.Item label="模型名称">
          <Input
            onChange={(e) => {
              setNM(e.target.value);
            }}
          ></Input>
        </Form.Item>
        <Form.Item label="功能描述">
          <TextArea
            rows={4}
            onChange={(e) => {
              setBZ(e.target.value);
            }}
          />
        </Form.Item>
        <Form.Item label="识别物">
          <TextArea
            rows={4}
            placeholder="用英文逗号分隔"
            onChange={(e) => {
              setWT(e.target.value);
            }}
          />
        </Form.Item>

        <Form.Item label={" "} colon={false}>
          <Button type="primary" onClick={onSave}>
            保存
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AIModelAddForm;
