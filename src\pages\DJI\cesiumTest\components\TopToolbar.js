import { useState, useEffect, useRef } from 'react';
import { Select, Input, Switch, Tooltip, InputNumber } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import AddButton from '@/components/AddButton';
import { axiosApi } from '@/services/general';
function TopToolbar({ wayline, wayLineData, addNotFlyZone, SwitchHanger, SwitchNotFlyZone, onSave, setWaylineCopy }) {
    // 机库列表
    let [aircraftSNList, setAircraftSNList] = useState([])
    // 页面载入
    useEffect(() => {
        let newRes = []
        axiosApi("/api/open/Device/GetAllList", "GET",).then((res) => {
            res.forEach((item, index) => {
                // if (item.IfOnLine) {
                newRes.push({ value: item.SN, label: item.DName, Lat: item.Lat, Lng: item.Lng, Height: item.Height })
                // }
            })
            setAircraftSNList([...newRes])
            if (newRes.length) {
                axiosApi(`/api/v1/FlyArea/GetCurrent`, "GET", {
                    SN: newRes[0].value,
                }).then((res) => {
                    res.data && addNotFlyZone(res.data)
                })
                if (wayLineData === undefined) {
                    setWaylineCopy({
                        ...wayline,
                        SN: newRes[0].value
                    })
                }
                SwitchHanger(newRes[0].Lng, newRes[0].Lat, newRes[0].Height, newRes[0].value)
            }
        })
    }, []);
    function GlobalSpeedChange(value) {
        wayline.PList.forEach((item, index) => {
            if (item.Speed === wayline.GlobalSpeed) {
                item.Speed = value
            }
        })
        wayline.GlobalSpeed = value
        setWaylineCopy({ ...wayline, })
    }
    function GlobalHeadingAngleChange(e) {
        let res = e.target.value
        if (res > 180) {
            res = 180
        } else if (res < -180) {
            res = -180
        }
        wayline.GlobalHeadingAngle = res
        setWaylineCopy({ ...wayline, })
    }
    function IsParallelLineChange(checked) {
        if (checked === false) {
            wayline.PList.forEach((item, index) => {
                item.ActionList = []
                item.IfStop = true
                item.PType = ''
                item.GlobalHeadingAngle = ''
            })
        }
        setWaylineCopy({
            ...wayline,
            IsParallelLine: checked
        })
    }
    return <div style={{ height: 48, position: 'absolute', top: 20, zIndex: 1, display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
        <Input
            style={{ width: 140, marginLeft: 10 }}
            value={wayline.WaylineName}
            onChange={(e) => {
                setWaylineCopy({
                    ...wayline,
                    WaylineName: e.target.value
                })
            }}>
        </Input>
        <Select
            style={{ width: 140, marginLeft: 10 }}
            value={wayline.SN}
            placeholder={'请选择机库'}
            onChange={(value, option) => {
                setWaylineCopy({
                    ...wayline,
                    SN: value
                })
                SwitchHanger(option.Lng, option.Lat, option.Height, value)
                SwitchNotFlyZone(value)
            }}
            options={aircraftSNList}
        />
        <Tooltip placement="bottom" title='输入完成后按回车确认'>
            <InputNumber
                style={{ width: 140, marginLeft: 10, backgroundColor: '#fdffff' }}
                min={1}
                max={15}
                value={wayline.GlobalSpeed}
                addonBefore={<span style={{}}>速度:</span>}
                onStep={(e, info) => {
                    GlobalSpeedChange(e)
                }}
                onPressEnter={(e) => {
                    GlobalSpeedChange(e)
                }}>
            </InputNumber>
        </Tooltip>
        {/* <Tooltip placement="bottom" title='输入完成后按回车确认'>
            <InputNumber
                style={{ width: 180, marginLeft: 10, backgroundColor: '#fdffff' }}
                min={-180}
                max={180}
                value={wayline.GlobalHeadingAngle}
                addonBefore={<span style={{}}>偏航角:</span>}
                addonAfter={<Tooltip placement="bottom" title='清空偏航角'>
                    <DeleteOutlined onClick={() => {
                        setWaylineCopy({ ...wayline, GlobalHeadingAngle: '' })
                    }} /></Tooltip>}
                onStep={(e, info) => {
                    GlobalHeadingAngleChange(e)
                }}
                onPressEnter={(e) => {
                    GlobalHeadingAngleChange(e)
                }}>
            </InputNumber>
        </Tooltip> */}
        <div style={{ marginLeft: 10, height: 32, padding: '0 10px', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#fdffff' }}>
            <Tooltip placement="bottom" title='是否平行于航线'>
                <Switch defaultChecked onChange={(checked, event) => {
                    IsParallelLineChange(checked)
                }} />
            </Tooltip>
        </div>
        <span style={{ marginLeft: 10 }}><AddButton onClick={onSave} >保存</AddButton></span>
    </div>
}
export default TopToolbar;