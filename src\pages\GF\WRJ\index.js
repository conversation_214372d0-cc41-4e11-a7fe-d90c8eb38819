import { Modal, Config<PERSON>rovider } from "antd";

import { useModel } from "umi";
import locale from "antd/locale/zh_CN";

import "dayjs/locale/zh-cn";
import { useEffect } from "react";

import { getBodyH, isEmpty } from "@/utils/utils";
import TopMenu from "@/components/TopMenu4";
import { Get2 } from "@/services/general";
import { JCStart } from "@/pages/DJI/DRCPage/Panels/RtmpChange";

export default function HomePage() {
  const { page, modal, open, setOpen, lastPage } = useModel("pageModel");

  useEffect(() => {
    // const StartJCZB = async () => {
    //     await HGet2('/api/v1/RtmpSource/Close1')
    //     HGet2('/api/v1/RtmpSource/Start3')
    // };
    const startJC = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      JCStart(device);
    };

    startJC();

    const CloseRtmp = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      Get2(
        `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
      );
      // Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
    };

    window.addEventListener("beforeunload", (event) => {
      CloseRtmp();
    });

    document.body.addEventListener(
      "touchmove",
      function (e) {
        e.preventDefault();
      },
      { passive: false }
    );

    document.body.addEventListener("popstate", function (event) {
      lastPage();
      // e.stopPropagation();
    });
  }, []);

  return (
    <div style={{ width: "100%" }}>
      {" "}
      <ConfigProvider locale={locale}>
        <TopMenu></TopMenu>
        {/* <WebSocketDemo sn={'7CTDLCE00AC2J4'} sn2={'1581F6Q8D23CT00A5N49'} /> */}

        <div style={{ height: getBodyH(56) }}>
          {page}
          <Modal
            title={null}
            footer={null}
            onOk={null}
            style={{ paddingBottom: 72.0 }}
            open={open}
            onCancel={() => setOpen(false)}
          >
            {modal}
          </Modal>
        </div>
      </ConfigProvider>{" "}
    </div>
  );
}
