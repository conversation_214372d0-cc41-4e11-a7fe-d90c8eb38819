
import { Space, Tag, message,Modal,Switch } from 'antd';
import { downloadFile, getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { getGuid, timeFormat, timeFormat2 } from '@/utils/helper';
import { HGet2, HPost2 } from '@/utils/request';
import { Post2 } from '@/services/general';

const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const TableCols =(handleClick)=>{
   
    return [
      {
        title: getTableTitle('机场名称'),
        dataIndex: 'DeviceSN',
        key: 'DeviceSN',
        align:'center',
        render: (record) => (
          getDeviceName(record)
         )
      },
  
            {
              title: getTableTitle('任务名称'),
              dataIndex: 'TaskContent',
              key: 'TaskContent',
              align:'center',
             // width:200,
            },
           
            {
              title: getTableTitle('开始时间'),
              dataIndex: 'CreateTime',
              key: 'CreateTime',
              align:'center',
              render: (record) => (
                timeFormat(record)
               )
            //  width:200,
            },

            {
              title: getTableTitle('飞行时间'),
              dataIndex: 'FlyTM',
              key: 'FlyTM',
              align:'center',
              render: (record) => (
                (record/60).toFixed(0)+" 分钟"
               )
            //  width:200,
            },
            {
              title: getTableTitle('拍摄文件'),
             //dataIndex: 'TaskState',
            // key: 'TaskState',
             align:'center',
             render: (record) => (
  
             <div>
               {/* <Badge style={{marginRight:4.0}} status= {"success"} /> */}
               {/* <span>{record["PhotoUpload"]+"/"+record["PhotoCount"]}</span> */}
              <span>{record["PhotoUpload"]}</span>
             </div>
             )
           }
        
  ,
           ];}
  
  
  
  
  export default TableCols;
  