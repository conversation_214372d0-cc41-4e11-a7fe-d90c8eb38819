// 全景拍照进度模态框样式
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
  
  // 动画效果
  animation: fadeIn 0.3s ease-in-out;
}

.modalContent {
  background: #ffffff;
  border-radius: 16px;
  padding: 40px 32px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  text-align: center;
  min-width: 320px;
  max-width: 400px;
  position: relative;
  
  // 动画效果
  animation: slideUp 0.3s ease-out;
}

.progressContainer {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.progressText {
  text-align: center;
  
  .percentText {
    font-size: 24px;
    font-weight: 600;
    color: #1890ff;
    line-height: 1;
    margin-bottom: 4px;
  }
  
  .stepText {
    font-size: 12px;
    color: #666;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
  }
}

.mainText {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20px;
  font-weight: 500;
}

.statusIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  
  .statusDot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    
    &.idle {
      background-color: #d9d9d9;
    }
    
    &.in_progress {
      background-color: #1890ff;
      animation: pulse 1.5s infinite;
    }
    
    &.ok {
      background-color: #52c41a;
    }
    
    &.fail {
      background-color: #ff4d4f;
    }
  }
  
  .statusText {
    font-size: 14px;
    color: #666;
  }
}

.actionButtons {
  display: flex;
  justify-content: center;
  gap: 12px;
  
  .cancelButton {
    padding: 8px 24px;
    background: #ff4d4f;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    
    &:hover {
      background: #ff7875;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modalContent {
    margin: 20px;
    padding: 32px 24px;
    min-width: auto;
    max-width: calc(100vw - 40px);
  }
  
  .progressContainer {
    :global(.ant-progress-circle) {
      width: 100px !important;
      height: 100px !important;
    }
  }
  
  .progressText {
    .percentText {
      font-size: 20px;
    }
    
    .stepText {
      font-size: 11px;
      max-width: 70px;
    }
  }
  
  .mainText {
    font-size: 15px;
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .modalContent {
    background: #1f1f1f;
    color: #fff;
    
    .mainText {
      color: #fff;
    }
    
    .progressText .stepText {
      color: #999;
    }
    
    .statusText {
      color: #999;
    }
  }
}