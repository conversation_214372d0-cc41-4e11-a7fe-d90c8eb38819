.Navbtn .items {
    filter: drop-shadow(#091445 0 5px 10px);
    width: 185px;
    height: 42px;
    border: 0;
    background: url('./../../../assets/img/btnNav.png') center center no-repeat;
    background-size: 100% 100%;

}

.Navbtn .items>span {
    color: #fff;
 
    text-shadow: #000e4c 2px 5px 8px;
}

.Navbtn .items:hover {
    filter: brightness(1.5);
    transform: translateY(-3px);
}

.Navbtn {
    margin: 0 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

// 通用图标样式基础类
.itemIconBase {
    width: 65px;
    height: 65px;
    background-size: 100% 100%;
    margin-bottom: 5px;
    cursor: pointer;
    &:hover{
        filter: brightness(1.5);
    }
}

// 保持原有的默认图标样式
.itemIcon {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_fly.png');
}
.itemText{
  // 需要从中间到两边渐变，两边透明度为0，中间透明度为0.3
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(8, 231, 203, 0.3), rgba(0, 0, 0, 0));
  padding: 3px 20px;
}

// 各功能按钮的专用图标
.itemIconFly {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_fly.png');
}

.itemIconAirline {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_airline.png');
}

.itemIconDrawline {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_drawline.png');
}

.itemIconBack {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_one_key_return.png');
}

.itemIconStop {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_flight_guidance.png');
}

.itemIconRemote {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_remote_control.png');
}

.itemIconAI {
    .itemIconBase();
    background-image: url('../../SI/assets/image/btn_ai_recognition.png');
}
