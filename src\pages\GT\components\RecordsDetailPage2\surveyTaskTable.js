import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, Button, Tooltip } from "antd";
import { timeFormat } from "@/utils/helper";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';

const getTableTitle = (title) => {
    return (
        <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
    );
};
const surveyTaskTableCols = () => {


    const statusMap = {
        0: { color: 'default', text: '未执行' },
        1: { color: 'processing', text: '执行中' },
        2: { color: 'success', text: '成功' },
        3: { color: 'error', text: '失败' },
        4: { color: 'warning', text: '部分成功' },
    };

    return [
        {
            title: getTableTitle("状态"),
            key: "ExecutionStatus",
            align: "center",
            render: (_, record) => {
                const { color, text } = statusMap[record.ExecutionStatus] || {};
                const statusContent = text ? (
                    <Tag color={color}>{text}</Tag>
                ) : (
                    <span>-</span>
                );
                
                return record.ExecutionStatus === 3 ? (
                    <Tooltip 
                        title={`执行反馈：${record.Remark || '暂无反馈信息'}`}
                        style={{ maxWidth: 400 }}
                    >
                        {statusContent}
                    </Tooltip>
                ) : statusContent;
            }
        },
        {
            title: getTableTitle("执行时间"),

            key: "ExecTime",
            align: "center",
            render: (_, record) => (
                <span>
                    {isEmpty(record.ExecTime)
                        ? "-"
                        : timeFormat(record.ExecTime)
                    }
                </span>
            )
        },
        {
            title: getTableTitle("下载报告"),
            key: "ReportPath",
            align: "center",
            render: (_, record) => {
                if (record.ReportPath) {
                    return (
                        <Button
                            type="link"
                            onClick={() => window.open(record.ReportPath)}
                        >
                            下载报告
                        </Button>
                    );
                }
                return <span>-</span>;
            }
        },

    ];
};

export default surveyTaskTableCols;
