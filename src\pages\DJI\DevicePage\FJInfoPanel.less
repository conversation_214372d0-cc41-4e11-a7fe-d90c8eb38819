@font-face {
  font-family: 'MiSan';
  src: url('../../../assets/fonts/Roboto-Bold.9ece5b48.ttf');
  font-weight: normal;
  font-style: normal;
}

.xxxx {
  background-color: red;
}
/*局部card*/
.cardCon :global(.ant-card) {
  width: 100%;
  height: 100%;
  background: transparent;
}

.cardCon :global(.ant-card .ant-card-head-title) {
  padding-left: 16px;

}

.cardCon :global(.ant-card .ant-card-head-title>div) {

  background-image: linear-gradient(180deg, #149eff 0%, #fff 50%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  cursor: pointer;
  userSelect: none;
}

.cardCon :global(.ant-card .ant-card-head) {
  border: none;
  background: url('./../../../assets/img/bg-stitle.png') bottom left no-repeat;
  background-size: 100% auto;

}

.cardCon :global(.ant-card-head .ant-btn) {
  padding: 0px 0 0px 0;
}


.cardCon :global(.ant-card-small >.ant-card-body) {
  // background: linear-gradient(135deg, rgba(14, 54, 107, 0.8), hsla(223, 75%, 16%, 0.2));
  // border: 1px solid;
  // border-image: linear-gradient(133deg, #007ac3, transparent, transparent) 1 1;
  // padding: 0 0 8px 0;
  // backdrop-filter: blur(5px);
  // background: linear-gradient(135deg, rgb(30 99 162), rgb(10 28 71 / 60%));
  // border: 1px solid;
  // border-image: linear-gradient(133deg, #0cebf7, #18416d 70%, #0071d2) 1 1;
  padding: 0 0 8px 0;
  // backdrop-filter: blur(0px);
}


.cardCon  :global(.ant-card-body .ant-descriptions-item-container) {
  padding: 8px 0;
}

.cardCon  :global(.ant-card-body .ant-descriptions-view td) {
  padding: 0 10px;
}

.cardCon  :global(.ant-card-body .ant-descriptions-view tr:nth-child(even)) {
  // background: linear-gradient(135deg, rgba(14, 54, 107, 0.8), transparent);
  background-color: rgba(8, 231, 203, 0.1);
}

/*局部card===end*/