import React from 'react';
import { Map, Marker } from 'react-amap';
import icon1 from '@/assets/icons/device2.png'
//import { Map as AMapMap } from 'AMap'; // 引入高德地图

window._AMapSecurityConfig = {
  securityJsCode:'88ba28663b4a46ae74e95dfd0d39347a',
}


const AMAP_KEY = '3b1158f01c7dd24f4470a8fbeb76a884'; // 需要替换成自己的高德地图API key
const mapStyle = "amap://styles/blue"; // 地图样式为‘靛青蓝’
//const center = { longitude: 114.298572, latitude: 30.584355 }; // 地图中心点位置，设置在武汉市
const zoom = 12; // 地图缩放级别
const  AMapContainer=({child,center,eve})=> {
  
  return (
    <Map
      amapkey={AMAP_KEY}
      zoom={zoom}
      center={center}
      mapStyle={mapStyle}
      viewMode="3D"
      pitch= {0}
      events={eve}
    >
      {child}
    </Map>
  );
}
export default AMapContainer;