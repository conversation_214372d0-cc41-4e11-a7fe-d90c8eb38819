import React, { useEffect, useMemo, useState } from "react";
import { Get2 } from "@/services/general";
import { HPost2 } from "@/utils/request";
import { BellOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Tooltip, Badge } from "antd";
import style from "./index.module.less";
import useConfigStore from "@/stores/configStore";
const myBell = (props) => {
  let { left, top } = props;
  const { headerHeight } = useConfigStore();
  let [allList, setAllList] = useState();
  async function GetAllList() {
    let arr = [];
    let res = await Get2("/api/v1/MessageData/GetAllList", {});
    if (res && res.length > 0) {
      res.map((item) => {
        if (item.State === 0) {
          arr.push(item);
        }
      });
      setAllList(arr);
    }
  }
  useEffect(() => {
    GetAllList();
  }, []);
  async function EditState(data) {
    data.State = parseFloat(1);
    let res = await HPost2(`/api/v1/MessageData/Update`, data);
    console.log(res.err);
    if (res && res.err == null) {
      GetAllList();
    }
  }

  const [arrow, setArrow] = useState("Show");
  const mergedArrow = useMemo(() => {
    if (arrow === "Hide") {
      return false;
    }
    if (arrow === "Show") {
      return true;
    }
    return {
      pointAtCenter: true,
    };
  }, [arrow]);
  const isBadge = () => {
    return (
      <Badge
        status="warning"
        count={allList?.length}
        overflowCount={10}
        style={{ zIndex: 999, boxShadow: "none", border: 0 }}
        showZero={false}
        title=""
      >
        <Button
          style={{ background: "none", border: 0 }}
          shape="circle"
          variant="text"
          icon={
            <BellOutlined
              style={{ color: "#FDB002", fontSize: 22, fontWeight: "bold" }}
            />
          }
          iconPosition={"end"}
        ></Button>
      </Badge>
    );
  };
  const list = () => {
    return (
      <div className={style.myBellList} style={{ maxHeight:`calc(100vh - ${headerHeight}px)` }}>
        {allList?.map((item) => {
          return (
            <div className={style.item}>
              <div className={style.itemText}>
                {item.MData}
                <div className={style.btn} onClick={() => EditState(item)}>
                  <div className={style.btnText}>EXIT</div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div id={style.MyBell} style={props.style}>
      <Button.Group
        ghost="true"
        type="text"
        shape="circle"
        style={{
          left: left,
          top: top,
        }}
      >
        <Tooltip
          placement="leftBottom"
          title={list}
          color="rgb(79 112 185 / 60%)"
          arrow={mergedArrow}
        >
          {allList && allList.length > 0 ? isBadge() : ""}
        </Tooltip>
      </Button.Group>
    </div>
  );
};
export default myBell;
