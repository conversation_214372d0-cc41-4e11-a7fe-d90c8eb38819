import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ayer,
   Polygon
} from 'react-leaflet'
import 'leaflet/dist/leaflet.css';
import { isEmpty, getBodyH } from '@/utils/utils'
import {  Location_Market5,WaylinePointMarker } from '@/pages/Maps/dt_market';
import {getLayers} from '@/pages/Maps/DiTuJson/ditu.js';


const WayLineMap = (h1,data,device) => {
 
  if(isEmpty(data)) return<div > 12</div>

  
 // const device=getDevice(data.SN)
  

 // if(isEmpty( device)) return <div></div>
  const center=[device.Lat,device.Lng,device.Height];


  if(h1===0){
    h1 = getBodyH(56);
  }
  
  const getPList=(sdata)=>{
     // console.log('getPList',pL);
      const pL=sdata.polygon_unlock;
      const list=[];
      //const xL=pL.split(";");
     
      pL.points.forEach(p => {
          //const p1=p.split(",");
        //  console.log(p,p1[0],p1[1]);
         
            list.push([ p.latitude, p.longitude])
        
      });

   //   console.log('getPList',list);

      return list;
  }


  const getPList2=(pL)=>{
    // console.log('getPList',pL);
     const list=[];
     let i=1;
     
     

     pL.forEach(p=>{
      //
       // const p2= Wgs84ToGcj02(p[0],p[1]);
       // const p3= Wgs84ToGcj02(p2[0],p2[1]);
        list.push(WaylinePointMarker(p,'点位'+i));
        i++;
     })

   return list;
 }

  const xL=getPList(data);

  console.log('getPList',xL)
  return (
    <div>

      <MapContainer layersOptions={null} attributionControl={null} zoomControl={null} preferCanvas={true} center={center} zoom={11} style={{ width:'100%', height: h1 }}>


      <TileLayer
    attribution={null}
 
    url="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
/>
      {Location_Market5({Lat:center[0],Lng:center[1],DeviceName:device.DName})}
      {getLayers()}
        <Polygon weight={2} color={'yellow'} positions={xL}>
          <Tooltip sticky>{'解禁范围'}</Tooltip>
        </Polygon>
      {getPList2(xL)}
      </MapContainer>
    </div>
  )
}


export default WayLineMap;