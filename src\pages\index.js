import { Modal, ConfigProvider } from "antd";
import { useModel } from "umi";
import locale from "antd/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { useEffect } from "react";
import { getBodyH, isEmpty } from "@/utils/utils";
import TopMenu from "@/components/TopMenu4";
import useConfigStore from "@/stores/configStore";
import { Get2 } from "@/services/general";
import { JCStart } from "@/pages/DJI/DRCPage/Panels/RtmpChange";
import GlobalWarning from "@/components/GlobalWarning";
export default function HomePage() {
  const {headerHeight} = useConfigStore();
  const { page, modal, open, setOpen, lastPage } = useModel("pageModel");

  useEffect(() => {
    const startJC = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      JCStart(device);
    };

    startJC();

    const CloseRtmp = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      Get2(
        `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
      );
      // Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
    };

    window.addEventListener("beforeunload", (event) => {
      CloseRtmp();
    });

    document.body.addEventListener(
      "touchmove",
      function (e) {
        e.preventDefault();
      },
      { passive: false }
    );

    document.body.addEventListener("popstate", function (event) {
      lastPage();
    });
  }, []);

  return (
    <div style={{ width: "100%" }}>
      <ConfigProvider locale={locale}>
        <TopMenu></TopMenu>
        <div style={{ height: `calc(100vh - ${headerHeight}px)`, overflow: "auto" }}>
          {page}
          {/* 全局预警弹窗 */}
          <GlobalWarning></GlobalWarning>
          {/* <Modal
            title={null}
            footer={null}
            onOk={null}
            style={{ paddingBottom: 72.0 }}
            open={open}
            onCancel={() => setOpen(false)}
            width={{
            xl: '50%',
            }}
          >
            {modal}
          </Modal> */}
        </div>
      </ConfigProvider>
    </div>
  );
}
