import {Outlet,useLocation, useModel } from 'umi';
import styles from './index.less';
import { Modal, ConfigProvider } from "antd";
import { MainColor } from '@/utils/colorHelper';
import { useEffect } from 'react';
import useConfigStore from "@/stores/configStore";

export default function NewLayout() {
  const initConfig = useConfigStore((state) => state.initConfig)
  const { modal, open, setOpen } = useModel("pageModel");
  const location = useLocation();

  useEffect(() => {
    initConfig()
  }, []);

  if (location.pathname === '/login') {
    return <Outlet />
  }

  return (
    <ConfigProvider
    theme={{
      token: {
        colorPrimary: MainColor,
        borderRadius: 2,
      },
    }}
  >
    <div className={styles.navs}>
      <Outlet /> 
      {/* 添加全局模态框支持 */}
        <Modal
          title={null}
          footer={null}
          open={open}
          onCancel={() => setOpen(false)}
          width={{ xl: '50%' }}
          style={{ paddingBottom: 72.0 }}
        >
          {modal}
        </Modal>
  </div>

  </ConfigProvider>
  );
}
