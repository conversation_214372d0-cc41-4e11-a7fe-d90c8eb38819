import {
    Card, Table, Button, Modal, Form, Input, Select, DatePicker, Checkbox, InputNumber,
    Radio, Upload, message, Spin, Splitter, Tooltip, List, Empty, Image
} from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { axiosApi } from "@/services/general";
import { useState, useEffect, useRef } from 'react';
import { useModel } from "umi";
import './index.css'
import { timeFormat } from '@/utils/helper'

const Orthophoto = () => {
    const searchInputRef = useRef(null);

    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize, setPageSize] = useState(8);
    const [dataSource, setDataSource] = useState([]);
    const [filteredDataSource, setFilteredDataSource] = useState([]); // 过滤后的 dataSource
    const [searchValue, setSearchValue] = useState('');
    const [dateRange, setDateRange] = useState([]);
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [user, setUser] = useState(''); // 登录用户信息

    const [fileList, setFileList] = useState([]); // 即将上传的文件列表

    // 在页数和 一页显示的条数发生变化时重新获取数据 (后端暂时没有分页 所以没效果 只是挂载的时候调用一次)
    useEffect(() => {
        getData();
        setUser(JSON.parse(localStorage.getItem("user"))) // 获取用户信息
    }, [pageIndex, pageSize]);


    // 调用接口获取数据
    const getData = async () => {
        try {
            const url = `/api/v1/Tiff/GetList?pageIndex=${pageIndex}&pageSize=${pageSize}`;
            const res = await axiosApi(url, "GET", null);
            setDataSource(res.data);
            setFilteredDataSource(res.data); //初次获取数据 没有过滤 直接赋值
        } catch (error) {

        }
    };

    // 处理搜索
    const handleSearch = () => {
        const filteredData = dataSource.filter(item => {
            // 文件名模糊匹配 (都转成小写 不区分大小写查询)
            const fileNameMatch = item.FileName
                .toLowerCase()
                .includes(searchValue.toLowerCase());

            // 时间范围匹配
            let dateMatch = true;
            // 如果存在 且长度为2 则进行时间筛选
            if (dateRange && dateRange.length === 2) {
                const start = dateRange[0].startOf('day');
                const end = dateRange[1].endOf('day');
                const createTime = new Date(item.CreateTime);
                dateMatch = createTime >= start && createTime <= end;
            }

            return fileNameMatch && dateMatch;
        });

        setFilteredDataSource(filteredData);
    };

    // 处理重置
    const handleReset = () => {
        setSearchValue('');
        setDateRange([]);
        setFilteredDataSource(dataSource);
    };

    const handlePageChange = (page, pageSize) => {
        setPageIndex(page);
        setPageSize(pageSize);
    };

    // 打开上传弹窗
    const openUpload = () => {
        setAddModalVisible(true);
        setFileList([]); // 清空上传列表
    };

    // 处理上传
    const handleUpload = async () => {
        const formData = new FormData();
        fileList.forEach(file => {
            formData.append('file', file);
        });
        formData.append('chunkNumber', "");
        formData.append('totalChunks', "");
        formData.append('identifier', "");


        try {
            const token = localStorage.getItem('token');
            const res = await axiosApi(
                "/api/v1/upload/file",
                "POST",
                formData,
                {
                    headers: {
                        'auth': token,
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': '*',
                        "Content-Type": "multipart/form-data",
                    },
                }
            );
            message.success("上传成功！");
            console.log('上传成功', res);
            setAddModalVisible(false); // 关闭弹窗
            setFileList([]); // 设置待上传的文件为空
            getData(); // 重新获取数据
        } catch (error) {
            console.log('上传失败', error);
            message.error("上传失败！");
        }
        setAddModalVisible(false);
    };


    // 渲染自定义列表项
    const getItem = (item) => {
        return (
            <div className="list-item-container" key={item.ID}>
                <div className="list-item-content">
                    {item.Thumbnail ? (
                        <Image
                            style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                top: 0
                            }}
                            src={item.Thumbnail}
                            preview={{
                                mask: <div style={{ position: 'absolute' }}>预览</div>,
                            }}
                        />
                    ) : (
                        <div className="placeholder">
                            <Empty
                                description="暂无缩略图"
                            />
                        </div>
                    )}
                </div>
                <span className="file-name">{item.FileName}</span>
                <span className="file-rest">所属组织: {item.OrgCode}</span>
                <span className="file-rest">上传时间: {timeFormat(item.CreateTime)}</span>
                {/* 后续添加控件放在下面 */}
                <div></div>
            </div>
        )
    };

    // useEffect(() => { 
    //     console.log('@@@@ fileList',fileList)
    // },[fileList])

    return (
        <div className='blackBackground'>
            <Card>
                <div className='header'>
                    <div className='search'>
                        <Input
                            ref={searchInputRef}
                            placeholder="请输入素材名"
                            style={{ width: 200 }}
                            value={searchValue}
                            onChange={e => setSearchValue(e.target.value)}
                            onPressEnter={handleSearch}
                            allowClear
                        />
                        <DatePicker.RangePicker
                            showTime
                            style={{ width: 350 }}
                            value={dateRange}
                            onChange={dates => setDateRange(dates)}
                        />
                    </div>
                    <div className='btns'>
                        <Button type="primary" onClick={() => {
                            handleSearch();
                            searchInputRef.current?.focus();
                        }}>
                            查询
                        </Button>
                        <Button type="primary" onClick={handleReset}>重置</Button>
                        <Button type="primary" onClick={openUpload}>上传</Button>
                    </div>
                </div>
                <div className='Orthophoto_body'>
                    <List
                        grid={{ gutter: 25, column: 4 }}
                        dataSource={filteredDataSource}
                        pagination={{
                            defaultCurrent: 1,
                            showSizeChanger: false,
                            showQuickJumper: true,
                            pageSize: pageSize,
                            onChange: handlePageChange,
                        }}
                        style={{
                            height: '70vh',
                            overflowY: "auto",
                            overflowX: "hidden",
                        }}
                        renderItem={item => (
                            <List.Item>
                                {getItem(item)}
                            </List.Item>
                        )}
                    />
                </div>
            </Card>
            <Modal
                title="上传正射文件"
                open={addModalVisible}
                onCancel={() => setAddModalVisible(false)}
                footer={null}
                width="40vw"
                styles={{
                    body: {
                        height: '50vh',
                        display: 'flex',
                        flexDirection: 'column',
                        padding: 24
                    }
                }}
            >
                <Upload.Dragger
                    name="file"
                    multiple={true}
                    accept=".tif,.tiff"
                    fileList={fileList}
                    beforeUpload={(file) => {
                        const isTiff = file.type === 'image/tiff' ||
                            file.name.toLowerCase().endsWith('.tif') ||
                            file.name.toLowerCase().endsWith('.tiff');
                        if (!isTiff) {
                            message.error('仅支持tiff格式文件');
                            return Upload.LIST_IGNORE;
                        }
                        // if (file.size > 1024 * 1024 * 500) { // 500MB限制
                        //     message.error('文件大小不能超过500MB');
                        //     return Upload.LIST_IGNORE;
                        // }
                        return false;
                    }}
                    onChange={({ fileList }) => (setFileList(fileList))}
                    onRemove={(file) => {
                        // 过滤掉被删除的文件
                        setFileList(prev => prev.filter(f => f.uid !== file.uid));
                        return true;
                    }}
                >
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                    <p className="ant-upload-hint">支持TIFF文件上传</p>
                </Upload.Dragger>

                {/* 底部按钮区域 */}
                <div style={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: 8,
                    margin: 16
                }}>
                    <Button
                        type="default"
                        onClick={() => {
                            setAddModalVisible(false);
                            setFileList([]);
                        }}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            handleUpload();
                        }}
                        disabled={fileList.length === 0}
                    >
                        确认提交
                    </Button>
                </div>
            </Modal>
        </div>
    )
};
export default Orthophoto;