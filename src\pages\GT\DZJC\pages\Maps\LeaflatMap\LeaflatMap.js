import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { getMarket5 } from "@/pages/Maps/dt_market";
import DevicePage from "@/pages/DJI/DevicePage";
import { isEmpty,getBodyH } from "@/utils/utils";
import { useEffect, useRef, useState } from "react";
import MeasurePanel from "@/pages/DJI/OrgPage/Panels/MeasurePanel";
import ToggleBaseMapButton from "@/pages/DJI/DevicePage/ToggleBaseMapButton";
import useConfigStore from "@/stores/configStore";
import { GetDiTuGps } from "@/pages/Maps/ditu";
import { wgs84Togcj02 } from "@/pages/Maps/gps_helper";
import { getGuid } from "@/utils/helper";
import { useLocation, history, useModel } from "umi";
import IfShowPanel from "@/components/IfShowPanel";
import AirportsListPanel from "@/pages/GT/DZJC/pages/dataAcquisition/Panels/AirportsListPanel";
import VideoPanel from "@/pages/GT/DZJC/pages/dataAcquisition/Panels/VideoPanel/VideoPanel";
const LeaflatMap = (props) => {

  const { data } = props;
  const { setPage } = useModel("pageModel");
  const { mapUrl, setMapUrl, baseMapLayers } = useModel("mapModel");
  const mapRef = useRef({});
  const baseLayerRef = useRef(null);
  const { setMapSelf, ZSMapUrl } = useConfigStore();
  const [showVideoPanel, setShowVideoPanel] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const handleVideoClick = (device) => {
    setSelectedDevice(device);
    setShowVideoPanel(true);
  };

  // 关闭视频面板
  const handleCloseVideoPanel = () => {
    setShowVideoPanel(false);
  };
  const getPanel = (
    <div style={{ height: "100%" }}>
      <AirportsListPanel
        key={getGuid()}
        data={data}
        route="/gt/DZJC/devicePage"
        onVideoClick={handleVideoClick}
        map={mapRef.current}
      ></AirportsListPanel>
    </div>
  );
  useEffect(() => {
    mapRef.current = L.map("mapDiv", {
      maxZoom: 22,
      minZoom: 3,
      zoomControl: false,
      attributionControl: false,
    });

    if (mapRef.current) {
      setMapSelf(mapRef.current);
    }

    if (!isEmpty(data)) {
      const p2 = getCenter(data);
      mapRef.current.setView(p2, 9);
    }
    //  else {
    //   if (navigator.geolocation) {
    //     navigator.geolocation.getCurrentPosition(
    //       (position) => {
    //         let latitude = position.coords.latitude;
    //         let longitude = position.coords.longitude;
    //         let loction = [latitude, longitude];
    //         mapRef.current.setView(loction, 8);
    //       },
    //       (error) => {
    //         console.log(error.message);
    //       }
    //     );
    //   } else {
    //     console.log("您的浏览器不支持地理位置API");
    //   }
    // }
  }, []);

  const getCenter = (data) => {
    let lat = 0;
    let lng = 0;
    let nn = 0;
    data.forEach((e) => {
      lat = lat + e.Lat;
      lng = lng + e.Lng;
      nn = nn + 1;
    });
    const center = [lat / nn, lng / nn];
    if (GetDiTuGps(mapUrl)) {
      return wgs84Togcj02(center[1], center[0]);
    }
    return center;
  };

  const updateCenter = (newCenter, newZS, newZoom) => {
    if (!mapRef.current) return;
    if (newCenter) {
      mapRef.current.setView(newCenter, 18);
    }
  };
  const layers = useRef({});

  useEffect(() => {
    if (mapRef.current && ZSMapUrl) {
      let item = ZSMapUrl;
      let layer = L.tileLayer(item.Url, {
        tms: item.MapType === 1 ? true : false,
        maxZoom: item.MaxZoom,
        minZoom: item.MinZoom,
        opacity: 1.0,
        attribution: "",
      });
      let center = L.latLng(item.Lat, item.Lng);
      mapRef.current.setView(center, 15);
      if (!isEmpty(layers.current)) {
        mapRef.current.removeLayer(layers.current);
      }
      if (layer) {
        layer.addTo(mapRef.current);
      }
      layers.current = layer;
    }
  }, [ZSMapUrl]);

  useEffect(() => {
    if (mapRef.current && mapUrl) {
      if (baseLayerRef.current) {
        mapRef.current.removeLayer(baseLayerRef.current);
      }
      baseLayerRef.current = L.tileLayer(mapUrl, {
        maxZoom: 22,
        maxNativeZoom: 18,
        zIndex: -1000,
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      }).addTo(mapRef.current);
    }
  }, [mapUrl]);

  useEffect(() => {
    if (mapRef.current) {
      const dt_bz2 =
        "http://t0.tianditu.gov.cn/cia_w/wmts?" +
        "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
        "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
        "&tk=d48a48254cdbd79edd0ed2c541639813";
      L.tileLayer(dt_bz2, { maxZoom: 20, zIndex: -999 }).addTo(mapRef.current);

      // L.tileLayer(GetDiTuUrl()).addTo(mapRef.current);
      //const dt_bz="http://t0.tianditu.gov.cn/cia_c/wmts?tk=d26935ae77bbc1fb3a6866a5b6ff573f"
      // const dt_bz="https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&VERSION=1.0.0&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=d26935ae77bbc1fb3a6866a5b6ff573f"
      const dt_bz =
        "https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=d26935ae77bbc1fb3a6866a5b6ff573f";
      L.tileLayer(
        "http://***************:9001/6251daf8-4127-40e0-980d-c86f8a765b20/map/mianzhu/{z}/{x}/{y}.png"
      ).addTo(mapRef.current);
      //  L.tileLayer("https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/filezip2/202302/{z}/{x}/{y}.png",{tms: true, maxZoom: 20,
      //   minZoom: 1, opacity: 1.0, attribution: ""}).addTo(mapRef.current);
      // L.tileLayer("/map/v1/Map/GetTile?p1=cyl20240610&z={z}&x={x}&y={y}",{tms: true, opacity: 1.0,maxZoom:20,minZoom:1, attribution: ""}).addTo(mapRef.current);
    }
  }, []);

  useEffect(() => {
    if (!isEmpty(data)) {
      const p2 = getCenter(data);
      mapRef.current.setView(p2, 8);
      const mL = getHomeIcon2();
      mL.forEach((e) => {
        e.addTo(mapRef.current);
      });
      // L.geoJSON(line,{style:{
      //   // "color": "#5790ca",
      //   "color": "#d9d6c3",
      //   // "color":"#147b99",
      //   "weight": 8,
      //   "opacity": 1,
      //   "fillColor": 'transparent',
      //   "fillOpacity": 0,
      //   }}
      //  ).addTo(mapRef.current);
    }
  }, [data]);

  const eventHandlers = {
    click: (e2) => {
      const e = e2.target.options.data;
      localStorage.setItem("device", JSON.stringify(e));
      setPage(<DevicePage device={e} />);
    },
  };

  const eventHandlers2 = (e2) => {
    localStorage.setItem("device", JSON.stringify(e2));
    // history.push(`/gt/DZJC/devicePage`, { device: e2 });
    setPage(<DevicePage device={e2} />);
  };

  //data所有设备的数据
  const prevCoordSystemRef = useRef(GetDiTuGps(mapUrl));

  useEffect(() => {
    if (!isEmpty(data)) {
      const currentCoordSystem = GetDiTuGps(mapUrl);
      const coordSystemChanged =
        currentCoordSystem !== prevCoordSystemRef.current;

      // 只有在坐标系统变化时才更新标记
      if (coordSystemChanged) {
        // 清除之前的标记
        mapRef.current.eachLayer((layer) => {
          if (
            layer._icon &&
            layer._icon.className.includes("leaflet-marker-icon")
          ) {
            mapRef.current.removeLayer(layer);
          }
        });

        // 添加新的标记
        const mL = getHomeIcon2();
        mL.forEach((e) => {
          e.addTo(mapRef.current);
        });
      }

      // 更新引用值
      prevCoordSystemRef.current = currentCoordSystem;
    }
  }, [data, mapUrl]);

  const getHomeIcon2 = () => {
    const list = [];
    data.forEach((e) => {
      // 创建设备数据的副本，避免修改原始数据
      const deviceData = { ...e };

      // 根据地图类型进行坐标转换
      if (GetDiTuGps(mapUrl)) {
        const convertedCoords = wgs84Togcj02(deviceData.Lng, deviceData.Lat);
        deviceData.Lat = convertedCoords[0];
        deviceData.Lng = convertedCoords[1];
      }

      list.push(getMarket5(deviceData, () => eventHandlers2(e)));
    });
    return list;
  };

  return (
    <div
      id="mapDiv"
      style={{
        position: "absolute",
        width: "100%",
        height: "calc(100vh - 56px)",
        overflow: "hidden",
      }}
    >
      {isEmpty(mapRef.current) ? null : (
        <MeasurePanel
          left={500}
          map={mapRef.current}
          updateCenter={updateCenter}
          showOrthofire={false}
          showBZBtn={true}
        />
      )}
      <ToggleBaseMapButton />
       {IfShowPanel(getBodyH(150), 280, 0, 8, null, null, getPanel, true)}
      <VideoPanel
        device={selectedDevice}
        visible={showVideoPanel}
        onClose={handleCloseVideoPanel}
      />
    </div>
  );
};

export default LeaflatMap;
