import { theme } from 'antd';

// SI系统全局主题配置
const SITheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#08E7CB',
    colorBgBase: '#091A1B', // 设置透明背景和文字色
    colorBgContainer: 'rgba(0,0,0,0)', // 卡片、组件背景透明
    colorTextBase: '#ffffff', // 文字颜色
    colorBorder: 'rgba(8, 231, 203, 0.25)', // 边框颜色
    colorSplit: 'rgba(8, 231, 203, 0.4)', // 分割线
    colorBgLayout: 'transparent', // Layout
    colorTextPlaceholder:"rgba(26, 188, 156, 1)"//控制占位文本的颜色
  },
  components: {
    Table: {
      // borderColor: '#08E7CB',
      lineHeight: 1.4286,
    },
    Layout: {
      siderBg: 'transparent',
    },
    // 表单组件
    Form: {
      labelColor: '#08E7CB',
      labelFontWeight: 500,
    },
    // 输入框组件
    Input: {
    },
    // 选择器组件
    Select: {
    },
    // 日期选择器组件
    DatePicker: {
    },
    // 按钮组件
    Button: {
    },
    Card: {
      colorBorderSecondary: 'transparent', // 透明
    },
  },
};

export default SITheme;
