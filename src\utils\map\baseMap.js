import { axiosApi } from "@/services/general";

export const defaultBaseMapLayers = [
  {
    id: '1',
    name: '卫星',
    type: 'ArcGISTiledMapServiceLayer',
    tileUrl: 'https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
  },
  {
    id: '2',
    name: '灰白',
    type: 'ArcGISTiledMapServiceLayer',
    tileUrl: 'https://thematic.geoq.cn/arcgis/rest/services/ChinaOnlineStreetGray/MapServer/tile/{z}/{y}/{x}'
  },
  {
    id: '3',
    name: '路网',
    type: 'WebTileLayer',
    tileUrl: 'http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}'
  }
];

export const fetchBaseMapLayers = async () => {
  try {
    const url = '/api/v1/DataDirectory/QueryCatalog'
    const params = {
      pcatalog: 'DDT'
    }
    const response = await axiosApi(url, 'GET', params)

    if (!response || !response.data) {
      console.error('获取底图服务失败：无效的响应数据结构。')
      return defaultBaseMapLayers
    }
    const loadBaseMap = response.data

    // 收集当前节点信息，并仅对第一个子节点进行递归处理
    const collectLayerConfigs = (nodes, configs = []) => {
      nodes.forEach(node => {
        if (node.Attribute?.servicePath) {
          configs.push({
            id: node.Rid,
            name: node.Name,
            type: node.Attribute.serviceTypeName,
            url: node.Attribute.servicePath,
          })
        }
        if (node.Children && node.Children.length > 0) {
          collectLayerConfigs([node.Children[0]], configs)
        }
      })
      return configs
    }

    let layerConfigs = collectLayerConfigs(loadBaseMap)
    const TIANDITU_KEY = 'd26935ae77bbc1fb3a6866a5b6ff573f'

    layerConfigs = layerConfigs.map(config => {
      const { url: layerUrlFromConfig, ...options } = config
      const hasToken = layerUrlFromConfig.includes('tk=')
      const isTianDiTu = layerUrlFromConfig.includes('tianditu.gov.cn')
      let tileUrl = layerUrlFromConfig 
      // 如果是天地图且未包含tk参数，则添加tk参数
      if (isTianDiTu && !hasToken) {
        tileUrl = `${layerUrlFromConfig}${layerUrlFromConfig.includes('?') ? '&' : '?'}tk=${TIANDITU_KEY}`
      }
      return {tileUrl, ...options}
    })

    if (layerConfigs.length === 0) {
        layerConfigs = defaultBaseMapLayers
    }
    return {layerConfigs, rawBaseMapData: loadBaseMap}
  } catch (error) {
    console.error('获取底图图层时出错:', error)
    return {layerConfigs: defaultBaseMapLayers, rawBaseMapData: []}
  }
}