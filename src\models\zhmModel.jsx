
import { useRef, useState } from "react";
import LastPagePanel from "@/components/LastPagePanel";
export default function Page() {
    const pList=useRef([])
    const setPage=(child)=>{
        pList.current.push(child);
        setP(child)
    }

    const setPage2=(child2)=>{
        const child=<LastPagePanel child={child2}></LastPagePanel>
        setPage(child);
     }
    
    const lastPage=()=>{
        const p=pList.current[pList.current.length-2]
        pList.current.pop()
        setP(p)
    }
    return {setPage,setPage2,lastPage};
};