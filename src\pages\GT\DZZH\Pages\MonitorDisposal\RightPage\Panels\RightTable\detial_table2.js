import { useEffect, useState } from "react";
import {
  Button,
  Radio,
  message,
  Modal,
  Descriptions,
  Input,
  Table,
  InputNumber,
} from "antd";
import { HPost2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import img from "@/assets/17bg.jpg";
import EditTable from "./edit_table";
const { TextArea } = Input;

const DetialTable = ({ mapList, record, refrush }) => {
  const [canSee, setCanSee] = useState(false);
  const [formData, setFormData] = useState(record);
  const [pageSize, setPageSize] = useState(3); // 初始页大小
  const handleChange = (key, value) => {
    setFormData((prevState) => ({ ...prevState, [key]: value }));
  };

  const submit = async () => {
    if (!formData.MapName) {
      message.warning("请填写地图名称");
      return;
    }

    let res = await HPost2(`/api/v1/MapData/Update`, formData);
    if (isEmpty(res.err)) {
      message.success(`编辑地图成功!`);
      setCanSee(false);
      refrush();
    } else {
      message.error("错误:" + res.err);
    }
  };

  const columns = [
    {
      title: "图层编号",
      dataIndex: "图层编号",
    },
    {
      title: "村名",
      dataIndex: "村名",
    },
    {
      title: "告警时间",
      dataIndex: "告警时间",
    },
    {
      title: "办理状态",
      dataIndex: "办理状态",
    },
    {
      title: "类型",
      dataIndex: "类型",
    },
    {
      title: "预览图",
      dataIndex: "预览图",
      render: (text) => <img width={110} height={80} src={img}></img>,
    },
    {
      title: "操作",
      align: "center",
      render: (record) => (
        <div
          style={{
            display: "flex",
            justifyContent: "flex-start",
            alignItems: "center",
            flexDirection: "column",
            whiteSpace: "nowrap",
            gap: 5,
          }}
        >
          <EditTable></EditTable>
          <MyButton style={{ padding: "2px 5px" }}>删除</MyButton>
          <MyButton style={{ padding: "2px 5px" }}>核查</MyButton>
          {/* <MyButton style={{ padding: "2px 5px" }}>办理</MyButton> */}
        </div>
      ),
    },
  ];
  const data = [
    {
      key: "图层编号",
      图层编号: "11024343",
      村名: "徐村",
      告警时间: "2024-01-04",
      办理状态: "待处理",
      类型: "违章建筑",
      预览图: 32,
    },
    {
      key: "图层编号",
      图层编号: "11024343",
      村名: "徐村",
      告警时间: "2024-01-04",
      办理状态: "待处理",
      类型: "违章建筑",
      预览图: 32,
    },
    {
      key: "图层编号",
      图层编号: "11024343",
      村名: "徐村",
      告警时间: "2024-01-04",
      办理状态: "待处理",
      类型: "违章建筑",
      预览图: 32,
    },
    {
      key: "图层编号",
      图层编号: "11024343",
      村名: "徐村",
      告警时间: "2024-01-04",
      办理状态: "待处理",
      类型: "违章建筑",
      预览图: 32,
    },
  ];
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );
    },
    getCheckboxProps: (record) => ({
      disabled: record.name === "Disabled User",
      // Column configuration not to be checked
      name: record.name,
    }),
  };
  return (
    <>
      {/* <a onClick={() => setCanSee(true)}>编辑</a> */}
      <MyButton style={{ padding: "2px 5px" }} onClick={() => setCanSee(true)}>
        详情
      </MyButton>
      <Modal
        title={"详情"}
        onOk={submit}
        open={canSee}
        onCancel={() => setCanSee(false)}
        okText="提交"
        cancelText="取消"
        width={900}
        footer={null}
      >
        <Table
          rowSelection={{
            type: "checkbox",
            ...rowSelection,
          }}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: false,
            hideOnSinglePage: true,
          }}
          columns={columns}
          // columns={TableCols(GetAllList, showMap)}
          dataSource={data}
          bordered
          size="small"
        />
      </Modal>
    </>
  );
};

export default DetialTable;
