import React, { useState, useEffect } from "react";

const useMapManager = () => {
  const parsedUrl = new URL(window.location.href);
  const host = parsedUrl.hostname; // 获取主机名

  const [ZSMapUrl, setZSMapUrl] = useState([]); //正射影像图层数组

  let addSWMapUrlArr = [
    {
      //绵竹机场
      SN: "7CTXMBE00B048E",
      url: "http://***************:9000/6251daf8-4127-40e0-980d-c86f8a765b20/b3dm/b3dm_20241212/tileset.json",
    },
    {
      //新津机场
      SN: "7CTXN1400B06DU",
      url: "http://*************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/b3dm/terra_b3dms/tileset.json",
    },
    {
      //温江机场
      SN: "7CTXN1600B06J9",
      url: "http://*************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/Data/tileset.json",
    },
    {
      //公路院上半段桥墩模型
      SN: "7CTDM7100B5U7J",
      url: "http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/公路院上半段桥墩模型/terra_b3dms/tileset.json",
    },
    {
      //公路院下半部分桥台
      SN: "7CTDM7100B5U7J",
      url: "http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/公路院b3dm/Data/tileset.json",
    },
  
  ];


  //桶地址+文件地址
  let addZSMapUrlArr = [
    "http://*************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/zhengshe/{z}/{x}/{y}.png",
    "http://***************:9000/6251daf8-4127-40e0-980d-c86f8a765b20/map/mianzhu/{z}/{x}/{y}.png",
    "http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/tif20241128/{z}/{x}/{y}.png",
    "http://*************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/WenJiangZhenFu/{z}/{x}/{y}.png",
    "http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/tif20241128/{z}/{x}/{y}.png",
    "http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/成果切片/{z}/{x}/{y}.png"
  ];
  //其他公共的和如通过阿里云oss存储的影像图层写在这里
  let addZSMapUrlArr_others = [
    "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/filezip2/202302/{z}/{x}/{y}.png",

  ];

  useEffect(() => {
    //定时器等待地图加载之后再设置正射影像图层
    const timer = setTimeout(() => {
      if (host) {
        initMap(host);
      } else {
        setZSMapUrl([]);
      }
    }, 10);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  function initMap(url) {
    let ZSArr = [];
    for (let i = 0; i < addZSMapUrlArr.length; i++) {
      //根据当前网站的ip地址添加属于自己的正射影像图层
      if (addZSMapUrlArr[i].includes(url)) {
        ZSArr.push(addZSMapUrlArr[i]);
      }
    }
    setZSMapUrl([...ZSArr, ...addZSMapUrlArr_others]);
  }
  return {
    addSWMapUrlArr,
    ZSMapUrl
  };
};

export default useMapManager;
