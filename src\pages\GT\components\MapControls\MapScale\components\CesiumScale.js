import React, { useEffect } from 'react';
import { EllipsoidGeodesic } from 'cesium';
import { useMapScale } from '../hooks/useMapScale';
import styles from '../index.module.css';

const CesiumScale = ({ viewer }) => {
  const { distanceLabel, barPixelWidth, updateScale, geodesicRef } = useMapScale(viewer);

  useEffect(() => {
    if (!viewer || !viewer.scene || !viewer.camera || !viewer.canvas) return;

    if (!geodesicRef.current && viewer.scene.globe) {
      geodesicRef.current = new EllipsoidGeodesic(viewer.scene.globe.ellipsoid);
    }

    if (!geodesicRef.current) return;

    updateScale();
    const removePostRenderListener = viewer.scene.postRender.addEventListener(updateScale);

    return () => {
      removePostRenderListener();
    };
  }, [viewer, updateScale, geodesicRef]);

  if (!distanceLabel || barPixelWidth <= 0) {
    return null;
  }

  return (
    <div className={styles['map-scale-container']}>
      <div className={styles['map-scale-label']}>{distanceLabel}</div>
      <div className={styles['map-scale-bar-wrapper']}>
        <div
          className={styles['map-scale-bar']}
          style={{ width: `${barPixelWidth}px` }}
        />
      </div>
    </div>
  );
};

export default CesiumScale;