import { Space, Tag, message,Modal } from 'antd';
import {  getDeviceName, isEmpty } from '@/utils/utils';
import { Post2 } from '@/services/general';
import { timeFormat } from '@/utils/helper';
import dayjs from 'dayjs';

const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const { confirm } = Modal;

const deleteAirSpaceManage=async(record,refrush)=>{

  const xx=await Post2("/api/v1/AirSpaceManage/Delete",record);

  console.log('deleteAirSpaceManage',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除申请',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该申请吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteAirSpaceManage(record,refrush);
    },
    onCancel() {
    },
  });
};

const TableCols =(refrush,showEdit)=>{return [
          {
            title: getTableTitle('所属机场'),
            dataIndex: 'DeviceSN',
            key: 'DeviceSN',
            align:'center',
            render:(e)=>getDeviceName(e)
          },
          {
            title: getTableTitle('生效时间'),
            dataIndex: 'BeginTM',
            key: 'BeginTM',
            align:'center',
            render:(e)=>timeFormat(e)
          }, 
          {
            title: getTableTitle('到期时间'),
            dataIndex: 'EndTM',
            key: 'EndTM',
            align:'center',
            render:(e)=>timeFormat(e)
          }, 
          {
            title: getTableTitle('剩余天数'),
            dataIndex: 'EndTM',
            key: 'EndTM',
            align:'center',
            render:(e)=>{
               const ts= dayjs(e).diff(dayjs(), 'days');
                let col1='black';
                if(ts<10) col1='orange';
                if(ts<0) col1='red';
                return <div style={{color:col1}}>{ts +' 天'}</div>
              }
          }, 
          {
            title: getTableTitle('申请人'),
            dataIndex: 'UserName',
            key: 'UserName',
            align:'center',
          },
          {
            title: getTableTitle('申请人电话'),
            dataIndex: 'UserPhone',
            key: 'UserPhone',
            align:'center',
          },
        {
             title: getTableTitle('操作'),
            align:'center',
            render: (record) => (
              <Space size="middle">
                <Tag><a onClick={()=>showEdit(record)}>编辑</a></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除</a></Tag>
              </Space>)
          }];
        }

export default TableCols;
