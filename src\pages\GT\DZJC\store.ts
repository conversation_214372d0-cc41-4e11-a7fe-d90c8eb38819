import { create } from "zustand";

interface RegionState {
  region: string;
  setRegion: (value: string) => void;
  device: string;
  setDevice: (value: string) => void;
  surveytask_select: any;
  setSurveytask_select: (value: any) => void;
  waylineData: any;
  setWaylineData: (value: any) => void;

}

const useContentStore = create<RegionState>((set) => ({
  region: "", // 区域
  setRegion: (value) => set({ region: value }),
  device: "", // 机场
  setDevice: (value) => set({ device: value }),
  surveytask_select: [],
  setSurveytask_select: (value) => set({ surveytask_select: value }),
  waylineData: [],
  setWaylineData: (value) => set({ waylineData: value })
}));

export default useContentStore;
