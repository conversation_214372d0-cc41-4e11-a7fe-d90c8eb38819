import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { message } from 'antd';
import { GetCesiumViewer, Cartesian3_TO_Position, SetCameraControl, DrawLookCone, SetCamera, PointImg2 } from '@/utils/cesium_help';
import * as turf from '@turf/turf'
import FJGLB from '@/assets/models/triangle.glb';
import AmapMercatorTilingScheme from '@/utils/cesiumtools/AmapMercatorTilingScheme'

const useWayline3DHooks = (waylineFileInfo, addPlacemark, deletePlacemark, PlacemarkListItemHeightChange, PlacemarkListItemPositionChange, seleteIndexChange, computeHeading, computePitch, actionIndexChange, getMapInstance, setCesiumViewer) => {
    // 大地图viewer
    const viewer = useRef(null);
    // 小地图viewer
    const viewer2 = useRef(null);
    // 飞机模型
    let airplaneModel = useRef(null)
    // 飞机对应地面上的点
    let airplaneModel_entity = useRef(null)
    // 飞机对应地面上的线的位置信息
    let entity_polyline_array = useRef([102.23528379027542, 29.3827550721585, 2000, 102.23528379027542, 29.3827550721585, 0])
    // cesium事件
    let handlerPoint = useRef(null)
    // 移动选中的点
    let move_entity_index = useRef(null);
    // 鼠标移动方式
    let cursor = useRef('')
    // 正在拖动的航点索引
    let move_index = useRef(null)
    // 航点索引
    const seleteIndex = useRef(null)
    // 延时器
    let timer = useRef(null)
    // 视椎体
    let cone = useRef(null)
    // 模型姿态
    let headingPitchRoll = useRef(new Cesium.HeadingPitchRoll());
    // 云台姿态
    let PTZ_headingPitchRoll = useRef(new Cesium.HeadingPitchRoll());
    // 模型初始位置
    let airplaneModel_position = useRef(new Cesium.Cartesian3.fromDegrees(102.23528379027542, 29.3827550721585, 2000));
    // 局部变换坐标系
    let fixedFrameTransform = Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
    // 每次操作姿态变化为5°
    let deltaRadians = useRef(Cesium.Math.toRadians(5.0));
    // 速度
    let speed = useRef(0);
    // 速度向量
    let speedVector = useRef(new Cesium.Cartesian3());
    // 模型平移方向
    let Direction = useRef(Cesium.Cartesian3.UNIT_X);
    // 航点实体集合
    let wayline_cesiumObjectEntity = useRef([]);
    // 航线点位信息
    let wayline_positions = useRef([]);
    // 航线实体
    let wayline_polyline = useRef(null);
    // 标记实体
    let waypointInformation = useRef(null);
    // 模型实体
    let model3Dtiles = useRef(null);
    // 机库图标
    let device_billboard = useRef(null);

    // 页面载入
    useEffect(() => {
        setTimeout(() => {
            // let MapInstance = getMapInstance()
            // if (!MapInstance) {
            //     return
            // }
            // viewer.current = getMapInstance()
            viewer.current = GetCesiumViewer('cesisss')
            setCesiumViewer(viewer.current)
            viewer2.current = GetCesiumViewer('cesisss2')
            viewer2.current.scene.camera.frustum.fov = Cesium.Math.toRadians(30);
            viewer2.current.scene.camera.frustum.aspectRatio = (4 / 3)
            viewer.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
                Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
            );
            SetCameraControl(viewer2.current);
            // 添加视椎体
            cone.current = DrawLookCone(viewer.current, {
                positions: airplaneModel_position.current,
                headingPitchRoll: {
                    heading: headingPitchRoll.current.heading,
                    pitch: PTZ_headingPitchRoll.current.pitch,
                    roll: 0
                },
            }, 1)
            waypointInformation.current = viewer.current.entities.add({
                name: "航点信息",
                position: Cesium.Cartesian3.fromDegrees(102.23528379027542, 29.3827550721585),//#00ee8b
                label: {
                    text: ``,
                    font: '12pt Source Han Sans CN',             //字体样式
                    fillColor: Cesium.Color.WHITE,                //字体颜色
                    backgroundColor: new Cesium.Color(0, 0, 0, 0.9), //背景颜色
                    showBackground: true,                         //是否显示背景颜色
                    style: Cesium.LabelStyle.FILL,                //label样式
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    pixelOffset: new Cesium.Cartesian2(0, -30),      //偏移
                },
            });
            // 创建机库图标
            device_billboard.current = viewer.current.entities.add({
                position: Cesium.Cartesian3.fromDegrees(103.99177815222232, 30.762915428775877, 573.5),
                billboard: {
                    image: require("@/assets/icons/device.png"),
                    scale: 0.1,
                    maxiumScale: 0.5,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
            });
            viewer.current.scene.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(103.99177815222232, 30.762915428775877, 5000),
                orientation: {
                    heading: 0,
                    pitch: Cesium.Math.toRadians(-90),
                    roll: 0,
                },
            });
            // 添加cesium事件
            handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer.current.scene.canvas)
            // 添加飞机模型
            fromGltfAsync()
            //注册事件
            document.addEventListener('keyup', onKeyup)
            document.addEventListener('keydown', onKeydown)
            //鼠标事件
            LEFT_DOWN()
            LEFT_UP()
            MOUSE_MOVE()
            RIGHT_CLICK()
        }, 1000)

        return () => {
            destroy()
        };
    }, []);
    // 使用primitive方式加载模型
    function fromGltfAsync() {
        // 使用primitive方式加载模型
        Cesium.Model.fromGltfAsync({
            url: FJGLB,
            scale: 2,
            modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                airplaneModel_position.current,
                headingPitchRoll.current,
                Cesium.Ellipsoid.WGS84,
                fixedFrameTransform
            ),
            minimumPixelSize: 64,
            maximumScale: 128
        }).then(res => {
            airplaneModel.current = viewer.current.scene.primitives.add(res)
            //渲染阶段添加侦听
            viewer.current.scene.preUpdate.addEventListener(() => {
                // 模型平移方向
                Cesium.Cartesian3.multiplyByScalar(
                    Direction.current,
                    speed.current / 10,
                    speedVector.current
                );
                // 生成新的位置信息
                Cesium.Matrix4.multiplyByPoint(
                    airplaneModel.current.modelMatrix,
                    speedVector.current,
                    airplaneModel_position.current
                )
                // 更新模型姿态与位置
                Update_model_position()
                let cartesian = new Cesium.Cartesian3(airplaneModel.current._boundingSphere.center.x, airplaneModel.current._boundingSphere.center.y, airplaneModel.current._boundingSphere.center.z);
                if (!Cesium.Cartographic.fromCartesian(cartesian)) {
                    return
                }
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                // 改变地面点的位置
                airplaneModel_entity.current.position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                // 改变飞机与地面点连接线的位置信息
                entity_polyline_array.current = [longitude, latitude, globeHeight, longitude, latitude, height]
                viewer.current.scene.primitives.remove(cone.current.c1);
                viewer.current.scene.primitives.remove(cone.current.c2);
                cone.current = DrawLookCone(viewer.current, {
                    positions: cartesian,
                    headingPitchRoll: {
                        heading: headingPitchRoll.current.heading,
                        pitch: PTZ_headingPitchRoll.current.pitch,
                        roll: 0
                    },
                }, 1)
                viewer2.current.scene.camera.frustum.fov = Cesium.Math.toRadians(15 / 1);
                SetCamera(viewer2.current, longitude, latitude, height, headingPitchRoll.current.heading, PTZ_headingPitchRoll.current.pitch, 0, 1200)
            })
        })
        // 添加一个点，用来表示无人机模型的经纬度，辅助判断
        airplaneModel_entity.current = viewer.current.entities.add({
            position: airplaneModel_position.current, // 使用特定经纬度来表示飞机位置
            name: 'airplaneModel_entity',
            point: {
                pixelSize: 5,
                color: Cesium.Color.YELLOW, // 点的颜色
            },
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(entity_polyline_array.current);
                }, false),
                // 宽度
                width: 1,
                // 线的颜色
                material: Cesium.Color.YELLOW,
            }
        })
    }
    // 更新模型姿态与位置
    function Update_model_position() {
        if (!airplaneModel.current) {
            setTimeout(() => {
                Update_model_position()
            }, 1000)
            return
        }
        Cesium.Transforms.headingPitchRollToFixedFrame(
            airplaneModel_position.current,
            headingPitchRoll.current,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransform,
            airplaneModel.current.modelMatrix
        )
    }
    // 鼠标左键按下事件
    function LEFT_DOWN() {
        handlerPoint.current.setInputAction(function (e) {
            let pickedObject = viewer.current.scene.pick(e.position);
            if (move_entity_index.current !== null) {
                timer.current = setTimeout(() => {
                    if (pickedObject.id.point && pickedObject.id.polyline) {
                        viewer.current._container.style.cursor = "move";
                        cursor.current = "move"
                        move_index.current = Number(pickedObject.id.name)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    } else if (pickedObject.id.billboard) {
                        viewer.current._container.style.cursor = "s-resize";
                        cursor.current = "s-resize"
                        move_index.current = Number(pickedObject.id.name)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    }
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            } else {
                timer.current = setTimeout(() => {
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            }
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }
    // 鼠标左键抬起事件
    function LEFT_UP() {
        handlerPoint.current.setInputAction(function (e) {
            if (timer.current) {
                clearTimeout(timer.current)
                if (move_entity_index.current === null) {
                    let ray = viewer.current.camera.getPickRay(e.position);
                    let cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                    airplaneModel_position.current = new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight + 200)
                    // 更新模型姿态与位置
                    Update_model_position()
                    // 103.99177815222232 30.762915428775877 476.94319398438785
                    // console.log(longitude, latitude, height, globeHeight);
                    // for (let i = 0; i < 16; i++) {
                    //     let destination = turf.destination(turf.point([longitude, latitude]), 0.005, -180 + 22.5 * i);
                    //     if (i<3) {
                    //         viewer.current.entities.add({
                    //             position: new Cesium.Cartesian3.fromDegrees(destination.geometry.coordinates[0], destination.geometry.coordinates[1], height + 5),
                    //             point: {
                    //                 pixelSize: 10,
                    //                 color: Cesium.Color.WHITE, // 点的颜色
                    //             },
                    //         })
                    //     }

                    // }
                } else {
                    SwitchWaypoint(move_entity_index.current)
                }
            } else {
                console.log('长按事件');
                viewer.current._container.style.cursor = "";
                cursor.current = ""
                move_index.current = null
                viewer.current.scene.screenSpaceCameraController.enableRotate = true;//开启旋转
            }
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
    // 鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            let pickedObject = viewer.current.scene.pick(e.endPosition);
            if (pickedObject && pickedObject.id && typeof pickedObject.id.name === 'number') {
                if (move_entity_index.current !== null) {
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.WHITE
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.WHITE
                    })
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.WHITE
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = new Cesium.Color.fromCssColorString('#2d8cf0')
                    move_entity_index.current = null
                }
                move_entity_index.current = Number(pickedObject.id.name)
                if (pickedObject.id.point && pickedObject.id.polyline) {
                    // viewer1.current._container.style.cursor = "move";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.RED
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.RED
                    })
                } else if (pickedObject.id.billboard) {
                    // viewer1.current._container.style.cursor = "s-resize";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.RED
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = Cesium.Color.RED
                }
            } else {
                if (move_entity_index.current !== null) {
                    viewer.current._container.style.cursor = "";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.WHITE
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.WHITE
                    })
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.WHITE
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = new Cesium.Color.fromCssColorString('#2d8cf0')
                    move_entity_index.current = null
                }
            }
            if (cursor.current === "move") {
                viewer.current._container.style.cursor = "move";
                SwitchWaypoint(move_index.current)
                if (viewer.current.scene.mode !== Cesium.SceneMode.MORPHING) {
                    // scene.pick: 返回scene中指定位置的顶端的primitive属性的一个对象
                    let pickedObject = viewer.current.scene.pick(e.endPosition);
                    // 判断是否拾取到模型
                    if (viewer.current.scene.pickPositionSupported && Cesium.defined(pickedObject)) {
                        let cartesian = viewer.current.scene.pickPosition(e.endPosition);
                        // 是否获取到空间坐标
                        if (Cesium.defined(cartesian)) {
                            // // 空间坐标转世界坐标(弧度)
                            let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                            // 弧度转为角度（经纬度）
                            let longitude = Cesium.Math.toDegrees(cartographic.longitude);
                            let latitude = Cesium.Math.toDegrees(cartographic.latitude);
                            //模型高度
                            let height = cartographic.height;
                            item_position_change_function(move_index.current, longitude, latitude, height, PlacemarkListItemPositionChange)
                        }
                    } else {
                        let ray = viewer.current.camera.getPickRay(e.endPosition);
                        let cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                        item_position_change_function(move_index.current, longitude, latitude, globeHeight, PlacemarkListItemPositionChange)
                    }
                }

            } else if (cursor.current === "s-resize") {
                viewer.current._container.style.cursor = "s-resize";
                SwitchWaypoint(move_index.current)
                let move_y = Math.ceil(e.startPosition.y - e.endPosition.y)
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[move_index.current].point_cesiumObjectEntity.polyline.positions._value[1], viewer.current)
                let val = height + move_y
                if (val < 10) {
                    val = 10
                }
                item_height_change_function(move_index.current, val, PlacemarkListItemHeightChange)
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    // 右击事件
    function RIGHT_CLICK() {
        handlerPoint.current.setInputAction(function (e) {
            if (viewer.current.scene.mode !== Cesium.SceneMode.MORPHING) {
                // scene.pick: 返回scene中指定位置的顶端的primitive属性的一个对象
                let pickedObject = viewer.current.scene.pick(e.position);
                // 判断是否拾取到模型
                if (viewer.current.scene.pickPositionSupported && Cesium.defined(pickedObject)) {
                    let cartesian = viewer.current.scene.pickPosition(e.position);
                    // 是否获取到空间坐标
                    if (Cesium.defined(cartesian)) {
                        // // 空间坐标转世界坐标(弧度)
                        let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                        // 弧度转为角度（经纬度）
                        let longitude = Cesium.Math.toDegrees(cartographic.longitude);
                        let latitude = Cesium.Math.toDegrees(cartographic.latitude);
                        //模型高度
                        let height = cartographic.height;
                        addWaypoint(longitude, latitude, height, height, cartesian, addPlacemark)
                    }
                } else {
                    let ray = viewer.current.camera.getPickRay(e.position);
                    let cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                    addWaypoint(longitude, latitude, height, globeHeight, cartesian, addPlacemark)
                }
            }

        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 添加航点
    function addWaypoint(longitude, latitude, height, globeHeight, cartesian, callback) {
        let point_cesiumObjectEntity = null
        let billboard_cesiumObjectEntity = null
        let height_cesiumObjectEntity = null
        let distance_cesiumObjectEntity = null
        if (wayline_cesiumObjectEntity.current.length < 1) {
            wayline_positions.current.push(longitude, latitude, height, longitude, latitude, height + waylineFileInfo.current.Folder.globalHeight)
            // 创建航线
            wayline_polyline.current = creat_wayline_polyline()
            point_cesiumObjectEntity = creat_point_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height, longitude, latitude, height + waylineFileInfo.current.Folder.globalHeight]))
            billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, Cesium.Cartesian3.fromDegrees(longitude, latitude, height + waylineFileInfo.current.Folder.globalHeight), PointImg2(1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE)
            height_cesiumObjectEntity = creat_height_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, new Cesium.Cartesian3.fromDegrees(longitude, latitude, (height + (height + waylineFileInfo.current.Folder.globalHeight)) / 2), (waylineFileInfo.current.Folder.globalHeight).toFixed(0))
            wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity })
            if (typeof callback === 'function') {
                callback(0, longitude, latitude, height)
            }
            SwitchWaypoint(0)
        } else {
            wayline_positions.current.splice((seleteIndex.current * 3 + 6), 0, longitude, latitude, (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : height) + waylineFileInfo.current.Folder.globalHeight)
            // 添加航点
            point_cesiumObjectEntity = creat_point_cesiumObjectEntity(seleteIndex.current + 2, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height, longitude, latitude, (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : height) + waylineFileInfo.current.Folder.globalHeight]))
            billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(seleteIndex.current + 2, Cesium.Cartesian3.fromDegrees(longitude, latitude, (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : height) + waylineFileInfo.current.Folder.globalHeight), PointImg2(wayline_cesiumObjectEntity.current.length + 1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE)
            height_cesiumObjectEntity = creat_height_cesiumObjectEntity(seleteIndex.current + 2, new Cesium.Cartesian3.fromDegrees(longitude, latitude, (height + ((waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : height) + waylineFileInfo.current.Folder.globalHeight)) / 2), (waylineFileInfo.current.Folder.globalHeight).toFixed(0))
            let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[(seleteIndex.current * 3 + 3)], wayline_positions.current[(seleteIndex.current * 3 + 4)], wayline_positions.current[(seleteIndex.current * 3 + 5)]); // 第一个点的坐标（x、y、z）
            let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[(seleteIndex.current * 3)], wayline_positions.current[(seleteIndex.current * 3 + 1)], wayline_positions.current[(seleteIndex.current * 3 + 2)]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            let distance = Cesium.Cartesian3.distance(point1, point2);
            distance_cesiumObjectEntity = creat_distance_cesiumObjectEntity(`第${seleteIndex.current + 2}个距离`, centerPosition, `${distance.toFixed(0)}m`)
            wayline_cesiumObjectEntity.current.splice(seleteIndex.current + 1, 0, { point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity, distance_cesiumObjectEntity })
            // 将所有航点实体遍历一遍调整数据
            adjust_wayline_cesiumObjectEntity()
            if (typeof callback === 'function') {
                callback(seleteIndex.current, longitude, latitude, height)
            }
            SwitchWaypoint(seleteIndex.current + 1)
        }
    }
    // 删除航点
    function deleteWaypoint(delete_index) {
        if (delete_index > 0) {
            seleteIndex.current = delete_index - 1
            seleteIndexChange(delete_index - 1)
            SwitchWaypoint(delete_index - 1)
        } else if (delete_index === 0) {
            if (waylineFileInfo.current.Folder.PlacemarkList.length > 1) {
                seleteIndex.current = 0
                seleteIndexChange(0)
            } else {
                seleteIndex.current = null
                seleteIndexChange(null)
            }
        }
        // 先处理航线点位信息
        if (delete_index === 0) {
            if (wayline_cesiumObjectEntity.current.length === 1) {//如果只有一个航点
                wayline_positions.current = []
                viewer.current.entities.remove(wayline_polyline.current)
            } else {//如果有不止一个航点
                wayline_positions.current.splice(0, 3)
                wayline_positions.current[0] = wayline_positions.current[3]
                wayline_positions.current[1] = wayline_positions.current[4]
                let height = viewer.current.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(new Cesium.Cartesian3.fromDegrees(wayline_positions.current[3], wayline_positions.current[4], wayline_positions.current[5])))
                wayline_positions.current[2] = height
            }
        } else {
            wayline_positions.current.splice((delete_index + 1) * 3, 3)
        }
        // 删除对应航点所有实体
        // eslint-disable-next-line guard-for-in
        for (const key in wayline_cesiumObjectEntity.current[delete_index]) {
            viewer.current.entities.remove(wayline_cesiumObjectEntity.current[delete_index][key])
        }
        // 删除航点实体数组中对应集合
        wayline_cesiumObjectEntity.current.splice(delete_index, 1)
        // 将所有航点实体遍历一遍调整数据
        adjust_wayline_cesiumObjectEntity()
        if (delete_index >= wayline_cesiumObjectEntity.current.length) {
            if (wayline_positions.current.length >= 9) {
                let N = wayline_positions.current.length - 1
                let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
                computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            }
        }
        deletePlacemark(delete_index)
        move_entity_index.current = null
    }
    // 将所有航点实体遍历一遍调整数据
    function adjust_wayline_cesiumObjectEntity() {
        wayline_cesiumObjectEntity.current.forEach((item, index) => {
            if (index === 0) {
                item.point_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.position.setValue(new Cesium.Cartesian3.fromDegrees(wayline_positions.current[3], wayline_positions.current[4], wayline_positions.current[5]))
                item.billboard_cesiumObjectEntity.billboard.image.setValue(PointImg2(index + 1))
                item.height_cesiumObjectEntity.name = index
                if (item.distance_cesiumObjectEntity) {
                    viewer.current.entities.remove(item.distance_cesiumObjectEntity)
                }
            } else {
                item.point_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.billboard.image.setValue(PointImg2(index + 1))
                item.height_cesiumObjectEntity.name = index
                item.distance_cesiumObjectEntity.name = `第${index}个距离`
                let N = (index + 2) * 3 - 1
                let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第一个点的坐标（x、y、z）
                let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 5], wayline_positions.current[N - 4], wayline_positions.current[N - 3]); // 第二个点的坐标（x、y、z）
                //计算中点坐标
                let centerPosition = new Cesium.Cartesian3();
                Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                let distance = Cesium.Cartesian3.distance(point1, point2);
                item.distance_cesiumObjectEntity.position.setValue(centerPosition)
                item.distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
            }
        })
    }
    // 销毁函数
    function destroy() {
        viewer.current.scene.primitives.remove(airplaneModel.current);
        viewer.current.scene.primitives.remove(cone.current.c1);
        viewer.current.scene.primitives.remove(cone.current.c2);
        viewer.current.entities.removeAll();
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.WHEEL, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.CTRL);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        //移除事件
        document.removeEventListener('keyup', onKeyup);
        document.removeEventListener('keydown', onKeydown);
    }
    // 切换高度模式
    function switchHeightMode(val) {
        for (let i = 3; i < wayline_positions.current.length; i += 3) {
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(new Cesium.Cartesian3.fromDegrees(wayline_positions.current[(i)], wayline_positions.current[(i + 1)], wayline_positions.current[(i + 2)]), viewer.current)
            if (val === 'relativeToStartPoint') {
                wayline_positions.current[(i + 2)] = waylineFileInfo.current.Folder.globalHeight + wayline_positions.current[2]
            } else if (val === 'aboveGroundLevel') {
                wayline_positions.current[(i + 2)] = globeHeight + waylineFileInfo.current.Folder.globalHeight
            }

        }
        for (let i = 0; i < wayline_cesiumObjectEntity.current.length; i++) {
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[i].billboard_cesiumObjectEntity.position._value, viewer.current)
            if (val === 'relativeToStartPoint') {
                let point_position = wayline_cesiumObjectEntity.current[i].point_cesiumObjectEntity.position.getValue()
                const cartographic = Cesium.Cartographic.fromCartesian(point_position);
                const height1 = cartographic.height;
                wayline_cesiumObjectEntity.current[i].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(longitude, latitude, (waylineFileInfo.current.Folder.globalHeight + wayline_positions.current[(2)])))
                wayline_cesiumObjectEntity.current[i].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height1, longitude, latitude, (waylineFileInfo.current.Folder.globalHeight + wayline_positions.current[(2)])]))
                wayline_cesiumObjectEntity.current[i].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(longitude, latitude, (height1 + (waylineFileInfo.current.Folder.globalHeight + wayline_positions.current[2])) / 2))
                if (wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity) {
                    let M = (i + 2) * 3 - 3
                    let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M - 3], wayline_positions.current[M - 2], wayline_positions.current[M - 1]); // 第一个点的坐标（x、y、z）
                    let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[M + 1], wayline_positions.current[M + 2]); // 第二个点的坐标（x、y、z）
                    //计算中点坐标
                    let centerPosition = new Cesium.Cartesian3();
                    Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                    let distance = Cesium.Cartesian3.distance(point1, point2);
                    wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity.position.setValue(centerPosition)
                    wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
                }
            } else if (val === 'aboveGroundLevel') {
                let point_position = wayline_cesiumObjectEntity.current[i].point_cesiumObjectEntity.position.getValue()
                const cartographic = Cesium.Cartographic.fromCartesian(point_position);
                const height1 = cartographic.height;
                wayline_cesiumObjectEntity.current[i].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(longitude, latitude, (globeHeight + waylineFileInfo.current.Folder.globalHeight)))
                wayline_cesiumObjectEntity.current[i].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height1, longitude, latitude, (globeHeight + waylineFileInfo.current.Folder.globalHeight)]))
                wayline_cesiumObjectEntity.current[i].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(longitude, latitude, (height1 + (globeHeight + waylineFileInfo.current.Folder.globalHeight)) / 2))
                if (wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity) {
                    let M = (i + 2) * 3 - 3
                    let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M - 3], wayline_positions.current[M - 2], wayline_positions.current[M - 1]); // 第一个点的坐标（x、y、z）
                    let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[M + 1], wayline_positions.current[M + 2]); // 第二个点的坐标（x、y、z）
                    //计算中点坐标
                    let centerPosition = new Cesium.Cartesian3();
                    Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                    let distance = Cesium.Cartesian3.distance(point1, point2);
                    wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity.position.setValue(centerPosition)
                    wayline_cesiumObjectEntity.current[i].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
                }
            }
        }
    }
    // 改变某个航点高度
    function item_height_change_function(index, val, callback) {
        let N = (index + 2) * 3 - 1
        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.position._value, viewer.current)
        let positionInfo = { lng: longitude, lat: latitude, height: val }
        wayline_positions.current[N] = positionInfo.height
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([positionInfo.lng, positionInfo.lat, height, positionInfo.lng, positionInfo.lat, positionInfo.height]))
        wayline_cesiumObjectEntity.current[index].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, positionInfo.height))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, (positionInfo.height + height) / 2))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.label.text.setValue(`${(positionInfo.height - wayline_positions.current[2]).toFixed(0)}m`)
        if (index !== 0) {
            let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第一个点的坐标（x、y、z）
            let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 5], wayline_positions.current[N - 4], wayline_positions.current[N - 3]); // 第二个点的坐标（x、y、z）
            let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            let distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N + 1], wayline_positions.current[N + 2], wayline_positions.current[N + 3]); // 第一个点的坐标（x、y、z）
            let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第二个点的坐标（x、y、z）
            let coordItem = [[wayline_positions.current[N - 2], wayline_positions.current[N - 1]], [wayline_positions.current[N + 1], wayline_positions.current[N + 2]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            let distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        SwitchWaypoint(index)
        // 执行回调函数（如果提供了）
        if (typeof callback === 'function') {
            callback(index, positionInfo.height - wayline_positions.current[2]);
        }
        // PlacemarkListItemHeightChange(index, positionInfo.height - wayline_positions.current[2])
    }
    // 改变某个航点位置
    function item_position_change_function(index, longitude, latitude, globeHeight, callback) {
        let M = (index + 2) * 3 - 3
        let N = (index + 2) * 3 - 2
        if (index === 0) {
            wayline_positions.current[0] = longitude
            wayline_positions.current[1] = latitude
            wayline_positions.current[2] = globeHeight
        }
        wayline_positions.current[M] = longitude
        wayline_positions.current[N] = latitude
        wayline_positions.current[N + 1] = (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : globeHeight) + waylineFileInfo.current.Folder.globalHeight
        let positionInfo = { lng: longitude, lat: latitude, height: wayline_positions.current[N + 1] }
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.position.setValue(new Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, globeHeight))
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([positionInfo.lng, positionInfo.lat, globeHeight, positionInfo.lng, positionInfo.lat, (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : globeHeight) + waylineFileInfo.current.Folder.globalHeight]))
        wayline_cesiumObjectEntity.current[index].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, (waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : globeHeight) + waylineFileInfo.current.Folder.globalHeight))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, (globeHeight + ((waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode === 'relativeToStartPoint' ? wayline_positions.current[2] : globeHeight) + waylineFileInfo.current.Folder.globalHeight)) / 2))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.label.text.setValue(`${(waylineFileInfo.current.Folder.globalHeight).toFixed(0)}m`)
        if (index !== 0) {
            let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]); // 第一个点的坐标（x、y、z）
            let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M - 3], wayline_positions.current[N - 3], wayline_positions.current[N - 2]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            let distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            let point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M + 3], wayline_positions.current[N + 3], wayline_positions.current[N + 4]); // 第一个点的坐标（x、y、z）
            let point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            let distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        // 执行回调函数（如果提供了）
        if (typeof callback === 'function') {
            callback(index, positionInfo.lng, positionInfo.lat, globeHeight);
        }
        // PlacemarkListItemPositionChange(index, positionInfo.lng, positionInfo.lat, globeHeight)
    }
    // 按下键盘
    function onKeydown(event) {
        if (airplaneModel.current._show === false) {
            return
        }
        switch (event.key) {
            // 左转
            case 'q':
                headingPitchRoll.current.heading -= deltaRadians.current;
                // 判断是否超过2π范围
                if (headingPitchRoll.current.heading < -Cesium.Math.TWO_PI) {
                    headingPitchRoll.current.heading += Cesium.Math.TWO_PI;
                }
                break;
            // 右转
            case 'e':
                headingPitchRoll.current.heading += deltaRadians.current;
                // 判断是否超过2π范围
                if (headingPitchRoll.current.heading > Cesium.Math.TWO_PI) {
                    headingPitchRoll.current.heading -= Cesium.Math.TWO_PI;
                }
                break;
            // 前进
            case 'w':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = -10
                break;
            // 后退
            case 's':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 10;
                break;
            // 上升
            case 'c':
                Direction.current = Cesium.Cartesian3.UNIT_Z
                speed.current = 10
                break;
            // 下降
            case 'z':
                Direction.current = Cesium.Cartesian3.UNIT_Z
                speed.current = -10;
                break;
            // 左平移
            case 'a':
                Direction.current = Cesium.Cartesian3.UNIT_Y
                speed.current = -10
                break;
            // 右平移
            case 'd':
                Direction.current = Cesium.Cartesian3.UNIT_Y
                speed.current = 10;
                break;
            // 加速
            case 'x':
                // speed.current = 20;
                break;
            // 添加航点
            case ' ':
                break;
            default:
                break;
        }
    }
    // 结束按键
    function onKeyup(event) {
        if (airplaneModel.current._show === false) {
            return
        }
        switch (event.key) {
            // 加速
            case 'w':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 减速
            case 's':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            // 停止加速
            case 'x':
                speed.current = 0;
                break;
            // 上升
            case 'c':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 下降
            case 'z':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            // 左平移
            case 'a':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 右平移
            case 'd':
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            default:
                break;
        }
    }
    // 创建航线
    function creat_wayline_polyline() {
        return viewer.current.entities.add({
            name: `航线`,
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_positions.current);
                }, false),
                // 宽度
                width: 6,
                arcType: Cesium.ArcType.RHUMB,
                material: Cesium.Color.fromCssColorString('#0aed8a'),
            },
        })
    }
    // 创建point_cesiumObjectEntity
    function creat_point_cesiumObjectEntity(name, point_position, polyline_positions) {
        return viewer.current.entities.add({
            name: name,
            position: point_position,
            point: {
                pixelSize: 10,
                color: Cesium.Color.WHITE, // 点的颜色
            },
            polyline: {
                positions: polyline_positions,
                // 宽度
                width: 2,
                // 线的颜色
                material: new Cesium.PolylineDashMaterialProperty({
                    color: Cesium.Color.WHITE
                }),
            },
        })
    }
    // 创建billboard_cesiumObjectEntity
    function creat_billboard_cesiumObjectEntity(name, position, image, color, scale, heightReference) {
        return viewer.current.entities.add({
            name: name,
            position: position,
            billboard: {
                image,
                heightReference,
                color,
                scale,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
            },
        })
    }
    // 创建height_cesiumObjectEntity
    function creat_height_cesiumObjectEntity(name, position, text) {
        return viewer.current.entities.add({
            name: name,
            position: position,
            label: {
                text: `${text}m`,
                font: '8pt Source Han Sans CN',             //字体样式
                fillColor: Cesium.Color.WHITE,                //字体颜色
                backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                showBackground: true,                         //是否显示背景颜色
                style: Cesium.LabelStyle.FILL,                //label样式
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.CENTER,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,//水平位置
                pixelOffset: new Cesium.Cartesian2(10, 0),      //偏移
            }
        })
    }
    // 创建distance_cesiumObjectEntity
    function creat_distance_cesiumObjectEntity(name, position, text) {
        return viewer.current.entities.add({
            name: name,
            position: position,
            label: {
                text: text,
                font: '8pt Source Han Sans CN',             //字体样式
                fillColor: Cesium.Color.WHITE,                //字体颜色
                backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                showBackground: true,                         //是否显示背景颜色
                style: Cesium.LabelStyle.FILL,                //label样式
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
            }
        })
    }
    // 切换正在编辑的航点,改变航点索引
    function SwitchWaypoint(index) {
        if (index === null) {
            waypointInformation.current.label.show = (false)
            return
        }
        seleteIndex.current = index
        actionIndexChange(null)
        seleteIndexChange(index)
        waypointInformation.current.position.setValue(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.getValue()[1])
        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.getValue()[1], viewer.current)
        waypointInformation.current.label.show = (true)
        waypointInformation.current.label.text.setValue(`航点${index + 1}\nHEA:${height.toFixed(0)}m`)
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            let M = (index + 2) * 3 - 3
            let N = (index + 2) * 3 - 2
            let coordItem = [[wayline_positions.current[M - 3], wayline_positions.current[N - 3]], [wayline_positions.current[M], wayline_positions.current[N]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]))
        } else if (index === wayline_cesiumObjectEntity.current.length - 1) {
            let N = wayline_positions.current.length - 1
            let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
        }
        if (computeHeading(index) !== null) {
            headingPitchRoll.current.heading = Cesium.Math.toRadians(computeHeading(index))
            PTZ_headingPitchRoll.current.heading = Cesium.Math.toRadians(computeHeading(index))
            // 更新模型姿态与位置
            Update_model_position()
        }
        PTZ_headingPitchRoll.current.pitch = Cesium.Math.toRadians(computePitch(index))
    }
    // 计算两个点的方向并调整飞机姿态
    function computeAngle(coordItem1, coordItem2, position) {
        //通过 turf 计算两个点的方向向量
        let angle = turf.rhumbBearing(coordItem1, coordItem2)
        //转换成cesium的角度
        const angleInRadians = Cesium.Math.toRadians(angle)
        headingPitchRoll.current.heading = angleInRadians
        PTZ_headingPitchRoll.current.heading = angleInRadians
        airplaneModel_position.current = position
        // 更新模型姿态与位置
        Update_model_position()
    }
    // 获取飞机偏航角
    function getAirplaneModelheading() {
        return Cesium.Math.toDegrees(headingPitchRoll.current.heading)
    }
    // 设置飞机偏航角
    function setAirplaneModelheading(heading) {
        headingPitchRoll.current.heading = Cesium.Math.toRadians(heading)
        PTZ_headingPitchRoll.current.heading = Cesium.Math.toRadians(heading)
    }
    // 获取飞机俯仰角
    function getAirplaneModelPitch() {
        return Cesium.Math.toDegrees(PTZ_headingPitchRoll.current.pitch)
    }
    // 设置飞机俯仰角
    function setAirplaneModelPitch(pitch) {
        PTZ_headingPitchRoll.current.pitch = Cesium.Math.toRadians(pitch)
    }
    // 航线回显
    function routeFeedback(wayline) {
        for (const item of wayline.Folder.PlacemarkList) {
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(new Cesium.Cartesian3.fromDegrees(item.Point.coordinates.longitude, item.Point.coordinates.latitude, 0), viewer.current)
            addWaypoint(longitude, latitude, globeHeight, globeHeight, new Cesium.Cartesian3.fromDegrees(item.Point.coordinates.longitude, item.Point.coordinates.latitude, globeHeight))
        }
    }
    // 相机飞到指定位置
    function cameraFlyTo(lat, lng) {
        viewer.current.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(lat, lng, 3000.51)
        })
        device_billboard.current.position.setValue(Cesium.Cartesian3.fromDegrees(lat, lng, 0))
    }
    // 加载或切换模型
    function addModel3Dtiles(modelUrl) {
        if (model3Dtiles.current) {
            viewer.current.scene.primitives.remove(model3Dtiles.current)
        }
        // 创建3D Tiles图层
        Cesium.Cesium3DTileset.fromUrl(modelUrl, {
            dynamicScreenSpaceError: true,
            dynamicScreenSpaceErrorDensity: 2.0e-4,
            dynamicScreenSpaceErrorFactor: 24.0,
            dynamicScreenSpaceErrorHeightFalloff: 0.1,
            maximumScreenSpaceError: 1.0,
        }).then((res) => {
            model3Dtiles.current = viewer.current.scene.primitives.add(res);
            viewer.current.flyTo(res)
        })
    }
    return {
        routeFeedback,
        deleteWaypoint,
        SwitchWaypoint,
        item_height_change_function,
        item_position_change_function,
        setAirplaneModelheading,
        setAirplaneModelPitch,
        cameraFlyTo,
        addModel3Dtiles,
        switchHeightMode
    }
}
export default useWayline3DHooks;
