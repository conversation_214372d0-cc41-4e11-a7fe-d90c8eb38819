import { Card, Tag, Space, Table } from "antd";
import { useState } from "react";
import FileViewer from "@/components/FileViewer/FileViewer";
import Pdf from "./pdf";
import LastPageButton from "@/components/LastPageButton";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
export default function detail(props) {
  const [jobUrl, setJobUrl] = useState();

  const TableCols = () => {
    return [
      {
        title: getTableTitle("任务名称"),
        dataIndex: "JobName",
        key: "JobName",
        align: "center",
      },
      {
        title: getTableTitle("照片点位"),
        dataIndex: "JobData",
        key: "JobData",
        align: "center",
      },
      {
        title: getTableTitle("状态"),
        dataIndex: "State",
        key: "State",
        align: "center",
      },
      {
        title: getTableTitle("备注"),
        dataIndex: "Remarks",
        key: "Remarks",
        align: "center",
      },

      {
        title: "操作",
        dataIndex: "JobUrl",
        key: "JobUrl",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <Tag>
              <a onClick={() => setJobUrl(record)}>生成报告</a>
            </Tag>
          </Space>
        ),
      },
    ];
  };
  return (
    <>
      <Card
        title={<LastPageButton title="施工进度报告" />}
        bordered={false}
        // extra={
        //   <Button type="primary" className={ComStyles.addButton}>
        //     下载
        //   </Button>
        // }
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-evenly",
            height: "calc(100vh - 177px)",
          }}
        >
          <div style={{ flex: 1, padding: "10px" }}>
            <Table
              pagination={{
                defaultPageSize: props?.record?.length,
                defaultCurrent: 1,
                showQuickJumper: true,
                pageSizeOptions: [5, 10, 15, 20, 30],
                showSizeChanger: true,
                locale: {
                  items_per_page: "条/页",
                  jump_to: "跳至",
                  page: "页",
                },
              }}
              bordered
              dataSource={props?.record}
              columns={TableCols()}
            />
          </div>
          <div style={{ flex: 1, padding: "10px", overflow: "auto" }}>
            <FileViewer
              fileType={"pdf"}
              filePath={
                "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/111/CDZS/%E5%85%89%E4%BC%8F%E7%94%B5%E7%AB%99%E6%96%BD%E5%B7%A5%E8%BF%9B%E5%BA%A6%20AI%20%E8%AF%86%E5%88%AB%E6%8A%A5%E5%91%8A_20240923.pdf"
              }
            />
            {/* <Pdf jobUrl={jobUrl}></Pdf> */}
          </div>
        </div>
      </Card>
    </>
  );
}
