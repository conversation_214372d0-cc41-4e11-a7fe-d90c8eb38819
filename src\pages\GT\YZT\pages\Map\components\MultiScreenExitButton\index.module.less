// 多屏对比模式控制按钮样式
.multiScreenExitContainer {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1002; // 比工具箱更高的层级

  :global(.ant-space) {
    gap: 8px !important;
  }

  .configButton,
  .exitButton {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 20px;
    height: 44px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;

    :global(.anticon) {
      font-size: 16px;
    }

    &:hover {
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .configButton {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #d9d9d9;
    color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      background: #fff;
      border-color: #40a9ff;
      color: #1890ff;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }

    &:active {
      background: #f0f7ff;
      border-color: #1890ff;
    }
  }

  .exitButton {
    background: #ff4d4f;
    border: 1px solid #ff4d4f;
    color: #fff;
    box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);

    &:hover {
      background: #ff7875;
      border-color: #ff7875;
      box-shadow: 0 6px 16px rgba(255, 77, 79, 0.4);
    }

    &:active {
      background: #d9363e;
      border-color: #d9363e;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .multiScreenExitContainer {
    top: 15px;
    right: 15px;

    .exitButton {
      padding: 10px 16px;
      height: 40px;
      font-size: 13px;

      :global(.anticon) {
        font-size: 14px;
      }
    }
  }
}
