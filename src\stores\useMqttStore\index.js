import { create } from "zustand";
import { GetMqttClient } from "@/utils/websocket";

const useMqttStore = create((set, get) => ({
  mqttClient: null,
  messages: {},

  connectMqtt: async() => {
    const client = await GetMqttClient();

    if (!client) {
      console.warn("MQTT 连接失败<<<<<");
      return;
    }

    client.on("connect", () => {
      console.log("MQTT连接成功>>>>>");
    });

    client.on("message", (topic, messageBuffer) => {
      const messageStr = messageBuffer.toString();
      try {
        const parsedMessage = JSON.parse(messageStr);
        get().updateVal(topic, parsedMessage);
        console.log(`收到消息（主题：${topic}）：`, parsedMessage);
      } catch (e) {
        console.error("消息解析错误：", e);
      }
    });

    set({ mqttClient: client });
  },

  disconnectMqtt: () => {
    const { mqttClient } = get();
    if (mqttClient) {
      mqttClient.end();
      set({ mqttClient: null });
      set({ messages: {} });
    }
  },
  sendMqttMessage: (topic, message) => {
    const { mqttClient } = get();
    mqttClient.subscribe(topic, (err) => {
      if (err) {
        console.error(`订阅话题 ${topic} 失败：`, err);
      } else {
        console.log(`成功订阅话题: ${topic}`);
      }
    });
  },

  updateVal: (topic, message) => {
    const topicParts = topic.split("/");
    const thirdParty = topicParts[2];
    console.log(`收到消息（主题：${topic},`, message);
    get().setMessages(message, topic);
  },

  setMessages: (msg, topic) => {
    set((state) => {
      const message = { ...msg, topic: topic };
      return { messages: message };
    });
    // set((state) => {
    //   const message = { ...msg, topic: topic };
    //   return { messages: [...(state.messages || []), message] };
    // });
  },
}));

export default useMqttStore;
