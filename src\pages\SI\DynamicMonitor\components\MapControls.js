import React from 'react';
import styles from '../index.less';
import { GlobalOutlined, GatewayOutlined, EditOutlined } from '@ant-design/icons'; // Using EditOutlined as a placeholder for measure tool

const MapControls = ({ onControlClick }) => {
  return (
    <div className={styles.mapControlsContainer}>
      <div className={`${styles.mapControlItem} ${styles.mapControlGroupFirst}`} onClick={() => onControlClick && onControlClick('basemap')}>
        {/* <GlobalOutlined title="地图" /> */}
        <span>地图</span>
      </div>
      <div className={styles.mapControlItem} onClick={() => onControlClick && onControlClick('layertree')}>
        {/* <GatewayOutlined title="图层" /> */}
        <span>图层</span>
      </div>
      <div className={`${styles.mapControlItem} ${styles.mapControlGroupLast}`} onClick={() => onControlClick && onControlClick('measure')}>
        {/* <EditOutlined title="测量" /> */}
        <span>测量</span>
      </div>
    </div>
  );
};

export default MapControls; 