import { Clone, downloadFile2, downloadFile, getBodyH, isEmpty, getImgUrl,getVideoSLTUrl } from '@/utils/utils';

import { useEffect, useState } from 'react';
import { Card,Table,Form, List, Row, Col, Button, Checkbox,Select ,DatePicker, message, Input, Descriptions} from 'antd';

import { useModel } from 'umi';


import LastPageButton from '@/components/LastPageButton';
import { timeFormat2 } from '@/utils/helper';
import dayjs from 'dayjs';
import { HGet2 } from '@/utils/request';
import TableCols from './video_table';
import { DoubleLeftOutlined, DoubleRightOutlined, SearchOutlined } from '@ant-design/icons';
import { Post2 } from '@/services/general';
import AddButton from '@/components/AddButton';


const getImgUrl2 = (obj) => {
  return `/${obj}`
}

const { RangePicker } = DatePicker;

const VideoSelectPanel = () => {

  console.log('VideoSelectPanel', mList)
  const [sList, setSList] = useState([]);
  const [xList, setXList] = useState([]);
  const [vName,setVName]=useState('111.mp4');
  const [sWay,setSWay]=useState('');
  const [sDate,setSDate]=useState({});
  const [mList,setMList]=useState([]);
  const [load,setIfLoad]=useState(true);
  const [index1,setIndex1]=useState({})
  const [index2,setIndex2]=useState({})
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRowKeys2, setSelectedRowKeys2] = useState([]);


  const {  setModal,  setOpen,lastPage } = useModel("pageModel")
  // eslint-disable-next-line react-hooks/exhaustive-deps



  const ifPush=(e)=>{

    if(!isEmpty(sWay)){
        if(e.WayLineNM!=sWay){
           return false;
        }
    }
    if(!isEmpty(sDate)){
     // 
      
      const t3=dayjs(e.CreatedTime);
     
      if(t1.isAfter(t3)||(t2.isBefore(t3))){
       // console.log('time',t1.format('YYYY-MM-DD HH:mm:ss'),t2.format('YYYY-MM-DD HH:mm:ss'),t3.format('YYYY-MM-DD HH:mm:ss'));
         return false;
      }
    }
    return true;
  }


  useEffect(() => {
    const xx=async ()=>{
      let yy=await  HGet2(`/api/v1/Media/GetVideoList`)
      if(isEmpty(yy)) yy=[];
      setMList(yy);
      const xL = []
      yy.forEach(e => {
        if (ifPush(e)) {
          e.key=e.ID;
          xL.push(e);
        }
      });
      setSList([...xL]);
      setIfLoad(false);
    }
    xx();
  }, []);

  useEffect(() => {
    let t1=dayjs('1900/1/1');
    let t2=dayjs('2900/1/1')
    if(!isEmpty(sDate)){
       t1=dayjs(sDate[0]);
       t2=dayjs(sDate[1]);
    }
   


    const xx = () => {
      const xL = []
      mList.forEach(e => {
        if (ifPush(e)) {
          e.key=e.ID;
          xL.push(e);
        }
      });
      setSList([...xL]);
      setIndex1({})
      setIndex2({})
    }
    xx();
  }, [sWay,sDate]);




  const onVideoClick=(item)=>{
    setModal(<div style={{height:350,width:'100%'}}>
       <div  style={{position:'absolute',left:20,top:18.0, fontWeight:'bold',height:36.0,color:'rgba(0,0,0,0.7)'}}>{item.WayLineNM+"-"+item.HangDianIndex} </div>
    <div style={{position:'absolute',cursor:'pointer',top:18.0,right:48,fontWeight:'bold', color:'rgba(0,0,0,0.7)'}}>{timeFormat2(item.CreatedTime)}</div>
     
      <video id={item.ID} key={item.ID} height={'100%'} width={'100%'} controls>
      <source src={getImgUrl(item.ObjectName)} type="video/mp4" />
    </video></div>);
    setOpen(true);
}

      //

     const getWaySelect = (wayList) => {
      
        const list = []
        wayList.forEach(e => {
          list.push(<Select.Option key={e} data={e}>{e}</Select.Option>)
        });
        console.log('CronAddForm', list);
    
        return list;
    }

    const onWayLineSelected=(e)=>{
        setSWay(e);
    }

    const onDateSelected=(e)=>{
     // 
      setSDate(e);
  }

    const getExr=()=>{
      const wList=[]
      mList.forEach(e => {
          if(!wList.includes(e.WayLineNM)){
            wList.push(e.WayLineNM);
          }
      });
      return <div>

        <span>      <Select allowClear={true} style={{width:200}} onClear={()=>setSWay('')}
          placeholder={'航线'}
          onSelect={onWayLineSelected}>
          {getWaySelect(wList)}
        </Select></span>

        <span style={{marginLeft:12.0}}> <RangePicker  onChange={onDateSelected}/></span>

      </div>
    }

    const StartMerge= async()=>{
        if(isEmpty(xList)){
          message.info('先选择需要拼接的视频！')
          return;
        }
        const fL=[]
        xList.forEach(e => {
            fL.push(e.ObjectName);
        });
        const fLStr=fL.join(',');
        const rr=await Post2('/api/v1/VideoMerge/Add',{VName:vName,InFList:fLStr,});
        message.info('创建成功!');
        lastPage();
    }


    const getExr2=()=>{
      const wList=[]
      mList.forEach(e => {
          if(!wList.includes(e.WayLineNM)){
            wList.push(e.WayLineNM);
          }
      });
      return <Row>
        <Col style={{paddingTop:2.0}}>任务名称：</Col>
        <Col>
        <Input style={{width:240}} placeholder='输入任务名称' defaultValue={vName} onChange={(e)=>setVName(e.target.value)}></Input>
   
        </Col>
          <Col style={{marginLeft:24.0}}><AddButton onClick={()=>{
            StartMerge();
          }}>提交任务</AddButton></Col>
      </Row>
    }


  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
    const v1=sList.find(p=>p.ID==newSelectedRowKeys[0])
    setIndex1(v1);
  };

   const rowSelection = {
      selectedRowKeys,
      type:'radio',
      onChange: onSelectChange,
   };

   const onSelectChange2 = (newSelectedRowKeys) => {
    setSelectedRowKeys2(newSelectedRowKeys);
    const v1=xList.find(p=>p.ID==newSelectedRowKeys[0])
    setIndex2(v1);
  };

   const rowSelection2 = {
      selectedRowKeys2,
      type:'radio',
      onChange: onSelectChange2,
   };

   const addVideo=()=>{
      xList.push(index1);
      setXList([...xList]);
      setSelectedRowKeys([]);
      setIndex1({})
   }

   const removeVideo=()=>{
    const xL2=xList.filter(item => item.ID !== index2.ID);
    setXList([...xL2]);
    setSelectedRowKeys2([]);
    setIndex2({})
 }

  return <Card title={<LastPageButton title='航拍视频' />} style={{ height: getBodyH(56) }} extra={getExr2()}>
    <Row>
      <Col span={11}>
      <Card extra={getExr()}>
    <Table   pagination={{pageSize:8}}
     rowSelection={rowSelection}
      bordered  dataSource={sList} columns={TableCols(onVideoClick)} size='small' />
      </Card>
    </Col>
    <Col span={2} style={{background:''}}> 
      <div style={{position:'relative',top:'30%',marginLeft:24.0}}>
        <Button iconPosition='end' onClick={addVideo} disabled={isEmpty(index1)} icon={<DoubleRightOutlined />}>添加</Button> 
        <Button style={{marginTop:24.0}}  onClick={removeVideo} disabled={isEmpty(index2)} icon={<DoubleLeftOutlined />}>移除</Button>
      </div>
    </Col>
    <Col span={11}>
    <Card title={'已选视频'}  >
    <Table   pagination={{pageSize:8}}
      rowSelection={rowSelection2}
      loading={load}
      bordered  dataSource={xList} columns={TableCols(onVideoClick)} size='small' />
     </Card>
    </Col>
    </Row>
  </Card>
}

export default VideoSelectPanel;
