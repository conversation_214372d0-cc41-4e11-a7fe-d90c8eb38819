import React, { useEffect } from "react";
import { ConfigProvider } from "antd";
import MyHead from "../components/MyHead";
import commonStyle from "../style/common.less";
import "@/pages/GT/style/antd-common.less";
import { queryPage } from "@/utils/MyRoute";
import { useModel } from "umi";
import HeadTabs from "@/pages/GT/components/HeadTabs";
import MyMenu from "@/pages/GT/components/MyMenu";
import useConfigStore from "@/stores/configStore";
import SITheme from '@/pages/SI/style/theme';
import locale from 'antd/locale/zh_CN';
function App() {
  const { page, setPage, pageData } = useModel("pageModel");
  const { MenuShow, setMenuShow } = useConfigStore();
  const headList = [
    { label: "大屏展示", key: "大屏展示" },
    { label: "智能巡飞", key: "智能巡飞" },
    { label: '监测处置', key: '监测处置' },

  ];
  const menuItems = [
    { label: "数据采集", key: "数据采集" },
    { label: "地灾分析", key: "地灾分析" },
    { label: "数据管理", key: "数据管理" },
  ];
  const handlePageChange = (page) => {
    if (page == "大屏展示" || menuItems.some((item) => item.key === page)) {
      setMenuShow(true);
    } else {
      setMenuShow(false);
    }
    setPage(queryPage(page));
  };

  return (
    <div className="gt-page">
      <div
        className={commonStyle.gt_back_white}
        style={{
          position: "relative", overflow: "hidden", height: "100vh", display: "flex",
          flexDirection: "column"
        }}
      >
        <ConfigProvider locale={locale} theme={SITheme}>
          <MyHead
            headList={headList}
            handlePageChange={handlePageChange}
          ></MyHead>
          <HeadTabs></HeadTabs>
          <div
            style={{
              display: "flex",
              justifyContent: "flex-start",
              position: "relative",
            }}
          >
            {
              MenuShow && (
                <>
                  <MyMenu
                    menuItems={menuItems}
                    handlePageChange={handlePageChange}
                  />
                </>
              )
            }
            <div style={{ width: "100vw" }}>
              {page}
            </div>
          </div>
        </ConfigProvider>
      </div>
    </div>
  );
}

export default App;
