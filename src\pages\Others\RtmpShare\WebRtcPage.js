import { useEffect, useRef, useState } from "react";
import Srs from "@/components/WebRtcPlayer/srs.sdk";
import "./style/WebRtcPage.css";
import { Get2 } from "@/services/general";

const WebRtc = (props) => {
  const parsedUrl = new URL(window.location.href);
  const { SN } = props;
  const videoRef = useRef(null);
  let url;
  // const url='webrtc://112.44.103.230/live/'+SN
  async function getRTCUrl() {
    const res = await Get2("/api/v2/APPConfig/Get");
    if (res && SN) {
      // if (parsedUrl.protocol === "https:") {
      //   url = res.rtmp?.webrtcUrlSsl + SN;
      // }
      url = res.rtmp?.webrtcUrl + SN;
      initializeWebRTC(url);
      console.log("webrtcUrl", url);
    }
  }

  const initializeWebRTC = async (url) => {
    const rtcPlayerRef = new Srs.SrsRtcPlayerAsync();
    if (videoRef.current) {
      try {
        await rtcPlayerRef.play(url);
        if (rtcPlayerRef.stream) {
          videoRef.current.srcObject = rtcPlayerRef.stream;
          await videoRef.current.play();
        }
      } catch (err) {
        handleError(err, "WebRTC");
      }
    }
  };
  const handleError = (err, type) => {
    console.error(`Error playing ${type} video:`, err);
  };
  useEffect(() => {
    getRTCUrl();
  }, []);

  return (
    <div style={{ textAlign: "center", position: "relative" }}>
      <video
        ref={videoRef}
        autoPlay
        style={{ objectFit: "cover", maxHeight: "calc(100vh)" }}
        width={"100%"}
        height={"100%"}
        muted
        controls
      ></video>
    </div>
  );
};

export default WebRtc;

// import flvjs from "flv.js";
// import Hls from "hls.js";
// import Srs from "@/components/WebRtcPlayer/srs.sdk";
// import { useEffect, useRef, useState } from "react";
// import { getRtmpHttpUrl, getRtmpWebrtcUrl } from "@/utils/config";
// import VideoLoading from "@/components/VideoLoading/VideoLoading";
// import {useConfigStore} from "@/stores/configStore"

// const MediaViewer = ({ SN, play = true }) => {
//   const videoRef = useRef(null);
//   const flvPlayerRef = useRef(null);
//   const hlsRef = useRef(null);
//   const [loading, setLoading] = useState(true);
//   let {config} = useConfigStore.getState();
//   const aiUrl = new URL(config.rtmp.aiUrl);
//   const host = aiUrl.host.split(':')[0];
//   const url = `webrtc://${host}/live/${SN}`;
//   useEffect(() => {
//     // const URL = "webrtc://push-b001.seepods.com/live/FD08218401896?txSecret=6c0a6394c220a9f6632c6b147a776247&txTime=767F010B"; // WebRTC地址
//     //  URL = "http://play-weiyou.seepods.com/live/8NCJXCMW87635.m3u8";
//     // const URL = getRtmpHttpUrl() + SN + ".flv"; // flvC地址

//     console.log(url,'??????????');

//     if (url.endsWith(".flv")) {
//       initializeFlvPlayer(url);
//     } else if (url.startsWith("webrtc://")) {
//       initializeWebRTC(url);
//     } else if (url.endsWith(".m3u8")) {
//       initializeMu38Player(url);
//     }

//     return cleanup;
//   }, [SN, play]);

//   const initializeWebRTC = async (url) => {
//     //WebRTC 格式播放器,可能无限制引用组件数量
//     const rtcPlayer = new Srs.SrsRtcPlayerAsync();
//     if (play) {
//       if (videoRef.current) {
//         try {
//           await rtcPlayer.play(url);
//           if (rtcPlayer.stream) {
//             videoRef.current.srcObject = rtcPlayer.stream;
//             await videoRef.current.play();
//             setLoading(false);
//           }
//         } catch (err) {
//           handleError(err, "WebRTC");
//         }
//       }
//     }
//   };

//   const initializeFlvPlayer = (url) => {
//     //flv格式播放器，镜头转动画面会停滞,可能只能播放六个
//     if (flvjs.isSupported()) {
//       flvPlayerRef.current = flvjs.createPlayer({
//         type: "flv",
//         url: url,
//         isLive: true,
//         hasAudio: false,
//         hasVideo: true,
//         enableStashBuffer: false,
//         cors: true,
//       });

//       flvPlayerRef.current.attachMediaElement(videoRef.current);
//       flvPlayerRef.current.load();
//       if (play) {
//         flvPlayerRef.current
//           .play()
//           .then(() => {
//             setLoading(false);
//           })
//           .catch((err) => handleError(err, "FLV"));
//       }
//     } else {
//       console.error("FLV.js is not supported in this browser.");
//     }
//   };

//   const initializeMu38Player = (url) => {
//     if (Hls?.isSupported()) {
//       const hls = new Hls();
//       hls.loadSource(url);
//       hls.attachMedia(videoRef.current);
//       hls.on(Hls.Events.MANIFEST_PARSED, () => {
//         if (play) {
//           videoRef.current.play();
//           setLoading(false);
//         }
//       });
//       hlsRef.current = hls;
//     } else if (videoRef.current.canPlayType("application/vnd.apple.mpegurl")) {
//       videoRef.current.src = url;
//       videoRef.current.addEventListener("loadedmetadata", () => {
//         if (play) {
//           videoRef.current.play();
//           setLoading(false);
//         }
//       });
//     }
//   };

//   const handleError = (err, type) => {
//     console.error(`Error playing ${type} video:`, err);
//     setLoading(false);
//   };

//   const cleanup = () => {
//     if (flvPlayerRef.current) {
//       flvPlayerRef.current.unload();
//       flvPlayerRef.current.detachMediaElement();
//       flvPlayerRef.current.destroy();
//       flvPlayerRef.current = null;
//     }
//     if (hlsRef.current) {
//       hlsRef.current.destroy();
//       hlsRef.current = null;
//     }
//   };

//   return (
//     <div style={{ position: "relative", width: "100%", height: "100%" }}>
//       {loading && (
//         <div
//           style={{
//             position: "absolute",
//             top: "50%",
//             left: "50%",
//             transform: "translate(-50%, -50%)",
//             zIndex: 10,
//           }}
//         >
//           <VideoLoading />
//         </div>
//       )}
//       <video
//         ref={videoRef}
//         muted
//         style={{
//           width: "100%",
//           height: "100%",
//           objectFit: "fill",
//           display: loading ? "none" : "block",
//         }}
//       />
//     </div>
//   );
// };

// export default MediaViewer;
