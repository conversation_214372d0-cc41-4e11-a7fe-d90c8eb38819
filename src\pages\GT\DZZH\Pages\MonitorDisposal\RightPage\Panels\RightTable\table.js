import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { HGet2, HPost2 } from "@/utils/request";
import { Post2 } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { render } from "react-dom";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (refrush) => {
  return [
    {
      title: getTableTitle("任务名称"),
      dataIndex: "Title",
      key: "Title",
      align: "center",
    },

    {
      title: getTableTitle("航线名称"),
      dataIndex: "Content",
      key: "Content",
      align: "center",
    },

    {
      title: getTableTitle("发现时间"),
      align: "center",
      render: (record) => (
        <span>{timeFormat(record.CreateTM)}</span>
      )
    },

    {
      title: getTableTitle("异常类型"),
      dataIndex: "DangerType",
      key: "DangerType",
      align: "center",
    },

    {
      title: getTableTitle("影像查看"),
      key: "ImgObjectName",
      align: "center",
      render: (record) => (
        <>
          <img src={getImgUrl(record.ImgObjectName)} alt="图片" width="50" height="50"></img>
          {/* <div style={{width:"50px",height:"50px",border: "1px solid #ccc"}}></div> */}
        </>
      )

    },

    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle" style={{ display: "flex", flexDirection: "column" }}>
          <MyButton
            style={{ padding: "2px 5px" }}
            onClick={() => console.log(record)}
          >
            核查
          </MyButton>
          <MyButton style={{ padding: "2px 5px" }}>整改</MyButton>
          <MyButton style={{ padding: "2px 5px" }}>删除</MyButton>
        </Space>
      ),
    },
  ];
};

export default TableCols;
