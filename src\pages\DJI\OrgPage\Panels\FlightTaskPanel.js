import BlockTitle from "../../../../components/BlockPanel/BlockTitle";
import { BorderBox7 } from "@jiaminghi/data-view-react";
import { MainColor } from "@/utils/colorHelper";
import Chart from "./../../../../utils/chart";
import { useState, useEffect } from "react";
import { HGet2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import { Badge } from "antd";
import { number } from "echarts";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { LoginOutlined } from "@ant-design/icons";

const FlightTaskPanel = () => {
  const [ifLoad, setIfLoad] = useState(true);
  const [flyData, setFlyData] = useState();
  const [distance, setDistance] = useState();
  const [photoCount, setPhotoCount] = useState();
  const [picCount, setPicCount] = useState();
  const [vidCount, setVidCount] = useState();
  const [vidSize, setVidSize] = useState();
  const [options, setOptions] = useState({});

  const option = {
    tooltip: {
      trigger: "item",
    },
    legend: null,
    backgroundColor: "",
    animationDurationUpdate: 500,
    xAxis: {
      axisLabel: {
        interval:0,
        rotate:0
     },
      type: "category",
      data: ["一键起飞","巡检航线", "定时任务", "绘制航线","现场巡查"],
      inverse: true,
      axisPointer: {
        label: {
          show: true,
          margin: 30
        }
      }
  },
    yAxis: {
      type: "value",
      
      axisTick: {
        show: true 
        },
        axisLine: {
        show: true
        },
        splitLine: {
        show: false 
        }
    },
    grid: { x: 35, y: 25, x2: 30, y2: 30 },
    animation: true, 
    animationDuration: 1000, 
    animationDelay: 0, 
    animationEasing: 'linear',
    series: [
      {
        realtimeSort: true,
        name: "飞行记录统计",
        type: "bar",
        label: {
          show: true,
          position: 'right',
          valueAnimation: true
        },
        symbolRepeat: true,
        symbolSize: ['80%', '60%'],
        barCategoryGap: '40%',
        universalTransition: {
          enabled: true,
          delay: function (idx, total) {
            return (idx / total) * 1000;
          }
        },
        itemStyle: {   
          normal:{  
              color: function (params){
                  var colorList = ['rgb(58, 195, 214)','rgb(192, 216, 58)','rgb(66, 176, 242)','rgb(255, 231, 156)','rgb(250, 105, 18)'];
                  return colorList[params.dataIndex];
              }
          },
        },
        avoidLabelOverlap: false,
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1, name: "一键起飞" },
          { value: 120, name: "巡检航线" },
          { value: 120, name: "定时任务" },
          { value: 50, name: "绘制航线" },
          { value: 113, name: "现场巡查" },
        ],
      },
    ],
    legend: {
      show: false
    },
    
  };

  let detailList = [
    { title: "航线飞行",count:sessionStorage['photoCount'] + '次' },
    { title: "飞行距离",count:Math.round(sessionStorage['distance']) + '公里' },
    { title: "拍摄照片",count:sessionStorage['picCount'] + '幅'},
    { title: "录制视频",count:sessionStorage['vidCount'] + '篇'},

  ];
  useEffect(() => {
    getData();
    async function getData() {
      let res = await HGet2(`/api/v1/Task/GetAllList`);
      const media_res=await  HGet2(`/api/v1/Media/GetAllList`)
      if(!isEmpty(res) && !isEmpty(media_res))
      setIfLoad(false);
      optionFc(res)
      setPhotoCount(sessionStorage.setItem('photoCount',res.reduce((prev, item) =>prev + item.PhotoCount,0)))
      setDistance(sessionStorage.setItem('distance',res.reduce((prev, item) =>prev + item.Distance,0)))
      setPicCount(sessionStorage.setItem('picCount',media_res.reduce((prev, item) =>prev + item.FileName.includes('jpeg'),0)))
      setVidCount(sessionStorage.setItem('vidCount',media_res.reduce((prev, item) =>prev + item.FileName.includes('mp4'),0)))
    }
  }, []);
  function optionFc(res){
    const myArray = res.reduce(function (accumulator, currentValue) {
      accumulator[currentValue.FlightLineType] = ++accumulator[currentValue.FlightLineType] || 1
      return accumulator
    }, {})
    let data = option.series[0].data
    for(let i in data){
      data[i].value = Number(myArray[option.xAxis.data[i]]) 
    }
    setOptions(option)
  }
  const getItem = () => {
    return (
      <div>
        <div style={{ color: "white", marginTop: 12.0, marginLeft: 24.0 }}>
          {detailList?.map((item, key) => {
            return (
              <div key={key}>
                <span>
                  <Badge color="green" />
                </span>
                <span style={{ fontSize: 13.0, marginLeft: 8.0,whiteSpace:'pre',letterSpacing:'5px' }}>
                  {item.title} : {item.count}
                  
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  if(ifLoad) return <LoadPanel></LoadPanel>

  return (
    <div style={{ height: "45%", width: "100%", marginTop: 16.0 }}>
      <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
        <BlockTitle style={{ margin: 8.0 }} title="飞行记录统计"></BlockTitle>
        <div style={{ height: "240px", width: "100%" }}>
          {getItem()}
          <div style={{ height: 180 }}>
            <Chart style={{ marginBottom: 34.0}} option={options} />
          </div>
        </div>
      </BorderBox7>
    </div>
  );
};
export default FlightTaskPanel;
