//import {Modal,} from 'antd';
import { Form, Modal, Input, Button, InputNumber, message, Spin } from "antd";
import { connectAsync } from "mqtt";
import { useEffect, useRef, useState } from "react";
import { HGet2 } from "@/utils/request";
import { Get2 } from "@/services/general";
import { SyncOutlined  } from "@ant-design/icons";

const { TextArea } = Input;
const HanHuaPage = ({ device, setOpen }) => {
  let [x1, setX1] = useState();
  let [count, setCount] = useState();
  const [spinning, setSpinning] = useState(false);
  let [showCount,setShowCount] = useState(); 

  async function hanhua(value) {
    await Get2("/api/v1/PSDK/SpeakerSetVolume?sn=" + device.SN)
     HGet2("/api/v1/PSDK/Speaker?sn=" + device.SN + "&val="+value)
  }

  let myCounter = (function () {
    let value = x1;
    let intervalId = useRef;
    let step = count;

    if (!step) {
      step = 5;
    }
    function timeFc() {
      if (step < 1) {
        message.open({ type: "success", content: "已喊话完毕!" });
        clearInterval(intervalId.current);
        setSpinning(false);
      } else {
        hanhua(value);
        // message.info(value);
        step--;
        setShowCount(step)
      }

    }

    function start() {
      if (!value) {
        message.open({ type: "warning", content: "喊话内容是空的哦!" });
        return;
      }
        if(intervalId){
          window.clearInterval(intervalId.current);
        }
        setSpinning(true);
        timeFc()
        intervalId.current = window.setInterval(()=>{
            timeFc()
          },x1?.length * 500)
    }
    function stop() {
      window.clearInterval(intervalId.current);
      intervalId = null;
      message.open({ type: "success", content: "已喊话完毕!" });
      setSpinning(false);

    }
    function reset() {
      stop();
      step = 0;
    }
    return { startCount: start, stopCount: stop, resetCount: reset };
  })();

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-around",
        alignItems: "center",
        padding: "20px 0px 0 0px",
      }}
    >
      <div style={{ marginBottom: 20, display: "flex", alignItems: "center",height:'20px' }}>
        
          <>
            <Spin
              indicator={<SyncOutlined  spin />}
              spinning={spinning}
              size="middle"
              style={{color:'#81ce27'}}
            />
            <span style={{ 
              marginLeft: 5,
              background: "linear-gradient(to right, #fffefe, #88ff00)",
             }}>{spinning?`正在喊话中...还剩${showCount}次。`:''}</span>
          </>
    
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <span>无人机喊话:</span>
        <span>
          {" "}
          <TextArea
            showCount
            maxLength={200}
            placeholder="请输入喊话内容"
            onChange={(e) => setX1(e.target.value)}
            defaultValue={x1}
            style={{ width: 340 }}
          />
        </span>
      </div>
      <div style={{ margin: "30px 0", lineHeight: "32px" }}>
        <span>设喊话次数:</span>
        <span>
          {" "}
          <InputNumber
            placeholder="若未设,默认为5次"
            addonAfter="次"
            defaultValue={count}
            onChange={(value) => setCount(value)}
            style={{ width: 340 }}
          />
        </span>
      </div>
      <div style={{ width: "100%", marginLeft: "115%" }}>
        <span style={{ marginLeft: 24.0 }}>
          <Button type="primary" onClick={() => {myCounter.stopCount(),setOpen(false)}}>
            取消
          </Button>
        </span>
        <span style={{ marginLeft: 24.0 }}>
          {spinning?<Button type="primary" onClick={myCounter.stopCount}>
           重喊
          </Button>:''}
          {
            spinning?'':<Button type="primary" onClick={myCounter.startCount}>
            确定
          </Button>
          }
        </span>
      </div>
    </div>
  );
};

export default HanHuaPage;
