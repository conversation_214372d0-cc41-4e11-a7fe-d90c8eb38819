@font-face {
  font-family: 'MiSan';
  src: url('@/assets/fonts/Roboto-Bold.9ece5b48.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Digiface';
  src: url('@/assets/fonts/Digiface.ttf');
  font-weight: normal;
  font-style: normal;
}

.camerasTool {
  position: absolute;
  opacity: 0.95;
  right: 80px;
  z-index: 1010;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px
}

.camerasTool .items {

  width: 50px;
  height: 50px;
  border: 3px solid #08e7cb;
  border-radius: 100rem;
  background: linear-gradient(0deg, rgb(0, 25, 49), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: #000e1b 4px 2px 10px;

}

.camerasTool .items>img {
  width: 30px;
  height: auto;
  cursor: pointer;
  user-select: none;
}

.camerasNums {
  text-align: center;
  font-family: 'Digiface';
  color: #08e7cb;
  font-weight: 500;
  font-size: 140%;
  width: 130%;
  margin-left: -15%;
  border-left: solid 1px;
  border-right: solid 1px;
  border-image: linear-gradient(0deg, transparent, #08e7cb, transparent) 1 1;

}