import React, { useRef, useEffect, useState } from "react";
import styles from "./Home.less";
import { getBodyH, isEmpty, getImgUrl, getImgSLTUrl } from "@/utils/utils";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { useNavigate, useLocation } from "react-router-dom";
import { Get2, Post2, Post3, axiosApi } from "@/services/general";
import handleFullScreen from "@/utils/handleFullScreen";
import { useSearchParams } from "umi";
import { queryPage2 } from "@/utils/MyRoute";
import { useModel, history } from "umi";
import { message } from "antd";
import { UserOutlined, LogoutOutlined } from "@ant-design/icons";

export default function (props) {
  const navigate = useNavigate();
  const location = useLocation();
  const List = [{ ID: 1, TheURL: "DZJC", Title: "地灾监测" }];
  const [data, setData] = useState([]);
  const [configData, setConfigData] = useState();
  const [isCenter, setIsCenter] = useState(true);
  const system = localStorage.getItem("system");

  useEffect(() => {
    const getData = async () => {
      if (system === "null") {
        return history.push("/HOME/login?system=");
      }
      let configRes = await axiosApi("/api/v2/APPConfig/Get", "GET", {
        sid: system,
      });
      if (configRes) {
        setConfigData(configRes);
      }

      let res = await axiosApi("/api/v1/SystemInfo/Get", "GET", {
        sid: system,
      });
      if (res && res.data && res.data.length > 0) {
        // 如果数据大于4个，则不居中显示
        if (res.data.length > 4) {
          setIsCenter(false);
        }
          setData(res.data);
        // if (
        //   window.location.hostname === "localhost" ||
        //   window.location.hostname === "127.0.0.1" ||
        //   window.location.hostname === "************"
        // ) {
        //   setData([...res.data, ...List]);
        // } else {
        //   setData(res.data);
        // }
      }
    };
    if (system) {
      getData();
    }
  }, [system]);

  function navigateTo(event, item) {
    event.stopPropagation();
    const targetUrl = `/${system}/${item?.TheURL}`;
    if (!item?.TheURL) {
      //地址为空时不跳转
      return message.warning("所访问地址无效");
    }
    if (
      item?.TheURL &&
      (item.TheURL.startsWith("http://") || item.TheURL.startsWith("https://"))
    ) {
      //在线地址时新开页面
      window.open(item.TheURL, "_blank");
    } else {
      history.push(targetUrl);
      if (item?.Title === "无人机空中巡查系统") {
        localStorage.setItem("currentPage", "态势感知");
      } else {
        localStorage.setItem("currentPage", item?.Title);
      }
      localStorage.setItem("IsSIJump", false);
    }
    // navigate(`${system}/${item?.TheURL}`, { state: {} });
  }
  function exit() {
    history.push(`/HOME/login?system=${system}`, {});
    localStorage.clear();
    return;
  }
  let intervalTimer, timeoutTimer;
  const domRef = useRef();
  useEffect(() => {
    if (domRef.current) {
      autoScroll();
      domRef.current.onmouseenter = () => {
        window.clearInterval(intervalTimer);
        window.clearTimeout(timeoutTimer);
      };
      domRef.current.onmouseleave = () => {
        autoScroll();
      };
    }
  }, [domRef]);
  const autoScroll = () => {
    if (timeoutTimer) {
      window.clearTimeout(timeoutTimer);
    }
    timeoutTimer = setTimeout(() => {
      const clientH = (domRef.current && domRef.current.clientHeight) || 0;
      const scrollH = (domRef.current && domRef.current.scrollHeight) || 0;
      if (intervalTimer) {
        window.clearInterval(intervalTimer);
      }
      if (scrollH == clientH) return;
      intervalTimer = setInterval(() => {
        if (domRef.current) {
          const scrollTop = domRef.current.scrollTop;
          const distance = scrollH - clientH;
          if (scrollTop >= distance) {
            domRef.current.scrollTop = -1;
          }
          domRef.current.scrollTop += 1;
        }
      }, 40);
    }, 2800);
  };
  return (
    <div className={styles.back}>
      <div className={styles.top_title_section}>
        <div></div>
        <div onClick={handleFullScreen}>
          {configData?.app?.title
            ? configData?.app?.title
            : "云端智行-无人机行业应用平台"}
        </div>
        <div className={styles.headSetting}>
          <div className={styles.headSettingItem}>
            <UserOutlined />
            <span className={styles.headSettingItem_text}>管理员</span>
          </div>
          <div className={styles.headSettingItem}>|</div>
          <div className={styles.headSettingItem} onClick={exit}>
            <LogoutOutlined />
            <span className={styles.headSettingItem_text}>退出</span>
          </div>
        </div>
      </div>
      <div className={`${styles.content} ${isCenter ? styles.center : ''} ${styles.swiper}`} ref={domRef}>
        {data?.map((item, index) => {
          return (
            <div className={styles.contentItem}>
              <div
                key={item.ID}
                className={styles.pic}
                style={{
                  background: `url(${getImgSLTUrl(item?.ImgURL)})`,
                  backgroundSize: "100% 100%",
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "center",
                  transition: "background-size 0.5s ease",
                  overflow: "hidden",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundSize = "150% 150%";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundSize = "100% 100%";
                }}
              >
                <div className={styles.picInto}>
                  <div onClick={(e) => navigateTo(e, item)}>进入系统</div>
                </div>
              </div>
              <div className={styles.titleTxt}>
                {/* <div style={{
                  minWidth: 40,
                  minHeight: 40,
                  // maxWidth: 30,
                  // maxHeight: 30,
                  background: `url(${item.Remark})`,
                  backgroundSize: "100% 100%",
                  backgroundRepeat: "no-repeat",
                }}></div> */}
                {/* {item?.Remark ? <img src={getOssUrl(item.Remark)}></img> : ""} */}
                <div>{item?.Title}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
