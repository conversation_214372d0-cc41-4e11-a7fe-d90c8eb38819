import { Input, Card, Tag, DatePicker, Radio, Descriptions, Select, Row, Col, Button, Form, Modal, message, Table, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect, useRef } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';
import {NewAliOssClient,OssPutFile} from '@/utils/AliossHelper';
import { getGuid } from '@/utils/helper';

const { TextArea } = Input;
const WayLineTypeList = ["巡检航线", "测绘航线"]
const WayLineAddForm = (props) => {


  console.log('www', props);
  const refrush = props.data;

  const [devices, setDevices] = useState([]);
  const [sn, setSN] = useState('');
  const [wType, setWType] = useState('');
  const [wName, setWName] = useState('');
  const [remark, setRemark] = useState('');
  const [canSee, setCanSee] = useState(false);
  const [stsData,setStsData]=useState({});
  const [sfile,setSFile]=useState({});




  const onFormChange = (e) => {
    console.log(e)
  }


  const pbV = () => {
    if ( isEmpty(wName)) {
      return true
    }
    return false
  }

  const handleFileChange =  () => {
    // setFile(event.target.files[0]);
   // 
    // if(!isEmpty(file)){
     // 
      OssPutFile(sfile,"videos/"+ getGuid()+'_'+sfile.name)
      
   //  }
    
  };


  const upProps = () => {
    return {
      name: 'file',
      action: '/api/v1/WayLine/Upload',
      
      showUploadList: false,
      multiple: false,

      beforeUpload: file => {
        setSFile(file);
        return false;
      },
    }
  };


  return <div><Button type="primary" onClick={() => setCanSee(true)}>航线上传</Button> <Modal title={null} footer={null} onOk={null} open={canSee} onCancel={() => setCanSee(false)}>
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}

    >

      <Descriptions title="视频上传" column={1}   >
       
        <Descriptions.Item label="视频名称"><Input onChange={(e) => { setWName(e.target.value) }} /></Descriptions.Item>
        <Descriptions.Item label="备注信息"><TextArea rows={4} onChange={(e) => { setRemark(e.target.value) }} /></Descriptions.Item>
        <Descriptions.Item label="上传视频">
          <Upload {...upProps()}>
          <Button >
            选择文件
          </Button>
        </Upload>
          
        </Descriptions.Item>
        <Descriptions.Item label="上传视频">
          
          <Button type='primary' disabled={pbV()} onClick={handleFileChange} >
            开始上传
          </Button>
 
        </Descriptions.Item>
      </Descriptions>



    </div>

  </Modal>
  </div>

}



export default WayLineAddForm;