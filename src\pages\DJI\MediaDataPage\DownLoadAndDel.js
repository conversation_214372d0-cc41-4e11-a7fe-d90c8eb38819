import {
  Button,
  Dropdown,
  Space,
  Progress,
  Modal,
  InputNumber,
  message,
} from "antd";
import {
  FileZipOutlined,
  DownOutlined,
  FileExcelOutlined,
} from "@ant-design/icons";
import { useEffect, useState, useRef } from "react";
import ComStyles from "@/pages/common.less";
import { HGet2 } from "@/utils/request";
import JSZ<PERSON> from "jszip";
import FileSaver from "file-saver";
import { getDeviceName, getImgUrl } from "@/utils/utils";

const { confirm } = Modal;

export default function downLoadAndDel({
  selectImgList,
  checkBoxList,
  refrush,
  checkBoxChange,
}) {
  const [open, setOpen] = useState(false);
  const [count, setCount] = useState();
  const [size, setSize] = useState(0);
  const [startNum, setStartNum] = useState(1);
  const [endNum, setEndNum] = useState(1);
  let loading = useRef(false);
  let [text, setText] = useState("下载所选择的文件");
  let [isCheck, setIsCheck] = useState(false);

  useEffect(() => {
    if ((checkBoxList && checkBoxList.length <= 0) || !checkBoxList) {
      setIsCheck(false);
    } else {
      setIsCheck(true);
    }
  }, [checkBoxList, refrush]);

  const handleMenuClick = (e) => {
    if (e.key === "0") {
      if (!checkBoxList) {
        message.open({ type: "warning", content: "请选择所要下载的文件!" });
      } else {
        handleBatchDownload(checkBoxList);
      }
    }
    if (e.key === "1" && selectImgList) {
      handleBatchDownload(selectImgList);
    }
    if (e.key === "2") {
      setOpen(true);
      if (startNum > endNum) {
        handleBatchDownload(selectImgList.slice(endNum, startNum + 1));
      } else {
        handleBatchDownload(selectImgList.slice(startNum, endNum + 1));
      }
    }
    if (e.key === "3") {
      if (!checkBoxList) {
        message.open({ type: "warning", content: "请选择所要删除的文件!" });
      } else {
        Delete();
      }
    }
  };
  function Delete() {
    confirm({
      title: "删除地图",
      content: `确定要删除这${checkBoxList.length}个文件嘛？`,
      okText: "删除",
      okType: "danger",
      cancelText: "取消",
      onOk() {
        delImg();
      },
    });
  }
  const delImg = async (item) => {
    const resList = [];
    for (let value of checkBoxList) {
      const res = await HGet2(`/api/v1/Media/DeleteById?id=${value.ID}`).then(
        () => {
          resList.push(res);
        }
      );
    }
    await Promise.all(resList).then(() => {
      message.open({ type: "success", content: "删除成功!" });
      refrush();
      checkBoxChange();
    });
  };

  const onCancel = () => {
    abortController.abort();
    loading.current = false;
    setText("下载所选择的文件");
  };

  const abortController = new AbortController();
  const signal = abortController.signal;
  const handleBatchDownload = async (selectImgList) => {
    if (loading.current) {
      //取消下载
      onCancel();
      return;
    } else {
      loading.current = true;
      setText("停止下载");
    }
    const zip = new JSZip();
    let totalSize = 0;

    // 下载所有文件
    for (let i = 0; i < selectImgList.length; i++) {
      const item = selectImgList[i];
      try {
        if (!loading.current) {
          setCount(100);
          break;
        }
        const response = await fetch(getImgUrl(item.ObjectName), {
          method: "GET",
          signal,
        });

        if (!response.ok) {
          throw new Error("数据响应不正常");
        }

        const reader = response.body.getReader();
        let receivedLength = 0; // 当前接收的字节数
        const chunks = []; // 存储接收到的文件片段

        while (true) {
          const { done, value } = await reader.read();
          if (done) break; // 下载完成

          chunks.push(value); // 将接收到的片段加入数组
          receivedLength += value.length;

          // 更新进度条和总大小
          setCount(((i + 1) / selectImgList.length) * 100); // 进度
          totalSize += Number(item.Size.slice(0, item.Size.indexOf("M")));
          setSize(totalSize.toFixed(2)); // 更新文件大小
        }

        // 将所有接收到的片段合并成一个 Blob
        const blob = new Blob(chunks);
        const file_name = `${item.WayLineNM}-航点${item.HangDianIndex}${item.FileName.slice(item.FileName.indexOf("."))}`;
        zip.file(file_name, blob, { binary: true });
      } catch (error) {
        console.error(`下载失败: ${item.FileName}`, error);
      }
    }
    let record = selectImgList[0];
    console.log(record);

    let zipName = `${getDeviceName(record?.SN)}_${record?.CreatedTime.slice(0, record?.CreatedTime.indexOf("T"))}.zip`;
    const content = await zip.generateAsync({ type: "blob" });
    if (content && content.size > 22) {
      FileSaver.saveAs(content, zipName);
      onCancel();
    }
  };

  const items = [
    {
      label: text,
      key: "0",
      icon: <FileZipOutlined style={{ fontSize: "25px" }} />,
    },
    // {
    //   label: loading.current ? "停止打包下载" : "打包下载全部",
    //   key: "1",
    //   icon: <FileZipOutlined style={{ fontSize: "25px" }} />,
    // },
    // {
    //   label: "自定义下载",
    //   key: "2",
    //   icon: <FormOutlined style={{ fontSize: "25px" }} />,
    // },
    {
      label: "删除所选择的文件",
      key: "3",
      icon: <FileExcelOutlined style={{ fontSize: "25px" }} />,
    },
  ];

  const renderProgress = () => {
    if (loading.current) {
      return (
        <Progress
          type="circle"
          percent={count}
          steps={{ count: 9, gap: 4 }}
          status="active"
          size={15}
          trailColor={"#fff"}
          strokeColor={"#f98e67"}
        />
      );
    }
  };

  return (
    <>
      {isCheck ? (
        <>
          <Dropdown menu={{ items, onClick: handleMenuClick }}>
            <Button
              type="primary"
              className={ComStyles.addButton}
              loading={loading.current}
              style={{marginLeft:10}}
            >
              <Space align="center">
                {renderProgress()}
                下载与删除
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </>
      ) : (
        ""
      )}

      <Modal
        title="自定义下载图片"
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        okText="确认"
        cancelText="取消"
      >
        <div
          style={{
            padding: "20px 0",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <span>
            第
            <InputNumber
              min={1}
              max={selectImgList.length}
              defaultValue={startNum}
              onChange={(value) => setStartNum(value)}
            />
            幅
          </span>
          &nbsp;&nbsp;&nbsp;
          <span>
            ~&nbsp;&nbsp;&nbsp;&nbsp;第
            <InputNumber
              min={1}
              max={selectImgList.length}
              defaultValue={endNum}
              onChange={(value) => setEndNum(value)}
            />
            幅
          </span>
        </div>
      </Modal>
    </>
  );
}
