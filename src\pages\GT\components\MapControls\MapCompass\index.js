import React, { useState, useEffect, useRef } from 'react';
import { Cesium } from 'umi';
import L from 'leaflet';
import { rotateCameraHeading } from '@/utils/cesium_help';
import compassIcon from '@/assets/icon/compass.svg';
import compassBgIcon from '@/assets/icon/compass_bg.svg';
import 'leaflet-compass/dist/leaflet-compass.min.css';
import 'leaflet-compass';
import styles from './index.module.less';

const MapCompass = ({ viewer: viewerInstance, isRightSidebarCollapsed, theme = 'dark', customCompassStyle = {} }) => {
    const viewer = useRef(null);
    const [heading, setHeading] = useState(0);
    const compassControl = useRef(null);

    // 朝向转文字
    function directionToString(num) {
        const n = parseFloat(num);
        if (n >= 0 && n <= 22.5) {
            return 'N';
        } else if (n > 22.5 && n <= 67.5) {
            return 'NE';
        } else if (n > 67.5 && n <= 112.5) {
            return 'E';
        } else if (n > 112.5 && n <= 157.5) {
            return 'SE';
        } else if (n > 157.5 && n <= 202.5) {
            return 'S';
        } else if (n > 202.5 && n <= 247.5) {
            return 'SW';
        } else if (n > 247.5 && n <= 292.5) {
            return 'W';
        } else if (n > 292.5 && n <= 337.5) {
            return 'NW'; // Corrected from WN to NW for standard abbreviation
        } else if (n > 337.5 && n <= 360) {
            return 'N';
        }
        return ''; // Default case
    }

    // 恢复朝向
    function recoverHeading() {
        if (viewer.current) {
            rotateCameraHeading(viewer.current, 0);
        }
    }

    useEffect(() => {
        if (!viewerInstance) return;

        viewer.current = viewerInstance;

        // 判断是Leaflet还是Cesium实例
        if (viewerInstance instanceof L.Map) {
            // Leaflet地图指南针
            if (window.DeviceOrientationEvent) {
                compassControl.current = L.control.compass({
                    position: 'bottomright',
                    autoActive: true,
                    showDigit: true,
                    deviceOrientationEventName: 'deviceorientationabsolute',
                    showCompass: true,
                    angleOffset: 0,
                });
                compassControl.current.addTo(viewerInstance);
            }
            return () => {
                if (compassControl.current) {
                    compassControl.current.remove();
                }
            };
        } else if (viewerInstance.scene) {
            // Cesium地图指南针
            const postRenderListener = () => {
                if (viewer.current && viewer.current.scene && viewer.current.scene.camera) {
                    let currentHeading = Cesium.Math.toDegrees(viewer.current.scene.camera.heading).toFixed(0);
                    setHeading(currentHeading === '360' ? 0 : parseFloat(currentHeading));
                }
            };
            viewer.current.scene.postRender.addEventListener(postRenderListener);

            return () => {
                if (viewer.current && viewer.current.scene && !viewer.current.isDestroyed()) {
                    viewer.current.scene.postRender.removeEventListener(postRenderListener);
                }
            };
        }
    }, [viewerInstance]);

    // 只为Cesium地图渲染自定义指南针UI
    if (!viewerInstance || viewerInstance instanceof L.Map) {
        return null;
    }

    const displayHeading = heading < 10 ? `00${heading}` : heading < 100 ? `0${heading}` : heading;

    const CompassContainerClasses = [
        styles.compassContainer,
        isRightSidebarCollapsed ? styles['right-sidebar-collapsed'] : styles['right-sidebar-expanded'],
        theme === 'dark' ? styles.darkTheme : styles.lightTheme,
    ].join(' ')

    return (
        <div
            className={CompassContainerClasses}
            style={{
                ...customCompassStyle
            }}
            onClick={recoverHeading}
            title="恢复指北"
        >
            <img
                src={compassBgIcon}
                alt="Compass Background"
                className={styles.compassBg}
                style={{ transform: `rotate(${-heading}deg)` }}
            />
            <img
                src={compassIcon}
                alt="Compass Needle"
                className={styles.compassNeedle}
            />
            <div className={styles.directionText}>{directionToString(heading)}</div>
            <div className={styles.degreeText}>{displayHeading}°</div>
        </div>
    );
};

export default MapCompass;