// src/store/configStore.js
import { create } from "zustand";
import { Get2 } from "@/services/general";
import { systemNameArr } from "@/stores/systemInfo";

// 定义默认配置,配置从后端获取，不需要前端设置，app的配置暂未支持
const defaultConfig = {
  mqtt: {
    wsUrl: "ws://***********:8083/mqtt",
    tcpAddress: "***********:1883",
  },
  rtmp: {
    webrtcUrl: "webrtc://***********/live/",
    httpUrl: "http://***********:8080/live/",
    shareUrl: "http://www.mzyj.unibeidou.com:11501/#/rtmpShare?sn=",
    aiUrl: "http://**************:12315",
    rtmpAddr: "***********",
  },
  oss: {
    url: "http://***********:9000",
    bucket: "300bdf2b-a150-406e-be63-d28bd29b409f",
  },
  //暂未使用
  app: {
    title: "",
    logo: "",
    backgroundImage: "",
  },
};

function getSystemInfos(Objective, url, app) {
  // 匹配标题、logo等系统信息
  if (app?.title) {
    //如果请求的config里配置了系统标题，则优先使用config里的系统信息，
    // 否则用到systemNameArr里的系统信息，其用IP地址判断是否匹配
    return app;
  }
  if (url) {
    const matchingInfo = Objective.find((obj) => url.includes(obj.ip));
    return matchingInfo || Objective[0];
  } else {
    return Objective.length > 0 ? Objective[0] : null;
  }
}
function replaceHosts(obj) {
  const parsedUrl = new URL(window.location.href);
  const host = parsedUrl.hostname; // 获取主机名

  for (const key in obj) {
    if (typeof obj[key] === "object" && obj[key] !== null) {
      // 递归处理子对象
      replaceHosts(obj[key]);
    } else if (typeof obj[key] === "string") {
      // 替换所有的 IP 地址为 'host'
      if (host.includes("localhost")) {
        obj[key] = obj[key].replace("ip", "***********");
      } else {
        obj[key] = obj[key].replace("ip", host);
      }
      // if (parsedUrl.protocol === "https:") {
      //   console.log("当前协议为https，替换ws为wss");
      //   obj[key] = obj[key].replace(/(ws:\/\/)/g, "wss://");
      // }
    }
  }
}

export const useConfigStore = create((set) => ({
  // 状态
  config: defaultConfig,
  loading: false,
  error: null,
  systemInfos: null, //系统标题、logo等
  headerHeight: 56,//导航栏高度
  MapSelf: null,//地图自身实例ref
  MapUrl: null,//地图地址
  ZSMapUrl: null,//正射地图信息
  isToggleRight: true,
  tableCurrent: 1,//表格/卡片页码存储
  stopMove: false,//是否禁止拖动画面，在飞控页面拖动摄像头时使用
  setStopMove: (value) => set({ stopMove: value }),
  setTableCurrent: (current) => set({ tableCurrent: current }),
  
  setIsToggleRight: (value) => {
    set({ isToggleRight: value });
  },
  setMapUrl: (value) => {
    set({ MapUrl: value });
  },
  setZSMapUrl: (value) => {
    set({ ZSMapUrl: value });
  },
  setMenuShow: (value) => {
    set({ MenuShow: value });
  },
  setMapSelf: (value) => {
    set({ MapSelf: value });
  },
  updateCenter: (newCenter, newZS, newZoom) => {
    set({ center: newCenter,data: newZS, zoom: newZoom }); 
  },
   setHeaderHeight: (value) => {
    set({ headerHeight: value });
  },

  // 初始化配置
  initConfig: async () => {
    try {
      set({ loading: true, error: null });
      const response = await Get2("/api/v2/APPConfig/Get");
      console.log("加载配置成功:", response);
      const config = response;
      replaceHosts(config);

      set({
        config: { ...config },
        loading: false,
        systemInfos: getSystemInfos(
          systemNameArr,
          config.mqtt.wsUrl,
          config?.app
        ),
      });
    } catch (error) {
      console.error("加载配置失败:", error);
      // 如果加载失败，使用默认配置
      set({
        config: defaultConfig,
        loading: false,
        error: error.message,
      });
    }
  },
}));

export default useConfigStore;
