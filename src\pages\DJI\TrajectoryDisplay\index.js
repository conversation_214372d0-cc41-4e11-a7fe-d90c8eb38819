import { useState, useEffect, useRef } from 'react';
import { message, Select, InputNumber, Input, Checkbox, Button, Tooltip, Card } from 'antd';
import { Cesium } from "umi";
import * as turf from '@turf/turf'
import { useModel } from 'umi';
import { getBodyH, isEmpty } from '@/utils/utils';
import MapControl from '@/hooks/mapControl';
import { GetCesiumViewer, PointImg3, addNotFlyZone } from '@/utils/cesium_help';
import { axiosApi } from '@/services/general';
import { queryPage2 } from '@/utils/MyRoute';
import FJGLB from '@/assets/models/triangle.glb';
import LastPageButton from "@/components/LastPageButton";
import "./index.less";

const TrajectoryDisplay = ({ record, data }) => {
    // 航线实体
    let wayline_polyline = useRef(null);
    let wayline_position = useRef([]);
    let viewer = useRef([]);
    let [viewerData, setviewerData] = useState(null)
    // 禁飞区实体合集
    let nfzList = useRef([])
    // 围栏实体合集
    let dfenceList = useRef([])
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    // 飞机模型
    let airplaneModel = useRef(null)
    // 模型初始位置
    let airplaneModel_position = useRef(new Cesium.Cartesian3.fromDegrees(120, 30, 1000));
    // 模型姿态
    let headingPitchRoll = useRef(new Cesium.HeadingPitchRoll());
    // 局部变换坐标系
    let fixedFrameTransform = Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
    // 记录上一帧的位置
    let previoustime = null;
    // 页面载入
    useEffect(() => {
        viewer.current = GetCesiumViewer('cesisss', true)
        // Enable lighting based on the sun position.
        viewer.current.scene.globe.enableLighting = true;
        // Enable depth testing so things behind the terrain disappear.
        viewer.current.scene.globe.depthTestAgainstTerrain = true;

        setviewerData(viewer.current)
        axiosApi(`/api/v1/FlyArea/GetCurrent`, "GET", {
            SN: record.DeviceSN,
        }).then((res1) => {
            let { res_nfzList, res_dfenceList } = addNotFlyZone(res1.data, viewer.current)
            nfzList.current = res_nfzList
            dfenceList.current = res_dfenceList
        })
        console.log(data);
        data.forEach((item, index) => {
            wayline_position.current.push(item.Longitude, item.Latitude, item.Height)
        });
        if (wayline_position.current.length >= 6) {
            wayline_polyline.current = viewer.current.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current);
                    }, false),
                    // 宽度
                    width: 3,
                    // 线的颜色
                    material: Cesium.Color.fromCssColorString('#0aed8a'),
                    clampToGround: false,
                },
                position: new Cesium.CallbackProperty(() => {
                    return Cesium.Cartesian3.fromDegrees(wayline_position.current[0], wayline_position.current[1], wayline_position.current[2]);
                }, false),
                billboard: {
                    image: PointImg3('s'),
                    color: Cesium.Color.WHITE,
                    scale: 1,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                },
            })
            viewer.current.zoomTo(wayline_polyline.current)
        }
        const start = Cesium.JulianDate.fromDate(timestampToDateString(data[0].Timestamp));
        const duration = Math.abs(data[data.length - 1].Timestamp - data[0].Timestamp) / 1000;
        const stop = Cesium.JulianDate.addSeconds(
            start,
            duration,
            new Cesium.JulianDate(),
        );
        viewer.current.clock.startTime = start.clone();
        viewer.current.clock.stopTime = stop.clone();
        viewer.current.clock.currentTime = start.clone();
        viewer.current.clock.multiplier = 1;
        viewer.current.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
        viewer.current.clock.shouldAnimate = true;
        const points = new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current)
        const times = data.map((item, index) => {
            return Math.abs(item.Timestamp - data[0].Timestamp) / 1000
        });
        const firstTime = times[0];
        const lastTime = times[times.length - 1];
        const delta = lastTime - firstTime;
        const positionSpline = new Cesium.CatmullRomSpline({
            times: times,
            points: points,
            firstTangent: Cesium.Cartesian3.subtract(
                points[0],
                points[0],
                new Cesium.Cartesian3(),
            ),
            lastTangent: Cesium.Cartesian3.subtract(
                points[points.length - 1],
                points[points.length - 1],
                new Cesium.Cartesian3(),
            ),
        });
        const position = new Cesium.CallbackPositionProperty(function (time, newResult) {
            const splineTime =
                (delta * Cesium.JulianDate.secondsDifference(time, start)) / duration;
            if (splineTime < firstTime || splineTime > lastTime) {
                return undefined;
            }
            // 计算当前帧的位置
            positionSpline.evaluate(splineTime, newResult)
            let oldResult = null
            if (previoustime !== null) {
                oldResult = positionSpline.evaluate(previoustime, oldResult)
                const cartographic1 = Cesium.Cartographic.fromCartesian(oldResult);
                const cartographic2 = Cesium.Cartographic.fromCartesian(newResult);
                let angle = turf.rhumbBearing([cartographic1.longitude, cartographic1.latitude], [cartographic2.longitude, cartographic2.latitude])
                const angleInRadians = Cesium.Math.toRadians(angle)
                headingPitchRoll.current.heading = angleInRadians
            }
            airplaneModel_position.current = newResult
            airplaneModel.current && Cesium.Transforms.headingPitchRollToFixedFrame(
                airplaneModel_position.current,
                headingPitchRoll.current,
                Cesium.Ellipsoid.WGS84,
                fixedFrameTransform,
                airplaneModel.current.modelMatrix
            )
            previoustime = splineTime
            return newResult;
        }, false);
        airplaneModel_position.current = new Cesium.Cartesian3.fromDegrees(data[0].Longitude, data[0].Latitude, data[0].Height)
        headingPitchRoll.current = {
            heading: Cesium.Math.toRadians(data[0].AttitudeHead),
            pitch: Cesium.Math.toRadians(data[0].AttitudePitch),
            roll: Cesium.Math.toRadians(data[0].AttitudeRoll),
        }
        Cesium.Model.fromGltfAsync({
            url: FJGLB,
            scale: 2,
            modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                airplaneModel_position.current,
                headingPitchRoll.current,
                Cesium.Ellipsoid.WGS84,
                fixedFrameTransform
            ),
            minimumPixelSize: 64,
            maximumScale: 128
        }).then(res => {
            airplaneModel.current = viewer.current.scene.primitives.add(res)
        })
        viewer.current.entities.add({
            availability: new Cesium.TimeIntervalCollection([
                new Cesium.TimeInterval({
                    start: start,
                    stop: stop,
                }),
            ]),
            position: position,
            point: {
                pixelSize: 1,
                color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
            },
        });
    }, []);
    function timestampToDateString(timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1); // 月份是从0开始的
        const day = String(date.getDate());
        const hours = String(date.getHours());
        const minutes = String(date.getMinutes());
        const seconds = String(date.getSeconds());

        return new Date(year, month, day, hours, minutes, seconds);
    }
    return (
        <div style={{ width: '100%', height: '100%', background: '#F5F5FF' }} className='layout_body'>
            <Card title={<LastPageButton title="轨迹回显" />}>
                <div style={{ height: '100%', width: '100%', padding: 12.0, position: 'relative' }} id="cesisss">
                    <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
                        <MapControl viewerData={viewerData} />
                    </div>
                </div>
            </Card>
        </div>
    )
};

export default TrajectoryDisplay;
