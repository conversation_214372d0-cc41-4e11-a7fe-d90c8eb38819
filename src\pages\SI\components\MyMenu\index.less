.sidebarContainer {
  :global {
    .ant-menu-dark {
      background-color: #0b1a36;
    }

    .ant-menu-dark .ant-menu-inline.ant-menu-sub {
      background-color: #0a1429;
    }

    .ant-menu-dark .ant-menu-item-selected {
      background-color: rgba(0, 170, 170, 0.2) !important;
      position: relative;
      color: rgb(0, 170, 170) !important;
      
      // &::after {
      //   content: '';
      //   position: absolute;
      //   left: 0;
      //   top: 0;
      //   bottom: 0;
      //   width: 3px;
      //   background-color: #59c3fd;
      // }

      .anticon, img {
        color: rgb(0, 170, 170) !important;
      }

      // 对选中菜单项中的SVG图标应用滤镜效果，使其变成指定颜色
      img {
        filter: brightness(0) saturate(100%) invert(44%) sepia(91%) saturate(3766%) hue-rotate(149deg) brightness(92%) contrast(101%);
        opacity: 1 !important; // 确保选中状态下图标完全不透明
      }
    }

    .ant-menu-submenu-title {
      &:hover {
        color: rgb(0, 170, 170) !important;
      }
    }

    .ant-menu-item {
      &:hover {
        color: rgb(0, 170, 170) !important;
        img {
          filter: brightness(0) saturate(100%) invert(44%) sepia(91%) saturate(3766%) hue-rotate(149deg) brightness(92%) contrast(101%);
          opacity: 1 !important; // 悬停时也确保不透明
        }
      }
    }

    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: rgb(0, 170, 170) !important;
    }

    .ant-menu-dark .ant-menu-item:hover {
      background-color: rgba(0, 170, 170, 0.1);
    }

    .ant-menu-dark .ant-menu-submenu-title:hover {
      background-color: rgba(0, 170, 170, 0.1);
    }

    .ant-menu-sub.ant-menu-inline {
    //   border-left: 2px solid #1c50a8;
    //   margin-left: 24px;
      padding-left: 0;
    }

    .ant-menu-inline .ant-menu-item {
    //   padding-left: 34px !important;
    }

    .ant-menu-submenu-arrow {
      color: rgb(0, 170, 170);
    }

    .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
      transform: rotate(0deg) !important;
    }

    .ant-menu-submenu > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
      transform: rotate(0deg);
    }

    .ant-menu-item .anticon, .ant-menu-submenu-title .anticon {
      font-size: 16px;
      margin-right: 0;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    }
    
    .ant-menu-title-content {
      font-size: 13px;
      margin: 0 !important;
    }

    .ant-btn-primary:hover, .ant-btn-primary:focus {
      background-color: #1c50a8;
      border-color: #1c50a8;
    }

    // 为菜单项添加过渡动画
    .ant-menu-item, .ant-menu-submenu-title {
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
      
      .anticon {
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
      }
      
      .ant-menu-title-content {
        transition: opacity 0.2s, width 0.2s, height 0.2s !important;
      }
    }

    // 折叠状态下的菜单样式
    .ant-menu-inline-collapsed .ant-menu-item,
    .ant-menu-inline-collapsed .ant-menu-submenu-title {
      padding: 0 !important;
      text-align: center;
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      height: 56px !important;
      
      .anticon {
        margin: 0 auto !important;
        font-size: 16px;
        line-height: normal !important;
        transform: scale(1.2) !important; // 折叠时图标放大
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important; // 弹性动画效果
      }
      
      .ant-menu-title-content {
        opacity: 0;
        width: 0;
        height: 0;
        overflow: hidden;
        display: inline-block;
      }
    }
  }

  // 折叠状态下的菜单样式
  :global(.ant-menu-inline-collapsed .ant-menu-item),
  :global(.ant-menu-inline-collapsed .ant-menu-submenu-title) {
    padding: 0 !important;
    text-align: center;
    
    .anticon {
      margin: 0 auto;
      font-size: 16px;
      line-height: 40px;
    }
    
    .ant-menu-title-content {
      opacity: 0;
      width: 0;
      height: 0;
      overflow: hidden;
      display: inline-block;
    }
  }
}

.customMenu {
  :global {
    // 菜单过渡动画
    transition: width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
    
    .ant-menu-item {
      height: auto;
      padding: 3px !important;
      // margin: 0 !important;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1.5;
      transition: all 0.3s !important;

      // 改进图标居中显示
      img {
        margin: 0 auto;
        display: block;
        width: 16px;
        height: 16px;
        opacity: 0.6;
        margin-bottom: 3px;
      }
    }

    .ant-menu-item:hover {
      img {
        opacity: 1 !important; // 悬停时也确保不透明
      }
    }

    .ant-menu-submenu-title {
      height: auto;
      padding: 10px 0;
      margin-top: 4px !important;
      margin-bottom: 4px !important;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 1.5;
      transition: all 0.3s !important;

      // 改进图标居中显示
      img {
        margin: 0 auto;
        display: block;
        width: 16px;
        height: 16px;
      }
    }
    
    .ant-menu-item .anticon, .ant-menu-submenu-title .anticon {
      margin-right: 0;
      margin-bottom: 4px;
      font-size: 18px;
      transition: all 0.3s !important;
    }
    
    .ant-menu-submenu-arrow {
      position: absolute;
      right: 8px;
      top: 50%;
      transition: transform 0.3s !important;
    }
  }
}

/* 额外添加折叠状态下的样式 */
:global(.ant-menu-inline-collapsed .ant-menu-item),
:global(.ant-menu-inline-collapsed .ant-menu-submenu-title) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 !important;
  
  :global(.anticon) {
    margin: 0 !important;
    line-height: 40px !important;
  }
}

// 在文件末尾添加自定义滤镜类
:global(.custom-teal-icon) {
  filter: brightness(0) saturate(100%) invert(44%) sepia(91%) saturate(3766%) hue-rotate(149deg) brightness(92%) contrast(101%);
} 