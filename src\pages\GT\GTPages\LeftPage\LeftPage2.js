import HeadTitle from "@/pages/GT/components/HeadTitle/HeadTitle";
import styles from "./LeftPage2.less";
import MyEcharts from "@/utils/chart2";
import alarmStatistics from "./Panles/EchartsData/alarmStatistics";
import alarmStatistics2 from "./Panles/EchartsData/alarmStatistics2";
import {
  WuRenJiIcon,
  ZuoBiaoIcon,
  ShanDianIcon,
} from "@/pages/GT/assets/svg.js";

export default function LeftPage2() {

  let arr = [
    { icon: WuRenJiIcon(), num: 100, type: "无人机" },
    { icon: ZuoBiaoIcon(), num: 5000, type: "任务巡检" },
    { icon: ShanDianIcon(), num: 31231, type: "告警" },
  ];
  let option = alarmStatistics;
  let option2 = alarmStatistics2;

  return (
    <div className={styles.LeftPage2}>
      <HeadTitle text="巡检统计" />
        <div className={styles.inspection_statistics}>
          {arr.map((item, index) => {
            return (
              <div className={styles.inspection_statistics_item} key={index}>
                <div className={styles.icon_box}>
                  <div>{item.icon}</div>
                </div>
                <div className={styles.type_num}>{item.num}</div>
                <div className={styles.type_name}>{item.type}</div>
              </div>
            );
          })}
          </div>
 
         <HeadTitle text="告警统计" style={{ marginTop: "-10px" }} />
          <div className={styles.myEcharts}>
            <MyEcharts height={150} option={option} />
          </div>
          <div className={styles.myEcharts}>
            <MyEcharts height={200} option={option2} />
          </div>

    </div>
  );
}
