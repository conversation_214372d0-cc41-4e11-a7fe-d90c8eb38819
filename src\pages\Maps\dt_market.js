
import {
     Marker,
    LayerGroup,
    LayersControl,
    Tooltip,
} from 'react-leaflet';


import { isEmpty } from '@/utils/utils';
import styles from './dt_market.less';
import { message, Modal } from 'antd';
import { Gcj02ToWgs84 } from './gps_helper';
import { className } from 'cesium';



const { Overlay } = LayersControl


const orangeIcon = L.icon({
    iconUrl: require('@/assets/icons/orange.png'),//图标地址
    shadowUrl: null,
    iconAnchor:[18, 36],
    iconSize: [36, 36], // 图标宽高
 });




 export const greenIcon = L.icon({
    iconUrl: require('@/assets/icons/green.png'),//图标地址
    iconAnchor:[18, 36],
    iconSize: [36, 36], // 图标宽高
});

export const redIcon = L.icon({
    iconUrl: require('@/assets/icons/red.png'),//图标地址
    iconAnchor:[12, 24],
    iconSize: [24, 24], // 图标宽高
});

export const redIcon3 = L.icon({
    iconUrl: require('@/assets/icons/red.png'),//图标地址
    iconAnchor:[9, 18],
    iconSize: [18, 18], // 图标宽高
});

export const redIcon2 = L.icon({
    iconUrl: require('@/assets/icons/green.png'),//图标地址
    iconAnchor:[12, 24],
    iconSize: [24, 24], // 图标宽高
});


export const deviceIcon = L.icon({
    iconUrl: require('@/assets/icons/device.png'),//图标地址
    iconAnchor:[24, 10],
    iconSize: [56, 56], // 图标宽高
});

export const deviceIcon2 = L.icon({
    iconUrl: require('@/assets/icons/device.png'),//图标地址
    iconAnchor:[21, 10],
    iconSize: [42, 42], // 图标宽高
});

export const feijiIcon = L.icon({
    iconUrl: require('@/assets/icons/feiji.png'),//图标地址
    iconAnchor:[18, 18],
    iconSize: [36, 36], // 图标宽高
});


function getIcon(color){
    if(color=="一般")
    return greenIcon;
    if(color=="特别重要")
    return redIcon;
    return orangeIcon;
}


export const Location_Market = (location) => {
 
   
    return    <Overlay  name="坐标点" checked>
            <LayerGroup>
                <Marker  position={location} icon={redIcon}  />
            </LayerGroup>
        </Overlay>
     

}

export const Location_Market2 = (location,title) => {
 
  console.log('Location_Market2',location);
    return  <Marker  position={location} icon={redIcon}  >
                <Tooltip permanent direction="right" offset={[5, -20]} >{title}</Tooltip>
                </Marker>
     
 
     

}



export function getMarket5(s,eve){
     //无人机设备mark1
    const lat1 = s.Lat;
    const lng1 = s.Lng;
    let nm1=s.DName;
    if(isEmpty(nm1)) nm1=s.DeviceName;
    let marker = L.marker([lat1,lng1], { icon: deviceIcon2 })
    marker.bindTooltip(nm1,{ direction:"bottom" ,permanent:true, offset:[0, 28],className: styles.deviceTooltip});
    marker.on('click', eve)
    return marker
}

const getcolor3=(nn)=>{
    if(nn==1) return 'red';
    if(nn==2) return 'orange';
    if(nn==3) return 'blue';
    if(nn==4) return 'green';
    return 'grey';
}
const getLocationIcon=(val,col)=>{
   return L.divIcon({
        html: `<div style="width: 30px; height: 30px; border-radius: 50%; box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);font-size:16px;font-weight:bold; line-height:30px; background-color: ${col}; color: #fff; text-align: center;">${val}</div>`,
        iconSize: [30, 30],
        iconAnchor: [16, 20], // [30, 10] 表示水平偏移30像素，垂直偏移10像素
      });
}

export function getLocationMarket6(e,nn,eve){
    //地点搜索结果mark
    const latLng=e.location
    let newMarker = L.marker(latLng, { icon: getLocationIcon(nn,getcolor3(nn)) });
    newMarker.bindTooltip(e.name,{ direction:"top" , offset:[0, -20],className: styles.locaSearchTooltip});
    newMarker.on('click', eve)
    return newMarker
}


export const Location_Market5 = (s,eve) => {
 //无人机设备mark2
    const lat1 = s.Lat;
    const lng1 = s.Lng;
    let nm1=s.DName;
    if(isEmpty(nm1)) nm1=s.DeviceName;
    return    <Marker  position={[lat1,lng1]} icon={deviceIcon2} data={s} eventHandlers={eve} >
                    <Tooltip className={styles.deviceTooltip}  direction="bottom" permanent offset={[0, 28]} >{nm1}</Tooltip>
                </Marker>
}

export const Location_Market6 = (s,eve) => {
 
    const lat1 = Number(s.Lat);
    const lng1 = Number(s.Lng);

    return    <Marker  position={[lat1,lng1]} icon={feijiIcon} data={s} eventHandlers={eve} >
                {isEmpty(s.DeviceName)?null: <Tooltip   direction="bottom" offset={[0, 5]} >{s.DeviceName}</Tooltip>}
                </Marker>
}

export const WaylinePointMarker = (location,title) => {
 
   
    return <Marker  position={location} icon={redIcon3} >
                <Tooltip  direction="bottom" offset={[0, 5]} >{title}</Tooltip>
         </Marker>

}

export const WaylinePointMarker2 = (location,title) => {
 
   
    return <Marker  position={location} icon={redIcon2} >
                <Tooltip  direction="bottom" offset={[0, 5]} >{title}</Tooltip>
         </Marker>
     

}

export const WaylinePointMarker3 = (location,title,click) => {
 
   
    return <Marker  position={location} icon={redIcon2}  eventHandlers={click}>
                <Tooltip  direction="bottom" offset={[0, 5]} >{title}</Tooltip>
         </Marker>
     

}


export const Location_Market3 = (location,title) => {
 
   
    return    <Overlay  name="坐标点" checked>
            <LayerGroup>
                <Marker  position={location} icon={redIcon}  >
                <Tooltip  sticky>{title}</Tooltip>
                </Marker>
            </LayerGroup>
        </Overlay>
     

}


export const DT_Market = () => {
 
    var list = [];
    var sd_data=GetInfoValue('info-RDike','info-RDike-time');
    var xg_data=GetInfoValue('info-REngineer','info-REngineer-time');
   
    list[0] =
        <Overlay  name="沙堤点">
            <LayerGroup>
                {getTC2(sd_data)}
            </LayerGroup>

        </Overlay>

 list[1] =
 <Overlay  name="险工点">
     <LayerGroup>
         {getTC3(xg_data)}
     </LayerGroup>

 </Overlay>
    

    return list;

}



function getPositions(d1) {
   
     var xx= [d1.Lat, d1.Lng];


    //console.log('getPositions',list)
    return xx;
}


const eventHandlers={
    click: (e) => {
    
      let data=e.target.options.data;
      Modal.info({
        title:data.Name,
        width:1000.0,
        okText:"确认",
        okType:"default",
        maskClosable:true,
        content:
        REngineerPanel(data),
      
    });
    },
};

const eventHandlers2={
    click: (e) => {
    
      let data=e.target.options.data;
      Modal.info({
        title:data.Name,
        width:1000.0,
        okText:"确认",
        okType:"default",
        maskClosable:true,
        content:
        RDikePanel(data),
    });
    },
};



function getTC2(data) {
    var list=[];
    var i=0;
   // console.log('getTC2',data);
    if(isEmpty(data))
    return list;
    data.forEach(s => {
        //console.log('rr',s);
        list[i] =
            <Marker key={s.Guid} position={getPositions(s)} icon={getIcon(s.Level)} data={s} eventHandlers={eventHandlers2} />
        i++;
    })
    return list;
}

function getTC3(data) {
    var list=[];
    var i=0;
   // console.log('getTC2',data);
    if(isEmpty(data))
    return list;
    data.forEach(s => {
        //console.log('rr',s);
        list[i] =
            <Marker key={s.Guid} position={getPositions(s)} icon={getIcon(s.Leven)} data={s}  eventHandlers={eventHandlers} />
        i++;
    })
    return list;
}


