import { useModel } from "umi"
import { useState, useEffect } from 'react'
import styles from './ToggleMapTypeButton.module.less'

const ToggleMapTypeButton = ({ onMapTypeChange }) => {
  const { pData } = useModel("pageModel")
  const [is3D, setIs3D] = useState(pData.current.pNM1 === "三维场景")
  
  // 监听 pData 变化，保持本地状态同步
  useEffect(() => {
    setIs3D(pData.current.pNM1 === "三维场景")
  }, [pData.current.pNM1])

  const toggleMapType = () => {
    const newType = is3D ? "飞行地图" : "三维场景"
    
    // 只更新一次，其他地方通过 effect 同步
    pData.current.pNM1 = newType
    
    // 通知父组件
    if (onMapTypeChange) {
      onMapTypeChange(newType)
    }
  }

  return (
    <div className={styles['toggle-map-type-container']}>
      <button
        className={styles['toggle-map-type-button']}
        onClick={toggleMapType}
        title={is3D ? "切换到二维场景" : "切换到三维场景"}
      >
        <span className={styles['map-type-text']}>{is3D ? "2D" : "3D"}</span>
      </button>
    </div>
  )
}

export default ToggleMapTypeButton