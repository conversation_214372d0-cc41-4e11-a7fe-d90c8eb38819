import { create } from 'zustand';
import { EllipsoidTerrainProvider } from 'cesium';
import { Cesium } from 'umi';

// 地形提供者状态枚举
export const TERRAIN_STATUS = {
  IDLE: 'idle',           // 初始状态
  LOADING: 'loading',     // 正在加载
  SUCCESS: 'success',     // 加载成功
  ERROR: 'error'          // 加载失败
};

// 地形提供者类型枚举
export const TERRAIN_TYPE = {
  DEFAULT: 'default',     // 默认椭球地形
  LOCAL: 'local',         // 本地Python地形服务（开发用）
  MINIO: 'minio',        // MinIO地形服务
  ONLINE: 'online'        // 在线地形服务
};

// 辅助函数：测试地形服务是否可用
const testTerrainService = async (url, timeout = 5000) => {
  try {
    // 尝试访问地形服务的layer.json
    const layerUrl = url.endsWith('/') ? `${url}layer.json` : `${url}/layer.json`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(layerUrl, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'Cache-Control': 'no-cache'
      }
    });

    clearTimeout(timeoutId);

    // 检查响应状态
    console.log(`地形服务测试 ${url}: ${response.status} ${response.statusText}`);
    return response.ok; // 返回 true 如果状态码是 2xx
  } catch (error) {
    console.warn(`地形服务测试失败 ${url}:`, error.message);
    return false;
  }
};

// 创建地形提供者 store
const useTerrainStore = create((set, get) => ({
  // 状态
  status: TERRAIN_STATUS.IDLE,
  currentType: TERRAIN_TYPE.DEFAULT,
  error: null,
  
  // 缓存的地形提供者
  providers: {
    [TERRAIN_TYPE.DEFAULT]: null,
    [TERRAIN_TYPE.LOCAL]: null,
    [TERRAIN_TYPE.MINIO]: null,
    [TERRAIN_TYPE.ONLINE]: null
  },
  
  // 配置
  config: {
    localTerrainUrl: 'http://localhost:8001/terrain_tiles',  // 本地服务（开发用）
    minioTerrainUrl: 'http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/cesium/terrain/terrain',       // MinIO地形服务
    // onlineTerrainUrl: 'http://localhost:8001/terrain_tiles',
    onlineTerrainUrl: Cesium.createWorldTerrainAsync(), // 可以配置在线地形服务URL或使用Cesium内置的在线地形服务
    retryCount: 3,
    retryDelay: 1000,
    enablePreCheck: true // 配置是否禁用预检测来调试问题
  },

  // Actions
  
  /**
   * 初始化默认地形提供者
   */
  initDefaultTerrain: () => {
    const { providers } = get();
    if (!providers[TERRAIN_TYPE.DEFAULT]) {
      const defaultProvider = new EllipsoidTerrainProvider();
      console.log('创建默认椭球地形提供者');
      
      set(state => ({
        providers: {
          ...state.providers,
          [TERRAIN_TYPE.DEFAULT]: defaultProvider
        }
      }));
    }
    return providers[TERRAIN_TYPE.DEFAULT] || new EllipsoidTerrainProvider();
  },

  /**
   * 异步加载本地地形提供者
   */
  loadLocalTerrain: async () => {
    const { providers, config } = get();

    // 如果已经有缓存的本地地形提供者，直接返回
    if (providers[TERRAIN_TYPE.LOCAL]) {
      console.log('使用缓存的本地地形提供者');
      return providers[TERRAIN_TYPE.LOCAL];
    }

    set({ status: TERRAIN_STATUS.LOADING, error: null });

    try {
      console.log('开始加载本地地形提供者:', config.localTerrainUrl);

      // 根据配置决定是否启用预检测
      if (!config.enablePreCheck) {
        const isServiceAvailable = await testTerrainService(config.localTerrainUrl, 3000);
        if (!isServiceAvailable) {
          throw new Error('本地地形服务不可用或无法访问');
        }
      }

      // 使用 Promise.race 来设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('本地地形服务请求超时')), 8000); // 8秒超时
      });

      const terrainPromise = Cesium.CesiumTerrainProvider.fromUrl(config.localTerrainUrl);
      const localProvider = await Promise.race([terrainPromise, timeoutPromise]);

      console.log('本地地形提供者加载成功:', localProvider);

      set(state => ({
        status: TERRAIN_STATUS.SUCCESS,
        currentType: TERRAIN_TYPE.LOCAL,
        providers: {
          ...state.providers,
          [TERRAIN_TYPE.LOCAL]: localProvider
        }
      }));

      return localProvider;
    } catch (error) {
      console.error('本地地形提供者加载失败:', error);

      set({
        status: TERRAIN_STATUS.ERROR,
        error: error.message || '本地地形提供者加载失败'
      });

      // 返回默认地形提供者作为备用
      return get().getDefaultTerrain();
    }
  },

  /**
   * 异步加载MinIO地形提供者
   */
  loadMinioTerrain: async () => {
    const { providers, config } = get();

    // 如果已经有缓存的MinIO地形提供者，直接返回
    if (providers[TERRAIN_TYPE.MINIO]) {
      console.log('使用缓存的MinIO地形提供者');
      return providers[TERRAIN_TYPE.MINIO];
    }

    set({ status: TERRAIN_STATUS.LOADING, error: null });

    try {
      console.log('开始加载MinIO地形提供者:', config.minioTerrainUrl);

      // 根据配置决定是否启用预检测
      if (config.enablePreCheck) {
        const isServiceAvailable = await testTerrainService(config.minioTerrainUrl, 3000);
        if (!isServiceAvailable) {
          throw new Error('MinIO地形服务不可用或无法访问');
        }
      }

      // 使用 Promise.race 来设置超时，避免长时间等待无响应的服务
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('MinIO地形服务请求超时')), 10000); // 10秒超时
      });

      const terrainPromise = Cesium.CesiumTerrainProvider.fromUrl(config.minioTerrainUrl);
      const minioProvider = await Promise.race([terrainPromise, timeoutPromise]);

      console.log('MinIO地形提供者加载成功:', minioProvider);

      set(state => ({
        status: TERRAIN_STATUS.SUCCESS,
        currentType: TERRAIN_TYPE.MINIO,
        providers: {
          ...state.providers,
          [TERRAIN_TYPE.MINIO]: minioProvider
        }
      }));

      return minioProvider;
    } catch (error) {
      console.error('MinIO地形提供者加载失败:', error);

      set({
        status: TERRAIN_STATUS.ERROR,
        error: error.message || 'MinIO地形提供者加载失败'
      });

      // 返回默认地形提供者作为备用
      return get().getDefaultTerrain();
    }
  },

  /**
   * 异步加载在线地形提供者
   */
  loadOnlineTerrain: async () => {
    const { providers, config } = get();

    // 如果已经有缓存的在线地形提供者，直接返回
    if (providers[TERRAIN_TYPE.ONLINE]) {
      console.log('使用缓存的在线地形提供者');
      return providers[TERRAIN_TYPE.ONLINE];
    }

    set({ status: TERRAIN_STATUS.LOADING, error: null });

    try {
      console.log('开始加载在线地形提供者:', config.onlineTerrainUrl);

      let onlineProvider;

      // 如果配置的是 URL 字符串，使用 CesiumTerrainProvider.fromUrl
      if (typeof config.onlineTerrainUrl === 'string') {
        // 根据配置决定是否启用预检测
        if (config.enablePreCheck) {
          const isServiceAvailable = await testTerrainService(config.onlineTerrainUrl, 3000);
          if (!isServiceAvailable) {
            throw new Error('在线地形服务不可用或无法访问');
          }
        }

        // 使用 Promise.race 来设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('在线地形服务请求超时')), 12000); // 12秒超时
        });

        const terrainPromise = Cesium.CesiumTerrainProvider.fromUrl(config.onlineTerrainUrl);
        onlineProvider = await Promise.race([terrainPromise, timeoutPromise]);

      } else {
        // 如果配置的是 Cesium 内置地形服务

        // 使用 Promise.race 来设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('在线地形服务请求超时')), 12000); // 12秒超时
        });

        const terrainPromise = config.onlineTerrainUrl;
        onlineProvider = await Promise.race([terrainPromise, timeoutPromise]);
      }

      console.log('在线地形提供者加载成功:', onlineProvider);

      set(state => ({
        status: TERRAIN_STATUS.SUCCESS,
        currentType: TERRAIN_TYPE.ONLINE,
        providers: {
          ...state.providers,
          [TERRAIN_TYPE.ONLINE]: onlineProvider
        }
      }));

      return onlineProvider;
    } catch (error) {
      console.error('在线地形提供者加载失败:', error);

      set({
        status: TERRAIN_STATUS.ERROR,
        error: error.message || '在线地形提供者加载失败'
      });

      // 返回默认地形提供者作为备用
      return get().getDefaultTerrain();
    }
  },

  /**
   * 获取默认地形提供者
   */
  getDefaultTerrain: () => {
    const { providers } = get();
    return providers[TERRAIN_TYPE.DEFAULT] || get().initDefaultTerrain();
  },

  /**
   * 获取本地地形提供者（同步）
   * 如果还没有加载，返回默认地形提供者
   */
  getLocalTerrain: () => {
    const { providers } = get();
    return providers[TERRAIN_TYPE.LOCAL] || get().getDefaultTerrain();
  },

  /**
   * 获取MinIO地形提供者（同步）
   * 如果还没有加载，返回默认地形提供者
   */
  getMinioTerrain: () => {
    const { providers } = get();
    return providers[TERRAIN_TYPE.MINIO] || get().getDefaultTerrain();
  },

  /**
   * 获取在线地形提供者（同步）
   * 如果还没有加载，返回默认地形提供者
   */
  getOnlineTerrain: () => {
    const { providers } = get();
    return providers[TERRAIN_TYPE.ONLINE] || get().getDefaultTerrain();
  },

  /**
   * 获取最佳可用的地形提供者（异步）
   * 优先级：在线地形 > 本地地形（测试） > MinIO地形 > 默认地形
   * 如果高优先级的地形提供者还没有加载，会尝试异步加载
   */
  getBestAvailableTerrain: async () => {
    const { providers } = get();

    // 优先级1：在线地形提供者
    if (providers[TERRAIN_TYPE.ONLINE]) {
      console.log('使用已缓存的在线地形提供者');
      return providers[TERRAIN_TYPE.ONLINE];
    }

    // 尝试加载在线地形提供者
    try {
      console.log('尝试加载在线地形提供者...');
      const onlineProvider = await get().loadOnlineTerrain();
      if (onlineProvider && onlineProvider !== get().getDefaultTerrain()) {
        console.log('成功加载在线地形提供者');
        return onlineProvider;
      }
    } catch (error) {
      console.warn('在线地形提供者加载失败，使用默认地形:', error);
    }

    // // 优先级2：本地地形提供者
    // if (providers[TERRAIN_TYPE.LOCAL]) {
    //   console.log('使用已缓存的本地地形提供者');
    //   return providers[TERRAIN_TYPE.LOCAL];
    // }

    // // 尝试加载本地地形提供者
    // try {
    //   console.log('尝试加载本地地形提供者...');
    //   const localProvider = await get().loadLocalTerrain();
    //   if (localProvider && localProvider !== get().getDefaultTerrain()) {
    //     console.log('成功加载本地地形提供者');
    //     return localProvider;
    //   }
    // } catch (error) {
    //   console.warn('本地地形提供者加载失败，尝试下一个优先级:', error);
    // }

    // 优先级3：MinIO地形提供者
    if (providers[TERRAIN_TYPE.MINIO]) {
      console.log('使用已缓存的MinIO地形提供者');
      return providers[TERRAIN_TYPE.MINIO];
    }

    // 尝试加载MinIO地形提供者
    try {
      console.log('尝试加载MinIO地形提供者...');
      const minioProvider = await get().loadMinioTerrain();
      if (minioProvider && minioProvider !== get().getDefaultTerrain()) {
        console.log('成功加载MinIO地形提供者');
        return minioProvider;
      }
    } catch (error) {
      console.warn('MinIO地形提供者加载失败，尝试下一个优先级:', error);
    }

    // 优先级4：默认地形提供者
    console.log('使用默认椭球地形提供者');
    return get().getDefaultTerrain();
  },

  /**
   * 切换地形类型
   */
  switchTerrainType: async (terrainType) => {
    try {
      let provider;

      switch (terrainType) {
        case TERRAIN_TYPE.LOCAL:
          provider = await get().loadLocalTerrain();
          break;
        case TERRAIN_TYPE.MINIO:
          provider = await get().loadMinioTerrain();
          break;
        case TERRAIN_TYPE.ONLINE:
          provider = await get().loadOnlineTerrain();
          break;
        case TERRAIN_TYPE.DEFAULT:
          provider = get().getDefaultTerrain();
          set({ currentType: TERRAIN_TYPE.DEFAULT });
          break;
        default:
          console.warn('未知的地形类型:', terrainType);
          provider = get().getDefaultTerrain();
          break;
      }

      return provider;
    } catch (error) {
      console.error('切换地形类型失败:', error);
      return get().getDefaultTerrain();
    }
  },

  /**
   * 更新配置
   */
  updateConfig: (newConfig) => {
    set(state => ({
      config: {
        ...state.config,
        ...newConfig
      }
    }));
  },

  /**
   * 清除缓存的地形提供者
   */
  clearCache: (type) => {
    if (type) {
      set(state => ({
        providers: {
          ...state.providers,
          [type]: null
        }
      }));
    } else {
      // 清除所有缓存，但保留默认地形
      const defaultProvider = get().providers[TERRAIN_TYPE.DEFAULT];
      set({
        providers: {
          [TERRAIN_TYPE.DEFAULT]: defaultProvider,
          [TERRAIN_TYPE.LOCAL]: null,
          [TERRAIN_TYPE.MINIO]: null,
          [TERRAIN_TYPE.ONLINE]: null
        },
        status: TERRAIN_STATUS.IDLE,
        error: null
      });
    }
  },

  /**
   * 重置状态
   */
  reset: () => {
    set({
      status: TERRAIN_STATUS.IDLE,
      currentType: TERRAIN_TYPE.DEFAULT,
      error: null
    });
  }
}));

export default useTerrainStore;
