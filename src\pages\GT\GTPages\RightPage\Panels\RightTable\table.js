import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { HGet2, HPost2 } from "@/utils/request";
import { Post2 } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (refrush) => {
  return [
    {
      title: getTableTitle("灾害体类型"),
      dataIndex: "灾害体类型",
      key: "灾害体类型",
      align: "center",
    },

    {
      title: getTableTitle("灾害体名称"),
      dataIndex: "灾害体名称",
      key: "灾害体名称",
      align: "center",
    },

    {
      title: getTableTitle("室内编号"),
      dataIndex: "室内编号",
      key: "室内编号",
      align: "center",
    },

    {
      title: getTableTitle("地理位置"),
      dataIndex: "地理位置",
      key: "地理位置",
      align: "center",
    },

    {
      title: getTableTitle("监测建议"),
      dataIndex: "监测建议",
      key: "监测建议",
      align: "center",
    },

    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle">
          <MyButton
            style={{ padding: "2px 5px" }}
            onClick={() => console.log(record)}
          >
            一键飞至
          </MyButton>
        </Space>
      ),
    },
  ];
};

export default TableCols;
