import React, { useState, useEffect, useRef } from "react";
import { message, Tabs } from "antd";
import { useModel } from "umi";
import { getGuid, getRouteLevel } from "@/utils/helper";
import { queryPage, queryPageByPath } from "@/utils/MyRoute";
import "@/pages/GT/style/antd-common.less";
const MyTabs = () => {
  const { page, setPage, lastPage, pageData, setPageData, setListPage } =
    useModel("pageModel");

  const [activeKey, setActiveKey] = useState();
  const [items, setItems] = useState([]);
  const isFirstRender = useRef(true); // 初次渲染标志

  const onChange = (newActiveKey) => {
    setActiveKey(newActiveKey);
  };
  const initTabs = () => {
    setItems([]);
  };
  // 初始化清空标签
  useEffect(() => {
    initTabs();
  }, []);

  useEffect(() => {
    if (isFirstRender.current) {
      // 第一次渲染后设置标志为false
      isFirstRender.current = false;
    } else {
      // 不是首次渲染时执行
      add();
    }
  }, [pageData]);

  useEffect(() => {
    if (activeKey) {
      currentPage(activeKey);
    }
  }, [activeKey]);

  // 判断key是否重复
  const hasDuplicateKey = (items, key) => {
    return items.some((item) => item.key === key);
  };

  const shouldClearItems = (key, title) => {
    for (let item of items) {
      const keyParts = item.key.split("/");
      const two = keyParts[2]; // 二级目录
      const three = keyParts[3]; // 三级目录

      // 如果当前item的标签不同（避免重复）、二级目录相同、三级目录不同，则返回true
      if (title !== item.label && two === key && key !== three) {
        return true;
      }
    }
    return false;
  };

  const add = () => {
    if (!pageData) {
      console.log("[add] 路由数据为空");
      return;
    }
    console.log("[add] 路由数据", pageData);
    const { key, title, children, isInit } = pageData;

    if (!key || !title || !children) {
      console.log("[add] 路由数据中缺少字段");
      return;
    }
    if (hasDuplicateKey(items, key)) {
      console.log("[add] 路由数据中存在重复的 key 值");
      setListPage(queryPageByPath(key)?.title);
      setActiveKey(key);
      return;
    }
    if (isInit) {
      // 初始路由，不存储
      return setItems([]);
    }
    // if (shouldClearItems(key, title)) {
    //   return setItems([]);
    // }

    const level = getRouteLevel(key);
    if (level <= 3) {
      console.log("[add] 路由数据级数不够 不存储");
      // 1,2,3级路由为主路由，不存储
      // 如果点击的是1，2，3级路由 则清空tabs
      return setItems([]);
    }
    setItems((prevItems) => [
      ...prevItems,
      {
        label: title,
        key: key,
        child: children, //这里是child字段，children字段会重现page内容
      },
    ]);

    setActiveKey(key);
  };
  const currentPage = (activeKey) => {
    // 先去item里面找
    const targetItem = items.find(item => item.key === activeKey);
    if (targetItem) {
      setPage({
        children: targetItem.child,
        key: activeKey,
        title: targetItem.label
      });
    } else {
      // 在item里面找不到的时候 再去路由里面查询
      setPage(queryPageByPath(activeKey));
    }
  };


  const remove = (targetKey) => {
    const newItems = items.filter((item) => item.key !== targetKey);
    let newActiveKey = activeKey;

    if (newItems.length && newActiveKey === targetKey) {
      const lastIndex = items.findIndex((item) => item.key === targetKey) - 1;
      newActiveKey = lastIndex >= 0 ? newItems[lastIndex].key : newItems[0].key;
    }

    setItems(newItems);
    setActiveKey(newActiveKey);
  };
  const onEdit = (targetKey, action) => {
    if (action === "add") {
      add();
    } else {
      remove(targetKey);
    }
  };

  return items.length > 1 ? (
    <Tabs
      hideAdd
      onChange={onChange}
      activeKey={activeKey}
      type="editable-card"
      size="small"
      onEdit={onEdit}
      items={items}
    />
  ) : null;
};

export default MyTabs;
