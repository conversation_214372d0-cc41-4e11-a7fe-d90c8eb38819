import React, { useEffect, useState } from 'react';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import styles from './MyHead.less';
import { useModel, history } from 'umi';
import useConfigStore from "@/stores/configStore";


const Nav = ({ headList, handlePageChange }) => {
  const [currentPageKey, setCurrentPageKey] = useState(null);
  // 动态获取系统标题信息
  const { systemInfos } = useConfigStore()
  const { title: systemTitle = '智巡—无人机智能云控中台' } = systemInfos || {}

  useEffect(() => {
    const pathSegments = location.pathname.split('/')
    const currentKeyFromPath = pathSegments[pathSegments.length - 1]

    // 检查这个 key 是否在 headList 中有效
    const isValidKeyInHeadList = headList.some(item => item.key === currentKeyFromPath)

    if (isValidKeyInHeadList) {
      setCurrentPageKey(currentKeyFromPath)
    } else if (location.pathname === '/SI/system') {
      setCurrentPageKey(null)
    }
  }, [location.pathname, headList])

  const handleHeadClick = (key) => {
    // 只有当点击的 key 和当前路由的 key 不一致时才跳转
    const currentPathKey = location.pathname.substring(location.pathname.lastIndexOf('/') + 1)
    if (key !== currentPathKey) {
      handlePageChange(key)
      setCurrentPageKey(key)
    }
  }

  function systemManger() {
    const fullPath = `/#/SI/system`
    window.open(fullPath, '_blank');
    // 防止继续执行原来的导航逻辑
    return false;
  }
  
  function exit() {
    localStorage.clear();
    history.replace('/SI/login', {});
    return;
  }

  return (
    <div>
      <div id={styles.MyHead}>
        {/* <div className={styles.headIconBox}>
          <div className={styles.headIcon2}></div>
        </div> */}
        <div className={styles.headTitle}>{systemTitle}</div>
        <div className={styles.headContent}>
          {headList?.map(item => (
            <div key={item.key} className={styles.menuItemWrapper}>
              <div
                className={`${styles.headContentItem} ${currentPageKey === item.key ? styles.active : ''}`}
                onClick={() => handleHeadClick(item.key)}
                role="button"
                tabIndex={0}
              >
                {item.label}
              </div>
            </div>
          ))}
        </div>
        <div className={styles.headSetting}>
          <div className={styles.headSettingItem} onClick={systemManger}>
            <SettingOutlined />
            <span className={styles.headSettingItem_text}>运维管理</span>
          </div>
          <div className={styles.headSettingItem}>
            <UserOutlined />
            <span className={styles.headSettingItem_text}>管理员</span>
          </div>
          <div className={styles.headSettingItem_divider}></div>
          <div className={styles.headSettingItem} onClick={exit}>
            <LogoutOutlined />
            <span className={styles.headSettingItem_text}>退出</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Nav;
