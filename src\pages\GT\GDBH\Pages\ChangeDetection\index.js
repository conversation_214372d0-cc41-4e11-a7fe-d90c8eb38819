import { queryPage } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { Button, Tabs, Card, Steps, Upload, List, Checkbox, Table, Form, Input, Select, Modal, DatePicker, message, Row, Col, Descriptions, Slider, InputNumber, Image, Spin } from 'antd';
import AImodelSelector from '@/pages/GT/components/RecordsPage/AImodelSelector.js';

const ChangeDetection = () => {
    const { setPage } = useModel("pageModel");

    const [modalVisible, setModalVisible] = useState(false);
    const [modalVisible2, setModalVisible2] = useState(false);
    const [currentStep, setCurrentStep] = useState(0); // 当前步骤
    const [beforeImgsLength, setBeforeImgsLength] = useState(0); // 前期影像数量
    const [afterImgsLength, setAfterImgsLength] = useState(0); // 后期影像数量
    const [modelList, setModelList] = useState([]); // 模型列表
    const [selectedModels, setSelectedModels] = useState([]); // 选择的模型
    const [cls, setCls] = useState({}) // 识别内容 (数字)
    const [user, setUser] = useState(); // 用户信息

    const [form] = Form.useForm();


    // 打开新增窗口
    const openModal = () => {
        // 重置表单
        form.resetFields();

        setModalVisible(true);
        reset();
    };

    const cloceCreatTask = () => {
        // 重置表单
        form.resetFields();

        setModalVisible(false);
        reset();
    };

    // 除了表单的其他状态重置
    const reset = () => {
        setCurrentStep(0);
        setBeforeImgsLength(0);
        setAfterImgsLength(0);
    };

    // 处理提交
    const handleSubmit = async (values) => {
        console.log('@@@form', form.getFieldsValue());
        console.log('@@@selectedModels', selectedModels);
        console.log('@@@cls', cls);
    };


    const steps = [
        {
            title: '选择前期影像',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item
                        name="beforeImgs"
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e?.fileList}
                    >
                        <Upload.Dragger
                            name="file"
                            multiple={true}
                            beforeUpload={(file) => {
                                // const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                                // if (!isValidType) {
                                //     message.error('仅支持jpg/png格式文件！');
                                //     return Upload.LIST_IGNORE; // 阻止上传
                                // }
                                return false;
                            }}
                            onChange={({ file, fileList }) => {
                                form.setFieldsValue({ beforeImgs: fileList });

                                setBeforeImgsLength(fileList.length);
                            }}
                            onRemove={(file) => {
                                const files = form.getFieldValue('beforeImgs') || [];
                                const newFiles = files.filter(f => f.uid !== file.uid);
                                form.setFieldsValue({ beforeImgs: newFiles });

                                setBeforeImgsLength(newFiles.length);
                                return true;
                            }}
                        >
                            <p className="ant-upload-text">点击或拖拽上传图片</p>
                            <p className="ant-upload-hint">支持jpg/png格式</p>
                        </Upload.Dragger>
                    </Form.Item>

                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 10,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>

                        <Button
                            type="primary"
                            disabled={!beforeImgsLength}
                            onClick={() => {
                                setCurrentStep(1);
                            }}
                        >
                            下一步
                        </Button>
                    </div>
                </div>
            ),
        },
        {
            title: '选择后期影像',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item
                        name="afterImgs"
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e?.fileList}
                    >
                        <Upload.Dragger
                            name="file"
                            multiple={true}
                            beforeUpload={(file) => {
                                // const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                                // if (!isValidType) {
                                //     message.error('仅支持jpg/png格式文件！');
                                //     return Upload.LIST_IGNORE; // 阻止上传
                                // }
                                return false;
                            }}
                            onChange={({ file, fileList }) => {
                                form.setFieldsValue({ afterImgs: fileList });

                                setAfterImgsLength(fileList.length);
                            }}
                            onRemove={(file) => {
                                const files = form.getFieldValue('afterImgs') || [];
                                const newFiles = files.filter(f => f.uid !== file.uid);
                                form.setFieldsValue({ afterImgs: newFiles });

                                setAfterImgsLength(newFiles.length);
                                return true;
                            }}
                        >
                            <p className="ant-upload-text">点击或拖拽上传图片</p>
                            <p className="ant-upload-hint">支持jpg/png格式</p>
                        </Upload.Dragger>
                    </Form.Item>
                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 10,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                setCurrentStep(0);
                            }}
                        >
                            上一步
                        </Button>
                        <Button
                            type="primary"
                            disabled={!afterImgsLength}
                            onClick={() => {
                                setCurrentStep(2);
                            }}
                        >
                            下一步
                        </Button>
                    </div>

                </div>
            ),
        },
        {
            title: '配置模型参数',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item name="TaskName" label="任务名称" rules={[{ required: true, message: '请输入任务名称' }]}>
                        <Input placeholder='请输入任务名称' />
                    </Form.Item>
                    <div
                        style={{
                            maxHeight: '40vh',
                            overflowY: 'auto',
                            scrollbarWidth : 'none',
                        }}
                    >
                        <AImodelSelector
                            selectedModels={selectedModels}
                            cls={cls}
                            onChange={({ selectedModels, cls }) => {
                                if (selectedModels) setSelectedModels(selectedModels);
                                if (cls) setCls(cls);
                            }}
                        />
                    </div>
                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 10,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                setCurrentStep(1);
                            }}
                        >
                            上一步
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                handleSubmit();
                            }}
                        >
                            提交
                        </Button>
                    </div>

                </div>
            ),
        }
    ];



    const exr =
        <div>
            <Button
                type="primary"
                onClick={() => {
                    console.log('新增');
                    openModal();
                }}>
                新增
            </Button>
        </div>

    const items = steps.map((item) => ({ key: item.title, title: item.title }));

    return (
        <div>
            <Card
                title={"变化检测"}
                styles={{
                    body: {
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        padding: 0,
                        overflow: "hidden",
                    },
                }}
                extra={exr}
            >
                <div style={{
                    display: 'flex',
                    height: '100%',
                    padding: 0,
                    gap: 16
                }}>
                    {/* 左侧信息板块 */}
                    <div style={{
                        flex: '0 0 300px',
                        borderRight: '1px solid #f0f0f0',
                        paddingRight: 16
                    }}>
                        左侧信息板块
                    </div>

                    {/* 右侧图片板块 */}
                    <div style={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'row',
                        gap: 16,
                        height: '100%',
                        alignItems: 'flex-start'
                    }}>
                        右侧差异对比图片板块
                    </div>
                </div>

                <Modal
                    title="新增"
                    open={modalVisible}
                    onCancel={() => cloceCreatTask()}
                    footer={null}
                    width='40vw'
                    styles={{
                        body: {
                            maxHeight: '70vh',
                            display: 'flex',
                            flexDirection: 'column',
                            padding: 24
                        }
                    }}
                >
                    <Form layout="vertical" form={form}>
                        <Steps
                            current={currentStep}
                            items={items}
                        // onChange={changeStep} // 允许点击步骤条切换步骤
                        />
                        {steps[currentStep].content}
                    </Form>
                </Modal>
            </Card>
        </div>
    )
};

export default ChangeDetection;