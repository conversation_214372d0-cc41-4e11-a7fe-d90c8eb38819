import { useState, useEffect } from "react";
import MyHead from "../components/MyHead";
import commonStyle from "../style/common.less";
import "@/pages/GT/style/antd-common.less";
import MyMenu from "./Pages/MyMenu";
import { useModel } from "umi";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import { queryPage } from "@/utils/MyRoute";
import HeadTabs from "@/pages/GT/components/HeadTabs";
import SITheme from '@/pages/SI/style/theme';

//自主测绘
function App() {
  const [collapsed, setCollapsed] = useState(false);
  const { page, setPage, lastPage, currentPage } = useModel("pageModel");
  // 判断是否显示菜单
  const isVideoPage =
    currentPage === "素材管理";
  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };

  const headList = [
    { label: "一张图", key: "一张图" },
    { label: "智能巡检", key: "智能巡检" },
    { label: "素材管理", key: "素材管理"},
    { label: "建模任务", key: "建模任务" },
  ];

  return (
    <div className="gt-page">
      <div
        className={commonStyle.gt_back_black}
        style={{ position: "relative", overflow: "hidden", height: "100vh" }}
      >
        <MyHead headList={headList} handlePageChange={handlePageChange} />

        <div
          style={{
            display: "flex",
            flex: 1,
            position: "relative",
            overflow: "hidden",
          }}
        >
          {isVideoPage && (
            <MyMenu
              handlePageChange={handlePageChange}
              setCollapsed={setCollapsed}
              collapsed={collapsed}
            />
          )}
          <div
            className="blackBackground"
            style={{
              flex: 1,
              overflow: "hidden",
              marginLeft: isVideoPage ? (collapsed ? 80 : 160) : 0,
              transition: isVideoPage ? "margin-left 0.2s" : "none",
            }}
          >
            <ConfigProvider locale={locale} theme={SITheme}>
              <HeadTabs></HeadTabs>
              {page}
            </ConfigProvider>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
