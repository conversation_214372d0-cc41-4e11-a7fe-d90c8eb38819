import { useMemo } from 'react'
import L from 'leaflet'
import LeafletSplit from './components/LeafletSplit'
import CesiumSplit from './components/CesiumSplit'

/**
 * 地图卷帘对比统一组件
 * 根据viewer类型自动选择对应的实现
 */
const MapSplit = ({
  viewer,
  enabled = false,
  onSplitPositionChange,
  leftLayers = [],
  rightLayers = [],
  splitPosition = 0.5,
  ...otherProps
}) => {
  // 判断viewer类型并选择对应的组件
  const SplitComponent = useMemo(() => {
    if (!viewer) return null
    
    // 检查是否为Leaflet地图实例
    if (viewer instanceof L.Map) {
      return LeafletSplit
    }
    
    // 检查是否为Cesium Viewer实例
    if (viewer.scene && viewer.camera && viewer.canvas) {
      return CesiumSplit
    }
    
    console.warn('MapSplit: 未识别的viewer类型', viewer)
    return null
  }, [viewer])

  if (!SplitComponent) {
    return null
  }

  // 根据viewer类型传递不同的props
  const componentProps = viewer instanceof L.Map ? {
    map: viewer,
    enabled,
    onSplitPositionChange,
    leftLayers,
    rightLayers,
    splitPosition,
    ...otherProps
  } : {
    viewer,
    enabled,
    onSplitPositionChange,
    leftLayers,
    rightLayers,
    splitPosition,
    ...otherProps
  }

  return <SplitComponent {...componentProps} />
}

export default MapSplit
