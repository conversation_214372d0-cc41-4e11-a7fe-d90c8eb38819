import React, { useState, useRef } from "react";
import styles from "./ResizableDiv.css";

const ResizableDiv = ({ child }) => {
  const [width, setWidth] = useState(500); 
  const divRef = useRef(null); 
  let animationFrameId; 

  const startDrag = (e) => {
    const startX = e.clientX;
    const startWidth = divRef.current.offsetWidth;
    const throttle = (func, delay) => {
      let lastCall = 0;
      return (...args) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
          lastCall = now;
          return func(...args);
        }
      };
    };
    const onMouseMove = throttle((moveEvent) => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId); // 取消上一个动画帧
      }
      // 使用 requestAnimationFrame 进行优化
      requestAnimationFrame(() => {
        const newWidth = startWidth + (moveEvent.clientX - startX);
        setWidth(newWidth); 
      });
    },16);

    const onMouseUp = () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
      cancelAnimationFrame(animationFrameId); // 清理动画帧
    };

    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
  };

  const toggleDiv = () => {
    setWidth(width > 0 ? 0 : 500); // 切换宽度
  };

  return (
    <div>
  
      <div
        ref={divRef}
        className={styles.resizable}
        style={{ width: `${width}px`,}} 
      >
            
          <>
            <div className={styles.resizeHandle} onMouseDown={startDrag}>
              {/* <div onClick={toggleDiv}>
                <ShowQiIcon width={30} height={30} color="#318071"></ShowQiIcon>
              </div> */}
            </div>
            <div className={styles.resizeContent}>{child}</div>
          </>
      </div>
      <div
        className={styles.leftButton}
        onClick={toggleDiv} 
      >
        <div className={styles.triangle}></div>
        {/* <ShowQiIcon width={30} height={30} color="#318071" /> */}
      </div>
    </div>
  );
};

export default ResizableDiv;
