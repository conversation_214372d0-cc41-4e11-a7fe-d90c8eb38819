import React, { createContext, useContext, useState } from 'react';

const PageContext = createContext();

export function useDronePage() {
  return useContext(PageContext);
}

export function PageProvider({ children }) {
  const [currentPage, setCurrentPage] = useState('home');

  const value = {
    currentPage,
    setCurrentPage,
  };

  return <PageContext.Provider value={value}>{children}</PageContext.Provider>;
}