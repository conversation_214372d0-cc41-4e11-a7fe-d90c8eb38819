import { Card, Input, Button, Form, message, Table, Modal,} from 'antd';
import { useState, useEffect } from 'react';
import { useModel } from "umi";
import { axiosApi } from "@/services/general";
import TableCols from './TableCols';
import {columnTranslation,FIELD_COMPONENT_MAPPING,getFieldComponent} from './TableHelper';
import MyModal from '@/components/MyModal';

const OperationManagement = () => {
    const [dataSource, setDataSource] = useState([]);
    const [loading, setLoading] = useState(false);
    const { listPage, setListPage } = useModel("pageModel");
    const [addVisible, setAddVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [addForm] = Form.useForm();
    const [editForm] = Form.useForm();
    const [currentRecord, setCurrentRecord] = useState(null);
    const [currentTablePage, setCurrentTablePage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const exr = <div><Button type="primary" onClick={() => setAddVisible(true)}>新增</Button> </div>

    useEffect(() => { 
      // 页面切换时,先重置表格数据,防止数据干扰
      setDataSource([]);
      getDefaultData();
    }, [listPage]);

    // 初始化表格数据
    const getDefaultData = async ()=>{
      try{ 
        setLoading(true);
        if(!listPage) {
          return
        };
        let url = ``
        if(listPage === '项目管理'){
          url = '/api/v1/Gf_Project/GetAllList'
        }else if(listPage === '组串管理'){
          url = '/api/v1/Guangfu/GetAllList'
        }else if(listPage === '子阵管理'){
          url = '/api/v1/Guangfuzizheng/GetAllList'
        }else{
          return;
        }
        const data = await axiosApi(url,"GET",null)
        const sortedData = data.data.sort((a, b) => a.ID - b.ID);
        setDataSource(sortedData);
        setLoading(false);
        console.log('@@data',data);
      }catch(err){
        message.error('初始化失败');
      }
   }

    // 按照数据的模板生成新增表单
    const renderAddForm = () => {
      if (!dataSource[0]) return null;
      
      return Object.keys(dataSource[0])
        .filter(key => key !== 'ID' && key !== 'sn' && key !== 'problem')
        .map(key => {
          const label = columnTranslation[key] || key;
          const fieldType = FIELD_COMPONENT_MAPPING[key] || 'default';
    
          return (
            <Form.Item
              key={key}
              label={label}
              name={key}
              rules={[{ required: true }]}
            >
              {getFieldComponent(fieldType)}
            </Form.Item>
          );
        });
    };

    // 新增数据
    const handleAdd = async () => {
      try {
          const values = await addForm.validateFields();
          let url = '';
          
          if(listPage === '项目管理'){
              url = '/api/v1/Gf_Project/AddData';
          } else if(listPage === '组串管理'){
              url = '/api/v1/Guangfu/AddData';
          } else if(listPage === '子阵管理'){
              url = '/api/v1/Guangfuzizheng/AddData';
          }

          await axiosApi(url, "POST", values);
          message.success('新增成功');
          setAddVisible(false);
          addForm.resetFields();
          getDefaultData();
      } catch (error) {
          message.error('新增失败');
          console.error('Add error:', error);
      }
    };

    // 显示编辑弹窗
    const showEditModal = (record) => {
      setCurrentRecord(record);
      editForm.setFieldsValue(record);
      setEditVisible(true);
    };

    // 按照字段生成编辑表单项 
    const renderDynamicForm = () => {
      if (!currentRecord) return null;
    
      return Object.keys(currentRecord)
      .map(key => {
        if (key === 'ID') {
          return (
            <Form.Item key={key} name={key} hidden>
              <Input type="hidden" />
            </Form.Item>
          );
        }
        if (key === 'sn') {
          return (
            <Form.Item key={key} name={key} hidden>
              <Input type="hidden" />
            </Form.Item>
          );
        }
        if (key === 'problem') {
          return (
            <Form.Item key={key} name={key} hidden>
              <Input type="hidden" />
            </Form.Item>
          );
        }
        const label = columnTranslation[key] || key;
        const fieldType = FIELD_COMPONENT_MAPPING[key] || 'default';
    
        return (
          <Form.Item
            key={key}
            label={label}
            name={key}
            rules={key === 'requiredField' ? [{ required: true }] : []}
          >
            {getFieldComponent(fieldType)}
          </Form.Item>
        );
      });
    };

    // 新增数据 弹窗组件
    const renderAddmodal = () => (
      <MyModal
        visible={addVisible}
        onOk={handleAdd}
        onCancel={() => setAddVisible(false)}
        title="新增记录"
        form={addForm}
      >
        {renderAddForm()}
      </MyModal>
    );
    
    // 编辑数据 弹窗组件
    const renderEditModal = () => (
      <MyModal
        visible={editVisible}
        onOk={handleUpdate}
        onCancel={() => setEditVisible(false)}
        title="修改记录"
        form={editForm}
      >
        {renderDynamicForm()}
      </MyModal>
    );
      
    // 修改数据
    const handleUpdate = async () => {
      try {
        const values = await editForm.validateFields();
        let url = "";
        const params = { 
          ...values,
          
        };

        // 根据页面类型配置不同接口
        if (listPage === "项目管理") {
          url = `/api/v1/Gf_Project/Update`;
        } else if (listPage === "组串管理") {
          url = `/api/v1/Guangfu/Update`;
        } else if (listPage === "子阵管理") {
          url = `/api/v1/Guangfuzizheng/Update`;
        }

        await axiosApi(url, "POST", params);
        message.success("修改成功");
        setEditVisible(false);
        getDefaultData();
      } catch (error) {
        message.error("修改失败");
        console.error("Update error:", error);
      }
    };
  
  


    return (

        <div style =
          {{ 
            margin: 0,
            height: 'calc(100vh - 56px)',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* 新增数据弹窗 */}
          {renderAddmodal()}
          {/* 编辑数据弹窗 */}
          {renderEditModal()}
          <Card 
            title={listPage} 
            bordered={false} 
            extra={exr}
            style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              flex: 1,
              overflow: 'hidden',
              padding: 0,
            }}
          >
            <div style={{ 
              flex: 1,
              overflow: 'hidden',
              padding: '24px'
            }}>
              <Table
                style={{ height: '100%' }}
                pagination={{
                  defaultPageSize: pageSize,
                  current: currentTablePage,
                  pageSizeOptions: [10, 20, 40, 60, 80],
                  showSizeChanger: true,
                  onShowSizeChange: (current, size) => {
                    setPageSize(size);
                    setCurrentTablePage(1); 
                  },
                  onChange: (page) => setCurrentTablePage(page),
                  total: dataSource.length,
                  locale: {
                    items_per_page: "条/页",
                    jump_to: "跳至",
                    page: "页",
                  },
                  showTotal: (total) => (
                    <span className="custom-pagination-text">
                      共 {total} 条 第 {currentTablePage} /{" "}
                      {Math.ceil(total / pageSize)} 页
                    </span>
                  ),
                  className: "custom-pagination-container",
                }}
                rowKey={(record) => record.ID}
                loading={loading}
                bordered
                dataSource={dataSource}
                columns={TableCols(dataSource, getDefaultData,showEditModal)}
                size='small'
              />
            </div>
          </Card>
        </div>
    )

}

export default OperationManagement;