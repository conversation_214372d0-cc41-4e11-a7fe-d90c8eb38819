@right-sidebar-expanded-width: 360px;
@right-sidebar-collapsed-offset: 40px;
@tree-panel-spacing: 6px; // 与 sidebar 或页面边缘的间距

.compassContainer {
    cursor: pointer;
    box-sizing: border-box;
    border-radius: 50%;
    height: 56px;
    width: 56px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    bottom: 8px;
    transition: right 0.35s ease-in-out;

    &:hover {
        background-color: #f0f0f0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }


    &.right-sidebar-expanded {
        right: calc(@right-sidebar-expanded-width + @tree-panel-spacing);
    }

    &.right-sidebar-collapsed {
        right: calc(@right-sidebar-collapsed-offset + @tree-panel-spacing);
    }

    // Light theme (default)
    &.lightTheme {
        background-color: #ffffff;

        &:hover {
            background-color: #f0f0f0;
        }
    }

    // Dark theme
    &.darkTheme {
        background-color: #1C313A;

        &:hover {
            background-color: rgba(28, 49, 58, 0.8);
        }
    }
}

.compassBg {
    width: 56px;
    height: 56px;
    transition: transform 0.2s ease-out;

    .darkTheme & {
        // 在dark主题下，将白色背景转换为深蓝绿色 (#1C313A)
        filter: invert(1) sepia(1) saturate(2) hue-rotate(180deg) brightness(0.3) contrast(1.2);
    }
}

.compassNeedle {
    width: 40px;
    height: 43px;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .darkTheme & {
        // 在dark主题下，调整指针颜色以适配深色背景
        filter: invert(1) brightness(0.8);
    }
}

.directionText {
    position: absolute;
    left: 50%;
    top: 35%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: #333;
    user-select: none;

    .darkTheme & {
        color: #ffffff;
    }
}

.degreeText {
    position: absolute;
    left: 50%;
    top: 60%;
    transform: translateX(-50%);
    font-size: 11px;
    color: #555;
    user-select: none;

    .darkTheme & {
        color: #ffffff;
    }
}

:global(.leaflet-compass) {
    bottom: 18px !important;
    right: 46px !important;
    z-index: 1000;
}