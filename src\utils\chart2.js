/*
 * ECharts 组件基础部分
 * 传入 option 和渲染方式 renderer
 * */

import React, { PureComponent } from 'react';
import * as echarts from 'echarts';
import { debounce } from './index'; // 一个节流函数

export default class Chart extends PureComponent {
  constructor(props) {
    super(props);
    this.chart = null;
    this.resizeObserver = null; // 用于存储 ResizeObserver 实例
  }

  async componentDidMount() {
    // 初始化图表
    await this.initChart(this.el);
    // 将传入的配置(包含数据)注入
    this.setOption(this.props.option);
    
    // 监听父元素的大小变化
    this.resizeObserver = new ResizeObserver(() => {
      this.resize(); // 当父元素大小变化时，重新绘制图表
    });
    if (this.el?.parentNode) {
      this.resizeObserver.observe(this.el.parentNode);
    }; // 观察父元素
  }

  componentDidUpdate() {
    // 每次更新组件都重置
    this.setOption(this.props.option);
  }

  componentWillUnmount() {
    // 组件卸载前卸载图表
    this.dispose();
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
  
  render() {
    const { width = '100%', height = '100%' } = this.props;

    return (
      <div
        className='default-chart'
        ref={el => (this.el = el)}
        style={{ width, height }}
      />
    );
  }

  initChart = el => {
    // renderer 用于配置渲染方式 可以是 svg 或者 canvas
    const renderer = this.props.renderer || 'canvas';
    const { onClick } = this.props;

    return new Promise(resolve => {
      setTimeout(() => {
        this.chart = echarts.init(el, 'light', {
          renderer,
        });
        
        this.chart.on('click', onClick);
        resolve();
      }, 0);
    });
  };

  setOption = option => {
    if (!this.chart) {
      return;
    }

    const { notMerge, lazyUpdate } = this.props;
    this.chart.setOption(option, notMerge, lazyUpdate);
  };

  dispose = () => {
    if (!this.chart) {
      return;
    }

    this.chart.dispose();
    this.chart = null;
  };

  resize = () => {
    // 重新绘制图表
    this.chart && this.chart.resize();
  };

  getInstance = () => {
    return this.chart;
  };
}
