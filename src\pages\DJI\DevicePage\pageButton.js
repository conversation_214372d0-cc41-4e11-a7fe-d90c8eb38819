import { Dropdown, Button } from "antd";
import { isIndividual } from "@/utils/helper";

const items = (handleClick, device) => {
  const list = [];

  list.push({
    key: "1",
    label: <a onClick={handleClick}>飞行地图</a>,
  });

  if (!isIndividual(device)) {
    list.push({
      key: "2",
      label: <a onClick={handleClick}>机场镜头</a>,
    });
  }

  list.push({
    key: "3",
    label: <a onClick={handleClick}>飞机镜头</a>,
  });

  // list.push({
  //   key: '4',
  //   label: (  <a onClick={handleClick}>飞机云直播</a>),
  // });

  list.push({
    key: "5",
    label: <a onClick={handleClick}>三维场景</a>,
  });

  return list;
};

const PageButton = (title, handleClick) => {
  let device = JSON.parse(localStorage.getItem("device"));
  return (
    <Dropdown
      menu={{
        items: items(handleClick, device),
      }}
      placement="bottomRight"
      arrow
      style={{
        zIndex: 1005,
      }}
    >
      <Button type="text">{title}</Button>
    </Dropdown>
  );
};

export default PageButton;
