import { getBodyH, isEmpty } from '@/utils/utils';
import { useEffect, useState } from 'react';
import { Button } from 'antd';
import styles from './index.less';
import DJBaseMap from '@/pages/Maps/DJBaseMap';
import JCXX from '@/pages/DJI/DevicePage/JXSSPanel';
import IfShowPanel from '@/components/IfShowPanel';
import HmsPanel from '@/pages/DJI/HmsPage/HmsPanel';
import GetPlayer from '@/pages/DJI/DevicePage/PlayerPanel';

import PageButton from '@/pages/DJI/DevicePage/pageButton';
import RtmpButton from '@/pages/DJI/DevicePage/RtmpButton';
import FJInfoPanel from '@/pages/DJI/DevicePage/FJInfoPanel';
import FJCtrPanel from '@/pages/DJI/FlyToPage/FJCtrPanel';
import BDZL from '@/assets/images/BDZL.png'
import { useModel } from 'umi';
import {Get2} from '@/services/general.js';

const DevicePage = ({device}) => {

  const [url, setUrl] = useState(1);
  const [p1, setP1] = useState(<DJBaseMap device={device} h1={getBodyH(56)} />);
  const [p2, setP2] = useState(GetPlayer('100%', '100%', 1));
  const [ifX, setIfX] = useState(true);
  const [ifY, setIfY] = useState(true);
  //const [device, setDevice] = useState({})
  const { sdata } = useModel('stateModel');
  // const sn="7CTDLCE00AC2J4";
  // const sn2="1581F6Q8D23CT00A5N49";
  if(isEmpty(device)){
    device=JSON.parse( localStorage.getItem('device'))
  }

  const onClickYY = () => {
    console.log(ifY);
    setIfY(!ifY);
  };

  useEffect(async() => {
      setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />);
      setP2(GetPlayer('300px', '240px', url));
  }, []);


  const onClickZHM = (e) => {
    console.log('onClickZHM', e)
    const tt = e.target.innerText;
    if (tt === "机场镜头") {

      setP1(GetPlayer('100%', getBodyH(62), 1, device.SN))

    }
    if (tt === "云台视频") {
      setP1(GetPlayer('100%', getBodyH(62), 2, device.SN))
    }
    if (tt === "机前视频") {
      setP1(GetPlayer('100%', getBodyH(62), 5, device.SN))
    }
    if (tt === "飞行地图") {
      setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />)
    }
  }

  const onClickCHM = (e) => {
    const tt = e.target.innerText;
    if (tt === "机场镜头") {
      setP2(GetPlayer('300px', '240px', 1, device.SN))
    }
    if (tt === "云台视频") {

      setP2(GetPlayer('300px', '240px', 2, device.SN))
    }
    if (tt === "机前视频") {

      setP2(GetPlayer('300px', '240px', 5, device.SN))
    }
    if (tt === "飞行地图") {
      setP2(<DJBaseMap  device={device} sn={device.SN} h1={'240px'} />)
    }
  }


  const btnPanel = <div
    style={{
      position: 'absolute',
      zIndex: 1000,
      width: '100%',
      background: 'rgba(255,255,255,0.5)',
    }}
  >

    {/* <Button type="text" onClick={() => onClickHH()}>
  切换{' '}
</Button> */}
    <RtmpButton sn={device.SN}/>
    {PageButton('主画面', onClickZHM, sdata)}
    {PageButton('画中画', onClickCHM, sdata)}

    {/* {DeBugButton()} */}
    <Button type="text" onClick={() => onClickYY()}>
      {' '}
      {ifY ? '隐藏' : '显示'}{' '}
    </Button>
  </div>

  const xxx = <div style={{ height: 24.0, background: 'red' }}></div>


  return (
    <div className={styles.IndexPageStyle} >
      {/* {isEmpty(device) ? <div /> : <WebSocketDemo sn={device.SN} sn2={device.SN2} />} */}
      <div
        className="container"
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
        }}
      >
        {p1}

        <div
          className={styles.XXX2}
          style={{
            zIndex: 1000,
          }}
        >
          {btnPanel}

          {ifY ? p2 : null}
        </div>

        {IfShowPanel(260, 260, 8, 8, null, null, <JCXX device={device}/>, ifY)}

        {IfShowPanel(290, 260, 320, 8, null, null, FJInfoPanel(device.SN), ifY)}

        {IfShowPanel(260, 300, 320, null, 8, null, HmsPanel(), ifY)}

        {/* {IfShowPanel(28, 1200, getBodyH(85), 80, null, 0, FJCtrPanel(device.SN), true)} */}

        {IfShowPanel(28, 200, getBodyH(100), null, 8, 0, <div style={{ height: 36, width: 200 }}><img style={{ height: 36 }} height={36} width={150} src={BDZL}></img></div>, true)}

      </div>
    </div>
  );
};

export default DevicePage;
