import { timeFormat2 } from '@/utils/helper';
import { Clone, downloadFile2, downloadFile, getBodyH, isEmpty, getImgUrl, getImgSLTUrl,getVideoSLTUrl } from '@/utils/utils';
import MediaInfoPanel from './MediaInfoPanel';
import { history } from 'umi';

export function GetImgItemPanel (item,mList, setPage, lastPage, doNotShowLastButton) {
    const onClick = (item) => {
        if(doNotShowLastButton){
            history.push("/gt/WRJ");
            // 显示返回按钮
            localStorage.setItem('showBackButton', 'true');
        }
        setPage(<MediaInfoPanel mList={mList} record={item} lastPage={lastPage} />)
    }

    const getSLTURL=(item)=>{
        let slt=item.SLTImg;
        if(isEmpty(item.SLTImg)){
            slt=item.ObjectName;
        }   
        return getImgSLTUrl(slt);
    }

    return <div style={{ cursor: 'pointer', margin:4.0 }}>

        <img style={{ width: '100%', cursor: 'pointer', height: '100%', borderRadius: 5.0 }}  src={getSLTURL(item)}
            onClick={() => {
                onClick(item);
            }}

        />
        <div style={{ margin: 4.0, cursor: 'pointer', fontWeight: 'bold', height: 36.0, color: 'rgba(0,0,0,0.7)' }}><span>{item.WayLineNM + "-航点" + item.HangDianIndex}</span>
            {/* <span style={{float:'right'}}>{item.Size}</span>  */}
        </div>
        {/* <div style={{width:'100%' ,position:'absolute',top:0,left:14,fontWeight:'bold', color:'white'}}>{item.WayLineNM}</div> */}
        <div style={{ position: 'absolute', cursor: 'pointer', top: 2, bottom: 40, right: 12, fontWeight: 'bold', color: 'white' }}>{timeFormat2(item.CreatedTime)}</div>
        {/* <div style={{position:'absolute',top:2,bottom:40,right:20,fontWeight:'bold', color:'white'}}>{item.Size}</div> */}
    </div>
}


export function GetVideoItemPanel  (item, setModal, setOpen, doNotShowLastButton) {
   

    const onClick = (item) => {
        if(doNotShowLastButton){
            history.push("/gt/WRJ");
            // 显示返回按钮
            localStorage.setItem('showBackButton', 'true');
        }
        setModal(<div style={{ height: 350, width: '100%' , margin:4.0 }}>
            <div style={{ position: 'absolute', left: 20, top: 18.0, fontWeight: 'bold', height: 36.0, color: 'rgba(0,0,0,0.7)' }}>{item.WayLineNM + "-" + item.HangDianIndex} </div>
            <div style={{ position: 'absolute', cursor: 'pointer', top: 18.0, right: 48, fontWeight: 'bold', color: 'rgba(0,0,0,0.7)' }}>{timeFormat2(item.CreatedTime)}</div>

            <video id={item.ID} key={item.ID} height={'100%'} width={'100%'} controls>
                <source src={getImgUrl(item.ObjectName)} type="video/mp4" />
            </video></div>);
        setOpen(true);
    }

    return <div style={{ cursor: 'pointer', }}>

        <img style={{ width: '100%', cursor: 'pointer', height: '100%', borderRadius: 5.0 }}  src={getImgUrl(item.SLTImg)}
            onClick={() => {
                onClick(item);
            }}

        />
        <div style={{ margin: 4.0, cursor: 'pointer', fontWeight: 'bold', height: 36.0, color: 'rgba(0,0,0,0.7)' }}><span>{item.WayLineNM + "-" + item.HangDianIndex}</span> <span style={{ float: 'right' }}>{item.Size}</span>  </div>
        {/* <div style={{width:'100%' ,position:'absolute',top:0,left:14,fontWeight:'bold', color:'white'}}>{item.WayLineNM}</div> */}
        <div style={{ position: 'absolute', cursor: 'pointer', top: 2, bottom: 40, right: 12, fontWeight: 'bold', color: 'white' }}>{timeFormat2(item.CreatedTime)}</div>
        {/* <div style={{position:'absolute',top:2,bottom:40,right:20,fontWeight:'bold', color:'white'}}>{item.Size}</div> */}
    </div>
}

