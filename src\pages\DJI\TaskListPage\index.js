import React, { Fragment, useState, useEffect } from 'react';

import { <PERSON>readcrumb, Card, Input, Tag, DatePicker, Descriptions, Row, Col, Button, Form, message, Table, Modal } from 'antd';

import { getBodyH, isEmpty } from '@/utils/utils';

import WayLineAddForm from './form_add';
import TableCols from './table';

import { HGet2 } from '@/utils/request';
import TaskMediaPanel from './TaskMediaPanel2';
import { Get2 } from '@/services/general';
import { useModel } from 'umi';
import MediaListPanel from './../MediaDataPage/MediaListPanel';
import LastPageButton from '@/components/LastPageButton';


const TaskListPage = () => {


  const [data, setData] = useState();
  const [loading, setLoading] = useState(false);
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 8,
     showSizeChanger:false,
    },
  });

  const [canSee, setCanSee] = useState(false);
  const [canSee2, setCanSee2] = useState(false);
  const [tList, setTList] = useState([]);
  const [index,setIndex]=useState({})
  const [mList,setMList]=useState({})
  const [count,setCount]=useState(0)

  const {setPage,lastPage}=useModel('pageModel');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect( () => {
    // const yy =async()=>{
    //   const xx= await HGet2(`/api/v1/Task/GetAllList`)
    //   setTList(xx)}

    const yy2 =async()=>{
        const xx= await HGet2(`/api/v1/Task/GetCount`)
        //setCount(xx)
        setTableParams({
          ...tableParams,
        pagination: {
          ...tableParams.pagination,
          total: xx,
        },
      });  
    
    }

    const getData2=async ()=>{
      //setLoading(true);
      const  pst= await Get2( '/api/v1/Task/GetList?p1=1&p2=8',{});
      
      setTList(pst);
      //setLoading(false);
   }

    yy2();
   getData2();
}, []);


  const getData=async ()=>{
      setLoading(true);
      const  pst= await Get2( '/api/v1/Task/GetList?p1='+tableParams.pagination?.current+'&p2=8',{});
      
      setTList(pst);
      setLoading(false);
   }

  useEffect(() => {
    getData();
  }, [tableParams.pagination?.current]);



  // const refrush = async () => {
  //   const  pst= await Get2( '/api/v1/Task/GetAllList',{});
  //   if(isEmpty(pst)) return;
  //   setTList(pst);
  // };


  const exr = <div><Button type="primary" onClick={() => getData()}>刷新</Button> </div>


const hxzp =(list)=>{ return <Modal title={null} footer={null} onOk={null} width={1000.0} height={600.0} open={canSee2} onCancel={() => setCanSee2(false)}>
     {isEmpty(list)?<div></div>:TaskMediaPanel(mList)}
</Modal>}

const onRow=(record) => {
  return {
    onClick: async() => {
      setIndex(record)
      const yy=await  HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`)
      console.log('onRow',yy)
      setMList(yy)

    }, // 点击行
    };
}

const hClick=async(record)=>{
  console.log(canSee2)
  const yy=await  HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`)
  console.log('onRow',yy)
  if(isEmpty(yy)){
    message.info('该航线未拍摄照片');
    return;
  }
  setMList(yy)
  setCanSee2(true)
}

const hClick2=async(record)=>{
  console.log(canSee2)
  const yy=await  HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`)
  console.log('onRow',yy)
  setMList(yy)
  setmodal(<MediaListPanel mList={yy} lastPage={lastPage} />);
  //setCanSee2(true)
}


const handleTableChange = (pagination, filters, sorter) => {
  setTableParams({
    pagination,
    filters,
    ...sorter,
  });
};



  return (

    <div style={{ margin: 0, height: 350}}>
      {/* <div><BreadTitle /></div> */}

      <Card title={<LastPageButton title= "飞行记录"></LastPageButton>} bordered={false} extra={exr} >
        <div>
          {isEmpty(tList) ? <div /> : <Table  style={{height:250}} 
            pagination={tableParams.pagination}
            onChange={handleTableChange}
            loading={loading}
            bordered dataSource={tList} columns={TableCols(hClick,getData,setPage)} size='small' />}

        </div>

        {hxzp(mList)}
      </Card>

    

    </div>
  )
};

export default TaskListPage;
