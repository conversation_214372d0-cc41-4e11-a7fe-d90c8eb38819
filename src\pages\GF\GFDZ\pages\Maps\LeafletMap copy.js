import { headHeight } from "../../../utils/utils";
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>eo<PERSON><PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import geojsonData from "@/pages/GT/GTPages/HomePage/Maps/geojsonData";
import MapComponent from "@/pages/GT/components/MapComponent/MapComponent";
// 解决 Leaflet Marker 图标未显示的问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require("leaflet/dist/images/marker-icon-2x.png"),
  iconUrl: require("leaflet/dist/images/marker-icon.png"),
  shadowUrl: require("leaflet/dist/images/marker-shadow.png"),
});
import useConfigStore from "@/stores/configStore";
const LeafletMap = () => {
  let dt_bz2 =
    "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f"; //路网
  let satellite =
    "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
  const position = [30.659835174171207, 104.0633797645569]; // 伦敦的坐标

  let mapRef = useRef();
  const { MapUrl, ZSMapUrl, setMapSelf } = useConfigStore();
  const [selectedLayer, setSelectedLayer] = useState(satellite);
  const [selectedZSLayer, setSelectedZSLayer] = useState("");
  useEffect(() => {
    mapRef.current = L.map("mapDiv", {
      maxZoom: 18,
      minZoom: 3,
      zoomControl: false,
      attributionControl: false,
    });

  }, []);
  useEffect(() => {
    if (mapRef.current) {
      setMapSelf(mapRef.current);
    }
  }, [mapRef]);
  useEffect(() => {
    if (MapUrl != satellite) {
      dt_bz2 = null;
    }

    if (MapUrl) {
      setSelectedLayer(MapUrl);
    }
    if (ZSMapUrl) {
      setSelectedZSLayer(ZSMapUrl);
    }
  }, [MapUrl, ZSMapUrl]);

  let currentPage = localStorage.getItem("currentPage");
  useEffect(() => {
    const handleResize = () => {
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [currentPage]);

  return (
    <div style={{ height: "100vh", width: "100%" }}>
      <MapContainer
        ref={mapRef}
        center={position}
        zoom={13}
        minZoom={6}
        maxZoom={18}
        attributionControl={null}
        zoomControl={false}
        style={{ height: `calc(100vh - 56px)`, width: "100%" }}
        whenCreated={(mapInstance) => {
          setMapSelf(mapInstance);
        }}
      >
        <TileLayer attribution={null} url={selectedLayer} />
        <TileLayer attribution={null} url={dt_bz2} />
        <TileLayer attribution={null} url={selectedZSLayer} />
        <Marker position={position}>
          <Popup>这是一个标记的弹出框。</Popup>
        </Marker>
        <GeoJSON data={geojsonData} />
      </MapContainer>
    </div>
  );
};

export default LeafletMap;
