import React, { Fragment, useState, useEffect } from 'react';

import { <PERSON>readcrumb,Card, Input, Tag, DatePicker,Descriptions, Row, Col, Button, Form ,message, Table, Modal, Select} from 'antd';

import { getBodyH, getDevice, isEmpty } from '@/utils/utils';

import { Get2, Post, Post2, Post3 } from '@/services/general';



import TableCols from './table';

import { useModel } from 'umi';
import { HGet2, HPost2 } from '@/utils/request';
import { ArrowLeftOutlined } from '@ant-design/icons';
import LastPageButton from '@/components/LastPageButton';

import UnLockMap from './UnLockMap';

const { RangePicker } = DatePicker;
const { Description } = Descriptions;
const FormItem = Form.Item;


const UnLockListPage = (props) => {
  let d2=localStorage.getItem('dSN');
    
  if(isEmpty(d2)){
    d2='';
  }

  const [chartData, setChartData] = useState({});
  const [devices, setDevices] = useState([]);
  const { CmdMqttConn,DoCMD,DoCMD2 ,DoCMD3 ,unLock} =useModel('cmdModel');
  const [dSn,setDSN]=useState(d2);
  const dateFormat="YYYY/MM/DD";

  const {setPage,lastPage,setModal,setOpen}=useModel('pageModel')

  

  useEffect(() => {
    const getDevices = async () => {
      const  pst= await Get2( '/api/v1/Device/GetAllList',{});
      setDevices(pst);
    };
   
    getDevices();

   
  },[]);


  useEffect(() => {
    if (!isEmpty(dSn)) { CmdMqttConn(dSn) };
  }, [dSn])

  const QiYong=(ifQY,sID)=>{
    DoCMD(dSn,'unlock_license_switch',{
      "enable": ifQY,
      "license_id": sID
    })
  }

  const UpdateUnLock=()=>{
    DoCMD(dSn,'unlock_license_update',{"file": {}
    })
  }

  const refrush = async () => {
    setChartData([]);
    return;
    const  pst= await HGet2( '/api/v1/UnLock/GetAllList',{});
    if(isEmpty(pst)) return;
    setChartData(pst);
  };

  const fhBtn=<Button icon={<ArrowLeftOutlined />} onClick={lastPage}>返回</Button>

  const showMap = (record) => {
    // setCanSee(true);
   //  setIndex(record);
   // setPage(<Card title='航线信息' extra={<Button onClick={()=>setPage(<UnLockListPage></UnLockListPage>)}>返回</Button>}>{UnLockMap(getBodyH(180),record)}</Card>)
   if(isEmpty(dSn)){
    message.info('先选择机场！');
    return;
   }
   const device=getDevice(dSn);
   //setModal(<Card style={{zIndex:10000}} title={fhBtn}>{UnLockMap(getBodyH(360),record,device)}</Card>)
   //setOpen(true);
   setPage(<Card  title={fhBtn}>{UnLockMap(getBodyH(180),record,device)}</Card>)
  
  };

  const LSWay=async()=>{
     await HPost2('/api/v1/UnLock/FlyTo',{SN:'7CTDLCE00AC2J4',PList:''})
  }


//   const exr= <div><Button type="primary" onClick={()=>setCanSee(true)}>航线上传</Button> <Modal  title={null} footer={null} onOk={null} visible={canSee}  onCancel={()=>setCanSee(false)}>
//             <UnLockAddForm/>
// </Modal></div>

  const exr=<div><span style={{marginRight:8.0}}><Button onClick={()=>{
    DoCMD(dSn,'unlock_license_list',{
      "device_model_domain": 0
    })
  }}>加载飞机解禁文件</Button></span>
  <span style={{marginRight:8.0}}><Button onClick={()=>{

    
    DoCMD(dSn,'unlock_license_list',{
      "device_model_domain": 3
    })
  }}>加载FlySafe解禁文件</Button></span>

<span><Button onClick={UpdateUnLock}>刷新证书</Button></span>


  </div>

  const sDiv=<div><Select></Select> </div>

  const getDeviceSelectButton=()=>{
    const list = []
    let nn = 0;
    if(isEmpty(devices)) return ;
    if(devices.length<=0) return;
    devices.forEach(e => {
        list.push(
        {
            value: e.SN,
            label: e.DName,
        }
    )
        nn++;
    });
  return  <Select
        defaultValue={dSn}
        style={{
            width: 240,
           
        }}

        options={list}
        onChange={(e)=>{setDSN(e);localStorage.setItem('dSN',e);}}
    />
}

  //if(isEmpty(chartData))return <div></div>
  return (
    
    <div style={{margin:0,height:getBodyH(56)}}>
      {/* <div><BreadTitle /></div> */}
    <div style={{padding:12.0,fontWeight:'bold',color:"black"}} className='unlock-title'><LastPageButton title='解禁文件列表'/> </div>

    <Card title={getDeviceSelectButton()} style={{marginTop:24.0}} bordered={false}  extra={exr} >
      <div>
      {isEmpty(unLock)?<div/>:   <Table   pagination={{pageSize:8}}
      bordered  dataSource={unLock['licenses']} columns={TableCols(refrush, QiYong,showMap)} size='small' />}
      {/* <Modal  title={null} footer={null} onOk={null} open={canSee} style={{height:600.0,width:600.0}}  onCancel={()=>setCanSee(false)}>
       <Card title='航线信息'> {UnLockMap(600,index)}</Card>
    </Modal> */}

      </div>
    </Card>
    

    </div>
 )
};

export default UnLockListPage;
