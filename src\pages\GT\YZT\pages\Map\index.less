.map-container {
  width: 100%;
  height: calc(100vh - 56px);
  position: relative;
}

.tree-panel-container {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
}

.tree-toggle-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  color: #333;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  transition: all 0.3s;
  padding: 0;
  text-align: center;

  span {
    margin-top: 4px;
    font-size: 13px;
    line-height: 1.2;
    white-space: nowrap;
  }

  &:hover,
  &:focus {
    background: #f0f7ff;
    border-color: #a0cfff;
    box-shadow: inset 0 0 0 1px #a0cfff, 0 2px 12px rgba(0,0,0,0.15);
  }

  &.expanded {
    background: #f0f7ff;
    border-color: #a0cfff;
    box-shadow: inset 0 0 0 1px #a0cfff, 0 2px 12px rgba(0,0,0,0.15);
  }

  .toggle-icon {
    font-size: 28px;
    transition: transform 0.2s ease-in-out;
    margin-bottom: 2px;
    color: #457379;
  }
}

.tree-panel-yzt {
  width: 300px;
  min-height: 300px;
  max-height: 60vh;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  margin-left: -308px;
  opacity: 0;
  visibility: hidden;

  &.expanded {
    margin-left: 0;
    opacity: 1;
    visibility: visible;
  }

  .fixed-header {
    padding: 8px 16px 0;
    background-color: #fff;
    z-index: 1001;
    flex-shrink: 0;
  }

  .scrollable-content {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px 16px;
      min-height: 0;
      max-height: 40vh;
      
      &::-webkit-scrollbar {
          width: 6px;
      }
      &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
      }
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }
  
  .ant-tree-list {
    color: #333 !important;
  }
}

.view-toggle {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;

  .view-toggle-button {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    position: relative;
    transition: all 0.3s;

    &.active {
      color: #1890ff;
      background: #f2f1f1;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -12px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #1890ff;
      }
    }

    &:hover:not(.active) {
      color: #40a9ff;
    }
  }
}

.map-view {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-control-panel {
  position: absolute;
  right: 20px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 1000;



  .toggle-button-yzt {
    width: 40px;
    height: 40px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    cursor: pointer;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    
    &:hover {
      background: #f0f7ff;
      color: #1890ff;
      border-color: #a0cfff;
    }
  }
}

.basemap-panel {
  position: absolute;
  right: 80px;
  bottom: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  max-height: 250px;
  overflow-y: auto;
  min-width: 100px;

  .no-basemap-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    color: #888;
    text-align: center;
    padding: 15px;

    svg {
      margin-bottom: 5px;
    }
  }

  .basemap-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
  }

  .basemap-item {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    background-color: #fff;
    transition: all 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    &:hover {
      border-color: #91d5ff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    &.selected {
      border-color: #1890ff;
      background-color: #e6f7ff;

      .basemap-label {
        color: #1890ff;
        font-weight: 500;
      }
    }

    .basemap-thumbnail-container {
      position: relative;
      width: 100%;
      margin-bottom: 8px;

      .basemap-thumbnail {
        display: block;
        width: 100%;
        height: 60px;
        object-fit: cover;
        border-radius: 2px;
        border: 1px solid #f0f0f0;
      }

      .basemap-selected-indicator {
        position: absolute;
        top: 4px;
        right: 4px;
        background-color: #1890ff;
        color: #fff;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        box-shadow: 0 0 0 2px #fff;
      }
    }

    .basemap-label {
      font-size: 12px;
      color: #555;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
  }
}

// 底图切换按钮
.basemap-toggle {
  cursor: pointer;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2px solid #ffffff;
  height: 40px;
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 70px;
  right: 20px;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);

  &:hover {
     box-shadow: 0 4px 10px rgba(0,0,0,0.3);
  }

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
}

.layer-list-container {
  background-color: #fff !important;
  color: rgba(0, 0, 0, 0.85) !important;

  h3 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .layer-list {
    .layer-item {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.85) !important;

      .layer-name {
        flex: 1;
        color: rgba(0, 0, 0, 0.85) !important;
      }

      .layer-controls {
        display: flex;
        gap: 8px;

        button {
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          background: #fff;
          color: rgba(0, 0, 0, 0.85) !important;
          cursor: pointer;

          &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
          }

          &.hidden {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .empty-message {
    text-align: center;
    padding: 20px 0;
    color: #999 !important;
  }
}

.map-bottom-controls {
  position: absolute;
  left: 20px !important;
  bottom: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 12px;
}
