
import { useModel } from 'umi';
import 'dayjs/locale/zh-cn';
import { useEffect, useState } from 'react';
import { HGet2 } from '@/utils/request';
import { getBodyH, isEmpty } from '@/utils/utils';
import TopMenu from './TopMenu4';
import IfShowPanel from '@/components/IfShowPanel';

import DevicePage from './DevicePage';

export default function HomePage() {
  const [device,setDevice]=useState({})

  useEffect(() => {
    const StartJCZB = async () => {
        await HGet2('/api/v1/RtmpSource/Start3')
    };
    const getDevice=async(sn)=>{
      const d2=await HGet2('/api/v2/Device/GetBySN?sn='+sn)
      localStorage.setItem('device',JSON.stringify(d2));
      setDevice(d2);
    }
    getDevice('7CTDLCE00AC2J4');
    StartJCZB();
  },[]);


  return (
    <div>     <TopMenu></TopMenu>
   
     <div style={{height:getBodyH(56)}}>
       {isEmpty(device)?<div></div>: <DevicePage device={device} />}
     </div></div>
  );
}
