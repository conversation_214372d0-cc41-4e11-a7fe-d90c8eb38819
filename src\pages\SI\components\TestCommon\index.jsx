import React, { useState } from 'react';
import { ConfigProvider, Tag, Button, Space, Dropdown, message } from 'antd';
import { EyeOutlined, FileSearchOutlined, FileTextOutlined, MoreOutlined, DownloadOutlined } from '@ant-design/icons';
import { DynamicSearchForm, DynamicDataTable } from '../Common';
import { TestCommonFormConfig, TestCommonTableConfig } from './config';
import theme from '../../style/theme';

const TestCommonPage = () => {
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 模拟数据
  const mockData = [
    {
      key: '1',
      taskNumber: 'T001',
      taskName: '巡飞任务1',
      planNumber: 'P001',
      routeName: 'LSXJJH2025004',
      airport: '首都机场',
      configTime: '2024-01-15 14:30:00',
      flyDistance: '120km',
      flyTime: '2h30m',
      checkResult: '正常',
      status: 'completed',
    },
    {
      key: '2',
      taskNumber: 'T002',
      taskName: '巡飞任务2',
      planNumber: 'P002',
      routeName: 'LSXJJH2025005',
      airport: '浦东机场',
      configTime: '2024-01-16 09:15:00',
      flyDistance: '95km',
      flyTime: '2h00m',
      checkResult: '异常',
      status: 'running',
    },
    {
      key: '3',
      taskNumber: 'T003',
      taskName: '巡飞任务3',
      planNumber: 'P003',
      routeName: 'LSXJJH2025006',
      airport: '白云机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '4',
      taskNumber: 'T004',
      taskName: '巡飞任务4',
      planNumber: 'P004',
      routeName: 'LSXJJH2025007',
      airport: '虹桥机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '5',
      taskNumber: 'T005',
      taskName: '巡飞任务5',
      planNumber: 'P005',
      routeName: 'LSXJJH2025008',
      airport: '深圳宝安机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '6',
      taskNumber: 'T006',
      taskName: '巡飞任务6',
      planNumber: 'P006',
      routeName: 'LSXJJH2025009',
      airport: '成都双流机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '7',
      taskNumber: 'T007',
      taskName: '巡飞任务7',
      planNumber: 'P007',
      routeName: 'LSXJJH2025010',
      airport: '广州白云机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '8',
      taskNumber: 'T008',
      taskName: '巡飞任务8',
      planNumber: 'P008',
      routeName: 'LSXJJH2025011',
      airport: '深圳宝安机场',
      configTime: '2024-01-17 16:45:00',
      flyDistance: '150km',
      flyTime: '3h15m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '9',
      taskNumber: 'T009',
      taskName: '巡飞任务9',
      planNumber: 'P009',
      routeName: 'LSXJJH2025012',
      airport: '成都双流机场',
      configTime: '2024-01-18 10:30:00',
      flyDistance: '180km',
      flyTime: '3h45m',
      checkResult: '未检查',
      status: 'pending',
    },
    {
      key: '10',
      taskNumber: 'T0010',
      taskName: '巡飞任务10',
      planNumber: 'P0010',
      routeName: 'LSXJJH2025013',
      airport: '成都双流机场',
      configTime: '2024-01-18 11:00:00',
      flyDistance: '200km',
      flyTime: '4h00m',
      checkResult: '未检查',
      status: 'pending',
    },
    {
      key: '11',
      taskNumber: 'T0011',
      taskName: '巡飞任务11',
      planNumber: 'P0011',
      routeName: 'LSXJJH2025014',
      airport: '成都双流机场',
      configTime: '2024-01-18 13:30:00',
      flyDistance: '165km',
      flyTime: '3h20m',
      checkResult: '正常',
      status: 'completed',
    },
    {
      key: '12',
      taskNumber: 'T0012',
      taskName: '巡飞任务12',
      planNumber: 'P0012',
      routeName: 'LSXJJH2025015',
      airport: '成都双流机场',
      configTime: '2024-01-18 14:15:00',
      flyDistance: '190km',
      flyTime: '3h50m',
      checkResult: '异常',
      status: 'running',
    },
    {
      key: '13',
      taskNumber: 'T0013',
      taskName: '巡飞任务13',
      planNumber: 'P0013',
      routeName: 'LSXJJH2025016',
      airport: '成都双流机场',
      configTime: '2024-01-18 15:45:00',
      flyDistance: '175km',
      flyTime: '3h30m',
      checkResult: '正常',
      status: 'completed',
    },
    {
      key: '14',
      taskNumber: 'T0014',
      taskName: '巡飞任务14',
      planNumber: 'P0014',
      routeName: 'LSXJJH2025017',
      airport: '西安咸阳机场',
      configTime: '2024-01-19 08:30:00',
      flyDistance: '210km',
      flyTime: '4h15m',
      checkResult: '未检查',
      status: 'pending',
    },
    {
      key: '15',
      taskNumber: 'T0015',
      taskName: '巡飞任务15',
      planNumber: 'P0015',
      routeName: 'LSXJJH2025018',
      airport: '西安咸阳机场',
      configTime: '2024-01-19 09:00:00',
      flyDistance: '195km',
      flyTime: '3h55m',
      checkResult: '已完成',
      status: 'completed',
    },
    {
      key: '16',
      taskNumber: 'T0016',
      taskName: '巡飞任务16',
      planNumber: 'P0016',
      routeName: 'LSXJJH2025019',
      airport: '天津滨海机场',
      configTime: '2024-01-19 10:30:00',
      flyDistance: '185km',
      flyTime: '3h40m',
      checkResult: '正常',
      status: 'completed',
    },
    {
      key: '17',
      taskNumber: 'T0017',
      taskName: '巡飞任务17',
      planNumber: 'P0017',
      routeName: 'LSXJJH2025020',
      airport: '重庆江北机场',
      configTime: '2024-01-19 11:15:00',
      flyDistance: '220km',
      flyTime: '4h20m',
      checkResult: '异常',
      status: 'running',
    },
    {
      key: '18',
      taskNumber: 'T0018',
      taskName: '巡飞任务18',
      planNumber: 'P0018',
      routeName: 'LSXJJH2025021',
      airport: '昆明长水机场',
      configTime: '2024-01-19 13:00:00',
      flyDistance: '235km',
      flyTime: '4h30m',
      checkResult: '未检查',
      status: 'pending',
    },
    {
      key: '19',
      taskNumber: 'T0019',
      taskName: '巡飞任务19',
      planNumber: 'P0019',
      routeName: 'LSXJJH2025022',
      airport: '南京禄口机场',
      configTime: '2024-01-19 14:30:00',
      flyDistance: '170km',
      flyTime: '3h25m',
      checkResult: '正常',
      status: 'completed',
    },
    {
      key: '20',
      taskNumber: 'T0020',
      taskName: '巡飞任务20',
      planNumber: 'P0020',
      routeName: 'LSXJJH2025023',
      airport: '杭州萧山机场',
      configTime: '2024-01-19 15:45:00',
      flyDistance: '200km',
      flyTime: '4h00m',
      checkResult: '已完成',
      status: 'completed',
    },
  ];

  React.useEffect(() => {
    setTableData(mockData);
    setPagination(prev => ({ ...prev, total: mockData.length }));
  }, []);

  // 搜索处理
  const handleSearch = values => {
    setLoading(true);
    console.log('搜索参数:', values);

    // 模拟搜索API调用
    setTimeout(() => {
      message.success('搜索完成');
      setLoading(false);
    }, 1000);
  };

  // 重置处理
  const handleReset = () => {
    message.info('表单已重置');
  };

  // 批量导出处理
  const handleBatchExport = selectedKeys => {
    console.log('导出选中项:', selectedKeys);
    message.success(`已导出 ${selectedKeys.length} 条数据`);
  };

  // 分页变化处理
  const handlePageChange = (page, pageSize) => {
    setPagination({
      ...pagination,
      current: page,
      pageSize: pageSize,
    });
    console.log('分页变化:', page, pageSize);
  };

  // 自定义渲染函数
  const customRender = {
    // 状态标签渲染
    statusTag: status => {
      const statusConfig = {
        已完成: { color: 'green' },
        正常: { color: 'green' },
        异常: { color: 'red' },
        未检查: { color: 'orange' },
      };

      const config = statusConfig[status] || { color: 'default' };
      return <Tag color={config.color}>{status}</Tag>;
    },

    // 操作按钮渲染
    actionButtons: (_, record) => {
      const actionItems = [
        {
          key: 'view',
          label: (
            <Space>
              <EyeOutlined />
              查看
            </Space>
          ),
          onClick: () => {
            console.log('查看', record);
            message.info(`查看任务: ${record.taskName}`);
          },
        },
        {
          key: 'check',
          label: (
            <Space>
              <FileSearchOutlined />
              检查
            </Space>
          ),
          onClick: () => {
            console.log('检查', record);
            message.info(`检查任务: ${record.taskName}`);
          },
        },
        {
          key: 'report',
          label: (
            <Space>
              <FileTextOutlined />
              巡检报告
            </Space>
          ),
          onClick: () => {
            console.log('巡检报告', record);
            message.info(`生成巡检报告: ${record.taskName}`);
          },
        },
      ];

      return (
        <Dropdown menu={{ items: actionItems }} trigger={['click']}>
          <Button type="link" icon={<MoreOutlined />}>
            操作
          </Button>
        </Dropdown>
      );
    },
  };

  // 工具栏按钮配置
  const toolbarButtons = [
    {
      key: 'export',
      text: '批量导出',
      type: 'primary',
      icon: <DownloadOutlined />,
      needsSelection: true,
      selectionMessage: '请选择要导出的数据',
      onClick: handleBatchExport,
    },
  ];

  return (
    <div
      style={{
        padding: '24px',
        background: '#0a1e2b',
        maxHeight: '100vh',
        overflow: 'auto',
      }}
    >
      <h1
        style={{
          color: '#00d4aa',
          marginBottom: '24px',
          fontSize: '24px',
          fontWeight: 'bold',
        }}
      >
        测试页面
      </h1>

      {/* 搜索表单 */}
      <DynamicSearchForm formConfig={TestCommonFormConfig} onSearch={handleSearch} onReset={handleReset} loading={loading} cols={4} />

      {/* 数据表格 */}
      <DynamicDataTable
        {...TestCommonTableConfig}
        dataSource={tableData}
        loading={loading}
        showToolbar={true}
        toolbarButtons={toolbarButtons}
        toolbarAlign="space-between"
        showSelectionInfo={true}
        showTotal={true}
        scroll={{ x: 1200, y: 240 }}
        pagination={true}
        onPageChange={handlePageChange}
        customRender={customRender}
        containerStyle={
          {
            // background: 'rgba(18, 40, 50, 0.6)',
            // padding: '16px',
            // borderRadius: '4px',
            // border: '1px solid #1abc9c'
          }
        }
      />
    </div>
  );
};

export default TestCommonPage;
