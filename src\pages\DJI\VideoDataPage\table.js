import { Space, Tag, message,Modal} from 'antd';
import { downloadFile, getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { timeFormat } from '@/utils/helper';
import { getImgUrl } from '@/utils/utils';
import { HGet2 } from '@/utils/request';
import { Post2 } from '@/services/general';
import WayLineEditPage from './form_edit';
import DevicePage from '../DevicePage';



const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const { confirm } = Modal;

const toFly=async(record,setPage)=>{
 // console.log('toFly')
 // const {newPList}=useModel('gpsModel')
  if(isEmpty(record)) return ;
  
 

  localStorage.setItem("wayPoints", record.PointList)
  localStorage.removeItem('gpsPoints')
  
  await HGet2("/api/v1/WayLine/Fly?fID="+record.WanLineId)

  const d1=await  HGet2("/api/v1/Device/GetBySN?sn="+record.SN)
  if(isEmpty(d1)){
    return;
  }
  localStorage.setItem('device',JSON.stringify( d1))
  setPage(<DevicePage device={d1}/>);

}


const deleteWayline=async(record,refrush)=>{

  const xx=await Post2("/api/v1/WayLine/Delete",record);

  console.log('deleteWayline',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除航线',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该航线吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteWayline(record,refrush);
    },
    onCancel() {
     // console.log('Cancel');
    },
  });
};

const  download=(url, title = "", artist = "")=> {
  const eleLink = document.createElement("a"); // 新建A标签
  eleLink.href =  url; // 下载的路径
  eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
  eleLink.style.display = "none";
  document.body.appendChild(eleLink);
  eleLink.click(); // 触发点击事件
  document.body.removeChild(eleLink);
}



const TableCols =(refrush,showMap,setPage)=>{return [
  {
    title: getTableTitle('所属机场'),
    dataIndex: 'SN',
    key: 'SN',
    align:'center',
    render: (record) => (
       getDeviceName(record)
     )
   // width:200,
  },
          {
            title: getTableTitle('航线类型'),
            dataIndex: 'WayLineType',
            key: 'WayLineType',
            align:'center',
           // width:200,
          },
          {
            title: getTableTitle('航线名称'),
            dataIndex: 'WayLineName',
            key: 'WayLineName',
            align:'center',
          //  width:300,
          //  className: 'table-header-cell'
          },
          {
            title: getTableTitle('上传时间'),
            dataIndex: 'CreateTime',
            key: 'CreateTime',
            align:'center',
            render: (record) => (
              timeFormat(record)
             )
          //  width:200,
          },
          {
             title: getTableTitle('上传用户'),
            dataIndex: 'UserName',
            key: 'UserName',
            align:'center',
            render: (e) => (
            
              e.length<2? '管理员':e
            )
         //   width:200,
          },

        {
             title: getTableTitle('航线操作'),
            align:'center',
            render: (record) => (
              <Space size="middle">
                <Tag><a onClick={()=>toFly(record,setPage)}>立即执行</a></Tag>
                <Tag><a onClick={()=>showMap(record)}>航线点位</a></Tag>
                <Tag><a onClick={()=>download(getImgUrl(record.ObjectName))}>下载</a></Tag>
                {/* <Tag><a>编辑</a></Tag> */}
                <Tag><WayLineEditPage refrush={refrush} w1={record}></WayLineEditPage></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除</a></Tag>
              </Space>)
          }];
        }



export default TableCols;
