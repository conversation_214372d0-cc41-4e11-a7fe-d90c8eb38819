import { useState, useEffect } from "react";
import { Card, Select, Row, Col, Button, Table } from "antd";
import { getBodyH, getDeviceName, isEmpty } from "@/utils/utils";
import { getGuid } from "@/utils/helper";
import { Get2 } from "@/services/general";
import CronAddForm from "./form_add";
import TableCols from "./table";
import { useModel } from "umi";
import useConfigStore from "@/stores/configStore";
import LastPageButton from "@/components/LastPageButton";
import ComStyles from "@/pages/common.less";
import WayLineMap from "@/pages/DJI/WayLine/WayLineMap";
const CronListPage = (props) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [wayList, setWayList] = useState([]);
  const [cronList, setCronList] = useState([]);
  const { setModal, setOpen, setPage, lastPage } = useModel("pageModel");
  const [sJC, setSjc] = useState("");
  const [xList, setXList] = useState([]);

  const handleTableChange = (current) => {
    setTableCurrent(current);
  };

  const ifPush = (e) => {
    if (!isEmpty(sJC)) {
      if (e.SN != sJC) {
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    const xx = () => {
      const xL = [];
      cronList.forEach((e) => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
      setXList([...xL]);
    };
    xx();
  }, [sJC]);

  useEffect(() => {
    const getLineData = async () => {
      const pst = await Get2("/api/v1/WayLine/GetAllList", {});
      setWayList(pst);
    };

    const getCronData = async () => {
      let pst = await Get2("/api/v1/CronJob/GetAllList", {});
      if (isEmpty(pst)) pst = [];
      setCronList(pst);
      setXList(pst);
    };
    getLineData();
    getCronData();
  }, []);

  const showMap = (values) => {
    const record = wayList?.find((item) => {
      return item.WanLineId === values.FLightId;
    });

    setPage(
      <Card title={<LastPageButton title="航线点位"/>}>
          <WayLineMap
            h1={getBodyH(180)}
            data={record}
            key={getGuid()}
        />
      </Card>
    );
  };

  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/CronJob/GetAllList", {});
    setCronList(pst);
    setXList(pst);
  };

  const getWaySelect = (wayList, getLabel) => {
    const list = [];
    wayList.forEach((e) => {
      list.push(
        <Select.Option key={e} data={e}>
          {getLabel(e)}
        </Select.Option>
      );
    });
    return list;
  };

  const getExr = () => {
    const dList = [];
    if (!isEmpty(cronList)) {
      cronList.forEach((e) => {
        if (!dList.includes(e.SN)) {
          dList.push(e.SN);
        }
      });
    }

    return (
      <Row>
        <Col style={{ marginLeft: 6.0 }}>
          {" "}
          <Select
            allowClear={true}
            style={{ width: 200 }}
            onClear={() => setSjc("")}
            placeholder={"选择机场"}
            onSelect={(e) => setSjc(e)}
          >
            {getWaySelect(dList, (e) => getDeviceName(e))}
          </Select>
        </Col>

        <Col style={{ marginLeft: 24.0 }}>
          {" "}
          <Button
            className={ComStyles.addButton}
            type="primary"
            onClick={() => {
              setModal(
                <CronAddForm
                  wayList={wayList}
                  refrush={refrush}
                  cronList={cronList}
                />
              );
              setOpen(true);
            }}
          >
            新建任务
          </Button>
        </Col>
      </Row>
    );
  };

  return (
    <div style={{ margin: 0, height: getBodyH(56) }}>
      <Card
        title={<LastPageButton title="定时任务" />}
        bordered={false}
        extra={getExr()}
      >
        <div>
          {isEmpty(cronList) ? (
            <div />
          ) : (
            <Table
               pagination={{
                showSizeChanger: true, 
                showQuickJumper: true,
                current: tableCurrent,
                onChange: handleTableChange,
              }}
              bordered
              dataSource={xList}
              columns={TableCols(refrush, showMap)}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default CronListPage;
