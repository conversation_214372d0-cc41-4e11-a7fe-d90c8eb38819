import React, { useEffect } from "react";
import {
  DownloadOutlined,
  LeftOutlined,
  RightOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SwapOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from "@ant-design/icons";
import { Image, Space, message } from "antd";
import { getBodyH } from "@/utils/utils";
import { getDeviceName,getImgUrl } from "@/utils/utils";

const ImagePreview = ({ mList, selectIndex, updateSelectIndex }) => {
  const [current, setCurrent] = React.useState(selectIndex);
  const imageList = mList?.map((item) => getImgUrl(item.ObjectName)) || [];

  useEffect(() => {
    updateSelectIndex(current);
  }, [current]);

  const onDownload = () => {
    const url = imageList[selectIndex]; // 使用当前图片的 URL
    if (!url) {
      message.error("下载失败，未找到图片 URL");
      return;
    }

    let everything = mList[selectIndex];
    let suffix = everything.FileName.slice(
      everything.FileName.indexOf("."),
      everything.FileName.length
    );
    let filename =
      getDeviceName(everything.SN) +
      "-" +
      everything.WayLineNM +
      "-" +
      "航点" +
      everything.HangDianIndex +
      suffix;

    fetch(url)
      .then((response) => {
        if (!response.ok) {
          throw new Error("网络响应异常");
        }
        return response.blob();
      })
      .then((blob) => {
        const blobUrl = URL.createObjectURL(new Blob([blob]));
        const link = document.createElement("a");
        link.href = blobUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(blobUrl);
        link.remove();
      })
      .catch((error) => {
        message.error(`下载失败: ${error.message}`);
      });
  };

  return (
    <Image.PreviewGroup
      preview={{
        toolbarRender: (
          _,
          {
            transform: { scale },
            actions: {
              onActive,
              onFlipY,
              onFlipX,
              onRotateLeft,
              onRotateRight,
              onZoomOut,
              onZoomIn,
              onReset,
            },
          }
        ) => (
          <Space size={12} className="toolbar-wrapper">
            <LeftOutlined onClick={() => onActive?.(-1)} />
            <RightOutlined onClick={() => onActive?.(1)} />
            <DownloadOutlined onClick={onDownload} />
            <SwapOutlined rotate={90} onClick={onFlipY} />
            <SwapOutlined onClick={onFlipX} />
            <RotateLeftOutlined onClick={onRotateLeft} />
            <RotateRightOutlined onClick={onRotateRight} />
            <ZoomOutOutlined disabled={scale === 1} onClick={onZoomOut} />
            <ZoomInOutlined disabled={scale === 50} onClick={onZoomIn} />
            <UndoOutlined onClick={onReset} />
          </Space>
        ),
        onChange: (index) => {
          setCurrent(index);
        },
      }}
      items={imageList}
    >
      <Image
        src={getImgUrl(mList[selectIndex]?.ObjectName)}
        width={"auto"}
        height={"auto"}
        style={{
          margin: "auto",
          maxWidth: 830,
          maxHeight: getBodyH(190),
          userSelect: "none",
        }}
      />
    </Image.PreviewGroup>
  );
};

export default ImagePreview;
