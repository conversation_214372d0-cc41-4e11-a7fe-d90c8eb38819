import React, { useState, useEffect } from 'react';
import { 
  initializeOpenCV, 
  isOpenCVReady, 
  getPoints ,
  getPoint
} from './no_pic.js';

function CoordinateConverter() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  
  // 初始化OpenCV
  useEffect(() => {
    const drcData  ={
            "attitude_head": 0,
            "gimbal_pitch": -90,
            "gimbal_roll": 0,
            "gimbal_yaw": 0,
            "height": 600,
            "latitude": 30.80355559,
            "longitude": 103.88466954,
            "speed_x": 10,
            "speed_y": 10,
            "speed_z": 10
        };
   const xx= getPoint(drcData, 30.80355559, 103.88466954,515);
   console.log('xx:', xx);
   
  }, []);
  
  const testData = {
    "GPitch": -90,
    "FYaw": 0,
    "FRoll": 0,
    "Lat": 30.80355559,
    "Lon": 103.88466954,
    "H": 600,
    "point": [
      [103.88466954, 30.80355559, 515],
      [103.88429403, 30.80381370, 515],
      [103.88406336, 30.80391971, 515],
      [103.88384879, 30.80404877, 515],
      [103.88345182, 30.80427462, 515],
      [103.88296366, 30.80458343, 515]
    ]
  };
  
  const handleCalculate = () => {
   
    
    try {
      setError(null);
      const startTime = performance.now();
      const calculationResult = getPoints(testData);
      const endTime = performance.now();
      
      setResult({
        points: calculationResult,
        time: (endTime - startTime).toFixed(2)
      });
    } catch(e) {
      console.error('计算错误:', e);
      setError('计算错误: ' + e.message);
    }
  };
  
  return (
    <div>
      <h1>坐标转换</h1>
      
      {loading ? (
        <p>正在加载OpenCV.js，请稍候...</p>
      ) : error ? (
        <p style={{color: 'red'}}>{error}</p>
      ) : (
        <>
          <button onClick={handleCalculate}>计算坐标</button>
          
          {result && (
            <div>
              <h2>计算结果:</h2>
              <pre>{JSON.stringify(result.points, null, 2)}</pre>
              <p>计算用时: {result.time} ms</p>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default CoordinateConverter;