import { isEmpty } from "@/utils/utils";

const GrayPanel=({width,height,top,left,radius, child,ifShow,ifSelect})=>{
    if(isEmpty(ifSelect)){
        ifSelect='none';
    }

    if (ifShow) {
        return (
          <div
            style={{
              position: 'absolute',
              width,left,top,height,
              zIndex: 1000,
              display: 'block',
              userSelect: ifSelect,
              background:'rgba(65, 65, 65, 0.8)',
              borderRadius:radius,
             // background:'white'
            }}
          >
            {child}
          </div>
        );
      }
      return (
        <div>
        </div>
      );
}

export default GrayPanel;