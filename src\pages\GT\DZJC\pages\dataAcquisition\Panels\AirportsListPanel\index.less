@width: 24px;
@height: 24px;


.airports-list-panel {
    color: #fff;
    width: 360px;
    font-size: 14px;
    max-height: 91vh;
    overflow: auto;
    cursor: pointer;
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 100%;
        background-color: rgba(118, 220, 251, 0.886);
        background: linear-gradient(to bottom, rgba(118, 220, 251, 0.301), rgb(118, 220, 251));
    }

    .airports-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;
        margin: 10px 0;
        background: rgba(#4c8086, 0.6);
        border-radius: 4px;
        width: 100%;
        transition: all .3s ease;
        overflow: hidden;
        white-space: nowrap;
        box-shadow: 0 1px 3px rgb(0, 0, 0);

        .airports-name {
            max-width: 100px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .airports-self-icon {
            width: @width;
            height: @height;
            background: url("@/pages/GT/DZJC/assets/image/airportItem.svg") center center no-repeat;
            margin-right: 10px;
        }

        .airports-location-icon {
            width: @width;
            height: @height;
            // background: url("@/pages/GT/DZJC/assets/image/location.svg") center center no-repeat;
            background: url("@/pages/GT/DZJC/assets/image/fly.svg") center center no-repeat;


        }

        .airports-camera-icon {
            width: @width;
            height: @height;
            background: url("@/pages/GT/DZJC/assets/image/camera.svg") center center no-repeat;
            background-size: 100% auto;
            background-repeat: no-repeat;
            background-position: 0 100%;
            width: 24px;
            height: 24px;
            justify-self: center;
            cursor: pointer;
            grid-column: 6;
        }

    }

    .airports-item-detail{
        transition: all .6s ease;
    }

    .airports-item-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

    }

    .airports-item-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 15px;

        .airports-status {
            font-size: 13px;
            padding: 0px 4px;
            font-family: "kaiti";
            
        }
    }

    .airports-null {
        opacity: 0;
    }

}



.routesList {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0;
    max-height: 300px; // 子项的height
    overflow-y: auto;
    background: rgba(#4c8086, 0.7);

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 100%;
        background-color: rgba(118, 220, 251, 0.886);
        background: linear-gradient(to bottom, rgba(118, 220, 251, 0.301), rgb(118, 220, 251));
    }
}

.routeItem {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    transition: background 0.3s;
    position: relative;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    gap: 10px;
}

.routeItem:hover {
    background: rgba(0, 0, 0, 0.2);
}

.WayLineName_con {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
}

.WayLineName_icon {
    width: @width;
    height: @height;
    background: url("@/pages/GT/DZJC/assets/image/flyDispatch.svg") center center no-repeat;
    display: block;
}

.routeDetail {
    font-size: 13px;
    color: #d8d8d8;
}

.operateBtns {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
}

.routeInfo {
    width: 100%;
}

.routeHeader {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 8px;
}

.routeIcon {
    background-image: url('@/pages/GT/DZJC/assets/image/airportItem.svg');
    background-size: 80% auto;
    background-repeat: no-repeat;
    background-position: center;
    width: 20px;
    height: 20px;
    filter: hue-rotate(180deg);
    margin-right: 8px;
    display: inline-block;
}

.routeName {
    color: white;
    font-size: 14px;
    font-weight: bold;
    flex: 1;
}

.routeBtns {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    gap: 8px;
}

.viewRouteBtn {
    background: #1890ff;
    border-color: #1890ff;
}

.executeBtn {
    background: #52c41a;
    border-color: #52c41a;
}

.routeDetails {
    margin-left: 24px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.routeDescRow,
.routeTimeRow,
.routeStatusRow {
    display: flex;
    align-items: flex-start;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.routeLabel {
    width: 70px;
    color: rgba(255, 255, 255, 0.6);
}

.routeDesc,
.routeTime,
.routeStatusValue {
    flex: 1;
}

.noRoutes {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
}

.airportStats {
    display: flex;
    justify-content: space-around;
    text-align: center;
    padding: 10px 0;
    height: 16vh;

    .airportStatBox {
        background-image: url('@/pages/GT/DZJC/assets/image/circle-bottom.png');
        background-size: 100% auto;
        background-repeat: no-repeat;
        background-position: 0 100%;
        padding: 18px 22px;
        margin-bottom: 10px;

        .statFigure {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin: 5px 0;
        }

        .statSubLabel {
            font-size: 12px;
            color: #a0a0a0;
        }
    }
}