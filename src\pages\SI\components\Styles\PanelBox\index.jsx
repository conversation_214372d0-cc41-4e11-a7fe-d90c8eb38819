import React from 'react';
import styles from './index.less';

/**
 * 背景板组件
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 子元素内容
 * @param {string} props.className - 自定义样式类名
 * @param {Object} props.style - 自定义行内样式
 * @param {number} props.width - 组件宽度
 * @param {number} props.height - 组件高度
 */
const PanelBox = ({ 
  children, 
  className = '', 
  style = {}, 
  width, 
  height 
}) => {
  const containerStyle = {
    ...style,
    ...(width && { width: `${width}px` }),
    ...(height && { height: `${height}px` })
  };

  return (
    <div 
      className={`${styles.panelBox} ${className}`} 
      style={containerStyle}
    >
      {/* 背景容器 */}
      {/* <div className={styles.panelBackground}></div> */}
      
      {/* 左下角装饰图标 */}
      <div className={styles.panelCornerLeft}></div>
      
      {/* 右下角装饰图标 */}
      <div className={styles.panelCornerRight}></div>
      
      {/* 内容区域 */}
      <div className={styles.panelContent}>
        {children}
      </div>
    </div>
  );
};

export default PanelBox;
