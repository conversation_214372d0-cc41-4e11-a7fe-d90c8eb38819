import styles from "./Login.less";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Form, Input, Button, message } from "antd";
import { useEffect, useState } from "react";
import { history, useSearchParams } from "umi";
import { HGet2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
export default function Login() {
  const [load, setIsload] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  let system = searchParams.get("system") || "gt";

  const submitData = (data) => {
    if (isEmpty(data)) return;
    if (!isEmpty(data.err)) {
      message.info(data.err);
      return;
    }

    localStorage.setItem("token", data.token);
    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("orgId", data.user.OrgCode);
    localStorage.setItem("orgName", data.user.OrgName);
    localStorage.setItem("system", system);

    if (window.location.href.indexOf("token") > 0) {
      let url = new URL(window.location.href);
      url.searchParams.delete("token");
      url.searchParams.delete("json");
      url.searchParams.delete("system");
      window.location.href = url.href.split("#")[0] + "#/HOME/login?system=";
    } else {
      history.push("/HOME/index");
    }
    console.log(data);
  };
  const handleSubmit = async (e) => {
    const data = await HGet2(
      "/api/v2/User/Login?userName=" + e.userName + "&password=" + e.password
    );
    submitData(data);
  };

  useEffect(() => {
    const getByToken = async () => {
      const params = new URLSearchParams(window.location.search);
      const p1 = params.get("api_token");
      if (isEmpty(p1)) {
        setIsload(true);
        return;
      }
      // const data=await Post3('/api/v2/User/Login2',p1);
      const data = await HGet2("/api/v2/User/LoginByTokenDFM?token=" + p1);
      submitData(data);
      setIsload(true);
    };

    getByToken();
    localStorage.removeItem("PageIndexTitle");
  }, []);
  return (
    <div>
      <div className={styles.login}>
        <div className={styles.login_back}></div>
        <div className={styles.login_app}>
          <div className={styles.login_app_centerBox}>
            <div className={styles.header}>云端智行-无人机行业应用平台</div>
            <div className="body">
              <Form
                name="basic-login"
                initialValues={{ remember: true, agreement: true }}
                onFinish={handleSubmit}
                autoComplete="off"
              >
                <Form.Item
                  name="userName"
                  rules={[{ required: true, message: "请输入用户名" }]}
                >
                  <Input
                    size="large"
                    prefix={
                      <UserOutlined
                        className="site-form-item-icon"
                        style={{ color: "#04a783" }}
                      />
                    }
                    placeholder="账户"
                    styles={{ background: "red" }}
                  />
                </Form.Item>
                <Form.Item
                  name="password"
                  rules={[{ required: true, message: "请输入密码" }]}
                >
                  <Input.Password
                    size="large"
                    prefix={
                      <LockOutlined
                        className="site-form-item-icon"
                        style={{ color: "#04a783" }}
                      />
                    }
                    placeholder="密码"
                  />
                </Form.Item>
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    block
                    size="large"
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = "#00a771";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = "#1abc9c";
                    }}
                    style={{ background: "#1abc9c" }}
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </div>
            <div className={styles.login_foot}>
              <div>北斗众联</div>
              <div>Uni BeiDou</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
