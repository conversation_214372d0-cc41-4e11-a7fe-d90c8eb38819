import { getGuid } from "../helper";


import DefectManagement from "@/pages/GF/GFDZ/pages/DefectManagement";
import RealTimeMonitoring from "@/pages/GF/GFDZ/pages/RealTimeMonitoring";
import OperationManagement from "@/pages/GF/GFDZ/pages/OperationManagement";

export const GFRouter = [
  {
    title: "缺陷管理",
    key: "/gf/qxgl",
    children: <DefectManagement key={getGuid()} />,
  },
  {
    title: "实时监测",
    key: "/gf/ssjc",
    children: <RealTimeMonitoring key={getGuid()} />,
  },
  {
    title: "运维管理",
    key: "/gf/ywgl",
    children: <OperationManagement key={getGuid()} />,
  },
  {
    title: "项目管理",
    key: "/gf/GFDZ/ywgl/xgml",
    children: <OperationManagement key={getGuid()} />,
  },
  {
    title: "子阵管理",
    key: "/gf/GFDZ/ywgl/zzgl",
    children: <OperationManagement key={getGuid()} />,
  },
  {
    title: "组串管理",
    key: "/gf/GFDZ/ywgl/zcgl",
    children: <OperationManagement key={getGuid()} />,
  },
];