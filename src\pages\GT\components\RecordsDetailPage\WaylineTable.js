import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty,getDeviceName } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const WaylineTable = (setCurrentPage) => {

  return [
    {
      title: getTableTitle("所属机场"),
      dataIndex: "SN",
      key: "SN",
      align: "center",
      render: (record) => getDeviceName(record),
    },
    {
      title: getTableTitle("航线类型"),
      dataIndex: "WayLineType",
      key: "WayLineType",
      align: "center",
    },
    {
      title: getTableTitle("航线名称"),
      dataIndex: "WayLineName",
      key: "WayLineName",
      align: "center",
    },
    {
      title: getTableTitle("上传时间"),
      dataIndex: "CreateTime",
      key: "CreateTime",
      align: "center",
      render: (record) => timeFormat(record),
    },
    {
      title: getTableTitle("上传用户"),
      dataIndex: "UserName",
      key: "UserName",
      align: "center",
      render: (e) => (e.length < 2 ? "管理员" : e),
    },

    {
      title: getTableTitle("操作"),
      width: 100,
      align: "center",
      render: (record) => (
        <Space size="middle">
          <MyButton
            style={{ padding: "2px 8px",color:'#17AF91',background:'none' }}
            onClick={() => {
              console.log('详情');
            }}
          >
            详情  
          </MyButton>
        </Space>
      ),
    },
  ];
};

export default WaylineTable;
