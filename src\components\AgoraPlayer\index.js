import React, { useEffect, useState } from "react";
import {
  AgoraVideoPlayer,
  createClient,
} from "agora-rtc-react";
import { isEmpty } from "@/utils/utils";
import { HGet2 } from "@/utils/request";
import { useModel } from "umi";
const config = {
  mode: "rtc", codec: "vp8",
};
const Videos = (props) => {
  const { h1, w1, sn3 } = props
  const url = '/api/v1/Live/AgoraToken?sn='+sn3;
  const useClient = createClient(config);
  const [v1, setV1] = useState()
  const client = useClient();
  const { setIfJCRtmp, setIfFJRtmp } = useModel('rtmpModel')
  const device = JSON.parse(localStorage.getItem('device'))
  useEffect(() => {
    const xx = async () => {
      if (sn3 == device.SN) {
        setIfJCRtmp(false)
      }
      if (sn3 == device.SN2) {
        setIfFJRtmp(false)
      }
      const yy = await HGet2(url)
      if (isEmpty(yy)) return;
      const yy2 = await client.join(yy.appid, yy.channel, yy.token, yy.uid);
      let ifOpen = false;
      client.on("user-published", async (user, mediaType) => {
        await client.subscribe(user, mediaType);
        if (mediaType === "video") {
          const remoteVideoTrack = user.videoTrack;
          setV1(remoteVideoTrack)
          ifOpen = true;
          if (yy.channel == device.SN) {
            setIfJCRtmp(true)
          }
          if (yy.channel == device.SN2) {
            setIfFJRtmp(true)
          }
        }
      });
    }
    xx();

  }, [url]);

  return (
    <div>
      <div id="videos" style={{background:'black'}}>
        {isEmpty(v1) ? <div /> : <AgoraVideoPlayer style={{ height: h1, width: w1,background:'black' }} videoTrack={v1} key={url} />}
      </div>
    </div>
  );
};

export default Videos;
