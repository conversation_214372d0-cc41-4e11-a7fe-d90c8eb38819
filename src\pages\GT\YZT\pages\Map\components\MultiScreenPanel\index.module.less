// 多屏对比配置面板样式
.multiScreenPanel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1003; // 比退出按钮更高的层级
  max-width: 320px;
  min-width: 280px;

  .configCard {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8e8e8;

    :global(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;

      :global(.ant-card-head-title) {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .closeButton {
        font-size: 18px;
        font-weight: bold;
        color: #999;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #ff4d4f;
          background: rgba(255, 77, 79, 0.1);
          border-radius: 4px;
        }
      }
    }

    :global(.ant-card-body) {
      padding: 16px;
    }
  }

  .configSection {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 12px 0;
      font-size: 13px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
    }
  }

  // 布局选择样式
  .layoutOptions {
    width: 100%;

    :global(.ant-radio-button-wrapper) {
      height: auto;
      padding: 0;
      border-radius: 6px;
      margin-bottom: 8px;
      margin-right: 8px;
      border: 1px solid #d9d9d9;
      background: #fff;

      &:hover {
        border-color: #40a9ff;
      }

      &:global(.ant-radio-button-wrapper-checked) {
        border-color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
        color: #1890ff;

        .layoutPreview {
          .layoutIcon {
            color: #1890ff;
          }
        }
      }

      .layoutOption {
        display: block;
        width: 100%;
      }

      .layoutPreview {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 8px;
        text-align: center;

        .layoutIcon {
          font-size: 16px;
          line-height: 1.2;
          margin-bottom: 6px;
          color: #666;
          white-space: pre-line;
          font-family: monospace;
        }

        .layoutLabel {
          font-size: 11px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  // 视图同步样式
  .syncOptions {
    .syncOption {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      margin-bottom: 8px;

      input[type="checkbox"] {
        margin-right: 8px;
        margin-top: 2px;
      }

      .syncLabel {
        font-size: 13px;
        color: #333;
        font-weight: 500;
      }
    }

    .syncDescription {
      font-size: 12px;
      color: #999;
      margin: 0;
      padding-left: 20px;
      line-height: 1.4;
    }
  }

  // 操作按钮样式
  .configActions {
    text-align: center;

    :global(.ant-btn) {
      font-size: 12px;
      height: 28px;
      padding: 0 12px;
    }
  }

  // 分割线样式
  :global(.ant-divider) {
    margin: 12px 0;
    border-color: #f0f0f0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .multiScreenPanel {
    top: 15px;
    left: 15px;
    max-width: 280px;
    min-width: 240px;

    .configCard {
      :global(.ant-card-head) {
        padding: 10px 12px;
      }

      :global(.ant-card-body) {
        padding: 12px;
      }
    }

    .configSection {
      margin-bottom: 12px;

      h4 {
        font-size: 12px;
        margin-bottom: 8px;
      }
    }

    .layoutOptions {
      :global(.ant-radio-button-wrapper) {
        .layoutPreview {
          padding: 8px 6px;

          .layoutIcon {
            font-size: 14px;
            margin-bottom: 4px;
          }

          .layoutLabel {
            font-size: 10px;
          }
        }
      }
    }

    .syncOptions {
      .syncOption {
        .syncLabel {
          font-size: 12px;
        }
      }

      .syncDescription {
        font-size: 11px;
      }
    }
  }
}
