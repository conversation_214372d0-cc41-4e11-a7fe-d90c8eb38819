import React, { useEffect, useState, useRef } from 'react';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import styles from './MyHead2.less';
import { useModel, history, useLocation } from 'umi';
import useConfigStore from '@/stores/configStore';
import {ifAdmin} from "@/utils/utils"
const Nav = ({ headList }) => {
  const headerRef = useRef(null);
  const { systemInfos, setHeaderHeight } = useConfigStore();
  const location = useLocation();
  const { title: systemTitle = '智巡—无人机智能云控中台' } = systemInfos || {};
  const [currentPageKey, setCurrentPageKey] = useState(null);

  useEffect(() => {
    if (headerRef.current) {
      //获得并传送导航栏高度
      const observer = new ResizeObserver(entries => {
        for (let entry of entries) {
          setHeaderHeight(headerRef.current?.getBoundingClientRect()?.height);
        }
      });
      observer.observe(headerRef.current);
    }
  }, [headerRef.current]);

  useEffect(() => {
    const key = location.pathname.split('/').pop();
    function activeCurrent() {
      //依据地址栏的路由激活导航动态
      setCurrentPageKey(key);
    }
    activeCurrent();
  }, [location]);

  const leftList = [
    { label: '低空监测', key: 'index' },
    { label: '飞控中心', key: 'controlCenter' },
  ];

  const rightList = [
    { label: '智能处理', key: 'ai' },
    { label: '行业应用', key: 'patrol' },
  ];

  const handlePageChange = page => {
    history.push(`/SI/${page}`);
    if(page == 'ai'){
      localStorage.setItem('currentPage','');
    }
  };

  function goSystemManger() {
    localStorage.setItem('currentPage', "运维管理");
    const fullPath = `/#/SI/system`;
    window.open(fullPath, '_blank');
    // 防止继续执行原来的导航逻辑
    return false;
  }

  function exit() {
    localStorage.clear();
    history.replace('/SI/login', {});
    return;
  }


  const renderLeft = list => {
    return list.map((item, index) => {
      return (
        <div
          className={`${styles.header_left_item}
          ${currentPageKey === item.key ? styles.btn_left_back_active : styles.btn_left_back}
        `}
          onClick={() => handlePageChange(item.key)}
          key={item.key}
        >
          <span>{item.label}</span>
        </div>
      );
    });
  };
  const renderRight = list => {
    return list.map((item, index) => {
      return (
        <div
          className={`${styles.header_right_item} 
          ${currentPageKey === item.key ? styles.btn_right_back_active : styles.btn_right_back}
        `}
          onClick={() => handlePageChange(item.key)}
          key={item.key}
        >
          <span>{item.label}</span>
        </div>
      );
    });
  };

  return (
    <div ref={headerRef} className={styles.header_back}>
      <div id={styles.MyHead}>
        <div className={styles.header_left}>{renderLeft(leftList)}</div>
        <div className={styles.header_center}>
          <div className={styles.header_center_title}>{systemTitle}</div>
        </div>
        <div className={styles.header_right}>{renderRight(rightList)}</div>
        <div className={styles.header_other}>
          {ifAdmin() && <div className={styles.header_other_setting} onClick={goSystemManger} title='运维管理'></div>}
          <div className={styles.header_other_user}>
            <div className={`${styles.header_other_userBox}`}>
              <span>{ifAdmin().OrgName}</span>
              <span onClick={exit}>退出</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Nav;
