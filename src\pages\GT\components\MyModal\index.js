import styles from "./MyModal.less";
export default function MyModal({
  title,
  status,
  content,
  closeText,
  isOpen,
  isCenterBtn,
  onClose,
  style,
}) {
  console.log(isOpen);

  if (!isOpen) return null;
  return (
    <>
      <div className={styles.myModal} style={{ ...style }}>
        <div className={styles.myModal_head}>
        <div className={styles.myModal_title}>{title ? title : "标题"}</div>
        {status && <div className={styles.myModal_status}>{status}</div>}
        </div>
        <div className={styles.myModal_content}>{content}</div>
        <div className={styles.myModal_foot}>
          <div className={styles.myModal_cancel}>详情</div>
          {isCenterBtn && (
            <div className={styles.myModal_cancel} onClick={onClose}>
              {isCenterBtn}
            </div>
          )}
          <div className={styles.myModal_close} onClick={onClose}>
            {closeText ? closeText : "关闭"}
          </div>
        </div>
      </div>
    </>
  );
}
