import React, { useState, useRef, useEffect, useCallback } from "react";
import { useModel } from "umi";
import configStore from "@/stores/configStore";

const TemperatureArea = () => {
  const {setStopMove} = configStore();
  const { DoCMD, temperatureMode } = useModel("cmdModel");
  const { cameraOsdData } = useModel("drcModel");
  let device = JSON.parse(localStorage.getItem("device") || "{}");
  const squareRef = useRef(null);

  // 方块的位置和尺寸
  const [areaPosition, setAreaPosition] = useState({ x: 100, y: 100 }); // 方块左上角在父容器中的位置
  const [areaSize, setAreaSize] = useState({ width: 400, height: 200 }); // 方块的显示尺寸
  const [isDragging, setIsDragging] = useState(false);
  const offset = useRef({ x: 0, y: 0 }); // 鼠标点击位置与方块左上角的偏移


  // 温度点显示状态
  const [tempMaxDisplay, setTempMaxDisplay] = useState(null); // 最高温度文本
  const [tempMinDisplay, setTempMinDisplay] = useState(null); // 最低温度文本
  const [tempMaxPointPos, setTempMaxPointPos] = useState({ x: 0, y: 0 }); // 最高温度点位置
  const [tempMinPointPos, setTempMinPointPos] = useState({ x: 0, y: 0 }); // 最低温度点位置

  // ------------------------- 区域拖动和设置命令 -------------------------

  const handleMouseDown = useCallback((e) => {
    if (temperatureMode !== 2 || !squareRef.current) return;
    setIsDragging(true);
    // 鼠标点击位置与方块左上角的偏移
    offset.current = {
      x: e.clientX - squareRef.current.getBoundingClientRect().left,
      y: e.clientY - squareRef.current.getBoundingClientRect().top,
    };
    e.preventDefault(); 
  }, [temperatureMode]);

  const handleMouseUp = useCallback((e) => {
    if (!isDragging) return; 
    setIsDragging(false);

    if (temperatureMode !== 2 || !squareRef.current) return;

    const parentContainer = squareRef.current.parentElement;
    if (!parentContainer) {
        return;
    }
    const parentRect = parentContainer.getBoundingClientRect();

    // 当前区域的左上角相对于父容器的比例坐标 (0-1)
    const currentAreaRect = squareRef.current.getBoundingClientRect();
    const relativeX = (currentAreaRect.left - parentRect.left) / parentRect.width;
    const relativeY = (currentAreaRect.top - parentRect.top) / parentRect.height;
    const relativeWidth = currentAreaRect.width / parentRect.width;
    const relativeHeight = currentAreaRect.height / parentRect.height;


    const data = {
      payload_index: device.Camera2,
      x: Math.max(0, Math.min(1, relativeX)), // 上云api规则在0-1之间
      y: Math.max(0, Math.min(1, relativeY)),
      width: Math.max(0, Math.min(1, relativeWidth)),
      height: Math.max(0, Math.min(1, relativeHeight)),
    };
    DoCMD(device.SN, "ir_metering_area_set", data);

  }, [isDragging, temperatureMode, device, DoCMD]);


  const handleMouseMove = useCallback((e) => {
    setStopMove(false);//我拖动测温小框时，不会拖动摄像头画面

    if (!isDragging || temperatureMode !== 2 || !squareRef.current) return;
    // 新的方块位置
    let newX = e.clientX - offset.current.x;
    let newY = e.clientY - offset.current.y;

    // 边界拖动限制
    const parentContainer = squareRef.current.parentElement;
    if (parentContainer) {
      const parentRect = parentContainer.getBoundingClientRect();// 父容器的尺寸
      const currentSquareRect = squareRef.current.getBoundingClientRect(); // 方块当前尺寸
      
      newX = Math.max(parentRect.left, Math.min(newX, parentRect.right - currentSquareRect.width));
      newY = Math.max(parentRect.top, Math.min(newY, parentRect.bottom - currentSquareRect.height));
    }
    
    // position 是相对于父容器的局部坐标，减去父容器的偏移
    const finalX = newX - (parentContainer?.getBoundingClientRect().left || 0);
    const finalY = newY - (parentContainer?.getBoundingClientRect().top || 0);

    setAreaPosition({ x: finalX, y: finalY });

    setStopMove(true);

  }, [isDragging, temperatureMode]);

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);


  // ------------------------- MQTT 数据更新温度显示 -------------------------

  useEffect(() => {
    const irArea = cameraOsdData?.ir_thermometry?.ir_metering_area;
    if (irArea && squareRef.current) {
      const currentSquareRect = squareRef.current.getBoundingClientRect();
      const squareWidth = currentSquareRect.width;
      const squareHeight = currentSquareRect.height;

      // 最高温度点
      if (irArea.max_temperature_point) {
        const { x: maxXRatio, y: maxYRatio, temperature: maxTemp } = irArea.max_temperature_point;
        // x, y 是相对于区域左上角的比例坐标 (0-1)
        const displayXMax = maxXRatio * squareWidth;
        const displayYMax = maxYRatio * squareHeight;

        setTempMaxPointPos({ x: displayXMax, y: displayYMax });
        setTempMaxDisplay(typeof maxTemp === 'number' ? maxTemp.toFixed(2) : null);
      } else {
        setTempMaxDisplay(null);
      }

      // 最低温度点
      if (irArea.min_temperature_point) {
        const { x: minXRatio, y: minYRatio, temperature: minTemp } = irArea.min_temperature_point;
        // x, y 是相对于区域左上角的比例坐标 (0-1)
        const displayXMin = minXRatio * squareWidth;
        const displayYMin = minYRatio * squareHeight;

        setTempMinPointPos({ x: displayXMin, y: displayYMin });
        setTempMinDisplay(typeof minTemp === 'number' ? minTemp.toFixed(2) : null);
      } else {
        setTempMinDisplay(null);
      }
    } else {
      // 如果没有区域数据或方块未渲染，清空
      setTempMaxDisplay(null);
      setTempMinDisplay(null);
    }
  }, [cameraOsdData]); 

  return (
    <div
      ref={squareRef}
      className="temperature-area-box"
      style={{
        position: "absolute",
        zIndex: 3,
        width: `${areaSize.width}px`,
        height: `${areaSize.height}px`,
        border: `2px solid #f4b804`, 
        borderRadius: "5px",
        left: `${areaPosition.x}px`,
        top: `${areaPosition.y}px`,
        cursor: isDragging ? "grabbing" : "grab",
        boxSizing: 'border-box',
        textShadow: '0px 0px 10px #ffffff',
      }}
      onMouseDown={handleMouseDown}
    >
      {/* 最高温度点 */}
      {tempMaxDisplay && (
        <div
          className="temperature-text"
          style={{
            position: "absolute",
            left: tempMaxPointPos.x,
            top: tempMaxPointPos.y,
            color: "red",
            transform: 'translate(-50%, -100%)', 
            whiteSpace: 'nowrap',
          }}
        >
          {tempMaxDisplay}
        </div>
      )}

      {/* 最低温度点 */}
      {tempMinDisplay && (
        <div
          className="temperature-text"
          style={{
            position: "absolute",
            left: tempMinPointPos.x,
            top: tempMinPointPos.y,
            color: "green",
            transform: 'translate(-50%, 0%)', 
            whiteSpace: 'nowrap',
          }}
        >
          {tempMinDisplay}
        </div>
      )}
    </div>
  );
};

export default TemperatureArea;
