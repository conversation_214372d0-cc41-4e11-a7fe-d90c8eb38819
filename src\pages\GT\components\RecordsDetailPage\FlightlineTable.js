import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, Select, DatePicker  } from "antd";
import { downloadFile, getImgUrl, isEmpty, getDeviceName, } from "@/utils/utils";
import { timeFormat,dateFormat2 } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { SearchOutlined } from '@ant-design/icons';
import TaskDetailPage from '@/pages/DJI/TaskDetailPage';


const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const FlightlineTable = (setPage) => {

  return [
    {
      title: getTableTitle('机场名称'),
      dataIndex: 'DeviceSN',
      key: 'DeviceSN',
      align:'center',
      render: (record) => (
        getDeviceName(record)
       )
    },

    {
      title: getTableTitle('任务类型'),
      dataIndex: 'FlightLineType',
      key: 'WayLineType',
      align:'center',
    },
    {
      title: getTableTitle('航线名称'),
      dataIndex: 'FlightLineName',
      key: 'FlightLineName',
      align:'center',

    },
    {
      title: getTableTitle('开始时间'),
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      align:'center',
      render: (record) => (
        dateFormat2(record)
       )
    },
    {
      title: getTableTitle('任务状态'),
      align:'center',
      render: (record) => (
        
      <span style={{width:'100%'}}>
        <Badge style={{marginRight:4.0}} status= {record.TaskState>0?"success":"error"} />
        <span style={{cursor:'pointer'}}>{record.TaskState>0?"已完成":"进行中"}</span>
        
        <span style={{cursor:'pointer',marginLeft:12.0,}}>
        {record.ExpectedCode>0? <Tooltip style={{marginLeft:8.0}} title={getError(record)}>
             <MessageOutlined style={{color:'orange'}}/>
        </Tooltip>:null}</span>
     
      </span>
      
      )
    },
    {
      title: getTableTitle('飞行距离'),
      dataIndex: 'Distance',
      key: 'Distance',
      align:'center',
      render: (record) => (
        record.toFixed(1)+" 公里"
       )
    },
    {
      title: getTableTitle('飞行时间'),
      dataIndex: 'FlyTM',
      key: 'FlyTM',
      align:'center',
      render: (record) => (
        (record/60).toFixed(0)+" 分钟"
       )
    },
    {
     title: getTableTitle('拍摄文件'),
     align:'center',
     render: (record) => (

     <div>
      <span>{record["PhotoUpload"]}</span>
     </div>
     )
   },

    {
      title: getTableTitle("操作"),
      width: 100,
      align: "center",
      render: (record) => (
        <Space size="middle">
          <MyButton
            style={{ padding: "2px 8px",color:'#17AF91',background:'none' }}
            onClick={() => {
              // setPage(<TaskDetailPage data2={record} doNotShowLastButton={true} />)

              // 传入完整的路由信息 能适配面包屑功能
              setPage(
                {
                  title: "飞行详情",
                  key: "/gt/gy/xjxq/fxxq",
                  children: <TaskDetailPage data2={record} doNotShowLastButton={true} />,
                }
              )
            }}
          >
            详情  
          </MyButton>
        </Space>
      ),
    },
  ];
};

export default FlightlineTable;
