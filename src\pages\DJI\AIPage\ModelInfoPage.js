import { Button, Card,Radio } from "antd";
import {  useState } from "react";
import  AIPredictPage from "./AIPredictPage";
import DataSetPage from './DataSetPage';
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useModel } from "umi";

const ModelInfoPage=({model})=>{
    const [s1,setS1]=useState("0");
    const {lastPage}=useModel('pageModel')
    const handleSizeChange=(e)=>{
        setS1(e.target.value);
    }
    const sBtn=  <Radio.Group value={s1} onChange={handleSizeChange}>
    <Radio.Button value="0" style={{marginLeft:4.0}}>模型识别</Radio.Button>
    <Radio.Button value="1" style={{marginLeft:4.0}}>样本库</Radio.Button>
    {/* <Radio.Button value="2" style={{marginLeft:4.0}}>模型训练</Radio.Button> */}
    {/* <Radio.Button value="3" style={{marginLeft:4.0}}>模型部署</Radio.Button> */}

  </Radio.Group>

  const titleButton=<Button style={{fontSize:16.0,fontWeight:'bold'}} onClick={()=>lastPage()} type={'text'} icon={<ArrowLeftOutlined />}>模型列表</Button>
    const getBody=()=>{
        if(s1=="0"){
            return <AIPredictPage model={model}></AIPredictPage>
        }
        if(s1=="1"){
            return <DataSetPage model={model}></DataSetPage>
        }
    }
    return <Card title={titleButton} extra={sBtn}>{getBody()}</Card>

}

export default ModelInfoPage;