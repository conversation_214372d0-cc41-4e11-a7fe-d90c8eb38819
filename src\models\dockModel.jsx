import { getBodyH, isEmpty } from "@/utils/utils";
import { useState, useRef } from "react";
import { GetMqttClient } from "@/utils/websocket";
import { Post2 } from "@/services/general";
import { getLocation,isIndividual } from "@/utils/helper";

export default function dockModel() {
  const [jc, setJC] = useState({});
  const [sdrData, setSDRData] = useState({});
  const jcData = useRef({});
  const jcData2 = useRef({});
  const jcData3 = useRef({});
  const mqttC = useRef({});

  const DockMqttConn = async (sn) => {
    setJC({});
    if (!isEmpty(mqttC.current)) {
      mqttC.current.end();
    }

    mqttC.current = await GetMqttClient();
    mqttC.current.on("message", (topic, message) => {
      updateVal(topic, message);
    });
    mqttC.current.unsubscribe("#");
    mqttC.current.subscribe("thing/product/" + sn + "/osd");
  };

  const updateVal = (t1, m1) => {
    const xx = JSON.parse(m1);
    const device = JSON.parse(localStorage.getItem("device"));
    if (isEmpty(device)) return;
    if (device.SN != xx.gateway) return;

    if (isIndividual(device)) {
      let data = {}; 
      data = {...xx.data, ...device.OsdData};
      jcData.current = data;
      setJC(data);
      localStorage.setItem("jcdata1", JSON.stringify(data));
      updateDevice(device, data);
      console.log("单兵设备无人机--OsdData", data);
    }
    if (!isEmpty(xx.data["sub_device"]) && device.SN == xx.gateway) {
      jcData.current = xx.data;
      setJC(xx.data);
      localStorage.setItem("jcdata1", JSON.stringify(xx.data));
      updateDevice(device, xx.data);
    }

    if (!isEmpty(xx.data["flighttask_step_code"]) && device.SN == xx.gateway) {
      localStorage.setItem("jcdata2", JSON.stringify(xx.data));
      jcData2.current = xx.data;
      setSDRData(xx.data);
    }

    if (!isEmpty(xx.data["job_number"]) && device.SN == xx.gateway) {
      localStorage.setItem("jcdata3", JSON.stringify(xx.data));
      jcData3.current = xx.data;
    }
  };

  const ifPC = (v1, v2, dt) => {
    if (v1 - v2 > dt || v2 - v1 > dt) {
      return true;
    }
    return false;
  };

  const updateDevice = async (device, data) => {
    if (isEmpty(data)) return;

    if (
      device.SN2 == "" ||
      ifPC(data.height, device.Height, 5) ||
      ifPC(data.latitude, device.Lat, 0.2) ||
      ifPC(data.longitude, device.Lng, 0.2)
    ) {
      const yy = await getLocation(data.latitude, data.longitude);
      device.Lat = data.latitude;
      device.Lng = data.longitude;
      device.Height = data.height;
      device.DName2 = yy;
      console.log("updateDevice", data);
      device.SN2 = data.sub_device.device_sn;
      localStorage.setItem("device", JSON.stringify(device));
      await Post2("/api/v1/Device/Update", device);
    }
  };
  return { jc, sdrData, jcData, jcData2, jcData3, DockMqttConn };
}
