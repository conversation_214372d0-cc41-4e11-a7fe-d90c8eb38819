import { Input, Form, Select, Button, Modal, message, Table } from "antd";
import React, { useState, useEffect } from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";

const CronAddForm = (props) => {
  const { org, refrush } = props;
  const [data, setData] = useState(org);

  useEffect(() => {}, []);

  const onSave = async (e) => {
    //
    if (isEmpty(data["OrgName"])) {
      message.info("请输入子组织名称！");
      return;
    }
    if (isEmpty(data["OrgCode"])) {
      message.info("请输入子组织代码！");
      return;
    }
    if (isEmpty(data["OrgManager"])) {
      message.info("请输入联系人！");
      return;
    }
    if (isEmpty(data["OrgManagerPhone"])) {
      message.info("请输入联系电话！");
      return;
    }
    const phone = data["OrgManagerPhone"];
    const phoneRegex = /^1\d{10}$/;
    if (!phoneRegex.test(phone)) {
      message.info("请输入有效电话号码!");
      return;
    }

    const xx = await HPost2("/api/v1/OrgInfo/Add", data);
    if (isEmpty(xx.err)) {
      message.info("创建成功！");
    }
    refrush();
  };

  return (
    <Form
      labelCol={{
        span: 4,
      }}
      wrapperCol={{
        span: 18,
      }}
      layout="horizontal"
      //disabled={componentDisabled}
      style={{
        maxWidth: "99%",
      }}
    >
      <Form.Item label="子组织名称">
        <Input
          defaultValue={org.OrgName}
          onChange={(e) => setData({ ...data, OrgName: e.target.value })}
        ></Input>
      </Form.Item>
      <Form.Item label="子组织代码">
        <Input
          defaultValue={org.OrgCode}
          onChange={(e) => setData({ ...data, OrgCode: e.target.value })}
        ></Input>
      </Form.Item>
      <Form.Item label="联系人">
        <Input
          defaultValue={org.OrgManager}
          onChange={(e) => setData({ ...data, OrgManager: e.target.value })}
        ></Input>
      </Form.Item>
      <Form.Item label="联系电话">
        <Input
          defaultValue={org.OrgManagerPhone}
          onChange={(e) =>
            setData({ ...data, OrgManagerPhone: e.target.value })
          }
        ></Input>
      </Form.Item>

      <Form.Item label={" "} colon={false}>
        <Button type="primary" onClick={onSave}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CronAddForm;
