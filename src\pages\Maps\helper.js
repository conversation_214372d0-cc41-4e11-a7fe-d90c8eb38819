import { isEmpty } from "@/utils/utils";
import { Gcj02ToWgs84, Wgs84ToGcj02, gcj02Towgs84 } from "./gps_helper";
import { GetDiTuGps } from "./ditu";
import * as turf from "@turf/turf";


export function getPStrArea(pstr) {
    if(isEmpty(pstr)) return 0;
    const pL=getPList2(pstr);
    pL.push(pL[0]);
 
 //  const polygon=turf.polygon([pL]);
 //  const area2=turf.area(polygon);
    // const Area = turf.area({
    //     type: "Polygon",
    //     coordinates: [pL],
    // });
    const Area = turf.area({
        type: "Polygon",
        coordinates: [pL],
      });

    const areaInSqKm = (Area / 1000000).toFixed(2); // 转换为平方公里
    return areaInSqKm;
}
export function getPList(pL){
     const list=[];
    if(isEmpty(pL)) return list;
     const xL=pL.split(";");
     xL.forEach(p => {
         const p1=p.split(",");
         if(p1.length>1){
           list.push([ Number(p1[1]), Number(p1[0])])
         }
     });

     return list;
 }

 export function getPList2(pL){
    const list=[];
   if(isEmpty(pL)) return list;
    const xL=pL.split(";");
    xL.forEach(p => {
        const p1=p.split(",");
        if(p1.length>1){
          list.push([ Number(p1[0]), Number(p1[1])])
        }
    });
    return list;
}

 export function getPListStr(pL){
    // console.log('getPList',pL);
     let st=""
     pL.forEach(e => {
        st=st+e[1]+","+e[0]+";"
     });
    
     return st;
 }

 export function ConvertPList   (pL,baseMap)  {
    const pb1 = GetDiTuGps(baseMap);
    if (pb1) {
      return  convertPListWgs(pL)
    }
    if(isEmpty(baseMap)){
        return  convertPListWgs(pL)
    }
    return pL;
  }
///将wgs 批量转为 gcj02
export function convertPListWgs(pL){
    // console.log('getPList',pL);
     const list=[];
    // console.log('ConvertPList',pL);
    // return list;
     if(isEmpty(pL)) return list;
     pL.forEach(p => {
        
       list.push(Wgs84ToGcj02(p[0],p[1]))
 
     });

   //  console.log('ConvertPList',pL,list);
     return list;
 }

 export function convertPListGcj02(pL){
    // console.log('getPList',pL);
     const list=[];
    // console.log('ConvertPList',pL);
    // return list;
     if(isEmpty(pL)) return list;
     pL.forEach(p => {
        
       list.push(Gcj02ToWgs84(p[0],p[1]))
 
     });
   //  console.log('ConvertPList',pL,list);
     return list;
 }

 export const aqqPoints=[
    [
        31.03526667051427,
        103.57094764709474
    ],
    [
        31.03388770985635,
        103.57206344604494
    ],
    [
        31.036185966525768,
        103.57755661010744
    ],
    [
        31.039807906437783,
        103.57781410217287
    ],
    [
        31.042492097882647,
        103.57189178466798
    ],
    [
        31.04135224500752,
        103.56738567352296
    ],
    [
        31.03999175758115,
        103.56536865234376
    ],
    [
        31.039329891803508,
        103.56142044067384
    ],
    [
        31.040175608369484,
        103.55339527130128
    ],
    [
        31.039513743869946,
        103.54631423950197
    ],
    [
        31.034181889805637,
        103.54176521301271
    ],
    [
        31.029916191596005,
        103.53858947753908
    ],
    [
        31.025356096026783,
        103.54004859924316
    ],
    [
        31.02344373291606,
        103.5432243347168
    ],
    [
        31.028445216878193,
        103.55978965759279
    ] ,[
        31.03526667051427,
        103.57094764709474
    ]
];
 export function IsAQQ(aLat, aLon){
    return true;
  return IsPtInPoly(aLat,aLon,aqqPoints);
 }

 export function GetDistance( lat1,  lng1,  lat2,  lng2){
    var radLat1 = lat1*Math.PI / 180.0;
    var radLat2 = lat2*Math.PI / 180.0;
    var a = radLat1 - radLat2;
    var  b = lng1*Math.PI / 180.0 - lng2*Math.PI / 180.0;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) +
    Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
    s = s *6378.137*1000 ;// EARTH_RADIUS;
    s = Math.round(s * 10000) / 10000;
    return s;
}


  function IsPtInPoly(aLat, aLon, pointList) {

  var iSum = 0  
  var iCount = pointList.length
    
  if(iCount < 3) {
      return false 
  }
  //  待判断的点(x, y) 为已知值
  var y = aLat
  var x = aLon
  for(var i = 0; i < iCount; i++) {
      var y1 = pointList[i][0] 
      var x1 = pointList[i][1]
      if(i == iCount - 1) {
          var y2 = pointList[0][0]
          var x2 = pointList[0][1]
      } else {
          var y2 = pointList[i + 1][0] 
          var x2 = pointList[i + 1][1]
      }
      // 当前边的 2 个端点分别为 已知值(x1, y1), (x2, y2)
      if (((y >= y1) && (y < y2)) || ((y >= y2) && (y < y1))) {
          //  y 界于 y1 和 y2 之间
          //  假设过待判断点(x, y)的水平直线和当前边的交点为(x_intersect, y_intersect)，有y_intersect = y
          // 则有（2个相似三角形，公用顶角，宽/宽 = 高/高）：|x1 - x2| / |x1 - x_intersect| = |y1 - y2| / |y1 - y|
          if (Math.abs(y1 - y2) > 0) {
              var x_intersect = x1 - ((x1 - x2) * (y1 - y)) / (y1 - y2);  
              if(x_intersect < x) {
                  iSum += 1 
              }
          }
      } 
  }
  if(iSum % 2 != 0) {
      return true  
  }else {
      return false 
  }  
}
