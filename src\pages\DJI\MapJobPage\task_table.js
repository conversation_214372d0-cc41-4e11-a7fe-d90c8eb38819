import { Space, Tag, message, Modal } from 'antd';
import { downloadFile, getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { timeFormat, timeFormat2 } from '@/utils/helper';
import { getImgUrl } from '@/utils/utils';
import { HGet2 } from '@/utils/request';
import { Post2 } from '@/services/general';
import WayLineEditPage from './form_edit';
import DevicePage from '../DevicePage';



const getTableTitle = (title) => { return <div style={{ fontWeight: 'bold', textAlign: 'center' }}>  {title}</div> }



const TableCols = (onPlay) => {
  return [
    {
      title: getTableTitle('航线名称'),
      key: 'VName',
      align: 'center',
      render: (item) => (
        item.FlightLineName
      )
      // width:200,
    },
    {
      title: getTableTitle('拍摄照片'),
      key: 'VName',
      align: 'center',
      render: (item) => (
         item.PhotoCount
      )
      // width:200,
    },
    {
      title: getTableTitle('上传照片'),
      dataIndex: 'PhotoUpload',
      key: 'PhotoUpload',
      align: 'center',
      // width:200,
    },
    {
      title: getTableTitle('作业时间'),
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      align: 'center',
      render: (record) => (
        (timeFormat2( record))
      )
      //  width:200,
    },
  ];
}



export default TableCols;
