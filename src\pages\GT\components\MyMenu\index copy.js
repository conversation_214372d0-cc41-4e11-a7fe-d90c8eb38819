import React, { useState, useEffect, useRef } from "react";
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
} from "@ant-design/icons";
import { But<PERSON>, Menu } from "antd";
import { useModel, history } from "umi";
import { queryPage } from "@/utils/MyRoute";

const items = [
  {
    key: "态势感知",
    icon: <PieChartOutlined />,
    label: "态势感知",
  },
  {
    key: "设备管理",
    icon: <DesktopOutlined />,
    label: "设备管理",
    children: [
      {
        key: "设备列表",
        label: "设备列表",
      },
      {
        key: "视频九宫格",
        label: "视频九宫格",
      },
      {
        key: "解禁文件",
        label: "解禁文件",
      },
    ],
  },
  {
    key: "航线列表",
    icon: <ContainerOutlined />,
    label: "航线管理",
  },
  {
    key: "定时任务",
    label: "定时任务",
    icon: <MailOutlined />,
  },
  {
    key: "飞行记录",
    label: "飞行记录",
    icon: <AppstoreOutlined />,
    children: [
      {
        key: "航行记录",
        label: "航行记录",
      },
      {
        key: "任务记录",
        label: "任务记录",
      },
      {
        key: "飞行统计",
        label: "飞行统计",
      },
    ],
  },
  {
    key: "数据管理",
    icon: <DesktopOutlined />,
    label: "数据管理",
    children: [
      {
        key: "航拍照片",
        label: "航拍照片",
      },
      {
        key: "航拍视频",
        label: "航拍视频",
      },
      {
        key: "正射影像",
        label: "正射影像",
      },
      {
        key: "正射对比",
        label: "正射对比",
      },
      {
        key: "三维模型",
        label: "三维模型",
      },
      {
        key: "地图管理",
        label: "地图管理",
      },
    ],
  },
  {
    key: "AI识别",
    icon: <DesktopOutlined />,
    label: "AI识别",
    children: [
      {
        key: "AI模型",
        label: "AI模型",
      },
      {
        key: "图像识别任务",
        label: "图像识别任务",
      },
      {
        key: "视频识别任务",
        label: "视频识别任务",
      },
      {
        key: "事件处理",
        label: "事件处理",
      },
    ],
  },
  {
    key: "系统配置",
    icon: <DesktopOutlined />,
    label: "系统配置",
    children: [
      {
        key: "组织管理",
        label: "组织管理",
      },
      {
        key: "用户管理",
        label: "用户管理",
      },
      {
        key: "系统日志",
        label: "系统日志",
      },
      {
        key: "公告发布",
        label: "公告发布",
      },
    ],
  },
];
const App = ({ setCurrentPage }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { setPage, setHeadHeight } = useModel("pageModel");

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  function handleMenu(e) {
    localStorage.getItem("currentPage", e.key)
    setPage(queryPage(e.key));
    setCurrentPage(e.key);
  }
  let currentPage = localStorage.getItem("currentPage");
  let [showHead,setShowHead] = useState(currentPage);
  useEffect(() => {
    setShowHead(setShowHead =>currentPage)
  }, [currentPage]);
  return (
    <>
        <div>
          <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{ marginBottom: 16 }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <Menu
            defaultSelectedKeys={["1"]}
            defaultOpenKeys={["sub1"]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={handleMenu}
          />
        </div>
    </>
  );
  
};
export default App;
