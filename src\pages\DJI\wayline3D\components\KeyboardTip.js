import { useState, useEffect, useRef } from 'react';
import { Tooltip } from 'antd';
import { SwapLeftOutlined, CaretUpOutlined, SwapRightOutlined, CaretLeftOutlined, CaretDownOutlined, CaretRightOutlined} from '@ant-design/icons';

function KeyboardTip({  }) {

    return <div style={{ position: 'absolute', bottom: 50, left: 20, width: 120, height: 150, zIndex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.6)', borderRadius: '10px' }}>
        <div style={{ height: '45%', width: '100%', display: 'flex', justifyContent: 'space-evenly', alignItems: 'center', borderRadius: '10px', color: 'white', fontSize: 18, fontWeight: 600 }}>
            <Tooltip placement="top" title='按Q键向左旋转云台'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><SwapLeftOutlined /></div>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>Q</div>
                </div>
            </Tooltip>
            <Tooltip placement="top" title='按W键飞机向前平移'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><CaretUpOutlined /></div>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>W</div>
                </div>
            </Tooltip>
            <Tooltip placement="top" title='按E键向右旋转云台'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><SwapRightOutlined /></div>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>E</div>
                </div>
            </Tooltip>

        </div>
        <div style={{ height: '45%', width: '100%', display: 'flex', justifyContent: 'space-evenly', alignItems: 'center', borderRadius: '10px', color: 'white', fontSize: 18, fontWeight: 600 }}>
            <Tooltip placement="bottom" title='按A键飞机向左平移'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>A</div>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><CaretLeftOutlined /></div>
                </div>
            </Tooltip>
            <Tooltip placement="bottom" title='按S键飞机向后平移'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>S</div>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><CaretDownOutlined /></div>
                </div>
            </Tooltip>
            <Tooltip placement="bottom" title='按D键飞机向右平移'>
                <div style={{ height: '80%', width: '25%', cursor: 'pointer' }}>
                    <div style={{ height: '60%', width: '100%', backgroundColor: '#3c3c3c', display: 'flex', justifyContent: 'center', alignItems: 'center', }}>D</div>
                    <div style={{ height: '40%', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', }}><CaretRightOutlined /></div>
                </div>
            </Tooltip>
        </div>
    </div>
}
export default KeyboardTip;