import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';

import icon4 from '@/assets/icons/device.png';

import { BorderBox7 } from '@jiaminghi/data-view-react';
import { Badge, Progress, Row, Col, message } from 'antd';
import { getGuid, timeFormat } from '@/utils/helper';
import { getBodyH, isEmpty } from '@/utils/utils';

import { useEffect, useState } from 'react';
import { Get2 } from '@/services/general';
import styles from './notice.less';


const NoticePanel = () => {

  const [data, setData] = useState({ Content: '' });

  const getNotice = async () => {
    let pst = await Get2('/api/v1/Notice/GetNotice', {});
    if (isEmpty(pst)) {
      pst = { Content: '' };
    }
    if(!isEmpty(pst.err)) {
      message.error("公告数据错误："+pst.err);
      pst = { Content: '' };
    }
    setData(pst);
  };

  useEffect(() => {
    getNotice();
  }, []);

  const getCList = (d1) => {
    const list = []
    
    const xx = d1.split('\n');
    xx.forEach(e => {
      list.push(<p key={e}>{e}</p>)
    });
    return list;
  }
  const getCDiv = () => {
    if (isEmpty(data)) return '暂无公告';
    if (data.ID == 0) return '暂无公告';


    return <div className={styles.noticebox}>
      <div className={[styles.segmented, styles.noticeCon].join(' ')}>{getCList(data.Content)}</div>
      <div className={styles.noticeFoot}>
        <div>发布单位：{data.UserName} </div>
        <div>发布时间：{timeFormat(data.CreateTM)} </div>
      </div>
      {/* <div style={{position:'absolute',left:8.0,top:null,bottom:8.0,fontSize:14.0}}>联系电话：{data.UserPhone} </div>  */}

    </div>

  }
  if (isEmpty(data)) return <div/>;
  if (data.ID == 0) return <div/>;
  if (data.Content=="") return <div/>;

  return <div style={{ height: 360.0, width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="公告信息" />
      <div style={{ color: 'white', height: 220, fontSize: 14.0, padding: 8.0 }}>
        {getCDiv()}
      </div>
    </BorderBox7>
  </div>
}

export default NoticePanel;
