import { useEffect } from "react"
const AIMediaPanel = ({img1,data,w1,h1,aList}) => {
    useEffect(() => {
        const xx3=(item,w2,h2)=>{
            let x1=item.box.x1/w2
            let x2=item.box.x2/w2
            let y1=item.box.y1/h2
            let y2=item.box.y2/h2
            const aa=[aList[item.class],(x1+x2)/2,(y1+y2)/2,(x2-x1),(y2-y1)];
            return aa;
        }

        if(img1=="/api/v2/AI/DownloadImg?id=") return ;
        const cc = document.getElementById('mycanvas');
        const ctx = cc.getContext('2d');
        var img = new Image()
        img.src = img1
        img.onload = function () {
           if(w1==0 && h1==0){
                w1=img.width;
                h1=img.height;
           }
           if(w1==0 && h1>0){
              w1=img.width/img.height*h1;
           }
           if(h1==0 && w1>0){
            h1=img.height/img.width*w1;
          }
           cc.width = w1;
           cc.height = h1;
            //剪切位置(0,0),剪切尺寸(600,600),
            //放置位置(0,0),放置尺寸(100,100);
            ctx.drawImage(img, 0, 0,img.width,img.height,0,0,w1,h1);
            // const xx=xx3();
            console.log('rr',data);
            ctx.strokeStyle = "red";
            data.forEach(item => {
                const xx=xx3(item,img.width,img.height);
             
                console.log('rr',xx);
                if(item.confidence>0.3){
                   // ctx.strokeText(xx[0], xx[1] * img.width - xx[3] * img.width / 2, xx[2] * img.height - xx[4] * img.height / 2,)
                  //  ctx.strokeRect(xx[1] * img.width - xx[3] * img.width / 2, xx[2] * img.height - xx[4] * img.height / 2, xx[3] * img.width, xx[4] * img.height)
                     ctx.strokeText(xx[0], xx[1] * w1- xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2,240)
                     ctx.strokeRect(xx[1] * w1 - xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2, xx[3] * w1, xx[4] * h1)
                }
                // ctx.strokeText(xx[0], xx[1] * w1- xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2,)
                //ctx.strokeRect(xx[1] * w1 - xx[3] * w1 / 2, xx[2] * h1 - xx[4] * h1 / 2, xx[3] * w1, xx[4] * h1)
            });
             }
    }, []);

    const getBody = () => {
        return <canvas id="mycanvas" >
            您的浏览器不支持canvas元素，请升级您的浏览器
        </canvas>
    }
    return <div>{getBody()}</div>
}

export default AIMediaPanel;