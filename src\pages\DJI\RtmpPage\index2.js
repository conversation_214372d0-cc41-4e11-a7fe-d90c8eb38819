import { Get2 } from "@/services/general";
import { getBodyH,getBodyW2, isEmpty } from "@/utils/utils";
import { Row, Col, Button } from "antd";
import { useEffect, useRef, useState } from "react";
import GetPlayer from "../DevicePage/PlayerPanel";
import { getGuid } from "@/utils/helper";
import gongge1 from "@/assets/icons/gongge1.png";
import gongge4 from "@/assets/icons/gongge4.png";
import gongge6 from "@/assets/icons/gongge6.png";
import gongge9 from "@/assets/icons/gongge9.png";
import WebRtcPlayer from '@/components/WebRtcPlayer';

const RtmpPage = () => {
  const OsdData=useRef({})
  const [dL,setDL]=useState([]);
  const [ind,setInd]=useState(0)
  const rData=useRef([])
  const [pb,setPB]=useState(4)
  const [index,setIndex]=useState({})


  useEffect(() => {
    const getOsdData=async (sn)=>{
      const xx = await Get2('/api/v1/Device/GetJcOsdData1?sn='+sn)
      
      OsdData.current[sn]=xx;
    }
    const getOsdData2=async (e)=>{
      const xx = await Get2('/api/v1/Device/GetJcOsdData1?sn='+e.SN)
      e.OsdData=xx;
    }

    const getDList = async () => {
      const xx = await Get2('/api/v1/Device/GetAllList')
      xx.forEach(e => {
        getOsdData2(e);
      });
      setDL(xx);
      // xx.forEach(e => {
      //     getOsdData(e.SN);
      // });
    }
    getDList();
  }, []);

  const getItem=(i,height)=>{
      let border="1px solid white"
      if(ind==i){
         border="1px solid red"
      }
      if(pb==0 && i==0) border="1px solid white"

      const d2=rData.current[i]
      if(isEmpty(d2)) return  <div style={{margin:2.0,cursor:'pointer',height:height,width:'100%',border:border}} onClick={()=>setInd(i)} />
      const d1=d2.data;
      let sn=d1.SN;
      if(d2.ifFJ) sn=d1.SN2;
      
      return <div style={{margin:2.0,cursor:'pointer',border:border,marginLeft:4.0}} onClick={()=>setInd(i)}>
        <div style={{ position:'absolute',top:8,left:8,zIndex:10,fontWeight:'bold', color:'yellow'}}>{d1.DName}</div>
            <WebRtcPlayer key={getGuid()}  sn3={sn} ctl={false} width={'100%'} height={height} />;
        </div>
  }

  const getDiv1 = () => {
    return <div style={{height:getBodyH(62),width:getBodyW2(10)-300}}>{getItem(0,getBodyH(62),'100%')}</div>
  }

  const getDiv4 = () => {
    const h1=getBodyH(72)/2
    return <Row style={{ height: '100%', width: '100%' ,}}>
      <Col span={12} style={{padding:2.0}} >{getItem(0,h1)}</Col>
      <Col span={12}  style={{padding:2.0}}>{getItem(1,h1)}</Col>
      <Col span={12}  style={{padding:2.0}}>{getItem(2,h1)}</Col>
      <Col span={12}  style={{padding:2.0}}>{getItem(3,h1)}</Col>
    </Row>
  }

  const getDiv6 = () => {
    const h1=getBodyH(80)/2
    return <div><Row style={{ height: '100%', width: '100%' ,}}>
      <Col span={8}  style={{padding:2.0}}>{getItem(0,h1,)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(1,h1,)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(2,h1,)}</Col>
    </Row>
    <Row style={{ height: '100%', width:'100%'  ,}}>
      <Col span={8}  style={{padding:2.0}}>{getItem(3,h1,)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(4,h1,)}</Col>
      <Col span={8} style={{padding:2.0}} >{getItem(5,h1,)}</Col>
    </Row>
    </div>
  }

  const getDiv9 = () => {
    const h1=getBodyH(80)/3
    return <div><Row style={{ height: '100%', width: '100%' ,}}>
      <Col span={8}  style={{padding:2.0}}>{getItem(0,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(1,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(2,h1)}</Col>
    </Row>
    <Row style={{ height: '100%', width: '100%' ,}}>
      <Col span={8}  style={{padding:2.0}}>{getItem(3,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(4,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(5,h1)}</Col>
    </Row>
    <Row style={{ height: '100%', width: '100%' ,}}>
      <Col span={8}  style={{padding:2.0}}>{getItem(6,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(7,h1)}</Col>
      <Col span={8}  style={{padding:2.0}}>{getItem(8,h1)}</Col>
    </Row>
    </div>
  }

  const w1=getBodyW2(0)
  const h1=getBodyH(60)
  
  const LeftTopDiv=<div><span style={{fontSize:18.0,color:'white',fontWeight:'bold',marginLeft:16.0, marginRight:36.0}}>视频墙</span>
  <span><img src={gongge1} style={{margin:8.0,height:32.0,width:32.0,cursor:'pointer'}} onClick={()=>setPB(0)}></img></span>
  <span><img src={gongge4} style={{margin:8.0, height:24.0,width:24.0,cursor:'pointer'}} onClick={()=>setPB(1)}></img></span>
  <span><img src={gongge6} style={{margin:8.0,height:24.0,width:24.0,cursor:'pointer'}} onClick={()=>setPB(2)}></img></span>
  <span><img src={gongge9} style={{margin:8.0,height:24.0,width:24.0,cursor:'pointer'}} onClick={()=>setPB(3)}></img></span>
  </div>

  const onClick=(d1,p1)=>{
      setIndex({data:d1,ifFJ:p1});
      rData.current[ind]={data:d1,ifFJ:p1};
  }

  const getDItem=(d1)=>{
      return <div style={{margin:8.0,background:'grey'}}>
            <div>{d1.DName}</div>
            <div><span><Button onClick={()=>onClick(d1,false)}>机场直播</Button></span> <span style={{marginLeft:8.0}}><Button >无人机</Button></span></div>
      </div>
  }
  const getDListPanel=()=>{
      const list=[]
      dL.forEach(d1 => {
          list.push(getDItem(d1));
      });
      return <div>
          {list}
      </div>
  }

  const getPBBody=(i)=>{
       if(i==0) return getDiv1();
       if(i==1) return getDiv4();
       if(i==2) return getDiv6();
       if(i==3) return getDiv9();
      return getDiv9(); 
  }

  return <div style={{display:'flex', height: h1}}>
      <div style={{width:300,background:'black',height:h1}}>
          {LeftTopDiv}
          {getDListPanel()}
      </div>
    
      <div style={{height:h1, width:w1-300,background:'black'}}>
        {getPBBody(pb)}
      
      </div>
    </div>

}

export default RtmpPage;