/* #side {
  border: 1px solid rgb(247, 8, 8);
  resize: both;
  border-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cstyle%3Epath%7Banimation:stroke 9s infinite linear%3B%7D%40keyframes stroke%7Bto%7Bstroke-dashoffset:776%3B%7D%7D%3C/style%3E%3ClinearGradient id='g' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%232d3561' /%3E%3Cstop offset='25%25' stop-color='%23c05c7e' /%3E%3Cstop offset='50%25' stop-color='%23f3826f' /%3E%3Cstop offset='100%25' stop-color='%23ffb961' /%3E%3C/linearGradient%3E %3Cpath d='M1.5 1.5 l97 0l0 97l-97 0 l0 -97' stroke-linecap='square' stroke='url(%23g)' stroke-width='3' stroke-dasharray='388'/%3E %3C/svg%3E") 1;
} */

.swiper {
  color: white;
  /* height: 500px; */
  width: 100%;
  font-size: 13px;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: pointer;
}

.swiper::-webkit-scrollbar {
  display: none;
}

.swiper-box {
  margin: 20px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.state {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.state-self {
  background: linear-gradient(to right, #fff, #f8e327);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  box-shadow: 0px 0px 2px 1px #f9b403;
}

.state-end {
  background: linear-gradient(to right, #fffefe, #88ff00);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  box-shadow: 0px 0px 1px 0px #9cea43;
}

.state-before {
  background: linear-gradient(to right, #fffefe, #0be7f5);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  box-shadow: 0px 0px 1px 0px #9cea43;
}

.state-text {
  color: #fff;
  padding: 0 10px;
  border-radius: 15px 0px 15px 0;
}

.line {
  width: 100%;
  height: 2px;
  background: linear-gradient(to bottom, #ccc, #1f5d95);
  box-shadow: -4px -4px 11px 1px #0ff;
}