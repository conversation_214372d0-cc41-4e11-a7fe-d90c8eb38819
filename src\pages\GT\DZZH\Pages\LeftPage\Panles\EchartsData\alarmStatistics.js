import * as echarts from 'echarts';
export default  {
    color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
    title: {
      text: ''
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['告警数量', '处理数量',],
      textStyle: {
        color: '#fff', // 设置图例文字颜色
        fontSize: 14   // 设置图例文字大小
    },
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    grid: {
      left: '0%',
      right:'4%',
      top:'15%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLabel: {
            color: '#fff', // 设置 x 轴刻度的颜色
            fontStyle: 'italic', // 字体样式
            fontSize: 12, // 字体大小
            interval: 0, // 强制显示所有刻度
        },
        splitLine: {
          show: true, // 确保显示网格线
            lineStyle: {
                type: 'dashed', // 设置网格线为虚线
                color: '#798283', // 网格线颜色
                width: 0.5, // 虚线宽度
                lineDash: [5, 5] // 虚线的长度和间隔
            }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        interval: 100, // 设置 Y 轴的间隔为 100
        // min:0,
        // max:800,
        axisLabel: {
            color: '#fff', // 设置 x 轴刻度的颜色
            fontStyle: 'italic', // 字体样式
            fontSize: 12, // 字体大小
        },
        splitLine: {
            show: true, // 确保显示网格线
            lineStyle: {
                type: 'dashed', // 设置网格线为虚线
                color: '#798283', // 网格线颜色
                width: 0.5, // 虚线宽度
                lineDash: [5, 5] // 虚线的长度和间隔
            }
        }
      }
    ],
    series: [
      {
        name: '告警数量',
        type: 'line',
        stack: 'Total',
        symbol: 'rect', // 设置为方形
        smooth: true,
        lineStyle: {
          width: 2, // 设置线条宽度
          color: 'rgb(20, 198, 210)' // 设置线条颜色
      },
        showSymbol: false,
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(20, 198, 210)'
            },
            {
              offset: 1,
              color: 'rgb(20, 198, 210)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: [120, 282, 111, 234, 220, 340, 310, 232, 101, 264, 90, 340, ]
      },
      {
        name: '处理数量',
        type: 'line',
        stack: 'Total',
        symbol: 'rect', // 设置为方形
        smooth: true,
        lineStyle: {
          width: 2, // 设置线条宽度
          color: 'rgb(12, 202, 130)' // 设置线条颜色
      },
        showSymbol: false,
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(12, 202, 130)'
            },
            {
              offset: 1,
              color: 'rgb(12, 202, 130)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: [320, 132, 201, 334, 190, 130, 220, 232, 101, 264, 90, 340, ]
      },
     
    ]
  };