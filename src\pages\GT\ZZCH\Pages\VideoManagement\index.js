import React, { useEffect } from "react";
import { useModel } from "umi";
import { queryPage } from '@/utils/MyRoute';
import MyMenu from "@/pages/GT/components/MyMenu";

function VideoManagement (){
  const { page, setPage, lastPage, currentPage } = useModel("pageModel");

  // const handlePageChange = (page) => {
  //   setPage(queryPage(page));
  // };

  return(
    setPage(queryPage('航拍图片'))
  )
  // const renderPage = () => {
  //     if(page){
  //       return page
  //     }
  // }

  // const menuItems = [
  //   { label: "航拍照片", key: "航拍照片" },
  //   { label: "航拍视频", key: "航拍视频" },
  //   { label: "二维正射图", key: "二维正射图" },
  // ];
  // return (
  //   <div style={{ display: "flex", justifyContent: "flex-start" }}>
  //     <MyMenu menuItems={menuItems} handlePageChange={handlePageChange} />
  //     {/* <div>{renderPage()}</div> */}
  //   </div>
  // );
};

export default VideoManagement;
