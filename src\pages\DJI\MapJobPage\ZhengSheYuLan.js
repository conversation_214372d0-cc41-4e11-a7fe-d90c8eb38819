import React, { useState } from "react";
import { LayersControl } from "react-leaflet";
import "leaflet/dist/leaflet.css";

import { isEmpty, getBodyH } from "@/utils/utils";

import L from "leaflet";
import "leaflet-side-by-side";
import { useRef } from "react";
import { useEffect } from "react";
import { Row, Col, Button, Radio, List, message, Card } from "antd";
import { HGet2 } from "@/utils/request";

const { Overlay } = LayersControl;

const ZhengSheYuLanPage = ({ jID }) => {
  const mapRef = useRef({});
  
  const [layer1, setLayer1] = useState({});

  const layers = useRef({});

  useEffect(() => {
    const xx = async () => {
      const oss=getOssConfig();
      const gpsJson = await HGet2(
        oss.OssUrl + "/map2D/" + jID + "2D/AT/sfm_geo_desc.json"
      );
      if (isEmpty(gpsJson)) {
        message.info("成果无法预览！");
        return;
      }
      //
      //gpsJson['ref_GPS']['latitude']
      mapRef.current = L.map("mapDiv").setView(
        [gpsJson["ref_GPS"]["latitude"], gpsJson["ref_GPS"]["longitude"]],
        16
      );

      const layer = getLayer();
      layer.addTo(mapRef.current);
    };
    xx();
    //    mapRef.current = L.map('mapDiv').setView([32.152679699,105.536594587], 16);
  }, []);

  const getLayer = () => {
    return L.tileLayer(OssUrl + "/map2D/" + jID + "2D/{z}/{x}/{y}.png", {
      maxZoom: 22,
      minZoom: 1,
      opacity: 1.0,
      attribution: "",
    });
  };

  return (
    <div>
      <div id="mapDiv" style={{ width: "100%", height: getBodyH(200) }}>
        {" "}
      </div>
    </div>
  );
};

export default ZhengSheYuLanPage;
