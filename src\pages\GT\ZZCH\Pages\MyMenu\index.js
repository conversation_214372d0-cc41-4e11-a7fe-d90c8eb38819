import { useState,useEffect } from "react";
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
} from "@ant-design/icons";
import { But<PERSON>, Menu } from "antd";
import { useModel } from "umi";
import { queryPage } from "@/utils/MyRoute";

const items = [
  {
    key: "航拍图片",
    icon: <PieChartOutlined />,
    label: "航拍图片",
  },
  {
    key: "航拍影像",
    icon: <DesktopOutlined />,
    label: "航拍影像",
  },
  {
    key: "二维正射图",
    icon: <DesktopOutlined />,
    label: "二维正射图",
  },
  // {
  //   key: "三维模型图",
  //   icon: <PieChartOutlined />,
  //   label: "三维模型图",
  // },
];
const App = ({handlePageChange,setCollapsed,collapsed}) => {
  const { pageData, setPageData } = useModel("pageModel");
  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  // 处理菜单点击
  function handleMenu(e) {
    console.log("click ", e);
    handlePageChange(e.key);
  };
  // 挂载时 默认打开第一个菜单
  // useEffect(() => {
  //   setPage(items[0].key);
    
  // }, []);
  return (
    <>
      { 
        <div 
        style={{
          position: 'fixed',
          left: 0,
          top: 56,
          bottom: 0,
          width: collapsed ? 80 : 160,
          background: '#001529',
          zIndex: 1000,
          transition: 'width 0.2s',
        }}
        >
          <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{ 
              marginBottom: 16 ,
            }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <Menu
            defaultSelectedKeys={[items[0].key]} 
            selectedKeys={[pageData?.title]}
            defaultOpenKeys={["sub1"]}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={items}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 56px)', // 100%可视高度减去按钮区域高度
              overflowY: 'auto'
            }}
          />
        </div>
      }
    </>
  );
  
};
export default App;
