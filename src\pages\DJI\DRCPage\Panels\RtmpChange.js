import { Get2 } from "@/services/general";
const parsedUrl = new URL(window.location.href);

export const onClickZHM = (tt, setP1) => {
  // console.log('onClickZHM', e)

  if (tt === "机场镜头") {
    setP1(GetPlayer("100%", getBodyH(56), 1, device.SN));
    JCStart();
  }
  if (tt === "飞机镜头") {
    setP1(GetPlayer("100%", getBodyH(56), 6, device.SN));
    FJStart();
  }
  if (tt === "飞行地图") {
    setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />);
  }
  if (tt === "三维场景") {
    setP1(<Map3D h1={getBodyH(56)} />);
  }

  if (tt === "识别视频") {
    setP1(GetPlayer("100%", getBodyH(56), 5, device.SN));
  }
};

export const onClickCHM = (tt, setP2) => {
  //  const tt = e.target.innerText;
  if (tt === "机场镜头2") {
    setP2(GetPlayer("300px", "240px", 1, device.SN));
    JCStart();
  }
  if (tt === "飞机镜头") {
    setP2(GetPlayer("300px", "240px", 2, device.SN));
    FJStart();
  }

  if (tt === "三维场景") {
    setP2(<Map3D h1={240} />);
  }
  if (tt === "飞行地图") {
    setP2(<DJBaseMap device={device} sn={device.SN} h1={"240px"} />);
  }

  if (tt === "机场镜头") {
    setP2(GetPlayer("300px", "240px", 3, device.SN));
  }
  if (tt === "识别视频") {
    FJStart2();
    setP2(GetPlayer("300px", "240px", 4, device.SN));
  }
};

function pauseMethod(seconds) {
  return new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}

const JCClose = async (device) => {
  await Get2(
    `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
  );
};

export const FJClose = async (device) => {
  await Get2(
    `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`
  );
  // await pauseMethod(0.3);
};

export const JCStart = async (device) => {
  //await Get2(`/api/v1/Live/RtmpStart?sn=${device.SN}&device=${device.SN}&camera=165-0-7&quality=3`)
  await Get2(`/api/v1/Live/RtmpStartJC?sn=${device.SN}`);
};

export const FJStart = async (device, pd) => {
  if (parsedUrl.protocol === "https:") {
    return await Get2(`/api/v1/Live/RtmpStartFJ?sn=${device.SN}&isSsl=true`);
  }
  await Get2(`/api/v1/Live/RtmpStartFJ?sn=${device.SN}&isSsl=false`);
  //const xx= Get2(`/api/v1/Live/RtmpStartByWebRtc2?sn=${device.SN}&sn2=${pd}&device=${device.SN2}&camera=${device.Camera2}&quality=4`)
  return pd;
};

export const FJStart4 = async (device) => {
  await Get2(`/api/v1/Live/RtmpStartFJForPilot?sn=${device.SN}`);
};


export const reOpenCamera = (device) => {
  // 重载镜头函数
  // 关闭摄像头
  device ? device : device = JSON.parse(localStorage.getItem("device"));
  JCClose(device);
  FJClose(device);
  // 2秒后打开摄像头
  setTimeout(() => {
    JCStart(device);
    FJStart(device);
  }, 2000);
};
