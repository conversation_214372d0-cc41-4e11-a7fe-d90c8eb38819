import { Input, Checkbox, Form, DatePicker,  Select, Button,  message,  } from 'antd';
import  { useState, } from 'react';
import { isEmpty } from '@/utils/utils';
import 'dayjs/locale/zh-cn';
import { HPost2 } from '@/utils/request';
const CronAddForm = (props) => {
  const {sn, wayList,refrush ,modelList} = props;
  const [way, setWay] = useState({})
  const [model, setModel] = useState({})
  const [cls, setCls] = useState([])
  const [name, setName] = useState("")
  const [dType, setDType] = useState("")
  const [content, setContent] = useState("")
  const [phone, setPhone] = useState("")


  const getWaySelect = (wayList) => {
    const list = []
    wayList.forEach(e => {
      list.push(<Select.Option key={e.<PERSON>LineId} data={e} value={e.<PERSON>Id}>{e.WayLineName}</Select.Option>)
    });
    console.log('CronAddForm', list);

    return list;
  }

  const getModelSelect = (modelList) => {
    const list = []
    modelList.forEach(e => {
      list.push(<Select.Option key={e.Guid} data={e} value={e.Guid}>{e.AName}</Select.Option>)
    });
    console.log('CronAddForm', list);

    return list;
  }


  const onSave =async (e) => {
    if (isEmpty(way)) {
      message.info("请先选择航线！");
      return;
    }

    if (isEmpty(model)) {
      message.info("请先选择模型！");
      return;
    }

    if (isEmpty(cls)) {
      message.info("请先选择识别内容！");
      return;
    }


    const user=JSON.parse( localStorage.getItem('user'));
    console.log('CronAddForm', user);

    let aCls=""
    let aClsNM=""
    const cL=model.AList.split(",")
    cls.forEach(e => {
        aCls=aCls+e+","
        aClsNM=aClsNM+cL[e]+","
    });

    aCls = aCls.substring(0, aCls.length - 1);  
    aClsNM = aClsNM.substring(0, aClsNM.length - 1);  

    console.log('ccc',aCls,aClsNM);

    const data = {
      JobName:name,
      WayLineID:way.WanLineId,
      WayLineName:way.WayLineName,
      Model:model.Guid,
      ModelName:model.AName,
      ModelPath:model.ObjectName,
      DangerType:dType,
      DangerContent:content,
      Phone:phone,
      SN:way.SN,
      ACls:aCls,
      AClsNM:aClsNM
    }

   const xx=await  HPost2('/api/v1/AIMediaJob/Add',data);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
    }
    refrush();
  };

  const onChange = (values) => {
    const xx = wayList.find((item) => {
      return item.WanLineId === values;
    })
    setWay(xx);
  };

  const onChange2 = (values) => {
    const xx = modelList.find((item) => {
      return item.Guid === values;
    })
    setModel(xx);
  };



  const onName = (e) => {
    setName(e.target.value);
  };

  const getAList=(m1)=>{
    if(isEmpty(m1)) return <div></div>
    const list=[]
    const  arr = m1.AList.split(",");
    let nn=0;
    arr.forEach(e => {
        list.push( {
          label: e,
          value: nn,
          disabled: false,
        },);
        nn++;
    });
    return <Checkbox.Group options={list}  onChange={(e)=>setCls(e)} />
  }

  return <Form

    labelCol={{
      span: 4,
    }}

    wrapperCol={{
      span: 18,
    }}
    layout="horizontal"
    style={{
      maxWidth: "98%",
    }}
  >


    <Form.Item label="任务名称">
      <Input
        onChange={ (e) => {
          setName(e.target.value);
        }}>

      </Input>
    </Form.Item>

    <Form.Item label="选择航线">
      <Select
        onSelect={onChange}>
        {getWaySelect(wayList)}
      </Select>
    </Form.Item>


    <Form.Item label="选择模型">
      <Select
        onSelect={onChange2}>
        {getModelSelect(modelList)}
      </Select>
    </Form.Item>

    <Form.Item label="识别内容">
      {getAList(model)}
    </Form.Item>

    <Form.Item label="事件标题">
      <Input
        onChange={ (e) => {
          setDType(e.target.value);
        }}>

      </Input>
    </Form.Item>

    <Form.Item label="事件描述">
      <Input
        onChange={ (e) => {
          setContent(e.target.value);
        }}>

      </Input>
    </Form.Item>


    <Form.Item label="通知电话">
      <Input
        onChange={ (e) => {
          setPhone(e.target.value);
        }}>

      </Input>
    </Form.Item>

    <Form.Item label={" "} colon={false}>
      <Button type="primary" onClick={onSave}>
        保存
      </Button>
    </Form.Item>
  </Form>
}

export default CronAddForm;