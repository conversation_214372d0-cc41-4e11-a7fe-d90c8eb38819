# CommonCard 公用卡片组件

一个可复用的卡片容器组件，提供统一的外观和布局结构。

## 功能特性

- 统一的卡片外观样式
- 可自定义标题内容（支持文本或React节点）
- 支持标题右侧额外内容区域
- 灵活的内容区域（通过children传入）
- 支持额外的CSS类名和样式
- 完美适配表格等内容组件

## 使用方法

### 基础用法

```jsx
import CommonCard from '@/components/CommonCard';

// 简单文本标题
<CommonCard title="标题文本">
  <div>卡片内容</div>
</CommonCard>
```

### 自定义标题组件

```jsx
import CommonCard from '@/components/CommonCard';
import LastPageButton from '@/components/LastPageButton';

// 使用组件作为标题
<CommonCard title={<LastPageButton title="页面标题" />}>
  <div>卡片内容</div>
</CommonCard>
```

### 带右侧额外内容的标题

```jsx
import CommonCard from '@/components/CommonCard';
import { Button, Space } from 'antd';

<CommonCard 
  title="数据管理" 
  extra={
    <Space>
      <Button type="primary">新增</Button>
      <Button>导出</Button>
    </Space>
  }
>
  <div>卡片内容</div>
</CommonCard>
```

### 与AutoResizeTable配合使用

```jsx
import CommonCard from '@/components/CommonCard';
import AutoResizeTable from '@/components/AutoResizeTable';
import { Form, Row, Col, Select, Button } from 'antd';

<CommonCard title="数据列表">
  {/* 搜索表单 */}
  <Form style={{ margin: "20px" }}>
    <Row gutter={50}>
      <Col span={6}>
        <Form.Item label="状态" name="status">
          <Select options={statusOptions} />
        </Form.Item>
      </Col>
      <Col span={6}>
        <Form.Item>
          <Button type="primary">查询</Button>
        </Form.Item>
      </Col>
    </Row>
  </Form>
  
  {/* 自适应表格 */}
  <AutoResizeTable
    dataSource={tableData}
    columns={columns}
    pagination={paginationConfig}
    offsetHeight={40} // 考虑表单高度
    rowKey="id"
  />
</CommonCard>
```

### 条件渲染标题

```jsx
import CommonCard from '@/components/CommonCard';

// 根据条件显示不同标题
<CommonCard title={showButton ? <Button>操作按钮</Button> : "普通标题"}>
  <div>卡片内容</div>
</CommonCard>
```

### 添加自定义样式

```jsx
import CommonCard from '@/components/CommonCard';

<CommonCard 
  title="标题" 
  className="custom-class"
  style={{ margin: '20px' }}
>
  <div>卡片内容</div>
</CommonCard>
```

## Props

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| title | string \| ReactNode | - | 卡片标题，可以是文本或React组件 |
| extra | ReactNode | - | 卡片标题右侧的额外内容 |
| children | ReactNode | - | 卡片内容区域 |
| className | string | "" | 额外的CSS类名 |
| style | object | {} | 额外的内联样式 |

## 样式结构

```
.customCard          // 卡片容器 (flex布局)
  ├── .title         // 标题区域（仅在有title时显示）
  │   ├── title内容  // 标题内容
  │   └── .extra     // 右侧额外内容（绝对定位）
  └── .content       // 内容区域 (flex: 1)
```

## 设计理念

1. **Flex布局**：使用flex布局确保内容区域能够自动填充剩余空间
2. **高度自适应**：设置`height: calc(100% - 108px)`，适应不同屏幕尺寸
3. **背景图案**：使用背景图片营造专业的视觉效果
4. **标题设计**：支持复杂的标题布局，包括按钮、图标等组件

## 最佳实践

1. **与AutoResizeTable搭配**：获得完美的表格布局体验
2. **合理使用extra**：在标题右侧放置操作按钮或状态信息
3. **保持样式一致**：所有使用该组件的页面将保持一致的外观
4. **响应式设计**：组件已考虑不同分辨率的适配 