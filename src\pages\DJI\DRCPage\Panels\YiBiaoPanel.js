import { useState } from "react"
import styles from './index.less';
import GrayPanel from './GrayPanel';
import {getGrayStyle,getFontStyle} from './helper';
import BPPanel from './OsdPanel2';
import RulerPanel from './RulerPanel';
import RulerPanel2 from './RulerPanel2';

import hImg from "@/assets/drcImgs/h2.png";
import { useModel } from "umi";
import { isEmpty } from "@/utils/utils";

const windType=["-","正北","东北","东","东南","南","西南","西","西北"]

const YiBiaoPanel=()=>{
    const { drc } = useModel('drcModel');
    const {fj}=useModel('droneModel');
   
    if(isEmpty(fj)||isEmpty(drc)) return <div></div>
    //const v1=<div style={{padding:12.0}}><p style={getFontStyle(18.0)}>红外</p></div>
   // const v2= <div style={{...getGrayStyle(),borderRadius:'5px 5px 0px 0px'}}>{v1}</div>
    const c1='#00DB00'
    const c2='white'

    const panel1=(data)=>{

      return  <div style={{opacity:0.8,height:160,width:480}}>
        <div style={{...getGrayStyle(0,0,40,110)}}><RulerPanel></RulerPanel></div>
        <div style={{...getGrayStyle(0,0,30,140)}}><BPPanel /></div>
        <div style={getFontStyle(75,8,c1,13)}>SPD</div>
        <div style={getFontStyle(90,9,c1,13)}>m/s</div>
        <div style={getFontStyle(70,40,c1,32)}>{data["horizontal_speed"].toFixed(1)}</div>
        {/* <div style={getFontStyle(5,180,c1,24)}>{data.attitude_head.toFixed(0)}</div> */}
        <div style={{...getGrayStyle(0,0,40,264)}}><RulerPanel2></RulerPanel2></div>
        <div style={{...getFontStyle(40,8,c2,13),width:80}}>{windType[data.wind_direction]} {(data.wind_speed/10).toFixed(1)}  m/s</div>
        <div style={{...getFontStyle(120,8,c2,13),width:80}}><img src={hImg} height={18} width={18}/>   {data["home_distance"].toFixed(0)}m</div>
        <div style={getFontStyle(40,90,c2,13)}>{drc.gimbal_pitch?.toFixed(0)}° </div>
        <div style={getFontStyle(70,290,c1,32)}>{data["elevation"].toFixed(1)}</div>
        <div style={getFontStyle(75,380,c1,13)}>ALT</div>
        <div style={getFontStyle(90,388,c1,13)}>m</div>
        <div style={{...getFontStyle(40,324,c2,13),width:80,textAlign:'right'}}>{data.vertical_speed.toFixed(1)} VS</div>
        <div style={{...getFontStyle(120,324,c2,13),width:80,textAlign:'right'}}>{data["height"].toFixed(1)}m ASL</div>
    </div>
    }
    return panel1(fj.data)
}

export default YiBiaoPanel