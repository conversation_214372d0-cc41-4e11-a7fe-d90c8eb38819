.back {
  background: url('@/assets/images/base.png');
  background-size: cover;
  height: 100vh;
}
.top_title_section {
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('@/assets/topTitle.png');
  background-size: cover;
  background-repeat: no-repeat;
  font-size: 1.5vw;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
  font-weight: bolder;
  color: #e3f5fb;
  white-space: nowrap;
  position: relative;
}

.headSetting {
  position: absolute;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  font-size: 13px;
  color: #fff;
  cursor: pointer;
}

.headSettingItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headSettingItem:nth-child(2) {
  margin: 0 20px;
}

.headSettingItem_text {
  margin-left: 10px;
}

.swiper {
  color: white;
  /* height: 500px; */
  width: 100%;
  font-size: 13px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: pointer;
  overflow: hidden;

}

.swiper::-webkit-scrollbar {
  display: none;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 20px;
  width: 100%;
  font-size: 13px;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: pointer;
  height: calc(100vh - 111px);
}

.center {
  justify-content: center;
}

.content:not(.center) {
  justify-content: flex-start;
}

.content::-webkit-scrollbar {
  display: none;
}

.contentItem {
  flex: 1;
  height: 180px;
  text-align: center;
  margin: 0 20px 0 0;
  // box-sizing: border-box;
  width: calc((100% - 60px) / 4);
  min-width: calc((100% - 60px) / 4);
  max-width: calc((100% - 60px) / 4);
  box-shadow: 0 5px 10px rgb(#4fc3c7);
  border: 1.5px solid #8eb0ff;
  cursor: pointer;
  // transform: rotate3d(1, 0, 0, 40deg);


  .pic {
    position: relative;
    width: 100%;
    height: 100%;
    color: #5cc5e8;
    font-weight: bold;
    .picInto {
      width: 100%;
      height: 0px;
      position: absolute;
      visibility: hidden;
      overflow: hidden;
      font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
      color: #ffffff;
      bottom: 0;
      font-weight: lighter;
      transition: all 0.3s;
      background: rgba(rgb(246, 241, 241), 0.6);
    }

    .picInto:hover {
      color: #d88703;
    }
  }

  .pic img:hover {
    transform: scale(1.05);
  }

  .pic:hover .picInto {
    visibility: visible;
    transition: all 0.5s;
    height: 50px;
    line-height: 50px;

  }

  .titleTxt {
    margin-top: 30px;
    background: repeating-linear-gradient(145deg,
        rgb(#0091f8) 0%,
        #ffffff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    // color: rgba(#378aa5,#fff);
    font-weight: bold;
    font-family: kaiti;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    display: -webkit-box;
    /* 创建弹性伸缩块模型容器 */
    -webkit-line-clamp: 3;
    /* 限制显示的行数为3行 */
    -webkit-box-orient: vertical;
    /* 设置伸缩盒子的排列方式为垂直 */
    overflow: hidden;
    /* 溢出隐藏 */
    text-overflow: ellipsis;

    img {
      width: 30px;
      height: 30px;
      margin-right: 1vw;
    }
  }

  transition: 0.4s;

}

.contentItem:hover {
  transform: scale(1.03);
}

.contentItem:nth-of-type(4n + 0) {
  margin-right: 0;
}