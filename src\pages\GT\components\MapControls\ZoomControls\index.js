import React from 'react'
import styles from './index.module.less'

const ZoomControls = ({ onBackToDefault, onZoomIn, onZoomOut, theme = 'dark' }) => {
  const zoomControlsClasses = [
    styles.zoomControls,
    theme === 'dark' ? styles.darkTheme : styles.lightTheme,
  ].join(' ')

  return (
    <div className={zoomControlsClasses}>
      <button
        onClick={onBackToDefault}
        className={`${styles.zoomButton} ${styles.globalButton}`}
      >
      </button>
      <button
        onClick={onZoomIn}
        className={`${styles.zoomButton} ${styles.zoomInButton}`}
      >
      </button>
      <button
        onClick={onZoomOut}
        className={`${styles.zoomButton} ${styles.zoomOutButton}`}
      >
      </button>
    </div>
  )
}

export default ZoomControls