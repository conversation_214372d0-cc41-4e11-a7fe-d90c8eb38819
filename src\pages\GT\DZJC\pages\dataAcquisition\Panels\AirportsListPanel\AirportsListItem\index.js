const AirportsListItem = ({
  item,
  styles,
  getColor,
  changeAlpha,
  getDCColor,
  getStatus,
  onVideoClick,
}) => {
  const { page, setPage } = useModel("pageModel");

  const [showPanel, setShowPanel] = useState(() => {
    const stored = sessionStorage.getItem("showPanel");
    return stored === "true";
  });

  const { MapSelf } = useConfigStore();

  const handleViewVideo = (e, value) => {
    if (onVideoClick) {
      onVideoClick(value);
    }
  };
  const handleSetCenter = (item) => {
    let latlng = [item?.Lat, item?.Lng];
    if (!latlng[0] || !latlng[1]) return;
    if (MapSelf) {
      MapSelf.setView(latlng, 18);
    }
  };
  const handleExecuteSmartSurveyTask = async (record) => {
    console.log("handleExecuteSmartSurveyTask", record);

    try {
      const res = await axiosApi(
        `/api/v1/Survey/ExecuteSmartSurveyTask`,
        "POST",
        { surveyTaskID: record.SurveyTaskId }
      );
      if (res.code === 1) {
        message.success(res.data.message);
        getRecords();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.log("error", error);
      message.error("立即执行失败");
    }
  };

  const renderWayline = (item) => {
    if (!showPanel) return null;
    return (
      <div className={styles["airports-item-detail"]}>
        <div className={styles.routesList}>
          {item.waylineData && item.waylineData.length > 0 ? (
            item.waylineData.map((route, index) => (
              <div
                key={index}
                className={styles.routeItem}
                onClick={(e) => e.stopPropagation()}
              >
                <div className={styles.WayLineName_con}>
                  <span className={styles.WayLineName_icon}></span>
                  <span>{route.WayLineName}</span>
                </div>
                <div className={styles.routeDetail}>
                  <span>创建时间:</span>
                  <span>{timeFormat(route.CreateTime)}</span>
                </div>
                <div className={styles.operateBtns}>
                  <MyButton>查看航线</MyButton>
                  <MyButton onClick={() => handleExecuteSmartSurveyTask(route)}>
                    立即执行
                  </MyButton>
                </div>
              </div>
            ))
          ) : (
            <div className={styles.noRoutes}>
              <p>暂无航线任务</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className={styles["airports-item"]}>
        <div className={styles["airports-item-left"]}>
          <div className={styles["airports-self-icon"]}></div>
          <div
            className={styles["airports-name"]}
            onClick={() => {
              handleSetCenter(item);
            }}
          >
            {item.DName}
          </div>
        </div>

        <div className={styles["airports-item-right"]}>
          <div
            style={{
              color: getColor(item),
              background: changeAlpha(getColor(item), 0.2),
              boxShadow: `inset 0 0 4px 0px ${changeAlpha(getColor(item), 1)}`,
            }}
            className={styles["airports-status"]}
          >
            {item.OsdData ? getStatus(item.OsdData.mode_code) : "离线"}
          </div>
          {item.IfOnLine ? (
            // <div style={{color: getDCColor(item.OsdData?.drone_charge_state?.capacity_percent)}}>
            //   电量:
            //   {item.OsdData?.drone_charge_state?.capacity_percent}%
            // </div>
            <div>
              {isEmpty(item.OsdData) ? null : (
                <span style={{ marginLeft: 8.0 }}>
                  <Progress
                    strokeColor={getDCColor(
                      isIndividual(item)
                        ? item.OsdData.capacity_percent
                        : item.OsdData.drone_charge_state?.capacity_percent
                    )}
                    steps={6}
                    percent={
                      isIndividual(item)
                        ? item.OsdData.capacity_percent
                        : item.OsdData.drone_charge_state?.capacity_percent
                    }
                    format={(percent) => (
                      <span style={{ color: "white", fontSize: 12.0 }}>
                        {percent + "%"}
                      </span>
                    )}
                    size="small"
                  />
                </span>
              )}
            </div>
          ) : (
            <div className={styles["airports-null"]}>空白占位</div>
          )}
          <div
            className={styles["airports-location-icon"]}
            title="设备详情"
            onClick={() => {
              localStorage.setItem("device", JSON.stringify(item));
              setPage({
                title: "设备详情",
                path: "/gt/DZJC/devicePage",
                children: <DevicePage />,
              });
              //  history.push(`/gt/DZJC/devicePage`, { device: item });
            }}
          ></div>
          <div
            className={styles["airports-camera-icon"]}
            title="查看视频"
            onClick={(e) => {
              handleViewVideo(e, item);
            }}
          ></div>
          <div
            onClick={() => {
              const newShow = !showPanel;
              setShowPanel(newShow);
              sessionStorage.setItem("showPanel", newShow.toString());
            }}
          >
            {showPanel ? <UpOutlined /> : <DownOutlined />}
          </div>
        </div>
      </div>
      {renderWayline(item)}
    </div>
  );
};