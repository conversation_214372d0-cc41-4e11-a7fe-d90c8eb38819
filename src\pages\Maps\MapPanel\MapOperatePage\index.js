import { useState } from "react";
import { useEffect } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { isEmpty, getBodyH } from "@/utils/utils";
import LastPageButton from "@/components/LastPageButton";
import { message, Card, Modal, Table, Space, Tag, Select, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import EditTable from "@/pages/Maps/MapPanel/MapOperatePage/edit_table";
import UpdateTable from "@/pages/Maps/MapPanel/MapOperatePage/update_table";
import { Get2, Post2 } from "@/services/general";
import { timeFormat,getGuid } from "@/utils/helper";
import useConfigStore from "@/stores/configStore";
const { confirm } = Modal;
const { Search } = Input;
const MapOperate = ({doNotShowLastButton}) => {
  const [ifLoad, setIfLoad] = useState(true);
  const [mapList, setMapList] = useState([]);
  let [SelectMapType, setSelectMapType] = useState(-1);
  const { tableCurrent, setTableCurrent } = useConfigStore();

  let mapTypeList = [
    { value: -1, label: "全部" },
    { value: 0, label: "普通地图" },
    { value: 1, label: "正射影像" },
    { value: 2, label: "矢量地图" },
    { value: 11, label: "三维模型" },
  ];
  const onSearch = (value, _e, info) => {
    getMList(value);
  };
  const handleTableChange = (page) => {
    setTableCurrent(page);
  };
  const getMList = async (searchValue) => {
    try {
      let pst = await Get2("/api/v1/MapData/GetAllList");
      let arr = [];
      let reg = new RegExp(searchValue, "i");

      if (pst && pst.length > 0) {
        // 选择地图类型，搜索、全部显示、显示指定类型
        if (searchValue) {
          arr = pst.filter((item) => item.MapName.match(reg));
        } else if (SelectMapType == -1) {
          arr = pst;
        } else {
          arr = pst.filter((item) => item.MapType == SelectMapType);
        }
      }

      setIfLoad(false);
      setMapList(arr);
      mapList.push({ MapName: 0 });
    } catch (error) {
      setIfLoad(false);
    }
  };

  useEffect(() => {
    getMList();
  }, [SelectMapType]);

  async function fc(record) {
    await Post2(`/api/v1/MapData/Delete`, record)
      .then((res) => {
        if (isEmpty(res.err)) {
          message.open({ type: "success", content: "已删除此地图!" });
          getMList();
        }
      })
      .catch((err) => {});
  }

  function Delete(record) {
    confirm({
      title: "删除地图",
      content: `确定要删除名为${record.MapName}的地图嘛？`,
      okText: "删除",
      okType: "danger",
      cancelText: "取消",
      onOk() {
        fc(record);
      },
    });
  }
  const TableCols = () => {
    const getMapTypeLabel = (mapType) => {
      const foundType = mapTypeList.find((item) => item.value === mapType);
      return foundType ? foundType.label : "未知类型";
    };
    return [
      {
        title: "地图名称",
        dataIndex: "MapName",
        key: "MapName",
        align: "center",
      },
      {
        title: "地图类型",
        align: "center",
        render: (record) => (
          <Space size="middle">{getMapTypeLabel(record.MapType)}</Space>
        ),
      },
      {
        title: "经纬度",
        align: "center",
        render: (record) => (
          <Space size="middle">{`${record.Lng} , ${record.Lat}`}</Space>
        ),
      },
      {
        title: "缩放级别",
        align: "center",
        render: (record) => (
          <Space size="middle">{`${record.MaxZoom},${record.MinZoom}`}</Space>
        ),
      },
      {
        title: "创建时间",
        align: "center",
        render: (record) => (
          <Space size="middle">{timeFormat(record.CreateTime)}</Space>
        ),
      },
      {
        title: "地图操作",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <Tag>
              <EditTable
                mapList={mapList}
                record={record}
                refrush={getMList}
                key={getGuid()}
              ></EditTable>
            </Tag>
            <Tag>
              <a onClick={() => Delete(record)}>删除</a>
            </Tag>
          </Space>
        ),
      },
    ];
  };

  let extra = (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
        gap: "10px",
      }}
    >
      <Search
        enterButton
        placeholder="输入关键词搜索..."
        onSearch={onSearch}
        key={getGuid()}
      />
      <Select
        allowClear
        style={{ width: 250 }}
        onClear={() => setSelectMapType(null)}
        placeholder={"选择地图类型"}
        onSelect={(e) => setSelectMapType(e)}
      >
        {mapTypeList.map((item) => {
          return (
            <Select.Option key={item.value} data={item.value}>
              {item.label}
            </Select.Option>
          );
        })}
      </Select>
      <UpdateTable mapList={mapList} refrush={getMList} key={getGuid()}></UpdateTable>
    </div>
  );
  return (
    <Card title={doNotShowLastButton ? '地图管理' : <LastPageButton title="地图管理" />} extra={extra}>
      <div style={{ cursor: "pointer" }}>
        <div>
          {isEmpty(mapList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                defaultPageSize: 10,
                defaultCurrent: 1,
                showQuickJumper: true,
                pageSizeOptions: [10, 20, 30, 40, 50],
                showSizeChanger: true,
                locale: {
                  items_per_page: "条/页",
                  jump_tp: "跳至",
                  page: "页",
                },
                current: tableCurrent,
                onChange: handleTableChange,
              }}
              bordered
              dataSource={mapList}
              columns={TableCols()}
              size="small"
              scroll={{ y: getBodyH(276) }}
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default MapOperate;
