import {
  Input,
  Checkbox,
  Form,
  Select,
  Button,
  message,
} from "antd";
import { useState} from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";
const CronAddForm = (props) => {
  const { wayList, refrush, modelList,cronList} = props;
  const [way, setWay] = useState({});
  const [model, setModel] = useState({});
  const [cls, setCls] = useState([]);
  const [name, setName] = useState("");
  const [remarks, setRemarks] = useState("");
  const [PList,setPList] = useState()

  const getWaySelect = (wayList) => {
    const list = [];
    wayList.forEach((e) => {
      list.push(
        <Select.Option key={e.WanLineId} data={e} value={e.WanLineId}>
          {e.WayLineName}
        </Select.Option>
      );
    });
    return list;
  };

  const getModelSelect = (modelList) => {
    const list = [];
    modelList.forEach((e) => {
      list.push(
        <Select.Option key={e.Guid} data={e} value={e.Guid}>
          {e.AName}
        </Select.Option>
      );
    });
    return list;
  };

  const onSave = async (e) => {
    if (isEmpty(name)) {
      message.open({type:'warning',content:"请填写任务名称!"});
      return;
    }

    for(let value of cronList){
      if(name === value.JobName){
        return message.warning("任务名称重复,请换个名称!")
      }
    }
    if (isEmpty(way)) {
      message.open({type:'warning',content:"请选择航线!"});
      return;
    }

    if (isEmpty(model)) {
      message.open({type:'warning', content:"请选择模型!"});
      return;
    }

    if (isEmpty(PList)) {
      message.open({type:'warning',content:"请填写照片点位!"});
      return;
    }

    let aCls = "";
    let aClsNM = "";
    const cL = model.AList.split(",");
    cls.forEach((e) => {
      aCls = aCls + e + ",";
      aClsNM = aClsNM + cL[e] + ",";
    });

    aCls = aCls.substring(0, aCls.length - 1);
    aClsNM = aClsNM.substring(0, aClsNM.length - 1);


    const data = {
      JobName:name,
      WayLineID:way.WanLineId,
      WayLineName:way.WayLineName,
      Model:model.Guid,
      ModelName:model.AName,
      ModelPath:model.ObjectName,
      JobData:PList,
      Remarks:remarks,
      SN:way.SN,
      ACls:aCls,
      AClsNM:aClsNM
    };


    const xx = await HPost2("/api/v1/AIDocJob/Add", data);
    if (isEmpty(xx.err)) {
      message.open({
        type:'success',
        content:"创建成功!"
      });
    }
    refrush();
  };

  const onChange = (values) => {
    const xx = wayList.find((item) => {
      return item.WanLineId === values;
    });
    setWay(xx);
  };

  const onChange2 = (values) => {
    const xx = modelList.find((item) => {
      return item.Guid === values;
    });
    setModel(xx);
  };

  const getAList = (m1) => {
    if (isEmpty(m1)) return <div></div>;
    const list = [];
    const arr = m1.AList.split(",");
    let nn = 0;
    arr.forEach((e) => {
      list.push({
        label: e,
        value: nn,
        disabled: false,
      });
      nn++;
    });
    return <Checkbox.Group options={list} onChange={(e) => setCls(e)} />;
  };

  return (
      <Form
        labelCol={{span: 4,}}
        wrapperCol={{span: 18}}
        layout="horizontal"
        style={{maxWidth: "98%"}}
      >
        <Form.Item label="任务名称">
          <Input
            onChange={(e) => {
              setName(e.target.value);
            }}
            allowClear
          ></Input>
        </Form.Item>

        <Form.Item label="选择航线">
          <Select onSelect={onChange}>{getWaySelect(wayList)}</Select>
        </Form.Item>

        <Form.Item label="选择模型">
          <Select onSelect={onChange2}>{getModelSelect(modelList)}</Select>
        </Form.Item>

        <Form.Item label="照片点位">
          <Input
            onChange={(e) => {
              setPList(e.target.value);
            }}
            allowClear
          ></Input></Form.Item>

        <Form.Item label="备注">
          <Input
            onChange={(e) => {
              setRemarks(e.target.value);
            }}
            allowClear
          ></Input>
        </Form.Item>

        <Form.Item label={" "} colon={false}>
          <Button type="primary" onClick={onSave}>
            保存
          </Button>
        </Form.Item>
      </Form>
  );
};

export default CronAddForm;
