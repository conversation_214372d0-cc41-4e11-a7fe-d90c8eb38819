.segmented {
  column-count: 1;
  /* 根据需要可以设置分段的列数 */
}

.segmented p {
  break-after: always;
  background: linear-gradient(90deg, rgb(81 169 252 / 30%), transparent); 
  padding: 8px ;
  border-style:  solid;
  border-width: 1px 0 1px 1px;
  border-image: linear-gradient(-90deg, rgb(0 96 187 / 30%), #51ecff) 1 ;
 
  /* 每个段落后强制分页 */
}

.segmented p:not(:last-child) {
  margin-bottom: 8px;

    /* 每个段落的底部边距 */
}

.noticeCon {
  
  line-height: 1.6;
  flex: 1;
}

.noticeFoot {
  font-size: 13px;
  border: #3094f0 solid 1px
}

.noticeFoot>div {
  padding: 4px 8px;
  background: linear-gradient(45deg, #3094f0, transparent);
  color: rgb(255 255 255 / 80%);
}

.noticeFoot>div:nth-child(odd) {
  background: linear-gradient(45deg, transparent, #3094f0);
}

.noticeFoot>div+div {
  border-top: #3094f0 solid 1px
}

.noticebox {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: url('@/assets/img/tu.png') ;
}


@media only screen and (max-width: 1334px) {
  .segmented  p {
    font-size: 12px;
}
  /**/
}