import { HGet2 } from '@/utils/request';
import { getBodyH, isEmpty } from '@/utils/utils';
import {Get2, Post2, Post3 } from '@/services/general';



export async function  allCS  (){
  
    await userLoginCS(); 
    await taskListCS();
  //  await UserJiaMi();
    
   // wayLineListCS()
   // beginFly();
   //drawWayLine();
   //deviceListCS();
}


export async function  UserJiaMi  (){
    
    const u1=await HGet2('/api/v1/UserInfo/GetAllList')
    debugger
}


export async function  mimaCS  (){
    
    const data=await Post3('http://223.70.139.221:1800/api/hsm/km/exportPubKey',{"keyIndex":1,"keyType":"RSA","forSign":true})
   // localStorage.setItem('token',data.token);
    debugger
}


export async function  userLoginCS  (){
    //debugger
    //const data=await Post2('/api/v2/User/Login',{Id:'admin',Password:'123456'})
    const data=await HGet2('/api/v2/User/Login?userName='+'admin'+'&password=123456');
    
    localStorage.setItem('token',data.token);
  //  debugger
}

export async function  taskListCS  (){
    const u1=await HGet2('/api/v1/Task/GetListTop10')
    debugger
}

export async function  deviceListCS  (){
    const u1=await HGet2('/api/open/Device/GetAllList')
    debugger
}

export async function  deviceOSDCS  (){
    const u1=await Post2('/api/open/Device/GetJcOsdData1?sn=7CTDM3800BQ680')
   // debugger
}

export async function  wayLineListCS  (){
   //const u1=await Post2('/api/open/WayLine/GetList?pageNum=1&pageSize=10&flightName=固&sn=&startTime=2024-03-18 19:18')
     const u1=await Post2('/api/open/WayLine/GetList?flightType=临时&pageNum=1&pageSize=100')

    debugger
}

export async function  wayLineGetByID  (){
    //const u1=await Post2('/api/open/WayLine/GetList?pageNum=1&pageSize=10&flightName=固&sn=&startTime=2024-03-18 19:18')
    //  const u1=await Post2('/api/open/WayLine/GetList?pageNum=1&pageSize=100')
      const u1=await Get2('/api/open/WayLine/GetByID?id=4b977871-670a-4d86-ae20-2e0dff50d3d6');
      http://10.70.165.48:12311/WayLine/GetByID?id=ba3009fe-e77a-426d-bb1d-b7643fc9edb8
      debugger
 }

export async function  startJCRtmpCS  (){
    const u1=await HGet2('/api/open/Live/RtmpStartJC?sn=7CTDM3800BQ680&quality=3')

    // {
    //     "flv":"http://10.70.165.49:1938/live/7CTDM3800BQ680.flv",
    //     "rtmp":"rtmp://10.70.165.49:1935/live/7CTDM3800BQ680",
    //     "webrtc":"webrtc://10.70.165.49/live/7CTDM3800BQ680",
    //     "whep":"http://10.70.165.49:1985/rtc/v1/whep/?app=live&stream=7CTDM3800BQ680"
    // }

    debugger
    console.log('rtmpcs',u1)
}


export async function  getFJDataCS  (){
    const u1=await HGet2('/api/open/Device/GetFjOsdData?sn=7CTDM3800BQ680')
    debugger
}
export async function  beginFly  (){
    const u1=await Post2('/api/open/WayLine/Fly?fID=24977e61-6b75-4f16-bbb8-c0123352f92f')
    debugger
}


export async function  drawWayLine(){
   const data= {
        "PList":[
            {
                "height": 189.9719,
                "lat": 30.6277757009,
                "lon": 114.0681310642
            },
            {
                "height": 189.9719,
                "lat": 30.6262616228,
                "lon": 114.0679164072
            },
            {
                "height": 189.9719,
                "lat": 30.6254307162,
                "lon": 114.0697839235
            },
            {
                "height": 189.9719,
                "lat": 30.6253753222,
                "lon": 114.0710504001
            }
        ],
        "SN":"7CTDM1200BJ802",
        "R1":-90,
        "IfRecord":0,
        "WName":"测试绘点xx"
    }
    const u1=await Post2('/api/open/WayLine/DrawLineByPList2',data)
    
    debugger
}


export async function  editWayLine(){
    const data= {
         "PList":[
             {
                 "height": 180,
                 "lat": 30.6277757009,
                 "lon": 114.0681310642
             },
             {
                 "height": 189.9719,
                 "lat": 30.6262616228,
                 "lon": 114.0679164072
             },
             {
                 "height": 189.9719,
                 "lat": 30.6254307162,
                 "lon": 114.0697839235
             },
             {
                 "height": 189.9719,
                 "lat": 30.6253753222,
                 "lon": 114.0710504001
             }
         ],
         "FID":"573a53bf-9906-462a-82d5-93d503187d06",
         "R1":-90,
         "IfRecord":0,
         "WName":"测试绘点xx"
     }
     const u1=await Post2('/api/open/WayLine/UpdatePList',data)
     debugger
 }

