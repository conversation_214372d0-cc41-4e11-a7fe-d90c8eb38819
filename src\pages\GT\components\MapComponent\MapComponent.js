import styles from "./MapComponent.module.less";
import { useRef, useState, useEffect } from "react";
import * as turf from "@turf/turf";
import ManagerBox from "@/pages/GT/components/MapComponent/ManagerBox"
import useConfigStore from "@/stores/configStore";

const MapComponent = ({ map }) => {
  const { MapSelf,setMapUrl,setZSMapUrl,isToggleRight } = useConfigStore();
  map = MapSelf;
  const [markersLayerGroup, setMarkersLayerGroup] = useState(L.layerGroup()); //存贮marker
  const [currentMovingMarker, setCurrentMovingMarker] = useState(null); // 跟随鼠标移动的marker
  const [DiatanceGroup, setDistanceGroup] = useState([]); // 存储线段各段的距离
  const [NodeLayerGroup, setNodeLayerGroup] = useState(L.layerGroup()); // 存贮点击添加的红点
  const [DangerLayerGroup, setDangerLayerGroup] = useState(L.layerGroup()); // 存贮点击添加的红点

  const [lineData, setLineData] = useState([]); // 保存绘制线的坐标点数据
  const [tempLine, setTempLine] = useState(null); // 临时绘制的线
  const [drawing, setDrawing] = useState(false); // 是否处于绘制线状态
  const [drawnLayers, setDrawnLayers] = useState([]); // 已经绘制完成的线图层

  const [polygonData, setPolygonData] = useState([]); // 保存绘制面的坐标点数据
  const [tempPolygon, setTempPolygon] = useState(null); // 临时绘制的面
  const [drawingPolygon, setDrawingPolygon] = useState(false); // 是否处于绘制面状态
  const [drawnPolygons, setDrawnPolygons] = useState([]); // 已经绘制完成的面图层
  const [isLnglat, setIsLnglat] = useState(false);
  const [distanceMarkers, setDistanceMarkers] = useState([]); // 使用数组存储多个距离标记
  //创建marker
  const createMarker = (text) => {
    const customIcon = L.divIcon({
      html: `<div style="width: 60px; height: 20px; background-color: rgba(253, 95, 96, 0.5); color: #fff; text-align: center; line-height: 20px;">${text}</div>`,
      iconSize: [60, 20],
      iconAnchor: [30, 30], // [30, 10] 表示水平偏移30像素，垂直偏移10像素
    });
    return customIcon;
  };
  //计算两点距离
  const calculateDistance = (point1LatLng, point2LatLng) => {
    const point1 = turf.point([point1LatLng.lng, point1LatLng.lat]);
    const point2 = turf.point([point2LatLng.lng, point2LatLng.lat]);
    const distance = turf
      .distance(point1, point2, { units: "kilometers" })
      .toFixed(2);
    return distance;
  };
  //点击位置添加marker
  const addMarker = (latlng) => {
    let test = "起点";
    if (lineData.length == 0) {
      test = "起点";
    } else {
      const distance = calculateDistance(lineData[lineData.length - 1], latlng);
      test = distance + " km";
      setDistanceGroup([...DiatanceGroup, distance]);
    }
    const customIcon = createMarker(test);
    const newMarker = L.marker(latlng, { icon: customIcon });
    markersLayerGroup.addLayer(newMarker);
    setMarkersLayerGroup(markersLayerGroup);
  };
  //跟随移动的marker
  const updateMarker = (latlng) => {
    if (drawing && lineData.length > 0) {
      const LastPoint = lineData[lineData.length - 1];
      const distance = calculateDistance(LastPoint, latlng);
      const text = `${distance} km`;
      const customIcon = createMarker(text);
      if (currentMovingMarker) {
        currentMovingMarker.setLatLng(latlng).setIcon(customIcon);
      } else {
        const newMovingMarker = L.marker(latlng, { icon: customIcon });
        markersLayerGroup.addLayer(newMovingMarker);
        setCurrentMovingMarker(newMovingMarker);
        setMarkersLayerGroup(markersLayerGroup);
      }
    }
  };
  //清除所有的Marker
  const clearMarkers = () => {
    markersLayerGroup.clearLayers();
    setMarkersLayerGroup(L.layerGroup());
    setCurrentMovingMarker(null);
    // 清除所有距离标记
    distanceMarkers.forEach((marker) => {
      map.removeLayer(marker);
    });
    setDistanceMarkers([]); // 清空状态
  };

  //清空所有红点
  const clearNodes = () => {
    NodeLayerGroup.clearLayers();
    setNodeLayerGroup(L.layerGroup());
  };
  //左键点击绘制
  const handleMapClick = (e) => {
    if (!drawing && !drawingPolygon) return;

    if (drawing) {
      const newLineData = [...lineData, e.latlng];
      setLineData(newLineData);
      addMarker(e.latlng); // 点击添加marker

      // 如果有两个以上的点，计算并显示距离
      // if (newLineData.length > 1) {
      //   const lastPoint = newLineData[newLineData.length - 2];
      //   const distance = calculateDistance(lastPoint, e.latlng);
      //   const midPoint = [
      //     (lastPoint.lat + e.latlng.lat) / 2,
      //     (lastPoint.lng + e.latlng.lng) / 2,
      //   ];
      //   const newDistanceMarker = L.marker(midPoint, {
      //     icon: createMarker(distance + " km"),
      //   }).addTo(map);
      //   // 将新标记添加到数组中
      //   setDistanceMarkers((prevMarkers) => [
      //     ...prevMarkers,
      //     newDistanceMarker,
      //   ]);
      // }
    }

    if (drawingPolygon) {
      const newPolygonData = [...polygonData, e.latlng];
      setPolygonData(newPolygonData);
    }

    //添加红点用于标记
    const node = L.circle(e.latlng, {
      color: "#fff", // 边框颜色
      fillOpacity: 0, // 填充透明度设置为0，形成镂空效果
      radius: 6, // 圆形标记大小
      weight: 2, // 边框宽度
    }).addTo(map);
    NodeLayerGroup.addLayer(node);
    setNodeLayerGroup(NodeLayerGroup);
  };
  ///移动
  const handleMouseMove = (e) => {
    if (!drawing && !drawingPolygon) return;
    updateMarker(e.latlng); // 更新移动的Marker

    if (drawing) {
      const updatedLine = [...lineData, e.latlng];
      if (tempLine) {
        tempLine.setLatLngs(updatedLine);
      } else {
        const newTempLine = L.polyline(updatedLine, {
          color: "#fd5f60",
          weight: 2,
        }).addTo(map);
        setTempLine(newTempLine);
      }
    }

    if (drawingPolygon) {
      const updatedPolygon = [...polygonData, e.latlng];
      if (tempPolygon) {
        tempPolygon.setLatLngs(updatedPolygon);
        if (updatedPolygon.length > 2) {
          updatedPolygon.push(updatedPolygon[0]);
          const AreaReturn = AreaMeasure(updatedPolygon); //调用函数计算面积
          tempPolygon
            .bindTooltip(`面积: ${AreaReturn} 平方公里`, {
              permanent: true,
              direction: "center",
              className: "polygon-tooltip",
            })
            .openTooltip();
        }
      } else {
        const newTempPolygon = L.polygon(updatedPolygon, {
          color: "#fd5f60",
          weight: 3,
        }).addTo(map);
        setTempPolygon(newTempPolygon);
      }
    }
  };
  //右键停止
  const handleRightClick = () => {
    if (drawing) {
      stopDrawing();
    }
    if (drawingPolygon) {
      stopDrawingPolygon();
    }
  };
  //面积计算
  const AreaMeasure = (polygon) => {
    if (!polygon || polygon.length < 3) {
      return 0; // 多边形至少需要三个点
    }
    const coordinates = polygon.map((latlng) => [latlng.lng, latlng.lat]); // 将经纬度转换为 Turf.js 所需的格式
    const area = turf.area({
      type: "Polygon",
      coordinates: [[...coordinates, coordinates[0]]], // 闭合多边形
    });
    const areaInSqKm = (area / 1000000).toFixed(2); // 转换为平方公里
    return areaInSqKm;
  };

  //开始绘制线
  const startDrawing = () => {
    setIsLnglat(false);
    setLineData([]);
    setDrawing(true);
  };

  //停止绘制线
  const stopDrawing = () => {
    if (lineData.length === 0) {
      setDrawing(false);
      return;
    }
    if (tempLine) {
      map.removeLayer(tempLine);
    }
    const finalLine = L.polyline(lineData, {
      color: "#fd5f60",
      weight: 3,
    });
    finalLine.addTo(map);

    let totalDistance = 0;
    for (let i = 0; i < DiatanceGroup.length; i++) {
      const distance = Number.parseFloat(DiatanceGroup[i]);
      totalDistance = totalDistance + distance;
    }
    finalLine
      .bindTooltip("共:" + totalDistance.toFixed(2) + "千米", {
        permanent: true,
        opacity: 1.0,
        sticky: false,
        direction: "top",
      })
      .openTooltip();
    setDistanceGroup([]);

    setDrawnLayers((prevLayers) => [...prevLayers, finalLine]);
    setDrawing(false);
    setTempLine(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };
  //开始绘制多边形
  const startDrawingPolygon = () => {
    setIsLnglat(false);
    setPolygonData([]);
    setDrawingPolygon(true);
  };

  // 停止绘制多边形
  const stopDrawingPolygon = () => {
    if (polygonData.length === 0) {
      setDrawingPolygon(false);
      return;
    }
    if (tempPolygon) {
      map.removeLayer(tempPolygon);
    }
    const finalPolygon = L.polygon(polygonData, {
      color: "#fd5f60",
      weight: 3,
    });
    finalPolygon.addTo(map);
    const AreaReturn = AreaMeasure(polygonData);
    finalPolygon
      .bindTooltip("总面积:" + AreaReturn + "平方公里", {
        permanent: true,
        opacity: 0.8,
        sticky: false,
        direction: "top",
      })
      .openTooltip();

    setDrawnPolygons((prevPolygons) => [...prevPolygons, finalPolygon]);
    setDrawingPolygon(false);
    setTempPolygon(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };

  //清除所有绘制
  const clearAllDrawings = () => {
    setIsLnglat(false);
    clearDrawnLayers();
    clearDrawnPolygons();
    clearMarkers();
    clearNodes();
  };

  const clearDrawnLayers = () => {
    drawnLayers.forEach((layer) => map.removeLayer(layer)); //清空绘制线图层
    setDrawnLayers([]);
  };

  const clearDrawnPolygons = () => {
    drawnPolygons.forEach((polygon) => map.removeLayer(polygon)); //清空绘制面图层
    setDrawnPolygons([]);
  };

  useEffect(() => {
    if (map) {
      markersLayerGroup.addTo(map);
      NodeLayerGroup.addTo(map);
      map.on("click", handleMapClick);
      map.on("mousemove", handleMouseMove);
      map.on("contextmenu", handleRightClick);
    }

    return () => {
      if (map) {
        map.off("click", handleMapClick);
        map.off("mousemove", handleMouseMove);
        map.off("contextmenu", handleRightClick);
      }
    };
  }, [
    map,
    lineData,
    drawing,
    tempLine,
    drawnLayers,
    polygonData,
    drawingPolygon,
    tempPolygon,
    drawnPolygons,
    markersLayerGroup,
    currentMovingMarker,
    NodeLayerGroup,
    DangerLayerGroup,
  ]);
  let [isOpen,setIsopen] = useState(false)

  const arr = [
    {
      title: "地图",
      child: [
        {
          title: "卫星图",
          url: "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
        },
        {
          title: "电子地图",
          url: "http://wprd04.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}",
        },
        {
          title: "地形渲染图",
          url: "https://services.arcgisonline.com/ArcGIS/rest/services/NatGeo_World_Map/MapServer/tile/{z}/{y}/{x}",
        },
        {
          title: "等高线地图",
          url: "https://c.tile.opentopomap.org/{z}/{x}/{y}.png",
        },
        {
          title: "海洋渲染图",
          url: "https://server.arcgisonline.com/ArcGIS/rest/services/Ocean/World_Ocean_Base/MapServer/tile/{z}/{y}/{x}",
        },
        {
          title: "土地利用分类",
          url: "https://services.terrascope.be/wmts/v2?layer=WORLDCOVER_2021_MAP&style=&tilematrixset=EPSG:3857&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix=EPSG:3857:{z}&TileCol={x}&TileRow={y}&TIME=2022-10-31",
        },
        {
          title: "全球交通地图",
          url: "https://tile.openstreetmap.bzh/br/{z}/{x}/{y}.png",
        },
      ],
    },
    {
      title: "图层",
      child:[
        {
          title:'图层管理',
          function:()=>{
            setIsopen(!isOpen)
          }
        }
      ]
    },
    {
      title: "测量",
      child: [
        {
          title: "测距",
          function: () => {
            console.log("开始测距");
            startDrawing();
          },
        },
        {
          title: "测面",
          function: () => {
            console.log("开始测面");
            startDrawingPolygon();
          },
        },
        {
          title: "取消",
          function: () => {
            console.log("取消测量");
            clearAllDrawings();
          },
        },
      ],
    },
  ];
  const [activeIndex, setActiveIndex] = useState(null);

  const handleParentClick = (value, index) => {
    if (value && !value.child) return;

    // 如果点击的父选项已经是活动的，设置为 null 隐藏子选项
    if (activeIndex === index) {
      setActiveIndex(null);
    } else {
      // 否则设置为当前点击的父选项索引
      setActiveIndex(index);
    }
  };

  const handleChildClick = (value, fc) => {
    console.log(`Clicked on: ${value.title}`);
    if (fc) {
      fc();
    }
    setMapUrl(value.url);
  };
function closeManager(){
  setIsopen(false)
}
  return (
    <>
    <div className={styles.MapComponent}>
    <ManagerBox isOpen={isOpen} onClose={closeManager} updateZSMapUrl={setZSMapUrl} isToggleRight={isToggleRight} />

      <div className={styles.MapComponent_content}>
        {arr.map((item, index) => (
          <div
            key={item.title}
            className={styles.MapComponent_content_item_box}
          >
            <div
              className={styles.MapComponent_content_item}
              onClick={() => handleParentClick(item, index)}
            >
              {item.title}
            </div>

            <div
              className={`${styles.MapComponent_content_item_child_box} ${activeIndex === index ? styles.child_box_active : ""}`}
            >
              {item?.child?.map((child, childIndex) => (
                <div
                  key={child.title}
                  className={`${styles.MapComponent_content_item_child} ${activeIndex === 0 ? styles.MapComponent_content_item_child2 : ""}`}
                  onClick={() => handleChildClick(child, child.function)}
                >
                  {child.title}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
    </>

  );
};

export default MapComponent;
