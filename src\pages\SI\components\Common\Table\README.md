# DynamicDataTable 通用表格组件

一个轻量级、高性能的表格组件，完全兼容 Ant Design Table 的所有属性，并提供额外的工具栏、选择功能和智能自适应高度。

## ✨ 特性

- 🚀 **轻量级设计** - 代码量减少 70%，性能提升
- 📦 **简洁API** - Props 数量减少 80%，更易使用
- 🎯 **完全兼容** - 支持所有 Ant Design Table 原生属性
- 🛠️ **工具栏支持** - 灵活的按钮配置和布局
- ✅ **行选择功能** - 支持单选、多选、批量操作
- 🎨 **自动样式** - 自动处理省略号、tooltip、斑马线条纹
- ⚡ **性能优化** - 使用 useMemo 减少不必要的渲染
- 📏 **智能自适应** - 自动计算表格高度，完美适配容器

## 🚀 快速开始

### 基础用法

```jsx
import DynamicDataTable from './components/Common/Table';

// 最简单的用法
<DynamicDataTable
  dataSource={data}
  columns={columns}
/>
```

### 启用自适应高度

```jsx
// 智能自适应高度 - 无需手动计算offsetHeight
<DynamicDataTable
  dataSource={data}
  columns={columns}
  autoHeight={true}
  pagination={{ pageSize: 10 }}
/>
```

### 在CommonCard中使用自适应高度

```jsx
import CommonCard from '@/components/CommonCard';
import DynamicDataTable from '@/components/DynamicDataTable';

<CommonCard title="数据列表">
  <Form style={{ margin: "20px" }}>
    {/* 搜索表单 */}
  </Form>
  
  <DynamicDataTable
    dataSource={tableData}
    columns={columns}
    autoHeight={true} // 自动检测表单高度，无需手动设置
    pagination={paginationConfig}
    rowKey="id"
  />
</CommonCard>
```

### 带工具栏和选择功能

```jsx
const [selectedRowKeys, setSelectedRowKeys] = useState([]);

const toolbar = {
  buttons: [
    {
      key: 'add',
      text: '新增',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: () => console.log('新增')
    },
    {
      key: 'delete',
      text: '批量删除',
      icon: <DeleteOutlined />,
      needsSelection: true,
      onClick: (keys) => console.log('删除:', keys)
    }
  ],
  align: 'left'
};

const selection = {
  selectedRowKeys,
  onChange: setSelectedRowKeys
};

<DynamicDataTable
  dataSource={data}
  columns={columns}
  autoHeight={true}
  toolbar={toolbar}
  selection={selection}
  pagination={{ pageSize: 10 }}
  striped
/>
```

## 📋 API 文档

### Props

| 参数 | 说明 | 类型 | 默认值 |
|---|---|---|---|
| dataSource | 数据数组 | Array | [] |
| columns | 表格列配置 | Array | [] |
| loading | 加载状态 | boolean | false |
| autoHeight | 启用智能自适应高度 | boolean | false |
| toolbar | 工具栏配置 | Object \| null | null |
| selection | 行选择配置 | Object \| null | null |
| striped | 斑马线条纹 | boolean | true |
| className | 自定义CSS类名 | string | '' |
| style | 自定义样式 | Object | {} |
| rowClassName | 行样式类名 | string \| Function | '' |
| ...tableProps | Ant Design Table 的所有原生属性 | - | - |

### autoHeight 智能自适应功能

**🎯 核心优势：**
- ✅ **零配置**：无需手动计算offsetHeight
- ✅ **智能检测**：自动识别同级元素（表单、工具栏等）高度
- ✅ **响应式**：窗口变化时自动调整
- ✅ **向后兼容**：不影响现有代码

**🔧 工作原理：**
1. 自动检测父容器和同级元素的高度
2. 智能计算表头、分页器、工具栏等组件的占用高度
3. 动态设置表格滚动区域的最佳高度
4. 使用ResizeObserver监听变化，实时调整

**📊 对比传统方案：**

| 特性 | 传统固定高度 | autoHeight智能模式 |
|------|-------------|-------------------|
| 配置复杂度 | 需要手动计算offsetHeight | ✅ 零配置 |
| 响应式支持 | ❌ | ✅ 自动适配 |
| 多场景适用 | ❌ 需要针对每个场景调整 | ✅ 通用解决方案 |
| 维护成本 | 高，每次布局变化需要重新计算 | ✅ 低，自动适配 |

### toolbar 配置

```javascript
toolbar: {
  buttons: [
    {
      key: 'unique-key',        // 按钮唯一标识
      text: '按钮文字',         // 按钮显示文字
      type: 'primary',          // 按钮类型
      icon: <Icon />,           // 按钮图标
      loading: false,           // 加载状态
      disabled: false,          // 禁用状态
      needsSelection: false,    // 是否需要选择数据
      selectionMessage: '提示信息', // 未选择时的提示
      onClick: (selectedKeys, dataSource) => {} // 点击回调
    }
  ],
  align: 'left' // 'left' | 'center' | 'right' | 'space-between'
}
```

### selection 配置

直接使用 Ant Design 的 rowSelection API：

```javascript
selection: {
  selectedRowKeys: [],
  onChange: (keys) => {},
  type: 'checkbox', // 'checkbox' | 'radio'
  // ... 其他 Ant Design rowSelection 属性
}
```

## 🎨 样式特点

### 深色主题设计
- **斑马线条纹**: 奇数行背景 `#0B2222`，偶数行背景 `#081618`
- **深色背景**: 表格采用深色主题，白色文字确保可读性
- **青绿色表头**: 背景 `#08E7CB`（10%透明度），文字 `#08E7CB`（60%透明度）
- **悬停效果**: 鼠标悬停显示深色高亮背景，表头悬停更亮的青绿色
- **选中效果**: 选中行显示深绿色背景
- **自动省略号**: 长文本自动显示省略号和 tooltip

## 🔧 完整示例

```jsx
import React, { useState } from 'react';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import DynamicDataTable from './components/Common/Table';

const Example = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const columns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '年龄', dataIndex: 'age', key: 'age', sorter: true },
    { title: '地址', dataIndex: 'address', key: 'address' },
    // ellipsis 会自动添加，无需手动处理
  ];

  const dataSource = [
    { key: '1', name: '张三', age: 32, address: '北京市朝阳区很长的地址信息' },
    { key: '2', name: '李四', age: 42, address: '上海市普陀区金沙江路 1518 弄' },
  ];

  const toolbar = {
    buttons: [
      {
        key: 'add',
        text: '新增',
        type: 'primary',
        icon: <PlusOutlined />,
        onClick: () => console.log('新增用户')
      },
      {
        key: 'edit',
        text: '编辑',
        icon: <EditOutlined />,
        needsSelection: true,
        onClick: (keys) => console.log('编辑用户:', keys)
      },
      {
        key: 'delete',
        text: '删除',
        icon: <DeleteOutlined />,
        type: 'primary',
        danger: true,
        needsSelection: true,
        selectionMessage: '请选择要删除的用户',
        onClick: (keys) => console.log('删除用户:', keys)
      }
    ],
    align: 'space-between'
  };

  const selection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
    ]
  };

  return (
    <DynamicDataTable
      dataSource={dataSource}
      columns={columns}
      toolbar={toolbar}
      selection={selection}
      pagination={{
        pageSize: 10,
        showQuickJumper: true,
        showSizeChanger: true
      }}
      scroll={{ x: 800 }}
      size="small"
      bordered
      striped
    />
  );
};
```

## 📝 注意事项

1. **ellipsis 处理**：新版本自动为所有列添加 `ellipsis: { showTitle: true }`，如果不需要可以在列定义中设置 `ellipsis: false`

2. **选择状态**：组件不再内部管理选择状态，需要在父组件中管理 `selectedRowKeys`

3. **自定义渲染**：如果需要复杂的自定义渲染，直接在列定义中使用 `render` 函数

4. **样式类名**：`striped` prop 控制斑马线效果，默认开启

## 🎯 性能优化

- 使用 `useMemo` 缓存列配置
- 避免不必要的状态更新
- 减少渲染次数
- 利用 Ant Design 5.x 的内置功能 