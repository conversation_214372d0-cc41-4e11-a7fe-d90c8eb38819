import { useState, useEffect, useRef } from "react";
import {
  Card,
  Table,
  Button,
  message,
  Flex,
  Progress,
  Select,
  Space,
  Col,
  Row,
  Result,
  Tag,
  Tooltip,
} from "antd";
import { MessageOutlined } from "@ant-design/icons";
import styles from "./DeviceLogPage.less";
import ErrJson from "@/pages/DJI/WebsocketDemo/error.json";
import { getBodyH, getDeviceName, isEmpty } from "@/utils/utils";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import { HGet2 } from "@/utils/request";
import { GetMqttClient } from "@/utils/websocket";
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";
import { axiosApi } from "@/services/general";
import { getImgUrl } from "@/utils/utils";
import useConfigStore from "@/stores/configStore";

const ExportLogs = () => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [dataList, setDataList] = useState([]); //从接口获取的所有的设备列表
  const [infodata, setinfodata] = useState([]); //table日志文件
  const [selectDevice, setSelectDevice] = useState(); //下拉列表的选择
  const isAvailable = useRef(); //下载链接是否可用
  const mqttC = useRef({});
  const MqttRequestList = useRef([]); //请求日志列表
  const [logList, setLogList] = useState([]); //存储返回的日志列表
  const [DeviceLogsList, setDeviceLogsList] = useState([]); //localStorage存储的日志列表
  const [minioBucket, setMinioBucket] = useState(); //服务器存储地址

  const DoCMD = (sn, method, data) => {
    console.log("DoCMD", sn, method, data);
    if (isEmpty(sn)) return;
    let s = {};
    s.bid = getGuid();
    s.tid = getGuid();
    s.gateway = sn;
    s.data = data;
    s.timestamp = dayjs().valueOf();
    s.method = method;
    let topic = "thing/product/" + sn + "/services"; //发送请求
    mqttC.current.publish(topic, JSON.stringify(s));
  };

  //启动监听日志上传
  const CmdMqttConn = async () => {
    if (!isEmpty(mqttC.current)) {
      mqttC.current.end();
    }
    const topic = "thing/product/" + selectDevice.SN + "/services_reply";
    //const topic2 = "thing/product/" + selectDevice.SN + "/events";
    mqttC.current = await GetMqttClient();
    mqttC.current.on("message", (topic, message) => {
      updateVal(topic, message);
    });
    mqttC.current.subscribe(topic); //订阅设备日志上传事件
    //mqttC.current.subscribe(topic2);
  };

  //添加对日志上传进度的订阅
  const CmdMqttConn2 = async () => {
    mqttC.current = await GetMqttClient();
    const topic = "thing/product/" + selectDevice.SN + "/events";
    mqttC.current.on("message", (topic, message) => {
      updateVal(topic, message);
    });
    mqttC.current.subscribe(topic); //取消订阅设备日志上传事件
  };

  //处理获取的日志上传进度
  const updateVal = (t1, m1) => {
    const xx = JSON.parse(m1);
    //返回的日志列表
    if (xx.method == "fileupload_start") {
      return;
    }
    if (xx.method == "fileupload_list") {
      console.log(xx);
      setLogList(xx.data.files[0].list);
      let DeviceLogsPullingList = JSON.parse(
        localStorage.getItem("DeviceLogsPullingList")
      );
      let updatedLogList = xx.data.files[0].list;
      if (DeviceLogsPullingList && DeviceLogsPullingList.length) {
        for (let j = 0; j < updatedLogList.length; j++) {
          const element = updatedLogList[j];
          element.progress = 0;
          for (let i = 0; i < DeviceLogsPullingList.length; i++) {
            if (DeviceLogsPullingList[i].boot_index === element.boot_index) {
              updatedLogList.splice(j, 1);
              j--;
            }
          }
        }
      }
      updatedLogList = updatedLogList.sort(
        (a, b) => b.boot_index - a.boot_index
      );
      setinfodata(updatedLogList);
      return;
    }
    //日志上传进度  fileupload_progress
    if (xx.method == "fileupload_progress") {
      let files = xx.data.output.ext.files[0];
      let keyName = files.key; //日志文件：+ XXXXXX  +  boot_index
      let keyParts = keyName.split(":"); //获取[{无关部分},{需要部分}]
      let lastpart = keyParts[1];
      let progressdata = files.progress;
      let status = files.progress.status; //状态
      let result = files.progress.result; //结果
      let device_sn = files.device_sn; //设备SN
      let newprogress = Math.round(
        progressdata.progress / 3 +
          100 * (progressdata.current_step / progressdata.total_step - 1 / 3)
      );
      for (let i = 0; i < infodata.length; i++) {
        if (infodata[i].boot_index == lastpart) {
          infodata.splice(i, 1);
          i--;
        }
      }
      // 进度信息同步到localStorage
      let DeviceLogsPullingList = JSON.parse(
        localStorage.getItem("DeviceLogsPullingList")
      );
      if (DeviceLogsPullingList) {
        let newDeviceLogsPullingList = [...DeviceLogsPullingList];
        newDeviceLogsPullingList.forEach((item, index) => {
          console.log("item", item, "????");
          if (item.boot_index == lastpart) {
            item.progress = newprogress;
            item.status = status;
            item.result = result;
            item.device_sn = device_sn;
          }
        });
        localStorage.setItem(
          "DeviceLogsPullingList",
          JSON.stringify(newDeviceLogsPullingList)
        );
      }
      setinfodata([...infodata]);
    }
  };
  function safeParse(jsonString) {
    if (jsonString === null) {
      return null;
    }
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error("Error parsing JSON:", error);
      return null;
    }
  }

  function useLocalStorageListener(localStorageKey, callback) {
    useEffect(() => {
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = function (key, newValue) {
        const setItemEvent = new CustomEvent("setItemEvent", {
          detail: { key, newValue },
        });
        window.dispatchEvent(setItemEvent);
        originalSetItem.apply(this, [key, newValue]);
      };

      const handleSetItemEvent = (event) => {
        const customEvent = event;
        if (event.detail.key === localStorageKey) {
          // 这里的key就是本地存储对应的key
          const updatedValue = safeParse(customEvent.detail.newValue);
          callback(updatedValue); // 将本地存储最新的值传给回调函数
        }
      };

      window.addEventListener("setItemEvent", handleSetItemEvent);

      return () => {
        window.removeEventListener("setItemEvent", handleSetItemEvent);
        localStorage.setItem = originalSetItem;
      };
    }, [localStorageKey, callback]);
  }
  useLocalStorageListener(
    "DeviceLogsPullingList",
    (newDeviceLogsPullingList) => {
      console.log("DeviceLogsPullingList最新值：", newDeviceLogsPullingList);
      setDeviceLogsList(newDeviceLogsPullingList);
      // for (let i = 0; i < newDeviceLogsPullingList.length; i++) {
      //   if ((newDeviceLogsPullingList[i].progress === 100)) {
      //     newDeviceLogsPullingList.splice(i, 1)
      //     i--
      //   }
      // }
      // setDeviceLogsList(newDeviceLogsPullingList)
    }
  );
  //请求数据，setDataList
  useEffect(() => {
    setDataList([]);
    setinfodata([]);
    const getDList = async () => {
      const result = await HGet2("/api/open/Device/GetAllList");
      let DeviceOnline = [];
      result.map((e) => {
        if (e.OsdData) {
          DeviceOnline.push(e);
        }
      });
      let devicedata = [];
      for (let i = 0; i < DeviceOnline.length; i++) {
        devicedata.push({
          label: "机场" + "--" + DeviceOnline[i].DName,
          value: DeviceOnline[i].SN + DeviceOnline[i].DName,
          SN: DeviceOnline[i].SN,
          module: "3",
        });
        devicedata.push({
          label: "无人机" + "--" + DeviceOnline[i].DName,
          value: DeviceOnline[i].SN2 + DeviceOnline[i].DName,
          SN: DeviceOnline[i].SN,
          module: "0",
        });
      }
      if (devicedata && devicedata.length > 0) {
        setDataList(devicedata);
      }
    };

    const getMinioBucket = async () => {
      const result = await HGet2("/api/v2/LogFile/DownloadToken"); //从后端获取请求数据（存贮位置、访问密钥等）
      setMinioBucket(
        result?.endpoint + result?.bucket + "/" + result?.object_key_prefix
      );
    };
    getDList(); //用于获取SN,SN2
    // getMinioBucket(); //用于获取服务器存储地址
    let DeviceLogsPullingList = JSON.parse(
      localStorage.getItem("DeviceLogsPullingList")
    );
    DeviceLogsPullingList = Array.isArray(DeviceLogsPullingList)
      ? [...DeviceLogsPullingList].sort((a, b) => b.boot_index - a.boot_index)
      : [];

    setDeviceLogsList(DeviceLogsPullingList);
  }, []); // 添加空依赖数组，确保只在组件挂载时调用

  useEffect(() => {
    // if (logList) {
    //   const updatedLogList = logList.map((item) => ({
    //     ...item,
    //     progress: 0,
    //   }));
    //   setinfodata(updatedLogList);
    // }
  }, [logList]);

  //请求日志
  const GetSystemInfo = () => {
    if (isEmpty(selectDevice)) {
      message.error("请选择设备");
      return;
    }
    if (!isEmpty(mqttC.current)) {
      mqttC.current.end();
    } //清空mqtt连接
    setinfodata([]); //清空已有的日志
    const data = {
      module_list: [selectDevice.module],
    };
    CmdMqttConn(selectDevice.SN);
    DoCMD(selectDevice.SN, "fileupload_list", data);
  };

  //请求上传到服务器
  const handleDownload = async (boot_index, record) => {
    let DeviceLogsPullingList = JSON.parse(
      localStorage.getItem("DeviceLogsPullingList")
    );
    let newDeviceLogsPullingList = DeviceLogsPullingList
      ? [...DeviceLogsPullingList]
      : [];
    newDeviceLogsPullingList.push(record);
    localStorage.setItem(
      "DeviceLogsPullingList",
      JSON.stringify(newDeviceLogsPullingList)
    );
    let newInfodata = [];
    for (let i = 0; i < infodata.length; i++) {
      if (infodata[i].boot_index !== boot_index) {
        newInfodata.push(infodata[i]);
      }
    }
    setinfodata(newInfodata);

    let data = {
      SN: (selectDevice && selectDevice.SN) || (record && record.device_sn),
      module: selectDevice.module,
      boot_index: boot_index,
    };
    const result = await axiosApi("/api/v1/LogFile/PushLogs", "POST", data);
    if (result && result.code === 1) {
      CmdMqttConn2();
    }
    // DoCMD(selectDevice.SN, "fileupload_start", result);
  };

  //判断链接是否存在
  const checkDownloadUrl = async (url) => {
    try {
      const response = await fetch(url, { method: "HEAD" });
      if (response.ok) {
        isAvailable.current = true;
      } else {
        isAvailable.current = false;
      }
    } catch (error) {
      isAvailable.current = false;
    }
  };

  //确定已经存在日志，开始下载
  const StartDown = (url) => {
    const link = document.createElement("a");
    link.href = url;
    link.download = "日志文件"; // 可选，指定下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  //选择机场/飞行器
  const SelectChange = (e) => {
    for (let i = 0; i < dataList.length; i++) {
      const item = dataList[i];
      if (item.value === e) {
        if (item.module == "0") {
          message.info("请先将无人机开机");
        }
        setSelectDevice(item);
        break; // 跳出所有迭代
      }
    }
  };

  //判断日志是否存在，存在则下载
  const downFile = async (boot_index) => {
    if (!selectDevice || !selectDevice.SN) {
      message.info("未选择设备/设备为空");
      return;
    }
    const url = getImgUrl(
      `debugLogs/${selectDevice.SN}/日志文件:` + boot_index
    );
    //"https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/%E6%97%A5%E5%BF%97%E6%96%87%E4%BB%B6%3A" +
    await checkDownloadUrl(url);

    if (isAvailable.current) {
      StartDown(url);
    } else {
      message.info("没有该文件，请先将文件上传服务器");
    }
  };

  const columns = [
    {
      title: "文件索引",
      dataIndex: "boot_index",
      key: "boot_index",
      align: "center",
    },
    {
      title: "日志开始时间",
      dataIndex: "start_time",
      key: "start_time",
      align: "center",
      render: (text) => new Date(text * 1000).toLocaleString(),
    },
    {
      title: "日志结束时间",
      dataIndex: "end_time",
      key: "end_time",
      align: "center",
      render: (text) => new Date(text * 1000).toLocaleString(),
    },
    {
      title: "日志文件大小",
      dataIndex: "size",
      key: "size",
      align: "center",
      render: (text) => {
        const sizeInMB = (text / (1024 * 1024)).toFixed(2); // 保留两位小数
        return `${sizeInMB} MB`;
        //return `${text} Bytes`;
      },
    },
    {
      title: "拉日志到服务器",
      key: "action",
      align: "center",
      render: (text, record) => (
        <div>
          <Button
            type="primary"
            onClick={() => handleDownload(record.boot_index, record)}
          >
            拉取
          </Button>

          <Progress
            type="circle"
            percent={record.progress}
            style={{ marginLeft: 12.0 }}
            size={35}
            key={record.boot_index}
          />
        </div>
      ),
    },

    {
      title: "操作",
      key: "action",
      align: "center",
      render: (text, record) => (
        <div>
          <Button
            style={{ marginLeft: 8 }}
            onClick={() => downFile(record.boot_index)}
          >
            下载
          </Button>
        </div>
      ),
    },
  ];
  const columns2 = [
    {
      title: "文件索引",
      dataIndex: "boot_index",
      key: "boot_index",
      align: "center",
    },
    {
      title: "日志开始时间",
      dataIndex: "start_time",
      key: "start_time",
      align: "center",
      render: (text) => new Date(text * 1000).toLocaleString(),
    },
    {
      title: "日志结束时间",
      dataIndex: "end_time",
      key: "end_time",
      align: "center",
      render: (text) => new Date(text * 1000).toLocaleString(),
    },
    {
      title: "日志文件大小",
      dataIndex: "size",
      key: "size",
      align: "center",
      render: (text) => {
        const sizeInMB = (text / (1024 * 1024)).toFixed(2); // 保留两位小数
        return `${sizeInMB} MB`;
        //return `${text} Bytes`;
      },
    },
    {
      title: "拉日志到服务器",
      key: "action",
      align: "center",
      render: (text, record) => {
        // record.result = 324019
        console.log("record.result", record);

        function failedFc(record) {
          if (record?.status === "failed") {
            return (
              <div>
                <Tooltip title={ErrJson[record?.result] || "未知错误"}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 8,
                    }}
                  >
                    <MessageOutlined style={{ color: "orange" }} />
                    <Tag
                      type="primary"
                      key="console"
                      onClick={() => handleDownload(record.boot_index, record)}
                    >
                      <a>重新拉取</a>
                    </Tag>
                  </div>
                </Tooltip>
              </div>
            );
          }
        }
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "space-around",
              alignItems: "center",
            }}
          >
            <Progress
              type="circle"
              percent={record.progress}
              style={{ marginLeft: 12.0 }}
              size={35}
              key={record.boot_index}
            />
            {failedFc()}
          </div>
        );
      },
    },

    {
      title: "操作",
      key: "action",
      align: "center",
      render: (text, record) => (
        <div>
          <Button
            style={{ marginLeft: 8 }}
            onClick={() => downFile(record.boot_index)}
          >
            下载
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div
      className={styles["DeviceLogPage"]}
      style={{ margin: 0, height: getBodyH(56), background: "#F5F5FF" }}
    >
      <Card
        title={
          <Row align="middle" justify="space-between">
            <Col>
              <LastPageButton title="日志信息" />
            </Col>{" "}
            <Col>
              <Space>
                <label>在线设备：</label>
                <Select
                  style={{
                    width: 200,
                  }}
                  onChange={SelectChange}
                  options={dataList}
                />
                <Button onClick={GetSystemInfo}>请求日志</Button>
              </Space>
            </Col>
          </Row>
        }
        bordered={false}
      >
        <div>
          {isEmpty(infodata) ? (
            <div />
          ) : (
            <Table
              pagination={{
                pageSize: DeviceLogsList
                  ? 10 -
                    ((DeviceLogsList.length < 5 ? DeviceLogsList.length : 4) -
                      1)
                  : 10,
                showSizeChanger: false,
                 current: tableCurrent,
                onChange: setTableCurrent,
              }} //不要分页
              bordered
              dataSource={infodata} // 使用 infodata 作为数据源
              columns={columns} // 使用定义的列
              size="small"
            />
          )}
          {DeviceLogsList && DeviceLogsList.length !== 0 && (
            <Table
              pagination={{
                pageSize: DeviceLogsList.length < 5 ? DeviceLogsList.length : 4,
                showSizeChanger: false,
              }} //不要分页
              bordered
              dataSource={DeviceLogsList} // 使用 infodata 作为数据源
              columns={columns2} // 使用定义的列
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default ExportLogs;
