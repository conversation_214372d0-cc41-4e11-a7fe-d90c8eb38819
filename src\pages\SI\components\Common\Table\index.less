/* DynamicDataTable 自定义样式 */

/* 表格容器样式 */
.dynamic-table-container {
  position: relative;
  
  /* 自适应高度模式 */
  // &.auto-height-enabled {
  //   height: 100%;
  //   display: flex;
  //   flex-direction: column;
    
  //   /* 确保表格包装器能够正确填充父容器 */
  //   :global(.ant-table-wrapper) {
  //     flex: 1;
  //     display: flex;
  //     flex-direction: column;
  //   }
    
  //   /* 让表格主体区域占据剩余空间 */
  //   :global(.ant-table) {
  //     flex: 1;
  //   }
    
  //   /* 确保表格容器的高度设置正确 */
  //   :global(.ant-table-container) {
  //     flex: 1;
  //     display: flex;
  //     flex-direction: column;
  //   }
    
  //   /* 表格主体部分 */
  //   :global(.ant-table-body) {
  //     flex: 1;
  //     overflow-y: auto;
  //   }
    
  //   /* 分页器样式调整 */
  //   :global(.ant-pagination) {
  //     margin-top: 16px;
  //     text-align: center;
  //     flex-shrink: 0; /* 防止分页器被压缩 */
  //   }
  // }
}

/* 表格基本样式 - 深色主题 */
.dynamic-table {
  background-color: #0B2222; /* 深色背景 */
  color: #ffffff; /* 白色文字 */

  /* 表格整体背景 */
  .ant-table {
    background-color: #0B2222;
    color: #ffffff;
  }

  /* 表头样式 - 青绿色主题 */
  .ant-table-thead > tr > th {
    background-color: rgba(8, 231, 203, 0.1); /* #08E7CB 10% 透明度 */
    border-bottom: 1px solid #2a4a4a; /* 深色边框 */
    color: rgba(8, 231, 203, 0.6); /* #08E7CB 60% 透明度 */

    /* 表头悬停效果 */
    &:hover {
      background-color: rgba(8, 231, 203, 0.15) !important;
    }

    /* 移除表头单元格之间的分割线 */
    &:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      display: none; /* 移除表头单元格右侧分割线，这是 Antd 默认行为 */
    }
  }

  /* 行样式 - 深色主题 */
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #2a4a4a; /* 深色边框 */
    border-right: none; /* 移除单元格右侧边框，保持 Antd 默认 */
    background-color: #0B2222; /* 默认行背景 */
    color: #ffffff; /* 白色文字 */
  }

  /* 斑马线条纹效果 - 深色主题 */
  .dynamic-table-row {
    transition: background-color 0.3s;

    /* 偶数行背景色 - 使用更深的颜色 */
    &.dynamic-table-row-even td {
      background-color: #081618 !important; /* 更深的背景色 */
    }

    /* 悬停效果 - 深色主题适配 */
    &:hover > td {
      background-color: #1a4a4a !important; /* 深色悬停效果 */
    }

    /* 偶数行悬停效果 */
    &.dynamic-table-row-even:hover > td {
      background-color: #2a5a5a !important; /* 偶数行悬停时的颜色 */
    }
  }

  /* 选中行样式 - 深色主题适配 */
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #1e4a2a !important; /* 深绿色选中背景 */

    /* 确保选中行的悬停效果 */
    &:hover {
      background-color: #2e5a3a !important; /* 选中行悬停时的颜色 */
    }
  }

  /* 排序图标颜色调整 - 青绿色主题 */
  .ant-table-column-sorter {
    color: rgba(8, 231, 203, 0.6);

    /* 排序图标激活状态 */
    &-up.active,
    &-down.active {
      color: rgba(8, 231, 203, 0.9);
    }
  }

  /* 筛选图标颜色调整 - 青绿色主题 */
  .ant-table-filter-trigger {
    color: rgba(8, 231, 203, 0.6);

    /* 筛选图标激活状态 */
    &.active {
      color: rgba(8, 231, 203, 0.9);
    }
  }

  /* 选择框样式调整 */
  // .ant-checkbox-wrapper {
  //   color: #ffffff;
  // }

  // .ant-checkbox-inner {
  //   background-color: #081618;
  //   border-color: #2a4a4a;
  // }

  // .ant-checkbox-checked .ant-checkbox-inner {
  //   background-color: #1890ff;
  //   border-color: #1890ff;
  // }

  /* 展开图标颜色调整 */
  .ant-table-row-expand-icon {
    background-color: #081618;
    border-color: #2a4a4a;
    color: #ffffff;
  }

  /* 空状态样式 */
  // .ant-empty {
  //   margin: 32px 0;

  //   .ant-empty-description {
  //     color: #ffffff;
  //   }
  // }

  /* 有边框模式下的特殊处理 */
  &.ant-table-bordered {
    .ant-table-tbody > tr > td,
    .ant-table-thead > tr > th {
      border-color: #2a4a4a;
    }
  }

  /* 小尺寸表格的斑马线调整 */
  &.ant-table-small .dynamic-table-row-even td {
    background-color: #081618 !important;
  }

  /* 中等尺寸表格的斑马线调整 */
  &.ant-table-middle .dynamic-table-row-even td {
    background-color: #081618 !important;
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  .dynamic-table-container {
    overflow-x: auto;
  }
  
  .dynamic-table {
    min-width: 600px;
  }
} 