import { getBodyH, getBodyW2, isEmpty } from "@/utils/utils";
import { useEffect, useState, useRef } from "react";
import { RollbackOutlined } from '@ant-design/icons';
import { But<PERSON> } from "antd";
import styles from "./index.less";
import DJBaseMap from "@/pages/Maps/DJBaseMap";
import JCXX from "@/pages/DJI/DevicePage/JXSSPanel";
import IfShowPanel from "@/components/IfShowPanel";
import HmsPanel from "@/pages/DJI/HmsPage/HmsPanel";
import GetPlayer from "@/pages/DJI/DevicePage/PlayerPanel";
import CameraPanel2 from "@/pages/DJI/DevicePage/Panels/CameraPanel2";
import PageButton from "@/pages/DJI/DevicePage/pageButton";
import RtmpButton from "@/pages/DJI/DevicePage/RtmpButton";
import ToggleBaseMapButton from "@/pages/DJI/DevicePage/ToggleBaseMapButton";
import ToggleMapTypeButton from "@/pages/DJI/DevicePage/ToggleMapTypeButton";
import FJInfoPanel from "@/pages/DJI/DevicePage/FJInfoPanel";
import FlyButtonPanel from "@/pages/DJI/FlyToPage/FlyButtonPanel";
import LevelPanel from "@/pages/DJI/DevicePage/Panels/LevelPanel";
import Map3D from "@/pages/Cesium";
import { FJStart, JCStart, FJStart4 } from "@/pages/DJI/DRCPage/Panels/RtmpChange";
import CameraPanel from "@/pages/DJI/DevicePage/Panels/CameraPanel";
import { useModel,useLocation, history } from "umi";
import CameraCtrPanel from "@/pages/DJI/FlyToPage/CameraCtrPanel";
import BianjiaoPanel from "@/pages/DJI/DevicePage/Panels/BianJiaoPanel";
import AircraftLensPanel from "@/pages/DJI/DRCPage/Panels/AircraftLensPanel";
import IrSetColor from "@/pages/DJI/DevicePage/Panels/IrSetColor";
import GasDetectorPanel from "@/pages/DJI/DevicePage/GasDetectorPanel";

const DevicePage = ({ device }) => {
  
  if (isEmpty(device)) {
    device = JSON.parse(localStorage.getItem("device"));
  }
  const [isDanBin, setIsDanbBin] = useState(device.BindCode === "pilot");
  const [isDanBinPlus2, setIsDanbBinPlus2] = useState(
    device.BindCode === "pilot" && device.Model === "RC_PLUS_2"
  );
  const [p1, setP1] = useState(<DJBaseMap device={device} h1={getBodyH(56)} />);
  const [p2, setP2] = useState(GetPlayer("100%", "100%", 3));
  const [ifX, setIfX] = useState(true);
  const [ifY, setIfY] = useState(true);
  const [ifY2, setIfY2] = useState(true);
  const [ifY3, setIfY3] = useState(true);
  const [ifY4, setIfY4] = useState(true);
  const [showSlidingBar, setShowSlidingBar] = useState(false);
  const { PDataInit, pData } = useModel("pageModel");
  const { MqttConnect } = useModel("mqttModel");
  const [ifShuaXin, setIfShuaXin] = useState(false);
  const { fjVideo } = useModel("stateModel");
  const [pNM1, setPNM1] = useState(pData.current.pNM1);
  const [pNM2, setPNM2] = useState(pData.current.pNM2);

  useEffect(() => {
    setPNM1(pData.current.pNM1);
  }, [pData.current.pNM1]);

  useEffect(() => {
    setPNM2(pData.current.pNM2);
  }, [pData.current.pNM2]);

  const onClickYY = () => {
    setIfY(!ifY);
  };

  useEffect(() => {
    MqttConnect(device);
    setP1(<DJBaseMap device={device} sn={device.SN} h1={getBodyH(56)} />);
    setP2(GetPlayer("300px", "240px", 3, device.SN));

    if (isDanBin) {
      //如果是单兵，则默认显示飞机镜头
      FJStart4(device);
      pData.current.pNM1 = "飞机镜头";
    } else {
      JCStart(device);
    }
    PDataInit(device.SN);
    return () => {};
  }, []);

  const getPanel = (tt, ww1, hh1) => {
    device = JSON.parse(localStorage.getItem("device"));
    if (tt === "机场镜头") {
      return GetPlayer(ww1, hh1, 3, device.SN);
    }
    if (tt === "飞机镜头") {
      if (isDanBin) {
        return GetPlayer(ww1, hh1, 12, device.SN2);
      } else {
        startLive(tt);
        if (pData.current.ifAI) {
          return GetPlayer("100%", hh1, 12, device.SN2 + "ai");
        }
        return GetPlayer(ww1, hh1, 12, device.SN2);
      }
    }

    if (tt === "飞行地图") {
      if (hh1 == "240px") {
        return (
          <DJBaseMap
            showBZBtn={false}
            device={device}
            sn={device.SN}
            h1={hh1}
            isDrc={true}
          />
        );
      }
      return (
        <DJBaseMap showBZBtn={true} device={device} sn={device.SN} h1={hh1} />
      );
    }

    if (tt === "三维场景") {
      return <Map3D h1={hh1} />;
    }
  };

  useEffect(() => {
    const div1 = getPanel(pNM1, "100%", getBodyH(59));
    setP1(div1);
    const div2 = getPanel(pNM2, "300px", "240px");
    setP2(div2);
  }, [pNM1, pNM2, pData.current.ifAI, ifShuaXin]);

  const startLive = (tt) => {
    if(tt=="飞机镜头"){
    if (isDanBin) {
      FJStart4(device);
    } else {
      FJStart(device, device.SN2);
    }
    }
  };

  const onClickZHM = async (e) => {
    const tt = e.target.innerText;
    startLive(tt);
    pData.current.pNM1 = tt;
    setPNM1(tt);
  };

  const onClickCHM = async (e) => {
    const tt = e.target.innerText;
    startLive(tt);
    pData.current.pNM2 = tt;
    setPNM2(tt);
  };

  const btnPanel = (
    <div className={styles.sideToolHead}>
      {(!isDanBin || isDanBinPlus2) && <RtmpButton sn={device.SN} />}
      {PageButton("主画面", onClickZHM, !isDanBin)}
      {(!isDanBin || isDanBinPlus2) && PageButton("画中画", onClickCHM)}
      {(!isDanBin || isDanBinPlus2) && (
        <Button type="text" onClick={() => onClickYY()}>
          {ifY ? "隐藏" : "显示"}
        </Button>
      )}
    </div>
  );

  const xxx = <div style={{ height: 24.0, background: "red" }}></div>;
  const jcNoDiv = (
    <div
      onClick={() => setIfY2(true)}
      style={{
        zIndex: 1010,
        background: "rgba(44, 131, 183)",
        userSelect: "none",
        cursor: "pointer",
        color: "white",
        padding: 8.0,
        writingMode: "vertical-rl",
        borderRadius: "0px 5px 5px 0px",
        position: "absolute",
        top: 8,
        left: 0,
      }}
    >
      机场信息
    </div>
  );
  const fjNoDiv = (
    <div
      onClick={() => setIfY3(true)}
      style={{
        zIndex: 1010,
        background: "rgba(44, 131, 183)",
        userSelect: "none",
        cursor: "pointer",
        color: "white",
        padding: 8.0,
        writingMode: "vertical-rl",
        borderRadius: "0px 5px 5px 0px",
        position: "absolute",
        top: 280,
        left: 0,
        height: 80,
      }}
    >
      飞行信息
    </div>
  );
  const GasDetectorNoDiv = (
    <div
      onClick={() => {
        setIfY4(true);
      }}
      style={{
        zIndex: 1010,
        background: "rgba(44, 131, 183)",
        userSelect: "none",
        cursor: "pointer",
        color: "white",
        padding: 8.0,
        borderRadius: "0px 5px 5px 0px",
        position: "absolute",
        bottom: 0,
        left: 0,
        height: 30,
      }}
    >
      气体检测仪
    </div>
  );

  return (
    <div className={styles.IndexPageStyle}>
      <div className="container">
        {p1}
        <div className={styles.sideTool}>
          {btnPanel}
          {(!isDanBin || isDanBinPlus2) && (ifY ? p2 : null)}
        </div>
        <LevelPanel />
        {/* <div style={{position:"absolute",zIndex:9999,top:0}}>
        <Button onClick={() => history.back()} icon={<RollbackOutlined />}>返回</Button>
        </div> */}
        {/* 机场信息面板 */}
        {!isDanBin &&
          IfShowPanel(
            260,
            260,
            8,
            8,
            null,
            null,
            <JCXX setIfY={setIfY2} device={device} />,
            ifY2,
            "none",
            jcNoDiv
          )}
        {IfShowPanel(
          290,
          260,
          280,
          8,
          null,
          null,
          <FJInfoPanel sn={device.SN} setIfY={setIfY3} />,
          ifY3,
          "none",
          fjNoDiv
        )}
        {/* 气体监测仪面板 */}
        {IfShowPanel(
          260,
          300,
          8,
          420,
          null,
          null,
          <GasDetectorPanel sn={device.SN} setIfY={setIfY4}></GasDetectorPanel>,
          ifY4,
          "none",
          GasDetectorNoDiv
        )}
        {IfShowPanel(100, 800, 20, 250, 8, null, HmsPanel(), true)}
        {/* {IfShowPanel(260, 300, 290, null, 8, null, JoyStickPanel(), ifY)} */}
        {/* {IfShowPanel(28, getBodyW2(200), getBodyH(86), 80, null, 0, FJCtrPanel(device.SN), ifY)} */}
        {(!isDanBin || isDanBinPlus2) &&
          IfShowPanel(
            48,
            getBodyW2(200),
            getBodyH(136),
            "50%",
            null,
            0,
            <FlyButtonPanel device={device} ></FlyButtonPanel>,
            true
          )}
        {/* 变焦镜头滑动条 */}
        {IfShowPanel(
          null,
          null,
          -135,
          1500,
          null,
          null,
          <BianjiaoPanel></BianjiaoPanel>,
          showSlidingBar
        )}
        {!isDanBin && <CameraCtrPanel sn={device.SN} fj={null} />}
        {!isDanBin && <CameraPanel />}
        {/* 飞机镜头 相关信息 RTK GPS 4G SDR 电池电量 */}
        {/* { pData.current.pNM1 === "飞机镜头" && <AircraftLensPanel /> } */}
        {/* 无人机直播镜头控制面板 */}
        {!isDanBin && (
          <CameraPanel2
            setShowSlidingBar={setShowSlidingBar}
            onFrush={() => setIfShuaXin(!ifShuaXin)}
          ></CameraPanel2>
        )}
        <ToggleBaseMapButton />
        <ToggleMapTypeButton onMapTypeChange={setPNM1} />
        {/* {IfShowPanel(160,360,getBodyH(300),600,null,null,<div style={{background:getBaseColor()}}>{cmdPanel}</div>,ifCMDPanel)}
         */}
        {/* {IfShowPanel(28, 200, getBodyH(100), null, 8, 0, <div style={{ height: 36, width: 200 }}><img style={{ height: 36 }} height={36} width={150} src={BDZL}></img></div>, true)} */}
        {fjVideo.video_type == "ir" ? <IrSetColor /> : null}
        {/* {
            !isDanBin ||isDanBinPlus2?<div style={{backgroundColor:'red'}}>
              jkj label
            </div>:<div style={{backgroundColor:'green'}}>
              ppppppp
            </div>
          } */}
      </div>
      <div className={styles.shadow}></div>
    </div>
  );

};

export default DevicePage;
