import { useState, useEffect, useRef } from 'react';
import { message, Select, InputNumber, Input, Slider, Button, Tooltip, Card } from 'antd';
import { useModel } from 'umi';
import { getBodyH, isEmpty } from '@/utils/utils';
import MapControl from '@/hooks/mapControl';
import { computeGSD, computeHeight, computeIntervalTime, EnumTomodel } from '@/utils/cesium_help';
import { axiosApi } from '@/services/general';
import { queryPage2 } from '@/utils/MyRoute';
import usePlanarRouteHooks from './hooks/PlanarRouteHooks';
import LastPageButton from "@/components/LastPageButton";
import "./index.less";

const CesiumTest = ({ wayLineInfo }) => {
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    let [viewerData, setviewerData] = useState(null)
    let { wayline, setWayline, modelChange, RouteFeedback, waylineChange, cameraFlyTo } = usePlanarRouteHooks(getWayline, setviewer, setModel);
    let [waylineData, setWaylineData] = useState(wayline.current)
    let [modelData, setModelData] = useState('M4TD')
    // 机库列表
    let [aircraftSNList, setAircraftSNList] = useState([])
    let center_line_angleRef = useRef(null);
    let line_rateRef = useRef(null);
    let side_rateRef = useRef(null);
    let GSDRef = useRef(null);
    let fly_heightRef = useRef(null);
    let GlobalSpeedRef = useRef(null);
    // 页面载入
    useEffect(() => {
        let newRes = []
        axiosApi("/api/open/Device/GetAllList", "GET",).then((res) => {
            res.forEach((item, index) => {
                // if (item.IfOnLine) {
                newRes.push({ value: item.SN, label: item.DName, Lat: item.Lat, Lng: item.Lng, Height: item.Height, Model2: item.Model2, key: item.ID })
                // }
            })
            setAircraftSNList([...newRes])
            if (newRes.length) {
                if (!wayLineInfo) {
                    setWayline({
                        ...waylineData,
                        SN: newRes[0].value
                    })
                }
                modelChange(newRes[0].Model2)
                // axiosApi(`/api/v1/FlyArea/GetCurrent`, "GET", {
                //     SN: newRes[0].value,
                // }).then((res) => {
                //     // res.data && addNotFlyZone(res.data)
                //     addNotFlyZone(res.data)
                // })
            }
        })
        if (wayLineInfo) {
            let newWayLineInfo = JSON.parse(wayLineInfo.Remarks)
            RouteFeedback({ ...newWayLineInfo })
            waylineChange({ ...newWayLineInfo })
            let model = EnumTomodel(newWayLineInfo.drone_enum, newWayLineInfo.payload_enum)
            modelChange(model)
        }
    }, []);
    function getWayline(data) {
        setWaylineData(data)
    }
    function setviewer(viewer) {
        setviewerData(viewer)
    }
    function setModel(data) {
        setModelData(data)
    }
    function save() {
        if (waylineData.WaylineName.trim().length === 0) {
            message.error("航线名不能为空");
            return
        }
        axiosApi(`/api/v1/WayLine/CreateMapping2d`, "POST", { ...waylineData }).then((res) => {
            console.log(res);
            if (res.code === 1) {
                message.success(`航线创建成功`);
                lastPage()
            }
        })
    }
    return (
        <div style={{ height: '100%', background: '#001c1a', padding: 12.0 }}>
            <div style={{ height: '100%', width: '100%', position: 'relative' }} id="cesisss"  >
                <div style={{ position: 'absolute', left: 0, top: 0, width: 300, height: '100%', zIndex: 1, backgroundColor: '#001c1a' }}>
                    <Card title={<LastPageButton title="面状航线" />}>
                        <div style={{ width: '100%', overflowY: 'auto' }}>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>选择机库</div>
                                <Select
                                    style={{ width: '100%', }}
                                    value={waylineData.SN}
                                    placeholder={'请选择机库'}
                                    onChange={(value, option) => {
                                        modelChange(option.Model2)
                                        setWayline({
                                            ...waylineData,
                                            SN: value
                                        })
                                        cameraFlyTo(option.Lng, option.Lat)
                                    }}
                                    options={aircraftSNList}
                                />
                            </div>
                            {/* <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>镜头选择</div>
                                        <Checkbox.Group options={[
                                            { label: '可见光', value: 'visable', disabled: (waylineData.payload_lens_index.length === 1 && waylineData.payload_lens_index[0] === 'visable') },
                                            { label: '红外', value: 'ir', disabled: (waylineData.payload_lens_index.length === 1 && waylineData.payload_lens_index[0] === 'ir') },
                                        ]} defaultValue={['visable']} onChange={(e) => {
                                            setWayline({
                                                ...waylineData,
                                                payload_lens_index: e
                                            })
                                        }} />
                                    </div> */}
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>航线名称</div>
                                <Input
                                    style={{ width: '100%', }}
                                    value={waylineData.WaylineName}
                                    onChange={(e) => {
                                        setWayline({
                                            ...waylineData,
                                            WaylineName: e.target.value
                                        })
                                    }}>
                                </Input>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>航线角度</div>
                                <Slider min={0} max={359} value={waylineData.center_line_angle} onChange={(e) => { setWayline({ ...waylineData, center_line_angle: e }) }} style={{ width: '100%' }} />
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>航向重叠率</div>
                                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                    <InputNumber
                                        ref={line_rateRef}
                                        min={10}
                                        max={90}
                                        style={{ width: '100%', }}
                                        value={waylineData.line_rate}
                                        onPressEnter={(e) => {
                                            if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 90)) {
                                                return
                                            }
                                            setWayline({
                                                ...waylineData,
                                                line_rate: e.target.value
                                            })
                                            line_rateRef.current.blur()
                                        }}
                                        onStep={(e, info) => {
                                            setWayline({
                                                ...waylineData,
                                                line_rate: e
                                            })
                                        }}>
                                    </InputNumber>
                                </Tooltip>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>旁向重叠率</div>
                                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                    <InputNumber
                                        ref={side_rateRef}
                                        min={10}
                                        max={90}
                                        style={{ width: '100%', }}
                                        value={waylineData.side_rate}
                                        onPressEnter={(e) => {
                                            if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 90)) {
                                                return
                                            }
                                            setWayline({
                                                ...waylineData,
                                                side_rate: e.target.value
                                            })
                                            side_rateRef.current.blur()
                                        }}
                                        onStep={(e, info) => {
                                            setWayline({
                                                ...waylineData,
                                                side_rate: e
                                            })
                                        }}>
                                    </InputNumber>
                                </Tooltip>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>GSD</div>
                                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                    <InputNumber
                                        ref={GSDRef}
                                        min={0.43}
                                        precision={2}
                                        style={{ width: '100%', }}
                                        value={waylineData.GSD}
                                        onPressEnter={(e) => {
                                            if ((e.target.value === '') || (e.target.value < 0.43)) {
                                                return
                                            }
                                            setWayline({
                                                ...waylineData,
                                                GSD: e.target.value,
                                                fly_height: computeHeight(modelData, e.target.value)
                                            })
                                            GSDRef.current.blur()
                                        }}
                                        onStep={(e, info) => {
                                            setWayline({
                                                ...waylineData,
                                                fly_height: e
                                            })
                                        }}>
                                    </InputNumber>
                                </Tooltip>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>飞行高度</div>
                                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                    <InputNumber
                                        ref={fly_heightRef}
                                        min={10}
                                        max={1500}
                                        precision={2}
                                        style={{ width: '100%', }}
                                        value={waylineData.fly_height}
                                        onPressEnter={(e) => {
                                            if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 1500)) {
                                                return
                                            }
                                            setWayline({
                                                ...waylineData,
                                                GSD: computeGSD(modelData, e.target.value),
                                                fly_height: e.target.value
                                            })
                                            fly_heightRef.current.blur()
                                        }}
                                        onStep={(e, info) => {
                                            setWayline({
                                                ...waylineData,
                                                fly_height: e
                                            })
                                        }}>
                                    </InputNumber>
                                </Tooltip>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>飞行速度</div>
                                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                    <InputNumber
                                        ref={GlobalSpeedRef}
                                        style={{ width: '100%', }}
                                        min={1}
                                        max={15}
                                        value={waylineData.GlobalSpeed}
                                        onPressEnter={(e) => {
                                            if ((e.target.value === '') || (e.target.value < 1) || (e.target.value > 15)) {
                                                return
                                            }
                                            setWayline({
                                                ...waylineData,
                                                shoot_time: computeIntervalTime(modelData, waylineData.GSD, waylineData.line_rate, e.target.value),
                                                GlobalSpeed: e.target.value
                                            })
                                            GlobalSpeedRef.current.blur()
                                        }}
                                        onStep={(e, info) => {
                                            setWayline({
                                                ...waylineData,
                                                shoot_time: computeIntervalTime(modelData, waylineData.GSD, waylineData.line_rate, e),
                                                GlobalSpeed: e
                                            })
                                        }}>
                                    </InputNumber>
                                </Tooltip>
                            </div>
                            <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                                <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}><Button type="primary" onClick={() => { save() }}>保存</Button></div>
                            </div>
                        </div>
                    </Card>
                </div>
                <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
                    <MapControl viewerData={viewerData} />
                </div>
            </div>
        </div>
    )
};

export default CesiumTest;
