import  {  useState, useEffect, useRef } from 'react';
import { Select } from 'antd';
import { isEmpty } from '@/utils/utils';
const SelectButton=({label,dL,keyField,getPlayLabel, <PERSON>le,defaultValue=null, showLabel=false})=>{
    const [Ind2,setInd2]=useState(null)
    useEffect(()=>{
       if(defaultValue!=null){
        setInd2(defaultValue[keyField]);
       }
    },[])

    const getSelect = () => {
        const list = []
        const kL=[]
        if(isEmpty(dL)) return list;
        
        dL.forEach(e=> {
          if(!kL.includes(e[keyField])){
                list.push(<Select.Option key={e[keyField]} data={e}>{getPlayLabel(e)}</Select.Option>)
                kL.push(e[keyField]);
          }
        });
        return list;
      }
    const onChange=(e,v)=>{
        setInd2(e);

        if(Handle!=null){
            Handle(e,v.data);
        }
   }

   return <div>
    {!showLabel?null: <span>{label+":"}</span>}
    
    <Select value={Ind2}  style={{ width: 200 }} 
    placeholder={label}
    onChange={onChange}>
   {getSelect()}
  </Select></div>
}


export default SelectButton;