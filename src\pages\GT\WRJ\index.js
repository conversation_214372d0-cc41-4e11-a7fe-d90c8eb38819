import { <PERSON><PERSON>, Confi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { RollbackOutlined } from '@ant-design/icons';
import { useModel,history, } from "umi";
import locale from "antd/locale/zh_CN";

import "dayjs/locale/zh-cn";
import { useEffect,useState } from "react";
import OrgPage from "@/pages/DJI/OrgPage";

import { getBodyH, isEmpty } from "@/utils/utils";
import TopMenu from "@/components/TopMenu4";
import { Get2 } from "@/services/general";
import { JCStart } from "@/pages/DJI/DRCPage/Panels/RtmpChange";

export default function HomePage() {
  const { page,setPage, modal, open, setOpen, lastPage } = useModel("pageModel");
  const [showBack, setShowBack] = useState(false);

  useEffect(() => {
    // const StartJCZB = async () => {
    //     await HGet2('/api/v1/RtmpSource/Close1')
    //     HGet2('/api/v1/RtmpSource/Start3')
    // };
    const startJC = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      JCStart(device);
    };

    startJC();

    const CloseRtmp = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      Get2(
        `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
      );
      // Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
    };

    window.addEventListener("beforeunload", (event) => {
      CloseRtmp();
    });

    document.body.addEventListener(
      "touchmove",
      function (e) {
        e.preventDefault();
      },
      { passive: false }
    );

    document.body.addEventListener("popstate", function (event) {
      lastPage();
      // e.stopPropagation();
    });

    const shouldShowBack = localStorage.getItem('showBackButton') === "true";
    setShowBack(shouldShowBack);

    // 挂载时自动跳转到首页
    setPage(<OrgPage />);
  }, []);

  const handleBack = () => {
    localStorage.removeItem('showBackButton'); 
    setPage(<OrgPage/>)
    history.back();
  };

  return (
    <div style={{ width: "100%" }}>
      {" "}
      <ConfigProvider locale={locale}>
        {showBack && (
          <Button 
            type="text"
            icon={<RollbackOutlined />}
            onClick={handleBack}
            style={{
              position: 'absolute',
              left: 16,
              top: 7,
              zIndex: 1001,
              color: 'rgba(255, 255, 255, 0.85)',
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: 20
            }}
          />
        )}
        <TopMenu></TopMenu>
        {/* <WebSocketDemo sn={'7CTDLCE00AC2J4'} sn2={'1581F6Q8D23CT00A5N49'} /> */}

        <div style={{ height: getBodyH(56) }}>
          {page}
          <Modal
            title={null}
            footer={null}
            onOk={null}
            style={{ paddingBottom: 72.0 }}
            open={open}
            onCancel={() => setOpen(false)}
          >
            {modal}
          </Modal>
        </div>
      </ConfigProvider>{" "}
    </div>
  );
}
