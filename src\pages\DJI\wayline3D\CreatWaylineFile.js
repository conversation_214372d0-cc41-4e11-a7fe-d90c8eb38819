// 生成wpml
export function CreatWaylineFile(waylineFileInfo) {
    let wpmlString = CreatWpml(waylineFileInfo)
    let kmlString = CreatKml(waylineFileInfo)
    console.log(wpmlString);
    console.log(kmlString);

    return { wpmlString, kmlString }
}
// 生成wpml
function CreatWpml(wpmlObject) {
    return `<?xml version="1.0" encoding="UTF-8"?>
    <kml xmlns="http://www.opengis.net/kml/2.2" xmlns:wpml="http://www.dji.com/wpmz/1.0.6">
        <Document>
            <wpml:missionConfig>
                <wpml:flyToWaylineMode>${wpmlObject.missionConfig.flyToWaylineMode}</wpml:flyToWaylineMode>
                <wpml:finishAction>${wpmlObject.missionConfig.finishAction}</wpml:finishAction>
                <wpml:exitOnRCLost>${wpmlObject.missionConfig.exitOnRCLost}</wpml:exitOnRCLost>
                <wpml:executeRCLostAction>${wpmlObject.missionConfig.executeRCLostAction}</wpml:executeRCLostAction>
                <wpml:takeOffSecurityHeight>${wpmlObject.missionConfig.takeOffSecurityHeight}</wpml:takeOffSecurityHeight>
                <wpml:globalTransitionalSpeed>${wpmlObject.missionConfig.globalTransitionalSpeed}</wpml:globalTransitionalSpeed>
                <wpml:globalRTHHeight>${wpmlObject.missionConfig.globalRTHHeight}</wpml:globalRTHHeight>
                <wpml:droneInfo>
                    <wpml:droneEnumValue>${wpmlObject.missionConfig.droneInfo.droneEnumValue}</wpml:droneEnumValue>
                    <wpml:droneSubEnumValue>${wpmlObject.missionConfig.droneInfo.droneSubEnumValue}</wpml:droneSubEnumValue>
                </wpml:droneInfo>
                <wpml:waylineAvoidLimitAreaMode>${wpmlObject.missionConfig.waylineAvoidLimitAreaMode}</wpml:waylineAvoidLimitAreaMode>
                <wpml:payloadInfo>
                    <wpml:payloadEnumValue>${wpmlObject.missionConfig.payloadInfo.payloadEnumValue}</wpml:payloadEnumValue>
                    <wpml:payloadSubEnumValue>${wpmlObject.missionConfig.payloadInfo.payloadSubEnumValue}</wpml:payloadSubEnumValue>
                    <wpml:payloadPositionIndex>${wpmlObject.missionConfig.payloadInfo.payloadPositionIndex}</wpml:payloadPositionIndex>
                </wpml:payloadInfo>
            </wpml:missionConfig>
            <Folder>
                <wpml:templateId>${wpmlObject.Folder.templateId}</wpml:templateId>
                <wpml:executeHeightMode>${wpmlObject.Folder.executeHeightMode}</wpml:executeHeightMode>
                <wpml:waylineId>${wpmlObject.Folder.waylineId}</wpml:waylineId>
                <wpml:distance>${wpmlObject.Folder.distance}</wpml:distance>
                <wpml:duration>${wpmlObject.Folder.duration}</wpml:duration>
                <wpml:autoFlightSpeed>${wpmlObject.Folder.autoFlightSpeed}</wpml:autoFlightSpeed>${wpmlObject.Folder.startActionGroup ? `<wpml:startActionGroup>${wpmlObject.Folder.startActionGroup.actionList.map((ite, ind) => {
        return `
                    <wpml:action>
                        <wpml:actionId>${ite.actionId}</wpml:actionId>
                        <wpml:actionActuatorFunc>${ite.actionActuatorFunc}</wpml:actionActuatorFunc>
                        <wpml:actionActuatorFuncParam>${actionActuatorFuncParamFun(ite.actionActuatorFuncParam).map(([key, value]) => {
            return `
                            <wpml:${key}>${value}</wpml:${key}>`
        })}
                        </wpml:actionActuatorFuncParam>
                    </wpml:action>`})}
                </wpml:startActionGroup>`: ''}${wpmlObject.Folder.PlacemarkList.map((item, index) => {
            return `
                <Placemark>
                    <Point>
                        <coordinates>${item.Point.coordinates.longitude},${item.Point.coordinates.latitude}</coordinates>
                    </Point>
                    <wpml:index>${item.index}</wpml:index>
                    <wpml:executeHeight>${item.executeHeight}</wpml:executeHeight>
                    <wpml:waypointSpeed>${item.waypointSpeed}</wpml:waypointSpeed>
                    <wpml:waypointHeadingParam>
                        <wpml:waypointHeadingMode>${item.waypointHeadingParam.waypointHeadingMode}</wpml:waypointHeadingMode>
                        <wpml:waypointHeadingAngle>${item.waypointHeadingParam.waypointHeadingAngle}</wpml:waypointHeadingAngle>
                        <wpml:waypointPoiPoint>${item.waypointHeadingParam.waypointPoiPoint}</wpml:waypointPoiPoint>
                        <wpml:waypointHeadingAngleEnable>${item.waypointHeadingParam.waypointHeadingAngleEnable}</wpml:waypointHeadingAngleEnable>
                        <wpml:waypointHeadingPathMode>${item.waypointHeadingParam.waypointHeadingPathMode}</wpml:waypointHeadingPathMode>
                        <wpml:waypointHeadingPoiIndex>${item.waypointHeadingParam.waypointHeadingPoiIndex}</wpml:waypointHeadingPoiIndex>
                    </wpml:waypointHeadingParam>
                    <wpml:waypointTurnParam>
                        <wpml:waypointTurnMode>${item.waypointTurnParam.waypointTurnMode}</wpml:waypointTurnMode>
                        <wpml:waypointTurnDampingDist>${item.waypointTurnParam.waypointTurnDampingDist}</wpml:waypointTurnDampingDist>
                    </wpml:waypointTurnParam>
                    <wpml:useStraightLine>${item.useStraightLine}</wpml:useStraightLine>${item.actionGroup && actionGroupFun(item.actionGroup)}
                    <wpml:waypointGimbalHeadingParam>
                        <wpml:waypointGimbalPitchAngle>${item.waypointGimbalHeadingParam.waypointGimbalPitchAngle}</wpml:waypointGimbalPitchAngle>
                        <wpml:waypointGimbalYawAngle>${item.waypointGimbalHeadingParam.waypointGimbalYawAngle}</wpml:waypointGimbalYawAngle>
                    </wpml:waypointGimbalHeadingParam>
                    <wpml:isRisky>${item.isRisky}</wpml:isRisky>
                    <wpml:waypointWorkType>${item.waypointWorkType}</wpml:waypointWorkType>
                </Placemark>`})}
            </Folder>
        </Document>
    </kml>`
}
// 生成kml
function CreatKml(kmlObject) {
    return `<?xml version="1.0" encoding="UTF-8"?>
    <kml xmlns="http://www.opengis.net/kml/2.2" xmlns:wpml="http://www.dji.com/wpmz/1.0.6">
        <Document>
            <wpml:author>${kmlObject.author}</wpml:author>
            <wpml:createTime>${kmlObject.createTime}</wpml:createTime>
            <wpml:updateTime>${kmlObject.updateTime}</wpml:updateTime>
            <wpml:missionConfig>
                <wpml:flyToWaylineMode>${kmlObject.missionConfig.flyToWaylineMode}</wpml:flyToWaylineMode>
                <wpml:finishAction>${kmlObject.missionConfig.finishAction}</wpml:finishAction>
                <wpml:exitOnRCLost>${kmlObject.missionConfig.exitOnRCLost}</wpml:exitOnRCLost>
                <wpml:executeRCLostAction>${kmlObject.missionConfig.executeRCLostAction}</wpml:executeRCLostAction>
                <wpml:takeOffRefPoint>${kmlObject.missionConfig.takeOffRefPoint.longitude},${kmlObject.missionConfig.takeOffRefPoint.latitude},${kmlObject.missionConfig.takeOffRefPoint.height}</wpml:takeOffRefPoint>
                <wpml:takeOffRefPointAGLHeight>${kmlObject.missionConfig.takeOffRefPointAGLHeight}</wpml:takeOffRefPointAGLHeight>
                <wpml:globalTransitionalSpeed>${kmlObject.missionConfig.globalTransitionalSpeed}</wpml:globalTransitionalSpeed>
                <wpml:globalRTHHeight>${kmlObject.missionConfig.globalRTHHeight}</wpml:globalRTHHeight>
                <wpml:droneInfo>
                    <wpml:droneEnumValue>${kmlObject.missionConfig.droneInfo.droneEnumValue}</wpml:droneEnumValue>
                    <wpml:droneSubEnumValue>${kmlObject.missionConfig.droneInfo.droneSubEnumValue}</wpml:droneSubEnumValue>
                </wpml:droneInfo>
                <wpml:waylineAvoidLimitAreaMode>${kmlObject.missionConfig.waylineAvoidLimitAreaMode}</wpml:waylineAvoidLimitAreaMode>
                <wpml:payloadInfo>
                    <wpml:payloadEnumValue>${kmlObject.missionConfig.payloadInfo.payloadEnumValue}</wpml:payloadEnumValue>
                    <wpml:payloadSubEnumValue>${kmlObject.missionConfig.payloadInfo.payloadSubEnumValue}</wpml:payloadSubEnumValue>
                    <wpml:payloadPositionIndex>${kmlObject.missionConfig.payloadInfo.payloadPositionIndex}</wpml:payloadPositionIndex>
                </wpml:payloadInfo>
            </wpml:missionConfig>
            <Folder>
                <wpml:templateType>${kmlObject.Folder.templateType}</wpml:templateType>
                <wpml:templateId>${kmlObject.Folder.templateId}</wpml:templateId>
                <wpml:waylineCoordinateSysParam>
                    <wpml:coordinateMode>${kmlObject.Folder.waylineCoordinateSysParam.coordinateMode}</wpml:coordinateMode>
                    <wpml:heightMode>${kmlObject.Folder.waylineCoordinateSysParam.heightMode}</wpml:heightMode>
                </wpml:waylineCoordinateSysParam>
                <wpml:autoFlightSpeed>${kmlObject.Folder.autoFlightSpeed}</wpml:autoFlightSpeed>
                <wpml:globalHeight>${kmlObject.Folder.globalHeight}</wpml:globalHeight>
                <wpml:caliFlightEnable>${kmlObject.Folder.caliFlightEnable}</wpml:caliFlightEnable>
                <wpml:gimbalPitchMode>${kmlObject.Folder.gimbalPitchMode}</wpml:gimbalPitchMode>
                <wpml:globalWaypointHeadingParam>
                    <wpml:waypointHeadingMode>${kmlObject.Folder.globalWaypointHeadingParam.waypointHeadingMode}</wpml:waypointHeadingMode>
                    <wpml:waypointHeadingAngle>${kmlObject.Folder.globalWaypointHeadingParam.waypointHeadingAngle}</wpml:waypointHeadingAngle>
                    <wpml:waypointHeadingPathMode>${kmlObject.Folder.globalWaypointHeadingParam.waypointHeadingPathMode}</wpml:waypointHeadingPathMode>
                </wpml:globalWaypointHeadingParam>
                <wpml:globalWaypointTurnMode>${kmlObject.Folder.globalWaypointTurnMode}</wpml:globalWaypointTurnMode>
                <wpml:globalUseStraightLine>${kmlObject.Folder.globalUseStraightLine}</wpml:globalUseStraightLine>${kmlObject.Folder.PlacemarkList.map((item, index) => {
        return `
                <Placemark>
                    <Point>
                        <coordinates>${item.Point.coordinates.longitude},${item.Point.coordinates.latitude}</coordinates>
                    </Point>
                    <wpml:index>${item.index}</wpml:index>
                    <wpml:ellipsoidHeight>${item.ellipsoidHeight}</wpml:ellipsoidHeight>
                    <wpml:height>${item.height}</wpml:height>
                    <wpml:waypointSpeed>${item.waypointSpeed}</wpml:waypointSpeed>
                    <wpml:waypointHeadingParam>
                        <wpml:waypointHeadingMode>${item.waypointHeadingParam.waypointHeadingMode}</wpml:waypointHeadingMode>
                        <wpml:waypointHeadingAngle>${item.waypointHeadingParam.waypointHeadingAngle}</wpml:waypointHeadingAngle>
                        <wpml:waypointHeadingPathMode>${item.waypointHeadingParam.waypointHeadingPathMode}</wpml:waypointHeadingPathMode>
                    </wpml:waypointHeadingParam>
                    <wpml:waypointTurnParam>
                        <wpml:waypointTurnMode>${item.waypointTurnParam.waypointTurnMode}</wpml:waypointTurnMode>
                        <wpml:waypointTurnDampingDist>${item.waypointTurnParam.waypointTurnDampingDist}</wpml:waypointTurnDampingDist>
                    </wpml:waypointTurnParam>
                    <wpml:useGlobalHeight>${item.useGlobalHeight}</wpml:useGlobalHeight>
                    <wpml:useGlobalHeadingParam>${item.useGlobalHeadingParam}</wpml:useGlobalHeadingParam>
                    <wpml:useGlobalTurnParam>${item.useGlobalTurnParam}</wpml:useGlobalTurnParam>
                    <wpml:useGlobalSpeed>${item.useGlobalTurnParam}</wpml:useGlobalSpeed>
                    <wpml:useStraightLine>${item.useStraightLine}</wpml:useStraightLine>${item.actionGroup && actionGroupFun(item.actionGroup)}
                    <wpml:isRisky>${item.isRisky}</wpml:isRisky>
                </Placemark>`})}
                <wpml:payloadParam>
                    <wpml:payloadPositionIndex>${kmlObject.Folder.payloadParam.payloadPositionIndex}</wpml:payloadPositionIndex>
                    <wpml:focusMode>${kmlObject.Folder.payloadParam.focusMode}</wpml:focusMode>
                    <wpml:meteringMode>${kmlObject.Folder.payloadParam.meteringMode}</wpml:meteringMode>
                    <wpml:returnMode>${kmlObject.Folder.payloadParam.returnMode}</wpml:returnMode>
                    <wpml:samplingRate>${kmlObject.Folder.payloadParam.samplingRate}</wpml:samplingRate>
                    <wpml:scanningMode>${kmlObject.Folder.payloadParam.scanningMode}</wpml:scanningMode>
                    <wpml:imageFormat>${kmlObject.Folder.payloadParam.imageFormat.toString()}</wpml:imageFormat>
                </wpml:payloadParam>
            </Folder>
        </Document>
    </kml>`
}
function actionGroupFun(actionGroup) {
    return actionGroup ? `
                    <wpml:actionGroup>
                        <wpml:actionGroupId>${actionGroup.actionGroupId}</wpml:actionGroupId>
                        <wpml:actionGroupStartIndex>${actionGroup.actionGroupStartIndex}</wpml:actionGroupStartIndex>
                        <wpml:actionGroupEndIndex>${actionGroup.actionGroupEndIndex}</wpml:actionGroupEndIndex>
                        <wpml:actionGroupMode>${actionGroup.actionGroupMode}</wpml:actionGroupMode>
                        <wpml:actionTrigger>
                            <wpml:actionTriggerType>${actionGroup.actionTrigger.actionTriggerType}</wpml:actionTriggerType>
                        </wpml:actionTrigger>${actionGroup.actionList.map((ite, ind) => {
        return `
                        <wpml:action>
                            <wpml:actionId>${ite.actionId}</wpml:actionId>
                            <wpml:actionActuatorFunc>${ite.actionActuatorFunc}</wpml:actionActuatorFunc>
                            <wpml:actionActuatorFuncParam>${actionActuatorFuncParamFun(ite.actionActuatorFuncParam).map(([key, value]) => {
            return `
                                <wpml:${key}>${value.constructor === Array ? value.toString() : value}</wpml:${key}>`
        })}
                            </wpml:actionActuatorFuncParam>
                        </wpml:action>`})}
                    </wpml:actionGroup>`: ''
}
function actionActuatorFuncParamFun(actionActuatorFuncParam) {
    return Object.entries(actionActuatorFuncParam)
}
export function analysisWaylineFile(wpmlFile, KmlFile) {
    let kml = analysisKml(KmlFile)
    let wpml = analysiswpml(wpmlFile)
    let waylineFileInfo = {
        author: kml.author,//作者
        createTime: kml.createTime,//创建时间
        updateTime: kml.updateTime,//更新时间
        missionConfig: {
            ...kml.missionConfig,
            ...wpml.missionConfig,
        },
        Folder: {
            ...kml.Folder,
            ...wpml.Folder,
            PlacemarkList: kml.Folder.PlacemarkList.map((item, index) => {
                return {
                    ...item,
                    ...wpml.Folder.PlacemarkList[index],
                    waypointHeadingParam: {
                        ...item.waypointHeadingParam,
                        ...wpml.Folder.PlacemarkList[index].waypointHeadingParam
                    }
                }
            })
        }
    }

    return waylineFileInfo
}
// 解析kml文件
export function analysisKml(Kml) {
    console.log(Kml.match(/<Placemark>[\s\S]*?<\/Placemark>/g));

    let kmlObject = {
        author: Kml.match(/<wpml:author>(.*?)<\/wpml:author>/)[1],//作者
        createTime: Number(Kml.match(/<wpml:createTime>(.*?)<\/wpml:createTime>/)[1]),//创建时间
        updateTime: Number(Kml.match(/<wpml:updateTime>(.*?)<\/wpml:updateTime>/)[1]),//更新时间
        missionConfig: {
            flyToWaylineMode: Kml.match(/<wpml:flyToWaylineMode>(.*?)<\/wpml:flyToWaylineMode>/)[1],//飞向首航点模式
            finishAction: Kml.match(/<wpml:finishAction>(.*?)<\/wpml:finishAction>/)[1],//航线结束动作
            exitOnRCLost: Kml.match(/<wpml:exitOnRCLost>(.*?)<\/wpml:exitOnRCLost>/)[1],//失控是否继续执行航线
            executeRCLostAction: Kml.match(/<wpml:executeRCLostAction>(.*?)<\/wpml:executeRCLostAction>/)[1],//失控动作类型
            takeOffRefPoint: {//参考起飞点
                longitude: Number(Kml.match(/<wpml:takeOffRefPoint>(.*?)<\/wpml:takeOffRefPoint>/)[1].split(',')[0]),
                latitude: Number(Kml.match(/<wpml:takeOffRefPoint>(.*?)<\/wpml:takeOffRefPoint>/)[1].split(',')[1]),
                height: Number(Kml.match(/<wpml:takeOffRefPoint>(.*?)<\/wpml:takeOffRefPoint>/)[1].split(',')[2])
            },
            takeOffRefPointAGLHeight: Number(Kml.match(/<wpml:takeOffRefPointAGLHeight>(.*?)<\/wpml:takeOffRefPointAGLHeight>/)[1]),//参考起飞点海拔高度
            globalTransitionalSpeed: Number(Kml.match(/<wpml:globalTransitionalSpeed>(.*?)<\/wpml:globalTransitionalSpeed>/)[1]),//全局航线过渡速度
            globalRTHHeight: Number(Kml.match(/<wpml:globalRTHHeight>(.*?)<\/wpml:globalRTHHeight>/)[1]),//全局返航高度
            droneInfo: {//飞行器机型信息
                droneEnumValue: Number(Kml.match(/<wpml:droneEnumValue>(.*?)<\/wpml:droneEnumValue>/)[1]),//飞行器机型主类型
                droneSubEnumValue: Number(Kml.match(/<wpml:droneSubEnumValue>(.*?)<\/wpml:droneSubEnumValue>/)[1])//飞行器机型子类型
            },
            waylineAvoidLimitAreaMode: (Kml.match(/<wpml:waylineAvoidLimitAreaMode>(.*?)<\/wpml:waylineAvoidLimitAreaMode>/)[1]),
            payloadInfo: {//负载机型信息
                payloadEnumValue: Number(Kml.match(/<wpml:payloadEnumValue>(.*?)<\/wpml:payloadEnumValue>/)[1]),//负载机型主类型
                payloadSubEnumValue: Number(Kml.match(/<wpml:payloadSubEnumValue>(.*?)<\/wpml:payloadSubEnumValue>/)[1]),
                payloadPositionIndex: Number(Kml.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),//负载挂载位置
            },
        },
        Folder: {
            templateType: Kml.match(/<wpml:templateType>(.*?)<\/wpml:templateType>/)[1],//预定义模板类型 * 注：模板为用户提供了快速生成航线的方案。用户填充模板元素，再导入大疆支持客户端（如DJI Pilot），即可快速生成可执行的测绘/ 巡检航线
            templateId: Number(Kml.match(/<wpml:templateId>(.*?)<\/wpml:templateId>/)[1]),
            autoFlightSpeed: Number(Kml.match(/<wpml:autoFlightSpeed>(.*?)<\/wpml:autoFlightSpeed>/)[1]),//全局航线飞行速度
            waylineCoordinateSysParam: {//坐标系参数
                coordinateMode: Kml.match(/<wpml:coordinateMode>(.*?)<\/wpml:coordinateMode>/)[1],//经纬度坐标系
                heightMode: Kml.match(/<wpml:heightMode>(.*?)<\/wpml:heightMode>/)[1],//航点高程参考平面
            },
            globalHeight: Number(Kml.match(/<wpml:globalHeight>(.*?)<\/wpml:globalHeight>/)[1]),//全局航线高度（相对起飞点高度）
            caliFlightEnable: Number(Kml.match(/<wpml:caliFlightEnable>(.*?)<\/wpml:caliFlightEnable>/)[1]),//是否开启标定飞行 *注：航电类型不可用。建图航拍时仅适用于M300 RTK与M350 RTK机型
            gimbalPitchMode: Kml.match(/<wpml:gimbalPitchMode>(.*?)<\/wpml:gimbalPitchMode>/)[1],//云台俯仰角控制模式
            globalWaypointHeadingParam: {//全局偏航角模式参数
                waypointHeadingMode: Kml.match(/<wpml:waypointHeadingMode>(.*?)<\/wpml:waypointHeadingMode>/)[1],//飞行器偏航角模式
                waypointHeadingAngle: Number(Kml.match(/<wpml:waypointHeadingAngle>(.*?)<\/wpml:waypointHeadingAngle>/)[1]),//飞行器偏航角度
                waypointHeadingPathMode: Kml.match(/<wpml:waypointHeadingPathMode>(.*?)<\/wpml:waypointHeadingPathMode>/)[1],//飞行器偏航角转动方向
            },
            globalWaypointTurnMode: Kml.match(/<wpml:globalWaypointTurnMode>(.*?)<\/wpml:globalWaypointTurnMode>/)[1],//全局航点类型（全局航点转弯模式
            globalUseStraightLine: Number(Kml.match(/<wpml:globalUseStraightLine>(.*?)<\/wpml:globalUseStraightLine>/)[1]),//全局航段轨迹是否尽量贴合直线
            PlacemarkList: Kml.match(/<Placemark>[\s\S]*?<\/Placemark>/g).map((itemx, index) => {
                let item = itemx.replace(/\n/g, '')
                let Placemark = {
                    Point: {
                        coordinates: {
                            longitude: Number(item.match(/<coordinates>(.*?)<\/coordinates>/)[1].split(',')[0]),
                            latitude: Number(item.match(/<coordinates>(.*?)<\/coordinates>/)[1].split(',')[1])
                        }
                    },
                    index: Number(item.match(/<wpml:index>(.*?)<\/wpml:index>/)[1]),
                    ellipsoidHeight: Number(item.match(/<wpml:ellipsoidHeight>(.*?)<\/wpml:ellipsoidHeight>/)[1]),
                    height: Number(item.match(/<wpml:height>(.*?)<\/wpml:height>/)[1]),
                    waypointSpeed: Number(item.match(/<wpml:waypointSpeed>(.*?)<\/wpml:waypointSpeed>/)[1]),//航点飞行速度，当前航点飞向下一个航点的速度
                    waypointHeadingParam: {
                        waypointHeadingMode: item.match(/<wpml:waypointHeadingMode>(.*?)<\/wpml:waypointHeadingMode>/)[1],
                        waypointHeadingAngle: Number(item.match(/<wpml:waypointHeadingAngle>(.*?)<\/wpml:waypointHeadingAngle>/)[1]),
                        waypointHeadingPathMode: item.match(/<wpml:waypointHeadingPathMode>(.*?)<\/wpml:waypointHeadingPathMode>/)[1],
                    },
                    waypointTurnParam: {
                        waypointTurnMode: item.match(/<wpml:waypointTurnMode>(.*?)<\/wpml:waypointTurnMode>/)[1],
                        waypointTurnDampingDist: Number(item.match(/<wpml:waypointTurnDampingDist>(.*?)<\/wpml:waypointTurnDampingDist>/)[1]),
                    },
                    useGlobalHeight: Number(item.match(/<wpml:useGlobalHeight>(.*?)<\/wpml:useGlobalHeight>/)[1]),
                    useGlobalHeadingParam: Number(item.match(/<wpml:useGlobalHeadingParam>(.*?)<\/wpml:useGlobalHeadingParam>/)[1]),
                    useGlobalTurnParam: Number(item.match(/<wpml:useGlobalTurnParam>(.*?)<\/wpml:useGlobalTurnParam>/)[1]),
                    useStraightLine: Number(item.match(/<wpml:useStraightLine>(.*?)<\/wpml:useStraightLine>/)[1]),
                    useGlobalSpeed: Number(item.match(/<wpml:useGlobalSpeed>(.*?)<\/wpml:useGlobalSpeed>/)[1]),
                    isRisky: Number(item.match(/<wpml:isRisky>(.*?)<\/wpml:isRisky>/)[1]),
                }
                if (item.match(/<wpml:actionGroup>[\s\S]*?<\/wpml:actionGroup>/g)) {
                    let actionGroupString = item.match(/<wpml:actionGroup>[\s\S]*?<\/wpml:actionGroup>/g)[0]
                    Placemark.actionGroup = {
                        actionGroupId: Number(actionGroupString.match(/<wpml:actionGroupId>(.*?)<\/wpml:actionGroupId>/)[1]),
                        actionGroupStartIndex: Number(actionGroupString.match(/<wpml:actionGroupStartIndex>(.*?)<\/wpml:actionGroupStartIndex>/)[1]),
                        actionGroupEndIndex: Number(actionGroupString.match(/<wpml:actionGroupEndIndex>(.*?)<\/wpml:actionGroupEndIndex>/)[1]),
                        actionGroupMode: actionGroupString.match(/<wpml:actionGroupMode>(.*?)<\/wpml:actionGroupMode>/)[1],
                        actionTrigger: {
                            actionTriggerType: actionGroupString.match(/<wpml:actionTriggerType>(.*?)<\/wpml:actionTriggerType>/)[1]
                        },
                        actionList: actionGroupString.match(/<wpml:action>[\s\S]*?<\/wpml:action>/g).map((ite, ind) => {
                            let action = {
                                actionId: Number(ite.match(/<wpml:actionId>(.*?)<\/wpml:actionId>/)[1]),
                                actionActuatorFunc: ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1],
                                actionActuatorFuncParam: {}
                            }
                            if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'takePhoto') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'startRecord') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'stopRecord') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'zoom') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    focalLength: Number(ite.match(/<wpml:focalLength>(.*?)<\/wpml:focalLength>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'rotateYaw') {
                                action.actionActuatorFuncParam = {
                                    aircraftHeading: Number(ite.match(/<wpml:aircraftHeading>(.*?)<\/wpml:aircraftHeading>/)[1]),
                                    aircraftPathMode: ite.match(/<wpml:aircraftPathMode>(.*?)<\/wpml:aircraftPathMode>/)[1],
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'gimbalRotate') {
                                action.actionActuatorFuncParam = {
                                    gimbalHeadingYawBase: ite.match(/<wpml:gimbalHeadingYawBase>(.*?)<\/wpml:gimbalHeadingYawBase>/)[1],
                                    gimbalRotateMode: ite.match(/<wpml:gimbalRotateMode>(.*?)<\/wpml:gimbalRotateMode>/)[1],
                                    gimbalPitchRotateEnable: Number(ite.match(/<wpml:gimbalPitchRotateEnable>(.*?)<\/wpml:gimbalPitchRotateEnable>/)[1]),
                                    gimbalPitchRotateAngle: Number(ite.match(/<wpml:gimbalPitchRotateAngle>(.*?)<\/wpml:gimbalPitchRotateAngle>/)[1]),
                                    gimbalRollRotateEnable: Number(ite.match(/<wpml:gimbalRollRotateEnable>(.*?)<\/wpml:gimbalRollRotateEnable>/)[1]),
                                    gimbalRollRotateAngle: Number(ite.match(/<wpml:gimbalRollRotateAngle>(.*?)<\/wpml:gimbalRollRotateAngle>/)[1]),
                                    gimbalYawRotateEnable: Number(ite.match(/<wpml:gimbalYawRotateEnable>(.*?)<\/wpml:gimbalYawRotateEnable>/)[1]),
                                    gimbalYawRotateAngle: Number(ite.match(/<wpml:gimbalYawRotateAngle>(.*?)<\/wpml:gimbalYawRotateAngle>/)[1]),
                                    gimbalRotateTimeEnable: Number(ite.match(/<wpml:gimbalRotateTimeEnable>(.*?)<\/wpml:gimbalRotateTimeEnable>/)[1]),
                                    gimbalRotateTime: Number(ite.match(/<wpml:gimbalRotateTime>(.*?)<\/wpml:gimbalRotateTime>/)[1]),
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'hover') {
                                action.actionActuatorFuncParam = {
                                    hoverTime: Number(ite.match(/<wpml:hoverTime>(.*?)<\/wpml:hoverTime>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'panoShot') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]],//拍摄照片存储类型
                                    panoShotSubMode: ite.match(/<wpml:panoShotSubMode>(.*?)<\/wpml:panoShotSubMode>/)[1]
                                }
                            }
                            return action
                        })
                    }
                }
                return Placemark
            }),
            payloadParam: {//负载设置
                payloadPositionIndex: Kml.match(/<wpml:focusMode>(.*?)<\/wpml:focusMode>/)[1],//负载设置
                focusMode: Kml.match(/<wpml:focusMode>(.*?)<\/wpml:focusMode>/)[1],//负载对焦模式
                meteringMode: Kml.match(/<wpml:meteringMode>(.*?)<\/wpml:meteringMode>/)[1],//负载测光模式
                returnMode: Kml.match(/<wpml:returnMode>(.*?)<\/wpml:returnMode>/)[1],//激光雷达回波模式
                samplingRate: Number(Kml.match(/<wpml:samplingRate>(.*?)<\/wpml:samplingRate>/)[1]),//负载采样率
                scanningMode: Kml.match(/<wpml:scanningMode>(.*?)<\/wpml:scanningMode>/)[1],//负载扫描模式
                imageFormat: Kml.match(/<wpml:imageFormat>(.*?)<\/wpml:imageFormat>/)[1].split(','),//图片格式列表
            }
        }
    }
    return kmlObject
}
export function analysiswpml(wpml) {
    let wpmlObject = {
        missionConfig: {
            flyToWaylineMode: wpml.match(/<wpml:flyToWaylineMode>(.*?)<\/wpml:flyToWaylineMode>/)[1],//飞向首航点模式
            finishAction: wpml.match(/<wpml:finishAction>(.*?)<\/wpml:finishAction>/)[1],//航线结束动作
            exitOnRCLost: wpml.match(/<wpml:exitOnRCLost>(.*?)<\/wpml:exitOnRCLost>/)[1],//失控是否继续执行航线
            executeRCLostAction: wpml.match(/<wpml:executeRCLostAction>(.*?)<\/wpml:executeRCLostAction>/)[1],//失控动作类型
            takeOffSecurityHeight: Number(wpml.match(/<wpml:takeOffSecurityHeight>(.*?)<\/wpml:takeOffSecurityHeight>/)[1]),//安全起飞高度
            globalTransitionalSpeed: Number(wpml.match(/<wpml:globalTransitionalSpeed>(.*?)<\/wpml:globalTransitionalSpeed>/)[1]),//全局航线过渡速度
            globalRTHHeight: Number(wpml.match(/<wpml:globalRTHHeight>(.*?)<\/wpml:globalRTHHeight>/)[1]),//全局返航高度
            droneInfo: {//飞行器机型信息
                droneEnumValue: Number(wpml.match(/<wpml:droneEnumValue>(.*?)<\/wpml:droneEnumValue>/)[1]),//飞行器机型主类型
                droneSubEnumValue: Number(wpml.match(/<wpml:droneSubEnumValue>(.*?)<\/wpml:droneSubEnumValue>/)[1])//飞行器机型子类型
            },
            waylineAvoidLimitAreaMode: (wpml.match(/<wpml:waylineAvoidLimitAreaMode>(.*?)<\/wpml:waylineAvoidLimitAreaMode>/)[1]),
            payloadInfo: {//负载机型信息
                payloadEnumValue: Number(wpml.match(/<wpml:payloadEnumValue>(.*?)<\/wpml:payloadEnumValue>/)[1]),//负载机型主类型
                payloadSubEnumValue: Number(wpml.match(/<wpml:payloadSubEnumValue>(.*?)<\/wpml:payloadSubEnumValue>/)[1]),
                payloadPositionIndex: Number(wpml.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),//负载挂载位置
            },
        },
        Folder: {
            templateId: Number(wpml.match(/<wpml:templateId>(.*?)<\/wpml:templateId>/)[1]),
            executeHeightMode: wpml.match(/<wpml:executeHeightMode>(.*?)<\/wpml:executeHeightMode>/)[1],//执行高度模式 * 注：该元素仅在waylines.wpml中使用。
            waylineId: Number(wpml.match(/<wpml:waylineId>(.*?)<\/wpml:waylineId>/)[1]),
            distance: Number(wpml.match(/<wpml:distance>(.*?)<\/wpml:distance>/)[1]),
            duration: Number(wpml.match(/<wpml:duration>(.*?)<\/wpml:duration>/)[1]),
            autoFlightSpeed: Number(wpml.match(/<wpml:autoFlightSpeed>(.*?)<\/wpml:autoFlightSpeed>/)[1]),//全局航线飞行速度
            PlacemarkList: wpml.match(/<Placemark>[\s\S]*?<\/Placemark>/g).map((itemx, index) => {
                let item = itemx.replace(/\n/g, '')
                let Placemark = {
                    Point: {
                        coordinates: {
                            longitude: Number(item.match(/<coordinates>(.*?)<\/coordinates>/)[1].split(',')[0]),
                            latitude: Number(item.match(/<coordinates>(.*?)<\/coordinates>/)[1].split(',')[1])
                        }
                    },
                    index: Number(item.match(/<wpml:index>(.*?)<\/wpml:index>/)[1]),
                    executeHeight: Number(item.match(/<wpml:executeHeight>(.*?)<\/wpml:executeHeight>/)[1]),
                    waypointSpeed: Number(item.match(/<wpml:waypointSpeed>(.*?)<\/wpml:waypointSpeed>/)[1]),//航点飞行速度，当前航点飞向下一个航点的速度
                    waypointHeadingParam: {
                        waypointHeadingMode: item.match(/<wpml:waypointHeadingMode>(.*?)<\/wpml:waypointHeadingMode>/)[1],
                        waypointHeadingAngle: Number(item.match(/<wpml:waypointHeadingAngle>(.*?)<\/wpml:waypointHeadingAngle>/)[1]),
                        waypointHeadingPathMode: item.match(/<wpml:waypointHeadingPathMode>(.*?)<\/wpml:waypointHeadingPathMode>/)[1],
                        waypointHeadingAngleEnable: Number(item.match(/<wpml:waypointHeadingAngleEnable>(.*?)<\/wpml:waypointHeadingAngleEnable>/)[1]),
                    },
                    waypointTurnParam: {
                        waypointTurnMode: item.match(/<wpml:waypointTurnMode>(.*?)<\/wpml:waypointTurnMode>/)[1],
                        waypointTurnDampingDist: Number(item.match(/<wpml:waypointTurnDampingDist>(.*?)<\/wpml:waypointTurnDampingDist>/)[1]),
                    },
                    useStraightLine: Number(item.match(/<wpml:useStraightLine>(.*?)<\/wpml:useStraightLine>/)[1]),
                    waypointGimbalHeadingParam: {
                        waypointGimbalPitchAngle: Number(item.match(/<wpml:waypointGimbalPitchAngle>(.*?)<\/wpml:waypointGimbalPitchAngle>/)[1]),
                        waypointGimbalYawAngle: Number(item.match(/<wpml:waypointGimbalYawAngle>(.*?)<\/wpml:waypointGimbalYawAngle>/)[1])
                    },
                    isRisky: Number(item.match(/<wpml:isRisky>(.*?)<\/wpml:isRisky>/)[1]),
                    waypointWorkType: Number(item.match(/<wpml:waypointWorkType>(.*?)<\/wpml:waypointWorkType>/)[1]),
                }
                if (item.match(/<wpml:actionGroup>[\s\S]*?<\/wpml:actionGroup>/g)) {
                    let actionGroupString = item.match(/<wpml:actionGroup>[\s\S]*?<\/wpml:actionGroup>/g)[0]
                    Placemark.actionGroup = {
                        actionGroupId: Number(actionGroupString.match(/<wpml:actionGroupId>(.*?)<\/wpml:actionGroupId>/)[1]),
                        actionGroupStartIndex: Number(actionGroupString.match(/<wpml:actionGroupStartIndex>(.*?)<\/wpml:actionGroupStartIndex>/)[1]),
                        actionGroupEndIndex: Number(actionGroupString.match(/<wpml:actionGroupEndIndex>(.*?)<\/wpml:actionGroupEndIndex>/)[1]),
                        actionGroupMode: actionGroupString.match(/<wpml:actionGroupMode>(.*?)<\/wpml:actionGroupMode>/)[1],
                        actionTrigger: {
                            actionTriggerType: actionGroupString.match(/<wpml:actionTriggerType>(.*?)<\/wpml:actionTriggerType>/)[1]
                        },
                        actionList: actionGroupString.match(/<wpml:action>[\s\S]*?<\/wpml:action>/g).map((ite, ind) => {
                            let action = {
                                actionId: Number(ite.match(/<wpml:actionId>(.*?)<\/wpml:actionId>/)[1]),
                                actionActuatorFunc: ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1],
                                actionActuatorFuncParam: {}
                            }
                            if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'takePhoto') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'startRecord') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'stopRecord') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'zoom') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    focalLength: Number(ite.match(/<wpml:focalLength>(.*?)<\/wpml:focalLength>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'rotateYaw') {
                                action.actionActuatorFuncParam = {
                                    aircraftHeading: Number(ite.match(/<wpml:aircraftHeading>(.*?)<\/wpml:aircraftHeading>/)[1]),
                                    aircraftPathMode: ite.match(/<wpml:aircraftPathMode>(.*?)<\/wpml:aircraftPathMode>/)[1],
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'gimbalRotate') {
                                action.actionActuatorFuncParam = {
                                    gimbalHeadingYawBase: ite.match(/<wpml:gimbalHeadingYawBase>(.*?)<\/wpml:gimbalHeadingYawBase>/)[1],
                                    gimbalRotateMode: ite.match(/<wpml:gimbalRotateMode>(.*?)<\/wpml:gimbalRotateMode>/)[1],
                                    gimbalPitchRotateEnable: Number(ite.match(/<wpml:gimbalPitchRotateEnable>(.*?)<\/wpml:gimbalPitchRotateEnable>/)[1]),
                                    gimbalPitchRotateAngle: Number(ite.match(/<wpml:gimbalPitchRotateAngle>(.*?)<\/wpml:gimbalPitchRotateAngle>/)[1]),
                                    gimbalRollRotateEnable: Number(ite.match(/<wpml:gimbalRollRotateEnable>(.*?)<\/wpml:gimbalRollRotateEnable>/)[1]),
                                    gimbalRollRotateAngle: Number(ite.match(/<wpml:gimbalRollRotateAngle>(.*?)<\/wpml:gimbalRollRotateAngle>/)[1]),
                                    gimbalYawRotateEnable: Number(ite.match(/<wpml:gimbalYawRotateEnable>(.*?)<\/wpml:gimbalYawRotateEnable>/)[1]),
                                    gimbalYawRotateAngle: Number(ite.match(/<wpml:gimbalYawRotateAngle>(.*?)<\/wpml:gimbalYawRotateAngle>/)[1]),
                                    gimbalRotateTimeEnable: Number(ite.match(/<wpml:gimbalRotateTimeEnable>(.*?)<\/wpml:gimbalRotateTimeEnable>/)[1]),
                                    gimbalRotateTime: Number(ite.match(/<wpml:gimbalRotateTime>(.*?)<\/wpml:gimbalRotateTime>/)[1]),
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'hover') {
                                action.actionActuatorFuncParam = {
                                    hoverTime: Number(ite.match(/<wpml:hoverTime>(.*?)<\/wpml:hoverTime>/)[1]),
                                }
                            } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'panoShot') {
                                action.actionActuatorFuncParam = {
                                    payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                                    useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                                    payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]],//拍摄照片存储类型
                                    panoShotSubMode: ite.match(/<wpml:panoShotSubMode>(.*?)<\/wpml:panoShotSubMode>/)[1]
                                }
                            }
                            return action
                        })
                    }
                }
                return Placemark
            }),
        }
    }
    if (wpml.match(/<wpml:startActionGroup>[\s\S]*?<\/wpml:startActionGroup>/g)) {
        let startActionGroupString = item.match(/<wpml:startActionGroup>[\s\S]*?<\/wpml:startActionGroup>/g)[0]
        startActionGroup = {
            actionList: startActionGroupString.match(/<wpml:action>[\s\S]*?<\/wpml:action>/g).map((ite, ind) => {
                let action = {
                    actionId: Number(ite.match(/<wpml:actionId>(.*?)<\/wpml:actionId>/)[1]),
                    actionActuatorFunc: ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1],
                    actionActuatorFuncParam: {}
                }
                if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'takePhoto') {
                    action.actionActuatorFuncParam = {
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                        useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                        payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'startRecord') {
                    action.actionActuatorFuncParam = {
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                        useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                        payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'stopRecord') {
                    action.actionActuatorFuncParam = {
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                        useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                        payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]]//拍摄照片存储类型
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'zoom') {
                    action.actionActuatorFuncParam = {
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                        focalLength: Number(ite.match(/<wpml:focalLength>(.*?)<\/wpml:focalLength>/)[1]),
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'rotateYaw') {
                    action.actionActuatorFuncParam = {
                        aircraftHeading: Number(ite.match(/<wpml:aircraftHeading>(.*?)<\/wpml:aircraftHeading>/)[1]),
                        aircraftPathMode: ite.match(/<wpml:aircraftPathMode>(.*?)<\/wpml:aircraftPathMode>/)[1],
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'gimbalRotate') {
                    action.actionActuatorFuncParam = {
                        gimbalHeadingYawBase: ite.match(/<wpml:gimbalHeadingYawBase>(.*?)<\/wpml:gimbalHeadingYawBase>/)[1],
                        gimbalRotateMode: ite.match(/<wpml:gimbalRotateMode>(.*?)<\/wpml:gimbalRotateMode>/)[1],
                        gimbalPitchRotateEnable: Number(ite.match(/<wpml:gimbalPitchRotateEnable>(.*?)<\/wpml:gimbalPitchRotateEnable>/)[1]),
                        gimbalPitchRotateAngle: Number(ite.match(/<wpml:gimbalPitchRotateAngle>(.*?)<\/wpml:gimbalPitchRotateAngle>/)[1]),
                        gimbalRollRotateEnable: Number(ite.match(/<wpml:gimbalRollRotateEnable>(.*?)<\/wpml:gimbalRollRotateEnable>/)[1]),
                        gimbalRollRotateAngle: Number(ite.match(/<wpml:gimbalRollRotateAngle>(.*?)<\/wpml:gimbalRollRotateAngle>/)[1]),
                        gimbalYawRotateEnable: Number(ite.match(/<wpml:gimbalYawRotateEnable>(.*?)<\/wpml:gimbalYawRotateEnable>/)[1]),
                        gimbalYawRotateAngle: Number(ite.match(/<wpml:gimbalYawRotateAngle>(.*?)<\/wpml:gimbalYawRotateAngle>/)[1]),
                        gimbalRotateTimeEnable: Number(ite.match(/<wpml:gimbalRotateTimeEnable>(.*?)<\/wpml:gimbalRotateTimeEnable>/)[1]),
                        gimbalRotateTime: Number(ite.match(/<wpml:gimbalRotateTime>(.*?)<\/wpml:gimbalRotateTime>/)[1]),
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'hover') {
                    action.actionActuatorFuncParam = {
                        hoverTime: Number(ite.match(/<wpml:hoverTime>(.*?)<\/wpml:hoverTime>/)[1]),
                    }
                } else if (ite.match(/<wpml:actionActuatorFunc>(.*?)<\/wpml:actionActuatorFunc>/)[1] === 'panoShot') {
                    action.actionActuatorFuncParam = {
                        payloadPositionIndex: Number(ite.match(/<wpml:payloadPositionIndex>(.*?)<\/wpml:payloadPositionIndex>/)[1]),
                        useGlobalPayloadLensIndex: Number(ite.match(/<wpml:useGlobalPayloadLensIndex>(.*?)<\/wpml:useGlobalPayloadLensIndex>/)[1]),
                        payloadLensIndex: [ite.match(/<wpml:payloadLensIndex>(.*?)<\/wpml:payloadLensIndex>/)[1]],//拍摄照片存储类型
                        panoShotSubMode: ite.match(/<wpml:panoShotSubMode>(.*?)<\/wpml:panoShotSubMode>/)[1]
                    }
                }
                return action
            })
        }
        Folder.startActionGroup = startActionGroup
    }
    return wpmlObject
}