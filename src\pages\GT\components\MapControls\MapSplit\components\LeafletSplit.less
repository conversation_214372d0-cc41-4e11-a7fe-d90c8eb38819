.leaflet-split-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

.leaflet-split-slider {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  pointer-events: all;
  cursor: ew-resize;
  z-index: 1001;
  transform: translateX(-50%);
  
  &:hover {
    .leaflet-split-handle {
      opacity: 1;
      transform: scale(1.1);
    }
  }
}

.leaflet-split-handle {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.leaflet-split-line {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(64, 169, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.9) 100%
  );
  transform: translateX(-50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.leaflet-split-grip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 40px;
  background: rgba(64, 169, 255, 0.9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.8);
  
  span {
    color: white;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: -1px;
  }
  
  &:hover {
    background: rgba(64, 169, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

// 拖拽状态样式
.leaflet-split-slider.dragging {
  .leaflet-split-handle {
    opacity: 1;
    transform: scale(1.2);
  }
  
  .leaflet-split-grip {
    background: rgba(64, 169, 255, 1);
    box-shadow: 0 4px 16px rgba(64, 169, 255, 0.4);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .leaflet-split-slider {
    width: 6px;
  }
  
  .leaflet-split-grip {
    width: 28px;
    height: 44px;
    border-radius: 14px;
    
    span {
      font-size: 14px;
    }
  }
}

// 使用leaflet-side-by-side插件默认的控制器

// 响应式设计
@media (max-width: 768px) {
  .leaflet-sbs-range {

      &::-webkit-slider-thumb {
        width: 28px;
        height: 44px;
        border-radius: 14px;
      }

      &::-moz-range-thumb {
        width: 28px;
        height: 44px;
        border-radius: 14px;
      }

      &::-ms-thumb {
        width: 28px;
        height: 44px;
        border-radius: 14px;
      }
  }
}

.leaflet-sbs-divider {
  // 分割线样式
  z-index: 1001;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(64, 169, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.9) 100%
  ) !important;
  width: 2px !important;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3) !important;
}

// 预览分割线样式
.leaflet-split-preview-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(64, 169, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  pointer-events: none;
  transform: translateX(-50%);

  // 添加一个脉冲动画效果，提示用户这是预览状态
  animation: preview-pulse 2s ease-in-out infinite;

  // 在预览线中央添加一个提示图标
  &::after {
    content: '⋮⋮';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(64, 169, 255, 0.9);
    color: white;
    padding: 4px 2px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
  }
}

@keyframes preview-pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}
