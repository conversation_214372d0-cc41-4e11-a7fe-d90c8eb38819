/**
 * 地图可见范围获取工具
 * 支持 Leaflet 和 Cesium 两种地图引擎
 */

import { detectMapEngine } from './mapEngineDetector';
import { Cesium } from 'umi';

/**
 * 获取当前地图可见范围
 * @param {Object} mapInstance - 地图实例
 * @param {string} engineType - 引擎类型（可选，自动检测）
 * @returns {Object|null} 返回 { ltlat, ltlng, rblat, rblng } 或 null
 */
export function getCurrentViewBounds(mapInstance, engineType = null) {
  if (!mapInstance) {
    console.warn('MapBoundsHelper: mapInstance is null or undefined');
    return null;
  }

  const engine = engineType || detectMapEngine(mapInstance);

  try {
    switch (engine) {
      case 'cesium':
        return getCesiumViewBounds(mapInstance);
      case 'leaflet':
        return getLeafletViewBounds(mapInstance);
      default:
        console.warn('MapBoundsHelper: Unsupported map engine:', engine);
        return null;
    }
  } catch (error) {
    console.error('MapBoundsHelper: Error getting view bounds:', error);
    return null;
  }
}

/**
 * 获取 Cesium 地图可见范围
 * @param {Object} viewer - Cesium Viewer 实例
 * @returns {Object|null} 边界对象
 */
function getCesiumViewBounds(viewer) {
  if (!viewer.scene || !viewer.camera) {
    console.warn('MapBoundsHelper: Cesium viewer is not ready');
    return null;
  }

  try {
    const canvas = viewer.scene.canvas;
    const rectangle = viewer.camera.computeViewRectangle();
    
    if (!rectangle) {
      // 如果无法直接获取，使用屏幕四个角点计算
      return getCesiumBoundsByCorners(viewer, canvas);
    }

    // 将弧度转换为度数
    const west = Cesium.Math.toDegrees(rectangle.west);
    const south = Cesium.Math.toDegrees(rectangle.south);
    const east = Cesium.Math.toDegrees(rectangle.east);
    const north = Cesium.Math.toDegrees(rectangle.north);

    return {
      ltlat: north,  // 左上角纬度
      ltlng: west,   // 左上角经度
      rblat: south,  // 右下角纬度
      rblng: east    // 右下角经度
    };
  } catch (error) {
    console.error('MapBoundsHelper: Error getting Cesium bounds:', error);
    return null;
  }
}

/**
 * 通过屏幕四个角点计算 Cesium 可见范围
 * @param {Object} viewer - Cesium Viewer 实例
 * @param {HTMLCanvasElement} canvas - 画布元素
 * @returns {Object|null} 边界对象
 */
function getCesiumBoundsByCorners(viewer, canvas) {
  try {
    const width = canvas.clientWidth;
    const height = canvas.clientHeight;

    // 获取四个角点的地理坐标
    const corners = [
      { x: 0, y: 0 },           // 左上角
      { x: width, y: 0 },       // 右上角
      { x: 0, y: height },      // 左下角
      { x: width, y: height }   // 右下角
    ];

    const positions = [];
    
    for (const corner of corners) {
      const ray = viewer.camera.getPickRay(new Cesium.Cartesian2(corner.x, corner.y));
      const position = viewer.scene.globe.pick(ray, viewer.scene);
      
      if (position) {
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        positions.push({
          lng: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude)
        });
      }
    }

    if (positions.length < 4) {
      console.warn('MapBoundsHelper: Unable to get all corner positions');
      return null;
    }

    // 计算边界
    const lngs = positions.map(p => p.lng);
    const lats = positions.map(p => p.lat);

    return {
      ltlat: Math.max(...lats),  // 最大纬度（北）
      ltlng: Math.min(...lngs),  // 最小经度（西）
      rblat: Math.min(...lats),  // 最小纬度（南）
      rblng: Math.max(...lngs)   // 最大经度（东）
    };
  } catch (error) {
    console.error('MapBoundsHelper: Error calculating Cesium bounds by corners:', error);
    return null;
  }
}

/**
 * 获取 Leaflet 地图可见范围
 * @param {Object} map - Leaflet Map 实例
 * @returns {Object|null} 边界对象
 */
function getLeafletViewBounds(map) {
  if (!map.getBounds) {
    console.warn('MapBoundsHelper: Leaflet map is not ready');
    return null;
  }

  try {
    const bounds = map.getBounds();
    
    return {
      ltlat: bounds.getNorth(),  // 左上角纬度
      ltlng: bounds.getWest(),   // 左上角经度
      rblat: bounds.getSouth(),  // 右下角纬度
      rblng: bounds.getEast()    // 右下角经度
    };
  } catch (error) {
    console.error('MapBoundsHelper: Error getting Leaflet bounds:', error);
    return null;
  }
}

/**
 * 扩展边界范围
 * @param {Object} bounds - 原始边界
 * @param {number} percentage - 扩展百分比（如 0.2 表示扩展20%）
 * @returns {Object} 扩展后的边界
 */
export function expandBounds(bounds, percentage = 0.2) {
  if (!bounds) return null;

  const { ltlat, ltlng, rblat, rblng } = bounds;
  
  const latDiff = ltlat - rblat;
  const lngDiff = rblng - ltlng;
  
  const latExpansion = latDiff * percentage / 2;
  const lngExpansion = lngDiff * percentage / 2;

  return {
    ltlat: Math.min(90, ltlat + latExpansion),    // 限制在有效纬度范围内
    ltlng: Math.max(-180, ltlng - lngExpansion),  // 限制在有效经度范围内
    rblat: Math.max(-90, rblat - latExpansion),
    rblng: Math.min(180, rblng + lngExpansion)
  };
}

/**
 * 检查两个边界是否重叠
 * @param {Object} bounds1 - 边界1
 * @param {Object} bounds2 - 边界2
 * @returns {boolean} 是否重叠
 */
export function boundsOverlap(bounds1, bounds2) {
  if (!bounds1 || !bounds2) return false;

  return !(bounds1.rblng < bounds2.ltlng || 
           bounds2.rblng < bounds1.ltlng || 
           bounds1.rblat > bounds2.ltlat || 
           bounds2.rblat > bounds1.ltlat);
}

/**
 * 计算两个边界的差异程度
 * @param {Object} bounds1 - 边界1
 * @param {Object} bounds2 - 边界2
 * @returns {number} 差异程度（0-1，0表示完全相同，1表示完全不同）
 */
export function calculateBoundsDifference(bounds1, bounds2) {
  if (!bounds1 || !bounds2) return 1;

  const area1 = (bounds1.ltlat - bounds1.rblat) * (bounds1.rblng - bounds1.ltlng);
  const area2 = (bounds2.ltlat - bounds2.rblat) * (bounds2.rblng - bounds2.ltlng);
  
  if (area1 === 0 || area2 === 0) return 1;

  // 计算重叠区域
  const overlapLtlat = Math.min(bounds1.ltlat, bounds2.ltlat);
  const overlapLtlng = Math.max(bounds1.ltlng, bounds2.ltlng);
  const overlapRblat = Math.max(bounds1.rblat, bounds2.rblat);
  const overlapRblng = Math.min(bounds1.rblng, bounds2.rblng);

  if (overlapLtlat <= overlapRblat || overlapLtlng >= overlapRblng) {
    return 1; // 没有重叠
  }

  const overlapArea = (overlapLtlat - overlapRblat) * (overlapRblng - overlapLtlng);
  const unionArea = area1 + area2 - overlapArea;

  return 1 - (overlapArea / unionArea);
}

/**
 * 格式化边界为字符串
 * @param {Object} bounds - 边界对象
 * @param {number} precision - 精度（小数位数）
 * @returns {string} 格式化的字符串
 */
export function formatBounds(bounds, precision = 6) {
  if (!bounds) return 'null';

  return `ltlat=${bounds.ltlat.toFixed(precision)}&ltlng=${bounds.ltlng.toFixed(precision)}&rblat=${bounds.rblat.toFixed(precision)}&rblng=${bounds.rblng.toFixed(precision)}`;
}

export default {
  getCurrentViewBounds,
  expandBounds,
  boundsOverlap,
  calculateBoundsDifference,
  formatBounds
};
