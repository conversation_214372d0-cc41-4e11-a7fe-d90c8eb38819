//import {Modal,} from 'antd';
import { Form, Modal, InputNumber, Button } from 'antd';
import { connectAsync } from 'mqtt';
import { useState } from 'react';
import { HGet2 } from "@/utils/request";

const HanHuaPage = () => {
  const [height, setHeight] = useState('输入喊话内容')
  const onClick=(val)=>{
    
    HGet2("/api/v1/PSDK/Speaker?sn=" + device.SN + "&val="+val)
  }
  return <div >
            <span>无人机喊话：</span>
            <span > <Input onChange={(e) => setHeight(e)} defaultValue={height} /></span>
            <span style={{marginLeft:24.0}}><Button type='primary' onClick={()=>onClick(height)}>确定</Button></span>
     </div>
};




export default HanHuaPage;
