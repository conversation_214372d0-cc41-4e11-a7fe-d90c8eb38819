import { useEffect, useState } from 'react';
import { Button, Radio, message, Modal, Descriptions, Input, InputNumber } from 'antd';
import { HPost2 } from '@/utils/request';
import { isEmpty } from '@/utils/utils';
import MyButton from "@/pages/GT/components/MyButton/MyButton";

const { TextArea } = Input;

const EditTable = ({ mapList, record, refrush }) => {
  const [canSee, setCanSee] = useState(false);
  const [formData, setFormData] = useState(record);

  const handleChange = (key, value) => {
    setFormData(prevState => ({ ...prevState, [key]: value }));
  };

  const submit = async () => {
    if (!formData.MapName) {
      message.warning('请填写地图名称');
      return;
    }

    let res = await HPost2(`/api/v1/MapData/Update`, formData);
    if (isEmpty(res.err)) {
      message.success(`编辑地图成功!`);
      setCanSee(false);
      refrush();
    } else {
      message.error("错误:" + res.err);
    }
  };


  let inputStyle = {
    margin:0,
    width:'100%'
    }
  return (
    <>
      {/* <a onClick={() => setCanSee(true)}>编辑</a> */}
      <MyButton style={{ padding: "2px 5px" }} onClick={() => setCanSee(true)}>编辑</MyButton>
      <Modal
        title={null}
        onOk={submit}
        open={canSee}
        onCancel={() => setCanSee(false)}
        okText="提交"
        cancelText="取消"
        width={900}
      >
        <Descriptions title="编辑" column={1} colonMarginRight={20} labelStyle={{width:'80px'}}>
          {/* <Descriptions.Item label="地图类型">
            <Radio.Group value={formData.MapType} onChange={(e) => handleChange('MapType', e.target.value)}>
              <Radio value={0}>普通地图</Radio>
              <Radio value={1}>正射影像(tms)</Radio>
              <Radio value={11}>三维模型(b3dm)</Radio>
            </Radio.Group>
          </Descriptions.Item>
          <Descriptions.Item label="地图名称">
            <Input value={formData.MapName} onChange={(e) => handleChange('MapName', e.target.value)} allowClear />
          </Descriptions.Item>
          <Descriptions.Item label="地图URL">
            <TextArea value={formData.Url} onChange={(e) => handleChange('Url', e.target.value)} allowClear autoSize />
          </Descriptions.Item>
          <Descriptions.Item label="最大缩放">
            <InputNumber style={inputStyle} min={0} max={20} value={formData.MaxZoom} onChange={(value) => handleChange('MaxZoom', value)} allowClear />
          </Descriptions.Item>
          <Descriptions.Item label="最小缩放">
            <InputNumber style={inputStyle} min={0} max={20} value={formData.MinZoom} onChange={(value) => handleChange('MinZoom', value)} allowClear />
          </Descriptions.Item>
          <Descriptions.Item label="初始经度">
            <InputNumber style={inputStyle} min={-180} max={180} value={formData.Lng} onChange={(value) => handleChange('Lng', value)} allowClear />
          </Descriptions.Item>
          <Descriptions.Item label="初始维度">
            <InputNumber style={inputStyle} min={-90} max={90} value={formData.Lat} onChange={(value) => handleChange('Lat', value)} allowClear />
          </Descriptions.Item> */}
        </Descriptions>
      </Modal>
    </>
  );
};

export default EditTable;
