@back-color: rgba(#9adaba, 1);
//框线颜色
@line-color: #42ab98;

// 顶级容器，撑满视口高度，禁止外层滚动
.map-management-container {
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  background: #f5f6f7;

  .ant-form {
    margin-bottom: 24px;
  }
}

// 主分栏布局
.map-data-directory {
  height: 100vh;
  display: flex !important;
  flex-direction: row;
  background: #fff !important;
}

// 左侧目录区
.directory-sider {
  height: 100vh;
  min-width: 260px;
  max-width: 350px;
  width: 300px !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff !important;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.03);

  .sider-header {
    flex: 0 0 auto;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: #fafafa;

    h2 {
      margin: 0;
      color: #333 !important;
      font-size: 18px;
      font-weight: 600;
    }
  }

  // 目录树区域，内容过长自动滚动
  .directory-tree {
    flex: 1 1 auto;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;
    background: #fff;

    .ant-tree {
      color: #333 !important;
      background: transparent !important;

      .ant-tree-node-content-wrapper:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .ant-tree-node-selected {
        background-color: #e6f7ff !important;
      }

      .ant-tree-draggable-icon {
        display: none;
      }
    }

    // 节点标题和操作按钮样式
    .tree-node-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 4px 0;
      transition: all 0.3s;

      .anticon {
        color: #666 !important;
        margin-right: 8px;
      }

      .tree-node-actions {
        display: none;
        margin-left: 8px;
        opacity: 0;
        transition: all 0.3s;

        .ant-btn {
          color: #666 !important;
          background: transparent !important;
          border: none !important;
          padding: 4px 8px;

          &:hover {
            color: #42ab98 !important;
            background: rgba(24, 144, 255, 0.1) !important;
          }
        }
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);

        .tree-node-actions {
          display: flex;
          opacity: 1;
        }
      }
    }
  }
}

// 右侧内容区
.directory-content {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-width: 0; // 避免flex子项溢出
  background: #fafafa !important;
  padding: 24px;
  overflow: hidden;
  height: 100vh;

  // 让右侧主要内容也可滚动
  > .ant-layout-content {
    flex: 1 1 auto;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fafafa !important;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  h2 {
    margin-top: 0;
    color: #333 !important;
    font-size: 18px;
    font-weight: 600;
  }

  .empty-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    color: #999 !important;
    font-size: 16px;
  }

  .ant-form-item {
    margin-bottom: 24px;

    .ant-form-item-label > label {
      color: #333 !important;
      font-weight: 500;
      font-size: 14px;
    }

    .ant-input,
    .ant-select-selector,
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      background: #fff !important;
      color: #333 !important;
      border-color: #d9d9d9 !important;

      &:hover,
      &:focus {
        border-color: #42ab98 !important;
      }
    }

    .ant-input-number {
      .ant-input-number-input {
        color: #333 !important;
      }
    }
  }

  .ant-btn {
    margin-right: 8px;

    &-primary {
      background: #42ab98 !important;
      border-color: #42ab98 !important;
      color: #fff !important;

      &:hover {
        background: #059e82 !important;
        border-color: #059e82 !important;
      }
    }

    &-default {
      background: #fff !important;
      border-color: #d9d9d9 !important;
      color: #333 !important;

      &:hover {
        color: #42ab98 !important;
        border-color: #42ab98 !important;
      }
    }
  }
}

// 下拉菜单样式
.map-data-directory {
  .ant-dropdown .ant-dropdown-menu {
    background: @back-color;

    .ant-dropdown-menu-item {
      color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  // 模态框按钮样式
  .ant-modal {
    .ant-modal-footer {
      .ant-btn {
        &.ant-btn-primary {
          background: @back-color;
          border-color: @line-color;
          color: #fff;

          &:hover {
            background: lighten(@back-color, 10%);
            border-color: lighten(@line-color, 10%);
          }
        }

        &.ant-btn-default {
          background: #fff;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.85);

          &:hover {
            border-color: @line-color;
            color: @line-color;
          }
        }
      }
    }
  }
}
