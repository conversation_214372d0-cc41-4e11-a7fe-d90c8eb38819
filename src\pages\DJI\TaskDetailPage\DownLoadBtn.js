import { DownLoadFile, axiosApi } from "@/services/general";
import { useState, useRef } from "react";
import { HGet2 } from "@/utils/request";
import { message, Modal } from "antd";
import AddButton from "@/components/AddButton";
import { getDeviceName, getImgUrl } from "@/utils/utils";
const { confirm } = Modal;

const DownLoadBtn = ({ record }) => {
  const [totalSize, setTotalSize] = useState(0);
  let loading = useRef(false);
  let [text, setText] = useState("下载全部媒体文件");
  const abortController = new AbortController();
  const signal = abortController.signal;
  const workerRef = useRef(null);

  const onCancel = () => {
    abortController.abort();
    loading.current = false;
    setTotalSize(0);
    setText("下载全部媒体文件");
  };

  const beginDownLoad = (record) => {
    // return streamedZip(record)
    if (loading.current) {
      message.warning("正在努力打包下载文件中，此过程时间较长，请稍待");
      return;
    }
    confirm({
      title: "下载媒体文件",
      content: "确定下载全部媒体文件吗？",
      okText: "下载",
      cancelText: "取消",
      async onOk() {
        downLoadMedia(record);
      },
    });
  };
  const downloadFile = async (url, fileName, setCount, message, onCancel) => {
    try {
      const response = await fetch(url);
      const total = +response.headers.get("Content-Length");
      const reader = response.body.getReader();
      let receivedLength = 0;
      const chunks = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
        receivedLength += value.length;
        const percentage = (receivedLength / total) * 100;
        setTotalSize(percentage.toFixed(0));
      }

      const blob = new Blob(chunks);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
      message.success("下载完成");
      onCancel();
    } catch (error) {
      console.error("下载错误:", error);
      message.error("下载文件时出现错误");
      onCancel();
    }
  };

  const downLoadMedia = async () => {
    let isFetching = false; // 添加状态标记
    const mL = await HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`);
    if (mL && mL.length > 0) {

        loading.current = true;
        setText("服务响应中...");
        let intervalId;

        const getFileZip = async () => {
            if (isFetching) return; 
            isFetching = true;
            try {
                const res = await axiosApi(
                    `/api/v1/Media/GetFileZip?id=${record.TaskID}`,
                    "GET"
                );
                if (res && res.code === 1) {
                    setText(`打包中`);
                    if (res.data.includes(".zip")) {
                        clearInterval(intervalId);
                        setText("正在下载...");
                        const zipUrl = getImgUrl(res.data.substring(1));
                        const zipName = `${getDeviceName(record.DeviceSN)}_${record.FlightLineName}_${record.CreateTime.slice(0, record.CreateTime.indexOf("T"))}.zip`;
                        await downloadFile(zipUrl, zipName, setTotalSize, message, onCancel);
                    } else {
                      console.error(res?.msg || "下载失败");
                    }
                }
            } catch (error) {
                console.error("请求获取文件链接错误:", error);
                // message.error("请求获取文件链接时出现错误");
                onCancel();
            } finally {
                isFetching = false; 
            }
        };

        intervalId = setInterval(getFileZip, 3000);
    } else {
        onCancel();
        message.warning("没有文件可下载");
    }
};


  // const downLoadMedia = async (record) => {
  //   const mL = await HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`);

  //   if (mL && mL.length > 0) {
  //     loading.current = true;
  //     setText("服务响应中...");

  //     const GetFileZip = async () => {
  //       let res = await axiosApi(
  //         `/api/v1/Media/GetFileZip?id=${record.TaskID}`,
  //         "GET"
  //       );

  //       if (res && res.code == 1) {
  //         setText(res.data);

  //         if(res.data.includes(".zip")){
  //           setText("正在下载...");
  //           const zipUrl = getImgUrl(res.data.substring(1));
  //           console.log("zipUrl:", zipUrl);

  //           try {
  //             const response = await fetch(zipUrl);
  //             const total = +response.headers.get("Content-Length"); // 获取文件的总长度
  //             const reader = response.body.getReader();
  //             let receivedLength = 0;
  //             const chunks = [];

  //             // 读取数据
  //             while (true) {
  //               const { done, value } = await reader.read();
  //               if (done) break;
  //               chunks.push(value);
  //               receivedLength += value.length;

  //               // 计算下载进度
  //               const percentage = (receivedLength / total) * 100;
  //               setTotalSize(percentage.toFixed(0));
  //             }

  //             const zipBlob = new Blob(chunks);

  //             // 生成下载链接
  //             const url = window.URL.createObjectURL(zipBlob);
  //             const link = document.createElement("a");
  //             link.href = url;
  //             let zipName = `${getDeviceName(record.DeviceSN)}_${record.FlightLineName}_${record.CreateTime.slice(0, record.CreateTime.indexOf("T"))}.zip`;
  //             link.setAttribute("download", zipName); // 设置下载文件的名称
  //             document.body.appendChild(link);
  //             link.click();
  //             document.body.removeChild(link);
  //             window.URL.revokeObjectURL(url);
  //             message.success("下载完成");
  //             onCancel();
  //           } catch (error) {
  //             console.error("下载错误:", error);
  //             message.error("下载文件时出现错误");
  //             onCancel();
  //           }
  //         } else {
  //           // onCancel();
  //           message.info(res?.msg || "下载失败");
  //         }
  //         }
  //     }

  //     setTimeout(()=>{
  //       GetFileZip()
  //     }, 2000)

  //   } else {
  //     onCancel();
  //     message.warning("没有文件可下载");
  //   }
  // };

  // const handleBatchDownload = async (selectImgList) => {
  //   const zip = new JSZip();
  //   for (let i = 0; i < selectImgList.length; i++) {
  //     const item = selectImgList[i];
  //     try {
  //       if (!loading.current) {
  //         break;
  //       }
  //       const response = await DownLoadFile(getImgUrl(item.ObjectName), {
  //         responseType: "blob",
  //         signal,
  //       });
  //       const blob = new Blob([response]);
  //       const file_name = `${item.WayLineNM}-航点${item.HangDianIndex}${item.FileName.slice(item.FileName.indexOf("."))}`;
  //       zip.file(file_name, blob, { binary: true });
  //       setTotalSize(`${i + 1} / ${selectImgList.length}`);
  //     } catch (error) {
  //       if (error.name === "AbortError") {
  //         console.warn("下载请求被中止");
  //       } else {
  //         console.warn("下载失败:", error);
  //       }
  //     }
  //   }
  //   let zipName = `${getDeviceName(record.DeviceSN)}_${record.FlightLineName}_${record.CreateTime.slice(0, record.CreateTime.indexOf("T"))}.zip`;
  //   const content = await zip.generateAsync({ type: "blob" });
  //   if (content.size > 22) {
  //     FileSaver.saveAs(content, zipName);
  //     onCancel();
  //   }
  // };

  // const handleBatchDownload = (files) => {
  //   const worker = new Worker(new URL('./downloadWorker.js',import.meta.url));

  //   const MAX_CONCURRENT_REQUESTS = 5; // 最大并发数
  //   const CHUNK_SIZE = 14024 * 14024; // 分片大小

  //   worker.postMessage({ files, maxConcurrentRequests: MAX_CONCURRENT_REQUESTS, chunkSize: CHUNK_SIZE });

  //   worker.onmessage = (event) => {
  //     const content = event.data;
  //     FileSaver.saveAs(content, `download_${Date.now()}.zip`);
  //     console.log("文件下载并压缩完成");
  //   };

  //   worker.onerror = (error) => {
  //     console.error("Web Worker 错误:", error);
  //   };
  // };

  return (
    <AddButton onClick={() => beginDownLoad(record)}>
      <span>{totalSize ? `${totalSize}%` : ""}</span>
      {text}
    </AddButton>
  );
};

export default DownLoadBtn;
