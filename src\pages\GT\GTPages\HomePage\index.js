import { useEffect, useState } from "react";
import LeafletMap from "./Maps/LeafletMap";
import LeftPage2 from "@/pages/GT/GTPages/LeftPage/LeftPage2";
import RightPage2 from "@/pages/GT/GTPages/RightPage/RightPage2";
import MyModal from "@/pages/GT/components/MyModal";
import LeftBox from "@/pages/GT/components/LeftBox";
import RightBox from "@/pages/GT/components/RightBox";
const HomePage = () => {
  const [isMyModalOpen, setIsMyModalOpen] = useState(true);
  const closeMyModal = () => {
    setIsMyModalOpen(false);
  };

  return (
    <div style={{ position: "relative" }}>
      <div style={{ position: "absolute", zIndex: 1, width: "100vw" }}>
        <LeafletMap />
      </div>
      <LeftBox child={<LeftPage2 />}/>
      <RightBox child={<RightPage2/>}/>


      <MyModal
        title=""
        content={<div>图斑属性，属性内容</div>}
        isOpen={isMyModalOpen}
        onClose={closeMyModal}
      />
    </div>
  );
};

export default HomePage;
