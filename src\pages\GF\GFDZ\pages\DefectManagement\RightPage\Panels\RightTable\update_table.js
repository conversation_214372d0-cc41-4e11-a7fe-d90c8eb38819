import { useEffect, useState, useRef } from "react";
import ComStyles from "@/pages/common.less";
import {
  Button,
  Radio,
  message,
  Modal,
  Descriptions,
  Input,
  InputNumber,
  Upload,
} from "antd";
import { InboxOutlined } from "@ant-design/icons";
import { HPost2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
const { Dragger } = Upload;
const { TextArea } = Input;
import MyButton from "@/pages/GT/components/MyButton/MyButton";

const updateTable = ({ mapList, refrush, title }) => {
  const reSet = useRef();
  const [canSee, setCanSee] = useState(false);
  const [MapName, setMapName] = useState("");
  const [MapType, setMapType] = useState();
  const [Url, setUrl] = useState("");
  const [MaxZoom, setMaxZoom] = useState();
  const [MinZoom, setMinZoom] = useState();
  const [Lat, setLat] = useState();
  const [Lng, setLng] = useState();
  const [opacity, setOpacity] = useState("");
  const [attribution, setAttribution] = useState("");
  const [Title, setTitle] = useState();
  const [sfile, setSFile] = useState();

  function clear() {
    setMapName(null);
    setMapType(null);
    setUrl(null);
    setMaxZoom(null);
    setMinZoom(null);
    setLat(null);
    setLng(null);
  }

  const submit = async () => {
    if (!MapType && MapType !== 0 && MapType == null && MapType == undefined) {
      return message.open({
        type: "warning",
        content: "请选择地图类型!",
      });
    }
    if (!MapName) {
      return message.open({
        type: "warning",
        content: "请填写地图名称!",
      });
    }
    for (let value of mapList) {
      if (value.MapName === MapName) {
        return message.open({
          type: "warning",
          content: "地图名称重复,请换个名称!",
        });
      }
    }
    if (!Url) {
      return message.open({
        type: "warning",
        content: "地图地址不能为空!",
      });
    }
    //  let rulePic = [
    //   'jpg','jpeg','png','gif','webp','tiff','svg','psd','raw',,'bmp',
    //   'ai','eps','ufo','pcd','cdr','exif','tif','fps','tga','dxf',
    //   'WMF','avif','apng'
    //  ]
    if (MapType === 11 && Url && !Url.endsWith("json")) {
      return message.open({
        type: "warning",
        content: "当前地图格式与所填URL可能不匹配,应该为json格式!",
      });
    }
    // if(MapType === 0 || MapType === 1 && Url && !Url.endsWith(rulePic)){
    //   return message.open({type:'warning',content:'当前地图格式与所填URL可能不匹配,应该为图片格式!'})
    // }

    if (MapType === 0 || MapType === 1) {
      if (!Lat || !Lng) {
        return message.open({
          type: "warning",
          content: "请输入该地图的经纬度!",
        });
      }
    }

    const submitData = {
      ID: "",
      OrgCode: "",
      MapType: MapType,
      State: "",
      MapName: MapName,
      DirPath: "",
      Url: Url,
      MaxZoom: MaxZoom,
      MinZoom: MinZoom,
      Lat: Lat,
      Lng: Lng,
      Remark: "",
      CreateTime: Date.now(),
    };

    let res = await HPost2("/api/v1/MapData/Add", submitData);
    if (isEmpty(res.err)) {
      clear();
      refrush();
      message.open({
        type: "success",
        content: `上传地图成功!`,
      });
      setCanSee(false);
    } else {
      message.open({
        type: "error",
        content: "错误:" + res.err,
      });
    }
  };
  const upProps2 = {
    name: "file",
    multiple: true,
    onChange(info) {
      const { status } = info.file;
      if (status !== "uploading") {
        console.log(info.file, info.fileList);
        setTitle(info.file.name);
      }
      if (status === "done") {
        setSFile(info.file.originFileObj);
      } else if (status === "error") {
        message.error(`${info.file.name} 文件上传失败.`);
      }
    },
  };
  let inputStyle = {
    margin: 0,
    width: "100%",
  };
  let radioList = [
    { value: 0, text: "普通地图" },
    { value: 1, text: "正摄影像(tms)" },
    { value: 11, text: "三维模型(b3dm)" },
  ];

  return (
    <div>
      {/* <Button
        className={ComStyles.addButton}
        onClick={() => setCanSee(true)}
        style={{ color: "#fff" }}
      >
        上传地图
      </Button> */}
      <MyButton onClick={() => setCanSee(true)}>上传</MyButton>
      <div>
        <div>
          <Modal
            title={null}
            onOk={submit}
            open={canSee}
            onCancel={() => setCanSee(false)}
            okText="提交"
            cancelText="取消"
            width={900}
          >
            <div>
              <Descriptions
                ref={reSet}
                title={`上传文件`}
                column={1}
                colonMarginRight={20}
                labelStyle={{ width: "80px" }}
              >
                <Descriptions.Item label="文件名称">
                  <Input
                    placeholder="若标题为空,则默认为文件名称"
                    style={{ margin: 0, width: "90%" }}
                    value={Title}
                    onChange={(e) => {
                      setTitle(e.target.value);
                    }}
                    allowClear
                  />
                </Descriptions.Item>
                <Descriptions.Item
                  label={""}
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <Dragger {...upProps2}>
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">拖拽或单击文件上传</p>
                    <p className="ant-upload-hint" style={{ width: 800 }}>
                      {/* 当前可支持预览的文件类型有:pdf,csv,xlsx,docx */}
                    </p>
                  </Dragger>
                </Descriptions.Item>
                {/* <Descriptions.Item label="地图类型">
                  <Radio.Group
                    value={MapType}
                    onChange={(e) => setMapType(e.target.value)}
                  >
                    {radioList.map((item) => {
                      return (
                        <Radio key={item.value} value={item.value}>
                          {item.text}
                        </Radio>
                      );
                    })}
                  </Radio.Group>
                </Descriptions.Item>
                <Descriptions.Item label="地图名称">
                  <Input
                    style={inputStyle}
                    value={MapName}
                    onChange={(e) => {
                      setMapName(e.target.value);
                    }}
                    allowClear
                  />
                </Descriptions.Item>
                <Descriptions.Item label="地图URL">
                  <TextArea
                    style={inputStyle}
                    value={Url}
                    onChange={(e) => {
                      setUrl(e.target.value);
                    }}
                    allowClear
                    autoSize
                  />
                </Descriptions.Item>
                <Descriptions.Item label="最大缩放">
                  <InputNumber
                    style={inputStyle}
                    min={0}
                    max={20}
                    value={MaxZoom}
                    onChange={(value) => {
                      setMaxZoom(value);
                    }}
                    allowClear
                  />
                </Descriptions.Item>
                <Descriptions.Item label="最小缩放">
                  <InputNumber
                    style={inputStyle}
                    min={0}
                    max={20}
                    value={MinZoom}
                    onChange={(value) => {
                      setMinZoom(value);
                    }}
                    allowClear
                  />
                </Descriptions.Item>
                <Descriptions.Item label="初始经度">
                  <InputNumber
                    style={inputStyle}
                    min={-180}
                    max={180}
                    value={Lng}
                    onChange={(value) => {
                      setLng(value);
                    }}
                    allowClear
                  />
                </Descriptions.Item>
                <Descriptions.Item label="初始维度">
                  <InputNumber
                    style={inputStyle}
                    min={-90}
                    max={90}
                    value={Lat}
                    onChange={(value) => {
                      setLat(value);
                    }}
                    allowClear
                  />
                </Descriptions.Item> */}
              </Descriptions>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default updateTable;
