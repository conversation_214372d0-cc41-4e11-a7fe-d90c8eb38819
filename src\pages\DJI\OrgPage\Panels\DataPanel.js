import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';
import Chart from './../../../../utils/chart';
import {BorderBox7} from '@jiaminghi/data-view-react';

const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
      }
    },
    backgroundColor:'',
    textStyle:{
      color:'white'
     },
    legend: {top:'10%',textStyle:{
      color:'white'
     }},
    grid: {
        top:'20%',
      left: '10%',
      right: '10%',
      bottom: '10%',
      containLabel: true,
     
    },
    xAxis: {
      type: 'value',
    
    },
    yAxis: {
      type: 'category',
      data: ['机场1', '机场2', '机场3', '机场4']
    },
    series: [
      {
        name: '一键起飞',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: [320, 302, 301, 334]
      },
      {
        name: '路标巡检',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: [120, 132, 101, 134 ]
      },
      {
        name: '事故巡查',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: [220, 182, 191, 234]
      },
     
    ]
  };


  

const DataPanel=()=>{
    return  <div style={{height:'45%',width:'100%',marginTop:16.0}}><BorderBox7 style={{background:`rgba(0,45,139,0.4)`}}><BlockTitle style={{margin:8.0}} title="飞行统计"></BlockTitle><Chart option={option}  /></BorderBox7></div>
}

export default DataPanel;
