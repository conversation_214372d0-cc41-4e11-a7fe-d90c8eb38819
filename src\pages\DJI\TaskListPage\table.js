import { Space, Tag, Badge, Modal, message, Tooltip } from "antd";
import { WayLineTypeToString, getDeviceName, isEmpty } from "@/utils/utils";
import { dateFormat2 } from "@/utils/helper";
import { Post2, Get2, axiosApi } from "@/services/general";
import TaskDetailPage from "@/pages/DJI/TaskDetailPage";
import { ErrJson } from "./ErrorData";
import { HGet2 } from "@/utils/request";
import { MessageOutlined } from "@ant-design/icons";
import DownLoad from "./downLoad";
import { queryPage2 } from "@/utils/MyRoute";
import TrajectoryDisplayPage from "@/pages/DJI/TrajectoryDisplay/index";
import styles from "@/pages/DJI/WayLine/table.module.less";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const { confirm } = Modal;
const deleteTask = async (record, refrush) => {
  const xx = await Post2("/api/v1/Task/Delete", record);

  console.log("deleteTask", xx);
  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err);
  } else {
    message.info("删除成功！");
    refrush();
  }
};

const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: "删除记录",
    //icon: <ExclamationCircleFilled />,
    content: "确定删除该航线记录吗？",
    okText: "删除",
    okType: "danger",
    cancelText: "取消",
    onOk() {
      deleteTask(record, refrush);
    },
    onCancel() {
      // console.log('Cancel');
    },
  });
};
function TrajectoryDisplay(record, setPage) {
  axiosApi("/api/v1/Task/GetLineById", "GET", { taskId: record.TaskID }).then(
    (res) => {
      if (res.code === 1 && res.data && res.data.length) {
        setPage(<TrajectoryDisplayPage record={record} data={res.data} />);
      } else {
        message.error(`无轨迹可回显`);
      }
    }
  );
}

const getError = (record) => {
  if (!isEmpty(record.ExceptionMessage)) {
    if (record.ExceptionMessage.length > 5) return record.ExceptionMessage;
  }
  if (!isEmpty(record.ExpectedCode)) {
    if (!isEmpty(ErrJson[record.ExpectedCode]))
      return ErrJson[record.ExpectedCode];
  }
  //
  return "错误:" + record.ExpectedCode;
};

const YouXian = async (record) => {
  await HGet2(
    "/api/v1/Media/Upload" +
      "?sn=" +
      record.DeviceSN +
      "&fID=" +
      record.TaskID.toLowerCase()
  );
};

const FlyContinue = async (record) => {
  await Post2("/api/v1/WayLine/FlyContinue", record);
};

const Map2DStart = async (record) => {
  await Post2("/deal/Map2D", record);
};

const ifDDXF = (record) => {
  if ((record.TaskState == 2) & (record.TaskStopTime.length > 5)) return true;
  return false;
};

const TableCols = (handleClick, refrush, setPage) => {
  const handleClick2 = (record) => {
    if (record["PhotoUpload"] > 0) {
      handleClick(record);
    }
  };
  const columns = [
    {
      title: getTableTitle("机场名称"),
      dataIndex: "DeviceSN",
      key: "DeviceSN",
      align: "center",
      render: (record) => getDeviceName(record),
    },

    {
      title: getTableTitle("任务类型"),
      // dataIndex: 'FlightLineType',
      // key: 'WayLineType',
      align: "center",
      render: (record) => (
        <div>{WayLineTypeToString(record.FlightLineType)}</div>
      ),
    },
    {
      title: getTableTitle("航线名称"),
      dataIndex: "FlightLineName",
      key: "FlightLineName",
      align: "center",
    },
    {
      title: getTableTitle("开始时间"),
      dataIndex: "CreateTime",
      key: "CreateTime",
      align: "center",
      render: (record) => dateFormat2(record),
      //  width:200,
    },
    {
      title: getTableTitle("任务状态"),
      //dataIndex: 'TaskState',
      // key: 'TaskState',
      align: "center",
      render: (record) => (
        <span style={{ width: "100%" }}>
          <Badge
            style={{ marginRight: 4.0 }}
            status={record.TaskState > 0 ? "success" : "error"}
          />
          <span style={{ cursor: "pointer" }}>
            {record.TaskState > 0 ? "已完成" : "进行中"}
          </span>

          <span style={{ cursor: "pointer", marginLeft: 12.0 }}>
            {record.ExpectedCode > 0 ? (
              <Tooltip style={{ marginLeft: 8.0 }} title={getError(record)}>
                <MessageOutlined style={{ color: "orange" }} />
              </Tooltip>
            ) : null}
          </span>
        </span>
      ),
    },
    {
      title: getTableTitle("飞行距离"),
      dataIndex: "Distance",
      key: "Distance",
      align: "center",
      render: (record) => record.toFixed(1) + " 公里",
      //  width:200,
    },
    {
      title: getTableTitle("飞行时间"),
      dataIndex: "FlyTM",
      key: "FlyTM",
      align: "center",
      render: (record) => (record / 60).toFixed(0) + " 分钟",
      //  width:200,
    },
    {
      title: getTableTitle("拍摄文件"),
      //dataIndex: 'TaskState',
      // key: 'TaskState',
      align: "center",
      render: (record) => (
        <div>
          {/* <Badge style={{marginRight:4.0}} status= {"success"} /> */}
          {/* <span>{record["PhotoUpload"]+"/"+record["PhotoCount"]}</span> */}
          <span>{record["PhotoUpload"]}</span>
        </div>
      ),
    },
    //   width:200,
    // ,{
    //   title: getTableTitle('关联告警'),
    //   dataIndex: 'ExceptionMessage',
    //   key: 'ExceptionMessage',
    //   align:'center',
    //   width:300,
    // }
    {
      title: getTableTitle("操作"),
      align: "center",
      render: (record) => (
        <Space size="middle" style={{ cursor: "pointer" }}>
          <Tag>
            <DownLoad record={record} key={record.TaskID} />
          </Tag>
          <Tag>
            <a onClick={() => setPage(<TaskDetailPage data2={record} />)}>
              详情
            </a>
          </Tag>
          {/* <Tag><a onClick={()=>handleClick(record)}>照片</a></Tag> */}
          {/* <Tag ><a enabled={false}  onClick={()=>handleClick2(record)}>文件</a></Tag> */}
          {/* <Tag><a onClick={()=>YouXian(record)}>优先</a></Tag> */}
          {ifDDXF(record) ? (
            <Tag>
              <a onClick={() => FlyContinue(record)}>继续飞行</a>
            </Tag>
          ) : null}
          {/* <Tag><a onClick={()=>Map2DStart(record)}>正射建模</a></Tag> */}
          <Tag>
            <a onClick={() => showDeleteConfirm(record, refrush)}>删除</a>
          </Tag>
          <Tag>
            <a onClick={() => TrajectoryDisplay(record, setPage)}>轨迹回显</a>
          </Tag>
        </Space>
      ),
    },
  ];
  const rowClassName = (record) => {
    switch (record.FlightLineType) {
      case "1":
        return styles["xunjian-row"];
      case "2":
        return styles["patrol-row"];
      case "3":
        return styles["inspection-row"];
      case "4":
        return styles["mapping-row"];
      case "自动创建测绘航线":
        return styles["auto-mapping-row"];
      default:
        return styles["default-row"];
    }
  };
  return { columns, rowClassName };
};

export default TableCols;
