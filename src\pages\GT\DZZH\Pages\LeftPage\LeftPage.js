import React, { useState } from "react";
import styles from './LeftPage.less'
// import ZhengSheYingXiang from "./MapPanel/ZhengSheYingXiang";
// import Model3DMap from "./MapPanel/Model3DMap";
// import Juan<PERSON><PERSON> from "./MapPanel/JuanLian";
// import MapOperatePage from "./MapPanel/MapOperatePage";
// const Home = () => <h2>Home Page</h2>;
// const About = () => <h2>About Page</h2>;

const pages = {
  // ZhengSheYingXiang: <ZhengSheYingXiang />,
  // Model3DMap: <Model3DMap></Model3DMap>,
  // JuanLian: <JuanLian></JuanLian>,
  // MapOperatePage: <MapOperatePage></MapOperatePage>,
};
const LeftPage = () => {
  const [currentPage, setCurrentPage] = useState("ZhengSheYingXiang");
  const renderPage = () => {
    return pages[currentPage] || pages["ZhengSheYingXiang"];
  };
  let list = [
    { label: "正射影像", key: "ZhengSheYingXiang" },
    { label: "三维影像", key: "Model3DMap" },
    { label: "正射对比", key: "JuanLian" },
    { label: "地图管理", key: "MapOperatePage" },
  ];
  return (
    <div>
      <div
      className={styles.curtainNavBox}
      >
        {list.map((item, index) => {
          return(
            <nav className={`${styles.curtainNav} ${currentPage === item.key?styles.curtainNavClick:''}`} key={item.key} onClick={() => setCurrentPage(item.key)}
            >{item.label}</nav>
          )
        })}
      </div>

      <div>{renderPage(currentPage)}</div>
    </div>
  );
};


export default LeftPage;
