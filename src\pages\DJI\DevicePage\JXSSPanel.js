import { isEmpty, bytesToSize } from "@/utils/utils";


import { Alert, Card, Descriptions } from 'antd';
import styles from './JXSSPanel.less';

import { useModel } from "umi";
import DeBugButton from "./DebugButton";
import { getBaseColor, getGuid, getLocation } from "@/utils/helper";
import { useEffect, useState } from "react";

import PanelBox from '@/pages/SI/components/Styles/PanelBox';


const ModeCodeJson = { "0": "空闲中", "1": "现场调试", "2": "远程调试", "3": "固件升级中", "4": "作业中" }
const CoverStateJson = { "0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常" }

const CoverRainJson = ["无雨", "小雨", "中雨", "大雨"];

const JCXX = ({ device, setIfY }) => {

  const { jc, jcData } = useModel('dockModel')
  const { setFj } = useModel('droneModel')
  const { setGps } = useModel('gpsModel')
  const { ifWayline, setIfWayLine } = useModel('mapModel')

  const sn = device.SN;
  const [device2, setD] = useState(device);
  // console.log('jc',jc)
  useEffect(() => {
    if (isEmpty(jc)) return;
    if (isEmpty(jc.sub_device)) return;
    if (jc.sub_device.device_online_status == "0") {
      setGps([jc.latitude, jc.longitude, jc.height]);
      setFj({})
    }
    // updateDevice();
  }, jcData.current);



  useEffect(() => {
    if (jc.mode_code == "4" && !ifWayline) {
      setIfWayLine(true)
    }

    if (jc.mode_code == "0" && ifWayline) {
      setIfWayLine(false)
      console.log('dock', 'xxxx')
      localStorage.removeItem('wayPoints')
      localStorage.removeItem('gpsPoints')
    }
    // console.log('jc', jc)
  }, [jc]);



  if (isEmpty(jc)) return <div />
  // const jc=JSON.parse(lastMessage.data)
  const data = jc;

  //console.log(data)


  const getItem = (label, val, dw, color = '#08e7cb') => {
    return <Descriptions.Item key={getGuid()} labelStyle={{ fontSize: 12.0, color: 'white' }} contentStyle={{ fontSize: 12.0, color: color }} label={label}>{val + dw}</Descriptions.Item>
  }

  const getPanel = (list) => {
    return <Descriptions style={{ fontSize: 12.0, color: 'white' }} column={2}>
      {list}
    </Descriptions>
  }
  const getWS = (v) => {
    if (v < 1000) {
      return v.toFixed(0) + "KB/s";
    }
    return (v / 1024).toFixed(1) + "MB/s";
  }
  const panel2 = (data) => {

    const list = [];
    list.push(getItem("机场状态", ModeCodeJson[data.mode_code], ""));
    list.push(getItem("舱盖状态", CoverStateJson[data.cover_state], ""));
    list.push(getItem("舱内温度", data["temperature"], "℃"));
    list.push(getItem("舱内湿度", data.humidity, "%"));

    list.push(getItem("无人机状态", data.sub_device?.device_online_status == "0" ? "关机" : "开机", ""));
    list.push(getItem("是否在舱", data.drone_in_dock == "1" ? "是" : "否", "", '#FFE16F'));

    // 设备端获取不到数据的异常值为 32767
    list.push(getItem("无人机电量", data.drone_charge_state?.capacity_percent > 100 ? '--':data.drone_charge_state?.capacity_percent, "%", '#FFE16F'));
    //list.push(getItem("剩余存储", (100 - data.storage.used / data.storage.total * 100).toFixed(1), "%"));
    list.push(getItem("剩余存储", bytesToSize((data.storage?.total - data.storage?.used) * 1000), ""));
    list.push(getItem("当前风速", data.wind_speed?.toFixed(1), "m/s"));
    list.push(getItem("降雨情况", CoverRainJson[data.rainfall], ""));
    //list.push(getItem("精确定位", device2.Location,""));
    const jcdata2 = localStorage.getItem('jcdata2')
    if (jcdata2 != null) {
      list.push(getItem("待上传文件", JSON.parse(jcdata2).media_file_detail.remain_upload, ""));
    }
    list.push(getItem("机场网速", getWS(data.network_state?.rate), ""));
    list.push(getItem("备降点", data.alternate_land_point.is_configured === 0 ? "未设置" : "已设置", ""));
    return getPanel(list);
  }

  const titleDiv = <div draggable={false} onClick={() => setIfY(false)}>{device.DName}</div>

  return <div className={styles.cardCon}> <Card bordered={false} size="small" title={titleDiv} extra={<DeBugButton sn={sn}></DeBugButton>} headStyle={{ color: '#fff' }}  >
    <PanelBox>
      {panel2(data)}
    </PanelBox>
  </Card> </div>
}

export default JCXX;
