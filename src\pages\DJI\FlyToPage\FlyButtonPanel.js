import styles from "./FlyButtonPanel.less";
import { Get2 } from "@/services/general";
import { MainColor } from "@/utils/colorHelper";
import { Row, Col, Button, message } from "antd";
import WaylinePanel from "@/pages/DJI/WayLine/WaylinePanel";
import { useModel, history } from "umi";
import { FlyTheLine, SaveTheLine, FlyToPoint, CheckIfCanFly } from "./helper";
import { useEffect, useState, useRef } from "react";
import { getBodyH, isEmpty } from "@/utils/utils";
import { getPListStr } from "@/pages/Maps/helper";
import IfShowPanel from "@/components/IfShowPanel";
import ChangeHeightPanel from "./Panels/ChangeHeightPanel";
import AIStartPanel from "./Panels/AIPanel";
import { getGuid } from "@/utils/helper";
import NewModal from "@/components/NewModal";
import { FJStart } from "../DRCPage/Panels/RtmpChange";
import { checkIfFlyer } from "@/utils/utils";
import { StopAIVideo } from "./Panels/AIHelper";
const FlyButtonPanel = ({ device }) => {
  const [modal, setModal] = useState(<div />);
  const [open, setOpen] = useState(false);
  const { ifWayline, setIfWayLine } = useModel("mapModel");
  const { DoCMD, DoCMD2, DoCMD3 } = useModel("cmdModel");
  const { pData } = useModel("pageModel");
  const [isDanBinPlus2, setIsDanbBinPlus2] = useState(
    device.BindCode === "pilot" && device.Model === "RC_PLUS_2"
  );
  const { cloudAuth } = useModel("eventModel");

  const { ifDrawLine, setIfDrawLine, setPList, pList, ifP, setIfP } =
    useModel("mapModel");
  const tL0 = [
    "一键起飞",
    "指点飞行",
    "航线飞行",
    "绘制航线",
    "航线暂停",
    "航线恢复",
    "一键返航",
    "取消返航",
    "重新绘制",
    "开始飞行",
    "取消绘制",
  ];
  const { jc } = useModel("dockModel");
  const { fj } = useModel("droneModel");
  const { newPList } = useModel("gpsModel");
  const [ifWayStop, setIfWayStop] = useState(false);

  useEffect(() => {
    return () => {
      setPList([]);
      setIfDrawLine(false);
    };
  }, []);

  useEffect(() => {
    if (cloudAuth?.output?.status === "ok") {
      handleClick("一键起飞");
    }
  }, [cloudAuth]);

  const handleClick = async (e) => {
    if (e === "授权") {
      let user = JSON.parse(localStorage.getItem("user"));
      let data = {
        user_id: user.UserID,
        user_callsign: user.UserName,
        control_keys: ["flight"],
      };
      DoCMD(device.SN, "cloud_control_auth_request", data);
    }
    if (e == "一键起飞") {
      if (isDanBinPlus2) {
        let user = JSON.parse(localStorage.getItem("user"));
        let data = {
          user_id: user.UserID,
          user_callsign: user.UserName,
          control_keys: ["flight"],
        };
        DoCMD(device.SN, "cloud_control_auth_request", data);
        await axiosApi(`/api/v1/FlyTo/TakeOffForPilot`, "GET", {
          sn: device.SN,
          lat: fj.data.latitude,
          lng: fj.data.longitude,
        }); //单兵设备无人机一键起飞到指定点位
        return;
      }

      if (checkIfFlyer()) {
        const pb1 = await CheckIfCanFly(device.SN);
        if (!pb1) return;
        setIfWayLine(false);
        localStorage.removeItem("gpsPoints");
        localStorage.removeItem("IfWayLine");
        newPList();
        Get2("/api/v1/FlyTo/TakeOff?sn=" + device.SN);
      }
    }
    if (e === "取消授权") {
      let data = {
        control_keys: ["flight"],
      };
      DoCMD(device.SN, "cloud_control_release", data);
    }
    if (e == "航线飞行") {
      if (checkIfFlyer()) {
        const pb1 = await CheckIfCanFly(device.SN);
        if (!pb1) return;

        newPList();
        setModal(<WaylinePanel key={getGuid()} device={device} />);
        setOpen(true);
      }
    }
    if (e == "航线暂停") {
      if (checkIfFlyer()) {
        setIfWayStop(true);
        Get2("/api/v1/WayLine/Pause?sn=" + device.SN);
      }
    }

    if (e == "停止指点飞行") {
      if (checkIfFlyer()) {
        if (isEmpty(fj)) return;
        if (isEmpty(fj.data)) return;

        if (fj.data?.mode_code == 17) {
          DoCMD(device.SN, "fly_to_point_stop", {});
        }
      }
    }

    if (e == "航线恢复") {
      if (checkIfFlyer()) {
        setIfWayStop(false);
        Get2("/api/v1/WayLine/Recory?sn=" + device.SN);
      }
    }
    if (e == "一键返航") {
      if (checkIfFlyer()) {
        Get2("/api/v1/WayLine/ToHome?sn=" + device.SN);
      }
    }
    if (e == "取消返航") {
      if (checkIfFlyer()) {
        Get2("/api/v1/WayLine/ToHomeCancel?sn=" + device.SN);
      }
    }
    if (e == "遥控模式") {
      if (checkIfFlyer()) {
        // 新版操作界面需要通过localStorage来传递信息，便于返回到驾驶舱
        localStorage.setItem('fromCockpit', 'true');
        localStorage.setItem('device', JSON.stringify(device));
        history.push("/DJ/device");
      }
    }
    if (e == "绘制航线") {
      if (checkIfFlyer()) {
        setIfDrawLine(true);
        setPList([]);
        newPList();
        message.info("在地图上点击绘制临时航线！");
      }
    }
    if (e == "重新绘制") {
      if (checkIfFlyer()) {
        setPList([]);
        message.info("已清除所有已绘制点，可以重新绘制航线！");
      }
    }
    if (e == "调整高度") {
      if (checkIfFlyer()) {
        // FlyToPoint(sn, fj.latitude,fj.longitude,fj.height+20)
        // ChangeHeightPanel(fj.height,(h1)=>FlyToPoint(sn,fj.latitude,fj.longitude,device.Height+h1))
        // setCMDPanel(<ChangeHeightPanel h1={fj.elevation} flyTo={(h1)=>FlyToPoint(sn,fj.latitude,fj.longitude,device.Height+h1)}/>);
        //
        const fj2 = fj.data;
        setModal(
          <ChangeHeightPanel
            h1={fj2.elevation}
            flyTo={(h1) => {
              FlyToPoint(
                device.SN,
                fj2.latitude,
                fj2.longitude,
                device.Height + h1
              );
              setOpen(false);
            }}
          />
        );
        setOpen(true);
      }
    }
    if (e == "取消绘制") {
      if (checkIfFlyer()) {
        setPList([]);
        setIfDrawLine(false);
      }
    }
    if (e == "静音模式") {
      const data = {
        silent_mode: 1,
      };

      DoCMD3(device.SN, `thing/product/${device.SN}/property/set`, data);
    }
    if (e == "开启夜航灯") {
      const data = {
        night_lights_state: 0,
      };

      DoCMD3(device.SN, `thing/product/${device.SN}/property/set`, data);
    }
    if (e == "开始飞行") {
      if (checkIfFlyer()) {
        const pb1 = await CheckIfCanFly(device.SN);
        if (!pb1) return;

        localStorage.removeItem("gpsPoints");
        newPList();
        FlyTheLine(device, pList, device.Height + 100);
        setIfDrawLine(false);
        localStorage.setItem("wayPoints", getPListStr(pList));
      }
    }
    if (e == "上传航线") {
      if (checkIfFlyer()) {
        SaveTheLine(device, pList, device.Height + 100);
        setIfDrawLine(false);
        message.info("上传成功");
      }
    }
    if (e == "指点飞行") {
      setIfP(true);
      message.info("在地图上点击选择点！");
      // Get2("/api/v1/FlyTo/TakeOff?sn="+device.SN)
    }

    if (e == "语音喊话") {
      Get2(
        "/api/v1/PSDK/Speaker?sn=" + device.SN + "&val=语音测试中1 2 3 4 5 6"
      );
    }
    if (e == "设置音量") {
      Get2("/api/v1/PSDK/SpeakerSetVolume?sn=" + device.SN);
    }

    if (e == "AI识别") {
      setModal(<AIStartPanel setOpen={setOpen}></AIStartPanel>);
      setOpen(true);
    }
    if (e == "停止识别") {
      // setIfAI(false);
      pData.current.ifAI = false;
      // FJStart();
      // const data = {
      //   method: "StopAI",
      //   data: {},
      // };
      // DoCMD2("thing/" + device.SN + "/ai", data);

      StopAIVideo(pData.current.AITaskID);
    }
    if (e == "飞行器急停") {
      const gateway_sn = device.SN;
      const topic = `thing/product/${gateway_sn}/drc/down`;
      const data = {
        data: {},
        method: "drone_emergency_stop",
      };
      DoCMD2(topic, data);
      /* let resultNum = await DoCMD2(topic, data);
      if (resultNum.data.result === 0) {
        message.info("飞行器急停成功！");
        return;
      } else {
        message.info("急停失败,正在尝试返航急停");
        Get2("/api/v1/WayLine/ToHome?sn=" + device.SN);
        setTimeout(() => {
          Get2("/api/v1/WayLine/ToHomeCancel?sn=" + device.SN);
        }, 400);
        return;
      } */
    }
  };

  const getItemList = () => {
    const jcdata1 = JSON.parse(localStorage.getItem("jcdata1"));
    if (isEmpty(jcdata1)) return;
    let aiTitle = "AI识别";

    if (pData.current.ifAI) {
      aiTitle = "停止识别";
    }


    if ((isEmpty(fj) && !isDanBinPlus2) || jcdata1.mode_code == 0) {
      if (ifDrawLine) {
        return ["重新绘制", "开始飞行", "取消绘制", "上传航线"];
      } else {
        return ["一键起飞", "航线飞行", "绘制航线"];
      }
    }

    const list = [];

    if (isDanBinPlus2) {
      if (
        fj?.data?.mode_code == 0 &&
        (JSON.stringify(cloudAuth) == "{}" || cloudAuth.output.status != "ok")
      ) {
        //  list.push("授权")
        list.push("一键起飞");
        //  list.push("取消授权")
      }
    }

    if (
      fj.data &&
      ((fj.data.mode_code > 2 && fj.data.mode_code < 9) ||
        fj.data.mode_code == 17)
    ) {
      list.push("一键返航");
    }

    if (fj.data && fj.data.mode_code == "17") {
      list.push("停止指点飞行");
    }

    if (fj.data && fj.data.mode_code == "3") {
      list.push(["调整高度"]);
      setIfP(true);
    } else {
      setIfP(false);
    }

    if (fj.data && fj.data.mode_code == "9") {
      list.push(["取消返航"]);
    }

    if (!isDanBinPlus2 && fj.data && fj.data.mode_code == "5") {
      list.push(["航线暂停"]);
    }

    if (!isDanBinPlus2 && fj.data && ifWayStop) {
      list.push("航线恢复");
    }

    if (fj.data && fj.data.mode_code > 0) {
      list.push(aiTitle);
      list.push("遥控模式");
    }
    return list;
  };

  // 按钮图标映射函数
  const getIconClass = (buttonText) => {
    const iconMap = {
      "一键起飞": "itemIconFly",
      "航线飞行": "itemIconAirline",
      "绘制航线": "itemIconDrawline",
      "一键返航": "itemIconBack",
      "停止指点飞行": "itemIconStop",
      "AI识别": "itemIconAI",
      "遥控模式": "itemIconRemote",
    };
    return iconMap[buttonText] || "itemIcon"; // 默认使用原来的itemIcon
  };

  const getItem = (e) => {
    return (
      <Col className={styles.Navbtn}>
        <div className={styles[getIconClass(e)]} onClick={() => handleClick(e)}></div>
        {/* <Button
          ghost="true"
          className={styles.items}
          onClick={() => handleClick(e)}
        > */}
          <div className={styles.itemText}>{e}</div>
        {/* </Button> */}
      </Col>
    );
  };

  const getItems = (xx) => {
    const list = [];
    if (isEmpty(xx)) return list;
    xx.forEach((e) => {
      list.push(getItem(e));
    });
    return list;
  };

  const Panel = () => {
    return (
      <div>
        <Row justify="center">
          {getItems(getItemList())}
          <NewModal open={open} setOpen={setOpen} child={modal}></NewModal>
        </Row>
      </div>
    );
  };

  return Panel();
};

export default FlyButtonPanel;
