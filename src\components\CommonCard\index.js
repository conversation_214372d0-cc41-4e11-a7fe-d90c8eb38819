import React from 'react';
import moduleStyles from './index.less';

/**
 * 公用卡片组件
 * @param {string|ReactNode} title - 标题内容，可以是字符串或React节点
 * @param {ReactNode} extra - 卡片右上角内容
 * @param {ReactNode} children - 卡片内容区域
 * @param {string} className - 额外的CSS类名
 * @param {object} style - 额外的样式
 */
const CommonCard = ({ title, extra, children, className = '', style = {}, isScroll = false }) => {
  return (
    <div className={`${moduleStyles.customCard} ${className}`} style={style}>
      {title && (
        <div className={moduleStyles.title}>
          {title}
          {extra && <div className={moduleStyles.extra}>{extra}</div>}
        </div>
      )}
      <div className={`${moduleStyles.content} ${isScroll ? moduleStyles.scroll : ''}`}>{children}</div>
    </div>
  );
};

export default CommonCard;
