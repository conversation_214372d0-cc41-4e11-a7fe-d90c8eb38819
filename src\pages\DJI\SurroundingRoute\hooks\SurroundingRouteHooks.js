import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { message } from 'antd';
import { GetCesiumViewer, Cartesian3_TO_Position, SetCameraControl, DrawLookCone, SetCamera, PointImg2 } from '@/utils/cesium_help';
import * as turf from '@turf/turf'
import AmapMercatorTilingScheme from '@/utils/cesiumtools/AmapMercatorTilingScheme'

const useSurroundingRouteHooks = () => {
    // 大地图viewer
    const viewer = useRef(null);
    // cesium事件
    let handlerPoint = useRef(null)
    // 移动选中的点
    let move_entity_index = useRef(null);
    // 鼠标移动方式
    let cursor = useRef('')
    // 正在拖动的航点索引
    let move_index = useRef(null)
    // 延时器
    let timer = useRef(null)
    // 航线实体
    let wayline_polyline = useRef(null);
    // 中心点列表
    let centerPointList = useRef([])
    // 中心点位置列表
    let centerPositionList = useRef([])
    // 环绕点列表
    let surroundingPointList = useRef([])
    // 环绕点位置列表
    let surroundingPositionList = useRef([])
    // 页面载入
    useEffect(() => {
        viewer.current = GetCesiumViewer('cesisss')
        viewer.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
            Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
        );
        viewer.current.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(103.99177815222232, 30.762915428775877, 5000),
            orientation: {
                heading: 0,
                pitch: Cesium.Math.toRadians(-90),
                roll: 0,
            },
        });
        document.getElementById('cesisss').oncontextmenu = function () {//地图上取消浏览器默认的右击事件
            return false;
        }
        // 添加cesium事件
        handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer.current.scene.canvas)
        viewer.current.imageryLayers.addImageryProvider(new Cesium.UrlTemplateImageryProvider({
            url: "http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
            minimumLevel: 0,
            maximumLevel: 18,
            tilingScheme: new AmapMercatorTilingScheme(),
        }))
        //鼠标事件
        LEFT_DOWN()
        LEFT_UP()
        MOUSE_MOVE()
        return () => {
            destroy()
        };
    }, []);
    // 鼠标左键按下事件
    function LEFT_DOWN() {
        handlerPoint.current.setInputAction(function (e) {
            let pickedObject = viewer.current.scene.pick(e.position);
            if (move_entity_index.current !== null) {
                timer.current = setTimeout(() => {
                    if (pickedObject.id.name === '中心点') {
                        viewer.current._container.style.cursor = "move";
                        cursor.current = "move"
                        move_index.current = Number(pickedObject.id.index)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    } else if (pickedObject.id.billboard) {
                        viewer.current._container.style.cursor = "s-resize";
                        cursor.current = "s-resize"
                        move_index.current = Number(pickedObject.id.index)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    }
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            } else {
                timer.current = setTimeout(() => {
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            }
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }
    // 鼠标左键抬起事件
    function LEFT_UP() {
        handlerPoint.current.setInputAction(function (e) {
            if (timer.current) {
                clearTimeout(timer.current)
                if (move_entity_index.current === null) {
                    let ray = viewer.current.camera.getPickRay(e.position);
                    let cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                    addWaypoint(longitude, latitude, height, globeHeight)
                } else {
                }
            } else {
                console.log('长按事件');
                viewer.current._container.style.cursor = "";
                cursor.current = ""
                move_index.current = null
                viewer.current.scene.screenSpaceCameraController.enableRotate = true;//开启旋转
            }
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
    // 鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            let pickedObject = viewer.current.scene.pick(e.endPosition);
            if (pickedObject && pickedObject.id && pickedObject.id.name === '中心点') {
                if (move_entity_index.current !== null) {
                    centerPointList.current[move_entity_index.current].point.color = Cesium.Color.WHITE
                    move_entity_index.current = null
                }
                move_entity_index.current = Number(pickedObject.id.index)
                if (centerPointList.current[move_entity_index.current].point) {
                    centerPointList.current[move_entity_index.current].point.color = Cesium.Color.RED
                }
            } else {
                if (move_entity_index.current !== null) {
                    viewer.current._container.style.cursor = "";
                    centerPointList.current[move_entity_index.current].point.color = Cesium.Color.WHITE
                    move_entity_index.current = null
                }
            }
            if (cursor.current === "move") {
                viewer.current._container.style.cursor = "move";
                let ray = viewer.current.camera.getPickRay(e.endPosition);
                let cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                item_position_change_function(move_index.current, longitude, latitude, globeHeight)

            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    // 添加航点
    function addWaypoint(longitude, latitude, height, globeHeight) {
        centerPositionList.current.push([longitude, latitude, height])
        centerPointList.current.push(viewer.current.entities.add({
            name: '中心点',
            index: centerPointList.current.length,
            position: new Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
            point: {
                pixelSize: 10,
                color: Cesium.Color.WHITE, // 点的颜色
            },
        }))
        let surroundingPoint = []
        let surroundingPosition = []
        for (let i = 0; i < 17; i++) {
            let destination = turf.destination(turf.point([longitude, latitude]), 0.05, -180 + 22.5 * i);
            surroundingPosition.push([destination.geometry.coordinates[0], destination.geometry.coordinates[1], height])
            surroundingPoint.push(viewer.current.entities.add({
                name: '环绕点',
                position: new Cesium.Cartesian3.fromDegrees(destination.geometry.coordinates[0], destination.geometry.coordinates[1], height),
                point: {
                    pixelSize: 10,
                    color: Cesium.Color.WHITE, // 点的颜色
                },
            }))
        }
        surroundingPointList.current.push(surroundingPoint)
        surroundingPositionList.current.push(surroundingPosition)
        if (centerPointList.current.length > 0) {
            // 创建航线
            wayline_polyline.current = creat_wayline_polyline()
        }
    }
    // 改变某个航点位置
    function item_position_change_function(index, longitude, latitude, globeHeight, callback) {
        centerPositionList.current[index] = [longitude, latitude, globeHeight]
        centerPointList.current[index].position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
        for (let i = 0; i < 17; i++) {
            let destination = turf.destination(turf.point([longitude, latitude]), 0.05, -180 + 22.5 * i);
            surroundingPositionList.current[index][i] = [destination.geometry.coordinates[0], destination.geometry.coordinates[1], globeHeight]
            surroundingPointList.current[index][i].position.setValue(new Cesium.Cartesian3.fromDegrees(destination.geometry.coordinates[0], destination.geometry.coordinates[1], globeHeight))
        }
    }
    // 销毁函数
    function destroy() {
        viewer.current.entities.removeAll();
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.WHEEL, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.CTRL);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 创建航线
    function creat_wayline_polyline() {
        return viewer.current.entities.add({
            name: `航线`,
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(surroundingPositionList.current.flat(Infinity));
                }, false),
                // 宽度
                width: 6,
                arcType: Cesium.ArcType.RHUMB,
                material: Cesium.Color.fromCssColorString('#0aed8a'),
            },
        })
    }
    return {

    }
}
export default useSurroundingRouteHooks;