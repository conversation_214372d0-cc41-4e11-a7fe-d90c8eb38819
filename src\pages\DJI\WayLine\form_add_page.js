import { Input, Radio, Descriptions, Button, Modal, message, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';

const { TextArea } = Input;
const WayLineTypeList = ["巡检航线", "测绘航线"]
const WayLineAddForm = (props) => {

  const refrush = props.data;
  const [devices, setDevices] = useState([]);
  const [sn, setSN] = useState('');
  const [wType, setWType] = useState('');
  const [wName, setWName] = useState('');
  const [remark, setRemark] = useState('');
  const [canSee, setCanSee] = useState(false);

  useEffect(() => {
    getDevices();
  }, []);


  const getDevices = async () => {
    let pst = await Get2('/api/v1/Device/GetAllList', {});
    setDevices(pst);
  };

  const upProps = () => {
    const token = localStorage.getItem('token');
    return {
      name: 'file',
      action: '/api/v1/WayLine/Upload',
      data: { sn, wName, wType, remark },
      showUploadList: false,
      //accept:'video/*',
      multiple: false,
      headers: {
        authorization: 'authorization-text',
        'auth': token,
      },
      // beforeUpload: file => {
      //   console.log('112',file.type)

      //   const isPNG = (file.type.indexOf("vnd.google-earth.kmz") == -1);
      //   if (isPNG) {
      //     message.error(`${file.name} 不是kmz文件`);
      //   }
      //   return !isPNG || Upload.LIST_IGNORE;
      // },

      onChange(info) {
        console.log('123213', info)
        if (info.file.status === 'done') {
          message.success(`${info.file.response} 航线上传成功`);
          setCanSee(false)
          refrush();
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 航线上传失败！`);
        }

      },
    }
  };


  const pbV = () => {
    if (isEmpty(sn) || isEmpty(wType) || isEmpty(wName)) {
      return true
    }
    return false
  }


  const getSNList = () => {
    const list = []
    devices.map(p => {
      list.push(<Radio value={p.SN}>{p.DName}</Radio>)
    })
    return list
  }


  const getTypeList = () => {
    const list = []
    WayLineTypeList.map(p => {
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }


  return <div><Button type="primary" onClick={() => setCanSee(true)}>kmz航线文件</Button> 
  <Modal title={null} footer={null} onOk={null} open={canSee} onCancel={() => setCanSee(false)}>
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Descriptions title="航线上传" column={1}   >
        <Descriptions.Item label="选择机场"><Radio.Group onChange={(e) => { setSN(e.target.value) }} >
          {getSNList()}
        </Radio.Group></Descriptions.Item>
        <Descriptions.Item label="航线类型"><Radio.Group onChange={(e) => { setWType(e.target.value) }} >
          {getTypeList()}
        </Radio.Group></Descriptions.Item>
        <Descriptions.Item label="航线名称"><Input onChange={(e) => { setWName(e.target.value) }} /></Descriptions.Item>
        <Descriptions.Item label="备注信息"><TextArea rows={4} onChange={(e) => { setRemark(e.target.value) }} /></Descriptions.Item>
        <Descriptions.Item label="上传航线"><Upload {...upProps()}>
          <Button disabled={pbV()} >
            选择文件
          </Button>
        </Upload>
        </Descriptions.Item>
      </Descriptions>
    </div>
  </Modal>
  </div>

}



export default WayLineAddForm;