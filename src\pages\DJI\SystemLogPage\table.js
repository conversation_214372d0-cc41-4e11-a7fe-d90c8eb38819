import { Space, Tag, message,Modal } from 'antd';
import {  isEmpty } from '@/utils/utils';
import { Post2 } from '@/services/general';

const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const { confirm } = Modal;

const deleteOrgInfo=async(record,refrush)=>{

  const xx=await Post2("/api/v1/OrgInfo/Delete",record);

  console.log('deleteOrgInfo',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除组织',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该组织吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteOrgInfo(record,refrush);
    },
    onCancel() {
     // console.log('Cancel');
    },
  });
};

const TableCols =(refrush,showEdit)=>{return [
          {
            title: getTableTitle('组织名称'),
            dataIndex: 'OrgName',
            key: 'OrgName',
            align:'center',
           // width:200,
          },
          {
            title: getTableTitle('组织代码'),
            dataIndex: 'OrgCode',
            key: 'OrgCode',
            align:'center',
           // width:200,
          }, {
            title: getTableTitle('联系人'),
            dataIndex: 'ZhiBan',
            key: 'ZhiBan',
            align:'center',
           // width:200,
          }, {
            title: getTableTitle('联系电话'),
            dataIndex: 'JianKong',
            key: 'JianKong',
            align:'center',
           // width:200,
          },
        {
             title: getTableTitle('操作'),
            align:'center',
            render: (record) => (
              <Space size="middle">
                <Tag><a onClick={()=>showEdit(record)}>编辑</a></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除</a></Tag>
              </Space>)
          }];
        }

export default TableCols;
