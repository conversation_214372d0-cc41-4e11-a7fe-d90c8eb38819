import React, { useRef, useEffect } from 'react';
 
const BatteryButton = ({value}) => {
  const canvasRef = useRef(null);
  const w1=12
  const h1=20
  const c1=5

  useEffect(() => {
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    const drawV=(v)=>{
      
        const x =2;
        
        const width = w1-4;
        let height = v*((h1-c1)/(c1));
        if(height>h1-4) height=h1-4;
        const y = h1-height-2;
        const fillColor = 'white'; // 填充颜色
        // 绘制矩形
        context.fillStyle = fillColor;
        context.fillRect(x, y, width, height);
        context.strokeStyle='white';
        context.strokeRect(0,0,w1,h1);
    }
    // 设置矩形的属性
    drawV(value);
  }, []); // 空依赖数组确保只在组件挂载时执行
 
  return (
    <canvas style={{}} ref={canvasRef} width={w1} height={h1}></canvas>
  );
};
 
export default BatteryButton;