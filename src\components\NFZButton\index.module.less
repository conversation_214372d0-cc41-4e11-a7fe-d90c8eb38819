/**
 * 禁飞区按钮组件样式
 */

.nfzButton {
  width: 45px !important;
  height: 45px !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  // 不同状态的颜色
  &.ant-btn-primary {
    background: linear-gradient(135deg, #1890ff, #096dd9) !important;
    border-color: #1890ff !important;
    
    &:hover {
      background: linear-gradient(135deg, #40a9ff, #1890ff) !important;
      border-color: #40a9ff !important;
    }
  }
  
  &.ant-btn-danger {
    background: linear-gradient(135deg, #ff4d4f, #cf1322) !important;
    border-color: #ff4d4f !important;
    
    &:hover {
      background: linear-gradient(135deg, #ff7875, #ff4d4f) !important;
      border-color: #ff7875 !important;
    }
  }
  
  &.ant-btn-default {
    background: linear-gradient(135deg, #ffffff, #f5f5f5) !important;
    border-color: #d9d9d9 !important;
    color: #595959 !important;
    
    &:hover {
      background: linear-gradient(135deg, #f5f5f5, #e6f7ff) !important;
      border-color: #40a9ff !important;
      color: #1890ff !important;
    }
  }
  
  // 图标样式
  .anticon {
    font-size: 20px !important;
    line-height: 1 !important;
  }
  
  // 加载状态
  &:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    
    &:hover {
      transform: none !important;
    }
  }
}

// 数据统计显示
.nfzStats {
  position: absolute;
  top: -35px;
  right: 0;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 12px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(0, 0, 0, 0.75);
  }
}

// 当按钮激活时显示统计信息
.nfzButton:hover + .nfzStats,
.nfzStats:hover {
  opacity: 1;
  transform: translateY(0);
}

// 响应式设计
@media (max-width: 768px) {
  .nfzButton {
    width: 42px !important;
    height: 42px !important;
    
    .anticon {
      font-size: 18px !important;
    }
  }
  
  .nfzStats {
    font-size: 11px;
    padding: 3px 6px;
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .nfzButton.ant-btn-default {
    background: linear-gradient(135deg, #1f1f1f, #2f2f2f) !important;
    border-color: #434343 !important;
    color: #d9d9d9 !important;
    
    &:hover {
      background: linear-gradient(135deg, #2f2f2f, #1e3a8a) !important;
      border-color: #40a9ff !important;
      color: #40a9ff !important;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

// 加载状态脉冲动画
.nfzButton.ant-btn-primary:disabled {
  animation: pulse 2s infinite;
}

// 错误状态闪烁动画
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}

.nfzButton.ant-btn-danger {
  animation: blink 1s ease-in-out 3;
}
