import { Input, Form, Radio, Button, Modal, message, Select } from "antd";
import { useState, useEffect } from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";
import { Get2 } from "@/services/general";
import { SM4Util } from "sm4util";

const userTypeList = ["系统管理员", "无人机飞手", "普通用户"];

const GetDepartment = () => {};

const CronAddForm = (props) => {
  const GetSM4 = (data) => {
    const sm4 = new SM4Util();
    const miStr1 = sm4.encryptDefault_ECB(data);
    return miStr1;
  };
  const GetSM4Back = (data) => {
    const sm4 = new SM4Util();
    const miStr1 = sm4.decryptDefault_ECB(data);
    return miStr1;
  };
  const [canSee, setCanSee] = useState(false);
  const { UserList, record, refrush } = props;
  const [ID, setID] = useState(record.ID);
  const [ph, setPH] = useState(record.Phone);
  const [password, setPassword] = useState(GetSM4Back(record.Password));
  const [userNM, setUserName] = useState(GetSM4Back(record.UserName));
  const [name, setName] = useState(record.Name);
  const [uType, setUType] = useState(record.Authority);
  const [dList, setDList] = useState([]);
  const [depart, setDpart] = useState({});

  const getDList = async () => {
    let pst = await Get2("/api/v1/Department/GetAllList", {});
    if (isEmpty(pst)) pst = [];
    setDList(pst);
  };

  useEffect(() => {
    getDList();
  }, []);

  const onSave = async (e) => {
    if (!name) {
      return message.warning("请输入用户名称");
    }
    if (!uType) {
      return message.warning("请选择用户权限");
    }
    if (!userNM) {
      return message.warning("请输入登录名");
    }
    if (!password) {
      return message.warning("请输入登录密码");
    }
    const phoneRegex = /^1\d{10}$/;
    if (ph && !phoneRegex.test(ph)) {
      message.warning("请输入有效电话号码!");
      return;
    }

    const data = {
      ID: ID,
      Name: name,
      UserName: userNM,
      Password: password,
      Phone: ph,
      Authority: uType,
      DepartCode: depart.DCode,
      DepartName: depart.DName,
    };

    const xx = await HPost2("/api/v1/UserInfo/Update", data);
    if (isEmpty(xx.err)) {
      refrush();
      setCanSee(false);
      message.success("编辑成功！");
    }else{
      message.error(xx.err);
    }
  };

  const getTypeList = (xL) => {
    const list = [];
    xL.map((p) => {
      list.push(<Radio value={p}>{p}</Radio>);
    });
    return list;
  };

  const onDT = (values) => {
    setDt(values);
  };

  const onName = (e) => {
    console.log("onName values of form: ", e.target.value);
    setName(e.target.value);
  };

  const getDSelect = (dList) => {
    const list = [];
    dList.forEach((e) => {
      list.push(
        <Select.Option key={e.DCode} data={e} value={e.DCode}>
          {e.DName}
        </Select.Option>
      );
    });
    console.log("CronAddForm", list);

    return list;
  };

  const onChange = (values) => {
    const xx = dList.find((item) => {
      return item.DCode === values;
    });
    console.log("onChange values of form: ", xx);
    setDpart(xx);
  };

  return (
    <div>
      <a onClick={(e) => setCanSee(true)}>编辑</a>
      <Modal
        title={null}
        onOk={onSave}
        open={canSee}
        onCancel={() => setCanSee(false)}
        okText="提交"
        cancelText="取消"
        width={500}
      >
        <Form
          labelCol={{
            span: 4,
          }}
          wrapperCol={{
            span: 18,
          }}
          layout="horizontal"
          style={{
            maxWidth: 600,
          }}
        >
          {/* <Form.Item label="所属部门">
        <Select onSelect={onChange}>{getDSelect(dList)}</Select>
      </Form.Item> */}

          <Form.Item label="用户名称">
            <Input
              defaultValue={name}
              onChange={(e) => setName(e.target.value)}
            ></Input>
          </Form.Item>
          <Form.Item label="用户权限">
            <Radio.Group
              defaultValue={uType}
              onChange={(e) => {
                setUType(e.target.value);
              }}
            >
              {getTypeList(userTypeList)}
            </Radio.Group>
          </Form.Item>
          <Form.Item label="登录名">
            <Input
              defaultValue={userNM}
              onChange={(e) => setUserName(e.target.value)}
            ></Input>
          </Form.Item>

          <Form.Item label="密码">
            <Input
              defaultValue={password}
              onChange={(e) => setPassword(e.target.value)}
            ></Input>
          </Form.Item>

          <Form.Item label="电话">
            <Input
              defaultValue={ph}
              onChange={(e) => setPH(e.target.value)}
            ></Input>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CronAddForm;
