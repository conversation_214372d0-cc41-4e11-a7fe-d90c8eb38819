import React, { useState } from "react";
import styles from "./RightPage.less";
import { Input, Space, ConfigProvider, Select } from "antd";
import RightTable from "@/pages/GT/GTPages/RightPage/Panels/RightTable";
// import RightTable from "@/pages/GT/DZZH/Pages/RightPage/RightPage2";
import HeadTitle from "@/pages/GT/components/HeadTitle/HeadTitle";
import MyEcharts from "@/utils/chart2";
import alarmStatistics from "./Panels/EchartsData/alarmStatistics";

const LeftPage2 = () => {
  let option = alarmStatistics;

  return (
    <div className={styles.RightPage2}>
      <HeadTitle text="缺陷处理统计"/>
      <div className={styles.myEcharts}>
        <MyEcharts height={200} option={option} />
      </div>
      <HeadTitle text="AI缺陷处理"/>
      <div>
        <RightTable></RightTable>
      </div>
    </div>
  );
};

export default LeftPage2;
