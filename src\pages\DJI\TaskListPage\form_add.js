import { Input, Card,Tag, DatePicker,Radio,Descriptions,Select, Row, Col, Button, Form ,message, Table,Upload} from 'antd';
import dayjs from 'dayjs';

import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { isEmpty } from '@/utils/utils';


const { TextArea } = Input;
const WayLineTypeList=["库区巡查","工程安全","电厂线路","一键起飞"]
const WayLineAddForm=()=>  { 
  const [devices, setDevices] = useState([]);
  const [sn, setSN] = useState('');
  const [wType, setWType] = useState('');
  const [wName, setWName] = useState('');
  const [remark, setRemark] = useState('');
  
  useEffect(() => {
    getDevices();
  },[]);


  const getDevices = async () => {
    let pst= await Get2( '/api/v1/Device/GetAllList',{});
    setDevices(pst);
  };
  
const onFormChange=(e)=>{
  console.log(e)
}
  
const upProps =()=> {
 // console.log('upProps2',data);
  return {
  name: 'file',
  action: '/api/v1/WayLine/Upload',
  data:{sn,wName,wType,remark},
  showUploadList: false,
  //accept:'video/*',
  multiple: false,
  headers: {
    authorization: 'authorization-text',
  },
  beforeUpload: file => {
    //console.log('112',file.type)
   
    const isPNG = (file.type.indexOf("vnd.google-earth.kmz") == -1);
    if (isPNG) {
      message.error(`${file.name} 不是kmz文件`);
    }
    return !isPNG || Upload.LIST_IGNORE;
  },

  onChange(info) {
    console.log('123213',info)
    if (info.file.status === 'done') {
      message.success(`${info.file.response} 航线上传成功`);
      //refrush();
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 航线上传失败！`);
    }
  },
}};


  const pbV=()=>{
   // console.log('pbV',sn,wType,wName)
    if(isEmpty(sn)||isEmpty(wType)||isEmpty(wName)){
      return true
    }
    return false
  }


  const getSNList=()=>{
    const list=[]
    devices.map(p=>{
      list.push(<Radio value={p.SN}>{p.DeviceName}</Radio>)
    })
    return list
  }
 

  const getTypeList=()=>{
    const list=[]
    WayLineTypeList.map(p=>{
      list.push(<Radio value={p}>{p}</Radio>)
    })
    return list
  }


 return <div  
 style={{
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
 }}

 >
  
  <Descriptions title="航线上传" column={1}   >
    <Descriptions.Item label="选择机场"><Radio.Group onChange={(e)=>{setSN(e.target.value)}} >
     {getSNList()}
  </Radio.Group></Descriptions.Item>
    <Descriptions.Item label="航线类型"><Radio.Group onChange={(e)=>{setWType(e.target.value)}} >
     {getTypeList()}
  </Radio.Group></Descriptions.Item>
    <Descriptions.Item label="航线名称"><Input onChange={ (e)=>{setWName(e.target.value)}} /></Descriptions.Item>
    <Descriptions.Item label="备注信息"><TextArea rows={4} onChange={ (e)=>{setRemark(e.target.value)}} /></Descriptions.Item>
    
   
    <Descriptions.Item label="上传航线"><Upload {...upProps()}>


<Button disabled={pbV()} >
    选择文件
  </Button>
</Upload>
</Descriptions.Item>
  
  </Descriptions>
  
  
  
  
  
  
{/*   
  <Form
 // onFinish={handleSubmit}
  onFormChange={onFormChange}
  layout="horizontal"
  labelCol={{ span: 6 }}
  wrapperCol={{ span: 18 }}
  style={{width:450,marginTop:24.0}}
  initialValues={{

  }}
  autoComplete='off'>

     <Form.Item
       name='SN'
       label='所属机场'
     >
       <Input/>
     </Form.Item>


     <Form.Item
       name='WayLineType'
       label='航线类型'
     >
       <Input/>
     </Form.Item>

     <Form.Item
       name='WayLineName'
       label='航线名称'
     >
       <Input/>
     </Form.Item>
   



<Form.Item label='上传航线文件'  colon={false}>

<Upload {...upProps()}>
<Button  >
    选择文件
  </Button>
</Upload>


</Form.Item>

<Form.Item label='  '  colon={false}>


<Button type='primary' htmlType='submit' style={{width:120}} >
    保存
  </Button>



</Form.Item>



</Form> */}

</div> }



export default WayLineAddForm;