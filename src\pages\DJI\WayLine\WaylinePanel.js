import { useState, useEffect } from "react";
import { DatePicker, Descriptions, Form,Table} from "antd";
import { isEmpty } from "@/utils/utils";
import TableCols from "./table2";
import { getGuid } from "@/utils/helper";
import { HGet2 } from "@/utils/request";
import { useModel } from "umi";

const WaylinePanel = ({ device }) => {
  const [chartData, setChartData] = useState({});
  const { setShowCanvas, setWanLineId } = useModel("pageModel");

  useEffect(() => {
    const getLineData = async () => {
      const pst = await HGet2("/api/v1/WayLine/GetBySN?SN=" + device.SN, {});
      setChartData(pst);
    };
    getLineData();
  }, []);

  if (isEmpty(chartData)) return <div></div>;
  return (
    <div style={{ zIndex: 1009 }}>
      {isEmpty(chartData) ? (
        <div />
      ) : (
        <Table
          key={getGuid()}
          pagination={{
            size: "small",
            showSizeChanger: false,
            pageSize: 6,
            align: "center",
          }}
          bordered
          dataSource={chartData}
          columns={TableCols(setShowCanvas, setWanLineId)}
          size="small"
        />
      )}
      {/* <Modal  title={null} footer={null} onOk={null} open={canSee} style={{height:600.0,width:600.0}}  onCancel={()=>setCanSee(false)}>
       <Card title='航线信息'> {WayLineMap(600,index)}</Card>
    </Modal> */}
    </div>
  );
};

export default WaylinePanel;
