import { useEffect, useState } from "react";
import { message, Card, List, Table, Space, Tag, Button } from "antd";
import L from "leaflet";
import useMqttStore from "@/stores/useMqttStore";
import useConfigStore from "@/stores/configStore";
import { axiosApi } from "@/services/general";
import { timeFormat, getGuid } from "@/utils/helper";
import { getBodyH, isEmpty } from "@/utils/utils";
import { useModel, history } from "umi";
import { CloseOutlined } from "@ant-design/icons";
import FireIncidentTask from "@/pages/DJI/FireIncidentTask";
import "./index.less";
const GlobalWarning = ({ map }) => {
  //全局弹窗组件
  let list = [
    {
      altitude: 50.5,
      event: "object_detected",
      id: 1,
      latitude: 39.9075,
      longitude: 116.3972,
      source: "test",
      status: 2,
      statusText: "已接收",
      type: "third_party_event",
    },
  ];
  const { setPage } = useModel("pageModel");
  const { connectMqtt, disconnectMqtt, messages, sendMqttMessage } =
    useMqttStore();
  const { MapSelf,tableCurrent, setTableCurrent } = useConfigStore();
  const [data, setData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(true);
  const ContainerHeight = getBodyH(166);
  const [pageCurrent, setPageCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [topicList, setTopicList] = useState([]);
  map = MapSelf;

  let third_party_list = [];
  try {
    const stored = localStorage.getItem("third_party_list");
    if (stored) {
      third_party_list = JSON.parse(stored);
    }
  } catch (e) {
    third_party_list = [];
  }

  function sendMessage() {
    // 订阅三方事件
    console.log(third_party_list, "third_party_list");

    if (!third_party_list || third_party_list.length === 0) return;
    for (let i = 0; i < third_party_list.length; i++) {
      let third_party = third_party_list[i].source;
      const topic = `thing/product/${third_party}/events`;
      setTopicList((prev) => [...prev, topic]);
      sendMqttMessage(topic);
    }
  }

  useEffect(() => {
    connectMqtt();
    return () => {
      disconnectMqtt();
    };
  }, [connectMqtt, disconnectMqtt]);

  useEffect(() => {
    let timoutId = null;
    timoutId = setTimeout(() => {
      sendMessage();
    }, 0);
    return () => {
      clearTimeout(timoutId);
    };
  }, []);

  useEffect(() => {
    getData();
  }, [messages]);

  function getData() {
    let mess = messages;

    if (!mess || Object.keys(mess).length === 0) {
      return setIsModalOpen(false);
    }
    if (!mess.topic.endsWith("/events")) {
      return setIsModalOpen(false);
    }

    setData((prevData) => {
      const newData = [...prevData];
      const existingIndex = newData.findIndex((item) => item.id === mess.id);
      if (existingIndex > -1) {
        // 更新
        newData[existingIndex] = { ...newData[existingIndex], ...mess };
      } else {
        // 新增
        newData.push(mess);
      }
      return newData;
    });
    setIsModalOpen(true);
  }

  // function getData() {
  //   console.log("getData", messages);
  //   if (!messages || messages.length <= 0) {
  //     return setIsModalOpen(false);
  //   }
  //     setData((prevData) => {
  //       const newData = [...prevData];
  //       messages.forEach((message) => {
  //         if (
  //           message.topic.includes("thing/product/") &&
  //           message.topic.endsWith("/events")
  //         ) {
  //           const existingIndex = newData.findIndex(
  //             (item) => item.id === message.id
  //           );
  //           if (existingIndex > -1) {
  //             // 更新现有条目
  //             newData[existingIndex] = {
  //               ...newData[existingIndex],
  //               ...message,
  //             };
  //           } else {
  //             // 添加新条目
  //             newData.push(message);
  //           }
  //         }
  //       });
  //       return newData;
  //     });
  //     setIsModalOpen(true);
  //   }

  function flyToPoint(element) {
    // 飞行到指定位置
    if (element.source) {
      GotoAndHolding(element);
    }
  }
  async function GotoAndHolding(element) {
    //森林防火-无人机飞行到指定位置
    let res = await axiosApi("/api/open/ForestFire/GotoAndHolding", "GET", {
      lat: element.latitude,
      lng: element.longitude,
      //  helight: element.altitude,
    });
    if (res.code === 1) {
      message.success("正在执行");
      updateStatus(element);
    } else {
      message.warning(res.msg);
    }
  }
  async function updateStatus(element, status) {
    // 更新状态
    let res = await axiosApi("/api/v1/ThirdPartyObject/UpdateStatus", "POST", {
      id: element.id,
      status: element.status,
    });
    if (res.code === 1) {
      getData();
    } else {
      message.error(res.msg);
    }
  }

  function handleToPage() {
    setPage(<FireIncidentTask />);
  }

  function handleClickItem(record) {
    let latlng = [record?.latitude, record?.longitude];
    if (MapSelf) {
      if (!latlng[0] || !latlng[1]) return;
      MapSelf.setView(latlng, 18);
      L.popup().setLatLng(latlng).setContent(`${record.event}`).openOn(MapSelf);
      //   L.marker([record?.latitude, record?.longitude])
      //     .addTo(MapSelf)
      //     .bindPopup(`${record.event}`)
      //     .openPopup();
    }
  }
  const onScroll = (e) => {
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          ContainerHeight
      ) <= 1
    ) {
      getData();
    }
  };
  const handleMouseEnter = () => {
    if (map) {
      map.dragging.disable(); // 禁用地图拖动
      map.scrollWheelZoom.disable(); // 禁用地图缩放
    }
  };

  const handleMouseLeave = () => {
    if (map) {
      map.dragging.enable(); // 启用地图拖动
      map.scrollWheelZoom.enable(); // 启用地图缩放
    }
  };
   const handleTableChange = (current) => {
    setTableCurrent(current);
  };
  const TableCols = () => {
    return [
      {
        title: "事件",
        dataIndex: "event",
        key: "event",
        align: "center",
      },
      {
        title: "来源",
        dataIndex: "source",
        key: "source",
        align: "center",
      },
      {
        title: "位置",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <div
              onClick={() => {
                handleClickItem(record);
              }}
            >
              {record.longitude}，{record.latitude}
            </div>
          </Space>
        ),
      },
      // {
      //   title: "创建时间",
      //   key: "created_at",
      //   align: "center",
      //   render: (record) => (
      //     <Space size="middle">{timeFormat(record.created_at)}</Space>
      //   ),
      // },
      {
        title: "地图操作",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <Tag>
              <a
                onClick={() => {
                  flyToPoint(record);
                }}
              >
                ✈飞到此处
              </a>
            </Tag>
          </Space>
        ),
      },
    ];
  };
  let extra = (
    <div style={{ display: "flex", justifyContent: "flex-end", gap: 10 }}>
      <Button
        type="primary"
        onClick={() => {
          handleToPage();
        }}
      >
        三方任务
      </Button>
      <div
        onClick={() => {
          setIsModalOpen(false);
          disconnectMqtt();
        }}
      >
        <CloseOutlined />
      </div>
    </div>
  );
  const elements = () => {
    if (!data || data.length === 0) return null;
    return (
      <Table
        pagination={{
          defaultPageSize: pageSize,
          defaultCurrent: pageCurrent,
          showQuickJumper: true,
          pageSizeOptions: [10, 20, 30, 40, 50],
          showSizeChanger: true,
          current: tableCurrent,
          onChange: handleTableChange,
          locale: {
            items_per_page: "条/页",
            jump_tp: "跳至",
            page: "页",
          },
        }}
        bordered
        dataSource={data}
        columns={TableCols()}
        rowKey={(record) => record.id}
        size="small"
        scroll={{ x: "calc(50%)", y: 27 * 5 }}
        style={{ cursor: "pointer" }}
      />
    );
  };
  return (
    <div
      id="Firedangerwarning"
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
    >
      {isModalOpen && (
        <Card title="三方事件" extra={extra}>
          {elements()}
        </Card>
      )}
    </div>
  );
};
export default GlobalWarning;
