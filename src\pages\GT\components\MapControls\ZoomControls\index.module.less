.zoomControls {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;

  // Light theme
  &.lightTheme {
    background: #fff;

    .zoomButton {
      color: #333; // 深色图标适合浅色背景

      &:hover {
        color: #1890ff;
        // 可以添加一个半透明的hover背景，但不能是实色背景
        box-shadow: inset 0 0 0 40px rgba(24, 144, 255, 0.1);
      }

      &:not(:last-child) {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }

  // Dark theme - 使用新的主题颜色
  &.darkTheme {
    background-color: #1D2834; // 新的深色背景
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.2);

    .zoomButton {
      color: #7EE0D4; // 青绿色图标
      position: relative;
      overflow: hidden;

      // 添加微妙的内阴影增加深度感
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(126, 224, 212, 0.05) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &:hover {
        color: #9FF5EA; // 稍微亮一点的青绿色
        transform: translateY(-1px); // 微妙的上浮效果

        // 多层阴影创造立体感
        box-shadow:
          inset 0 0 0 1px rgba(126, 224, 212, 0.3),
          0 4px 12px rgba(126, 224, 212, 0.2),
          0 2px 6px rgba(126, 224, 212, 0.1);

        &::before {
          opacity: 1;
        }

        // 添加微妙的脉冲效果
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: radial-gradient(circle, rgba(126, 224, 212, 0.3) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: pulse-effect 0.6s ease-out;
        }
      }

      &:active {
        transform: translateY(0);
        transition: transform 0.1s ease-out;
      }

      &:not(:last-child) {
        border-bottom: 1px solid rgba(126, 224, 212, 0.15);

        &:hover {
          border-bottom-color: rgba(126, 224, 212, 0.4);
        }
      }
    }
  }

  .zoomButton {
    width: 40px;
    height: 40px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    // 专业级过渡动画
    transition:
      color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      border-bottom-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 确保动画流畅
    will-change: transform, box-shadow;
    backface-visibility: hidden;
  }

  .globalButton {
    mask: url('../../../../SI/assets/image/低空监控/全局.svg') no-repeat center;
    mask-size: 20px 20px;
    -webkit-mask: url('../../../../SI/assets/image/低空监控/全局.svg') no-repeat center;
    -webkit-mask-size: 20px 20px;
    background-color: currentColor;
  }

  .zoomInButton {
    mask: url('../../../../SI/assets/image/低空监控/放大.svg') no-repeat center;
    mask-size: 20px 20px;
    -webkit-mask: url('../../../../SI/assets/image/低空监控/放大.svg') no-repeat center;
    -webkit-mask-size: 20px 20px;
    background-color: currentColor;
  }

  .zoomOutButton {
    mask: url('../../../../SI/assets/image/低空监控/缩小.svg') no-repeat center;
    mask-size: 20px 20px;
    -webkit-mask: url('../../../../SI/assets/image/低空监控/缩小.svg') no-repeat center;
    -webkit-mask-size: 20px 20px;
    background-color: currentColor;
  }

}

// 专业动画关键帧
@keyframes pulse-effect {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  50% {
    width: 30px;
    height: 30px;
    opacity: 0.4;
  }
  100% {
    width: 40px;
    height: 40px;
    opacity: 0;
  }
}