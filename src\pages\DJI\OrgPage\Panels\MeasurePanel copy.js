import React, { useEffect, useState } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import * as turf from "@turf/turf";
import styles from './MeasurePanel.less';

const MeasureTest = ({ map }) => {
  const [markersLayerGroup, setMarkersLayerGroup] = useState(L.layerGroup()); //存贮marker
  const [currentMovingMarker, setCurrentMovingMarker] = useState(null); // 跟随鼠标移动的marker
  const [DiatanceGroup, setDistanceGroup] = useState([]); // 存储线段各段的距离
  const [NodeLayerGroup, setNodeLayerGroup] = useState(L.layerGroup()); // 存贮点击添加的红点

  const [lineData, setLineData] = useState([]); // 保存绘制线的坐标点数据
  const [tempLine, setTempLine] = useState(null); // 临时绘制的线
  const [drawing, setDrawing] = useState(false); // 是否处于绘制线状态
  const [drawnLayers, setDrawnLayers] = useState([]); // 已经绘制完成的线图层

  const [polygonData, setPolygonData] = useState([]); // 保存绘制面的坐标点数据
  const [tempPolygon, setTempPolygon] = useState(null); // 临时绘制的面
  const [drawingPolygon, setDrawingPolygon] = useState(false); // 是否处于绘制面状态
  const [drawnPolygons, setDrawnPolygons] = useState([]); // 已经绘制完成的面图层

  //创建marker
  const createMarker = (text) => {
    const customIcon = L.divIcon({
      html: `<div style="width: 60px; height: 20px; background-color: rgba(0, 0, 0, 0.5); color: #fff; text-align: center; line-height: 20px;">${text}</div>`,
      iconSize: [60, 20],
      iconAnchor: [30, 30], // [30, 10] 表示水平偏移30像素，垂直偏移10像素
    });
    return customIcon;
  };

  //计算两点距离
  const calculateDistance = (point1LatLng, point2LatLng) => {
    const point1 = turf.point([point1LatLng.lng, point1LatLng.lat]);
    const point2 = turf.point([point2LatLng.lng, point2LatLng.lat]);
    const distance = turf
      .distance(point1, point2, { units: "kilometers" })
      .toFixed(2);
    return distance;
  };

  //点击位置添加marker
  const addMarker = (latlng) => {
    let test = "起点";
    if (lineData.length == 0) {
      test = "起点";
    } else {
      const distance = calculateDistance(lineData[lineData.length - 1], latlng);
      test = distance + " km";
      setDistanceGroup([...DiatanceGroup, distance]);
    }
    const customIcon = createMarker(test);
    const newMarker = L.marker(latlng, { icon: customIcon });
    markersLayerGroup.addLayer(newMarker);
    setMarkersLayerGroup(markersLayerGroup);
  };

  //跟随移动的marker
  const updateMarker = (latlng) => {
    if (drawing && lineData.length > 0) {
      const LastPoint = lineData[lineData.length - 1];
      const distance = calculateDistance(LastPoint, latlng);
      const text = `${distance} km`;
      const customIcon = createMarker(text);
      if (currentMovingMarker) {
        currentMovingMarker.setLatLng(latlng).setIcon(customIcon);
      } else {
        const newMovingMarker = L.marker(latlng, { icon: customIcon });
        markersLayerGroup.addLayer(newMovingMarker);
        setCurrentMovingMarker(newMovingMarker);
        setMarkersLayerGroup(markersLayerGroup);
      }
    }
  };

  //清除所有的Marker
  const clearMarkers = () => {
    markersLayerGroup.clearLayers();
    setMarkersLayerGroup(L.layerGroup());
    setCurrentMovingMarker(null);
  };

  //清空所有红点
  const clearNodes = () => {
    NodeLayerGroup.clearLayers();
    setNodeLayerGroup(L.layerGroup());
  };

  //左键点击绘制
  const handleMapClick = (e) => {
    if (!drawing && !drawingPolygon) return;

    if (drawing) {
      const newLineData = [...lineData, e.latlng];
      setLineData(newLineData);
      addMarker(e.latlng); // 点击添加marker
    }

    if (drawingPolygon) {
      const newPolygonData = [...polygonData, e.latlng];
      setPolygonData(newPolygonData);
    }

    //添加红点用于标记
    const node = L.circle(e.latlng, {
      color: "red",
      //fillColor: "ff0000",
      fillOpacity: 1,
      radius: 6, //圆形标记大小
      weight: 2,
    });
    NodeLayerGroup.addLayer(node);
    setNodeLayerGroup(NodeLayerGroup);
  };

  ///移动
  const handleMouseMove = (e) => {
    if (!drawing && !drawingPolygon) return;
    updateMarker(e.latlng); // 更新移动的Marker

    if (drawing) {
      const updatedLine = [...lineData, e.latlng];
      if (tempLine) {
        tempLine.setLatLngs(updatedLine);
      } else {
        const newTempLine = L.polyline(updatedLine, {
          color: "red",
          weight: 3,
        }).addTo(map);
        setTempLine(newTempLine);
      }
    }

    if (drawingPolygon) {
      const updatedPolygon = [...polygonData, e.latlng];
      if (tempPolygon) {
        tempPolygon.setLatLngs(updatedPolygon);
        if (updatedPolygon.length > 2) {
          updatedPolygon.push(updatedPolygon[0]);
          const AreaReturn = AreaMeasure(updatedPolygon); //调用函数计算面积
          tempPolygon
            .bindTooltip(`面积: ${AreaReturn} 平方公里`, {
              permanent: true,
              direction: "center",
              className: "polygon-tooltip",
            })
            .openTooltip();
        }
      } else {
        const newTempPolygon = L.polygon(updatedPolygon, {
          color: "red",
          weight: 3,
        }).addTo(map);
        setTempPolygon(newTempPolygon);
      }
    }
  };

  //右键停止
  const handleRightClick = () => {
    if (drawing) {
      stopDrawing();
    }

    if (drawingPolygon) {
      stopDrawingPolygon();
    }
  };

  
  //面积计算
  const AreaMeasure = (polygon) => {
    const MeaSurePolygon = polygon;
    const xx=[MeaSurePolygon.map((latlng) => [latlng.lng, latlng.lat])]
    const Area = turf.area({
      type: "Polygon",
      coordinates: [MeaSurePolygon.map((latlng) => [latlng.lng, latlng.lat])],
    });
    const areaInSqKm = (Area / 1000000).toFixed(2); // 转换为平方公里
    return areaInSqKm;
  };

  //开始绘制线
  const startDrawing = () => {
    setLineData([]);
    setDrawing(true);
  };

  //停止绘制线
  const stopDrawing = () => {
    if (lineData.length === 0) {
      setDrawing(false);
      return;
    }
    if (tempLine) {
      map.removeLayer(tempLine);
    }
    const finalLine = L.polyline(lineData, {
      color: "red",
      weight: 3,
    });
    finalLine.addTo(map);

    let totalDistance = 0;
    for (let i = 0; i < DiatanceGroup.length; i++) {
      const distance = Number.parseFloat(DiatanceGroup[i]);
      totalDistance = totalDistance + distance;
    }
    finalLine
      .bindTooltip("线条总长度:" + totalDistance.toFixed(2) + "千米", {
        permanent: true,
        opacity: 1.0,
        sticky: false,
        direction: "top",
      })
      .openTooltip();
    setDistanceGroup([]);

    setDrawnLayers((prevLayers) => [...prevLayers, finalLine]);
    setDrawing(false);
    setTempLine(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };

  //开始绘制多边形
  const startDrawingPolygon = () => {
    setPolygonData([]);
    setDrawingPolygon(true);
  };

  // 停止绘制多边形
  const stopDrawingPolygon = () => {
    if (polygonData.length === 0) {
      setDrawingPolygon(false);
      return;
    }
    if (tempPolygon) {
      map.removeLayer(tempPolygon);
    }
    const finalPolygon = L.polygon(polygonData, {
      color: "red",
      weight: 3,
    });
    finalPolygon.addTo(map);

    const AreaReturn = AreaMeasure(polygonData);
    finalPolygon
      .bindTooltip("总面积:" + AreaReturn + "平方公里", {
        permanent: true,
        opacity: 0.8,
        sticky: false,
        direction: "top",
      })
      .openTooltip();

    setDrawnPolygons((prevPolygons) => [...prevPolygons, finalPolygon]);
    setDrawingPolygon(false);
    setTempPolygon(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };

  //清除所有绘制
  const clearAllDrawings = () => {
    clearDrawnLayers();
    clearDrawnPolygons();
    clearMarkers();
    clearNodes();
  };

  const clearDrawnLayers = () => {
    drawnLayers.forEach((layer) => map.removeLayer(layer)); //清空绘制线图层
    setDrawnLayers([]);
  };

  const clearDrawnPolygons = () => {
    drawnPolygons.forEach((polygon) => map.removeLayer(polygon)); //清空绘制面图层
    setDrawnPolygons([]);
  };

  useEffect(() => {
    if (map) {
      markersLayerGroup.addTo(map);
      NodeLayerGroup.addTo(map);
      map.on("click", handleMapClick);
      map.on("mousemove", handleMouseMove);
      map.on("contextmenu", handleRightClick);
    }

    return () => {
      if (map) {
        map.off("click", handleMapClick);
        map.off("mousemove", handleMouseMove);
        map.off("contextmenu", handleRightClick);
      }
    };
  }, [
    map,
    lineData,
    drawing,
    tempLine,
    drawnLayers,
    polygonData,
    drawingPolygon,
    tempPolygon,
    drawnPolygons,
    markersLayerGroup,
    currentMovingMarker,
    NodeLayerGroup,
  ]);

  return (
    <div
      style={{
        height: "100px",
        width: "300px",
        padding: 8.0,
        position: "absolute",
        top: "24px",
        left:null,
        right: "30px",
        zIndex:1008,
      }}
    >
      <span className={styles.camerasBarItem} style={{fontSize:12.0}} onClick={startDrawing}>标注线段</span>
      <span className={styles.camerasBarItem} style={{fontSize:12.0}} onClick={startDrawingPolygon}>标注区域</span>
      <span className={styles.camerasBarItem} style={{fontSize:12.0}}  onClick={clearAllDrawings}>清空标注</span>
      <span className={styles.camerasBarItem} style={{fontSize:12.0}}  onClick={clearAllDrawings}>地址搜索</span>
    </div>
  );
};

export default MeasureTest;
