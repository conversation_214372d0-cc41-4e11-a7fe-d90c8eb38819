import { useState, useEffect } from 'react';
import {
  AppstoreOutlined,
  ContainerOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
  RocketOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  FileImageOutlined,
  VideoCameraOutlined,
  DeploymentUnitOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  DownOutlined,
} from '@ant-design/icons';
import { Button, Menu } from 'antd';
import { useModel } from 'umi';
import { queryPage, queryPageByPath } from '@/utils/MyRoute';
import styles from './index.less';
import eventBus from '@/utils/EventBus';

const App = ({ handlePageChange, setCollapsed, collapsed, menuItems, className }) => {
  const defaultPage = menuItems?.[0] || { key: '', label: '空页面' };
  const [openKeys, setOpenKeys] = useState([defaultPage.key]);
  const [selectedKey, setSelectedKey] = useState(defaultPage.key);

  // 监听标签页切换事件
  useEffect(() => {
    const unsubscribe = eventBus.on('tab-active-changed', activeKey => {
      setSelectedKey(activeKey);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // 处理伸缩
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 处理菜单点击
  function handleMenu(e) {
    console.log('click-MyMenu', e);

    // 获取页面信息
    const menuInfoKey = e.key;

    // 发送事件到HeadTabs组件添加标签页
    if (menuInfoKey) {
      eventBus.emit('add-tab', menuInfoKey);
      setSelectedKey(menuInfoKey)
    }

    // 仍然调用原来的处理函数来更新主页面内容
    handlePageChange(e.key);
  }

  // 处理菜单展开和收起
  const onOpenChange = keys => {
    setOpenKeys(keys);
  };

  return (
    <>
      {
        <div
          className={`${styles.sidebarContainer} ${className || ''}`}
          style={{
            position: 'fixed',
            left: 0,
            top: 47, /* 减去头部高度 MyHead.less 47px*/
            bottom: 0,
            width: collapsed ? 55 : 55, // MyMenu的width调整，需要同步调整对应ControlCenter的marginLeft
            background: '#001c1a',
            zIndex: 1000,
            transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',
          }}
        >
          {/* 折叠按钮 目前不需要 */}
          {/* <Button
            type="primary"
            onClick={toggleCollapsed}
            style={{
              marginBottom: 16,
              marginLeft: collapsed ? 16 : 16,
              marginTop: 8,
              background: '#162c53',
              borderColor: '#162c53',
              transition: 'all 0.3s',
              transform: collapsed ? 'rotate(0deg)' : 'rotate(0deg)',
            }}
          >
            <span style={{ display: 'inline-block', transition: 'transform 0.3s' }}>
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </span>
          </Button> */}
          <Menu
            selectedKeys={[selectedKey]}
            defaultOpenKeys={[selectedKey]}
            openKeys={!collapsed ? openKeys : []}
            onOpenChange={onOpenChange}
            mode="inline"
            theme="dark"
            inlineCollapsed={collapsed}
            items={menuItems}
            onClick={handleMenu}
            style={{
              height: 'calc(100vh - 47px)', /* 减去头部高度 MyHead.less 47px*/
              overflowY: 'auto',
              backgroundColor: 'transparent',
              borderRight: 'none',
            }}
            className={styles.customMenu}
          />
        </div>
      }
    </>
  );
};

export default App;
