
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,

} from 'react-leaflet'
import 'leaflet/dist/leaflet.css';



import {  Location_Market2 } from './dt_market';

const LocationMap = ({lat,lng,title,h1}) => {

  return (
    <div>

      <MapContainer layersOptions={null} attributionControl={null} zoomControl={null} preferCanvas={true} center={[lat,lng]} zoom={15} style={{ width: '100%', height: h1 }}>


      <TileLayer
    attribution={null}
 
    url="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
/>
      {Location_Market2([lat,lng],title)}
      </MapContainer>
    </div>
  )
}


export default LocationMap;