import {

  LockOutlined,

  UserOutlined,

} from '@ant-design/icons';
import { Form, Input, Checkbox, Button, message } from 'antd';
import React, { useState } from 'react';

import { history, useModel } from 'umi';

import styles from './index.less';
import styles from './LoginPage.less';
import { HGet2 } from '@/utils/request';
import { isEmpty } from '@/utils/utils';

const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const LoginPage = (props) => {
  const { userLogin = {}, submitting } = props;
  const { pageTitle } = useModel('pageModel')
  const handleSubmit = async (e) => {

    const data = await HGet2('/api/v2/User/Login?userName=' + e.userName + '&password=' + e.password);
    console.log('login', data);
    if (isEmpty(data)) {
      message.info('用户名或密码错误');
      return;
    }

    localStorage.setItem('token', data.token);
    localStorage.setItem('user', JSON.stringify(data.user));
    localStorage.setItem('orgId', data.user.OrgCode);
    localStorage.setItem('orgName', data.user.OrgName);

    history.push('/index');
    console.log(data);
  }

  return (

    <div className={styles.appLogin}>
      <div className={styles.appLoginForm}>
        <div style={{}}>
          <h2 style={{ width: '100%', textAlign: 'center', marginBottom: 12.0 }} className='title'>{'无人机智能巡检与应急管理系统'}</h2>
        </div>
        <div className='body'>
          <Form
            name='basic-login'
            // form={loginForm}
            initialValues={{ remember: true, agreement: true }}
            onFinish={handleSubmit}
            autoComplete='off'>
            <Form.Item
              name='userName'
              rules={[{ required: true, message: '请输入用户名' }]}>
              <Input
                size='large'
                prefix={<UserOutlined className='site-form-item-icon' />}
                placeholder='账户'
              />
            </Form.Item>
            <Form.Item
              name='password'
              rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password
                size='large'
                prefix={<LockOutlined className='site-form-item-icon' />}
                placeholder='密码'
              />
            </Form.Item>
            <Form.Item>
              <Button type='primary' htmlType='submit' block size='large'>
                登录
              </Button>
            </Form.Item>

          </Form>
        </div>
      </div>
      <div className={styles.bgvideo}>
      <video muted src="@/assets/img/bg.mp4" autoplay loop ></video>
      </div>
    </div>


  );
};

export default LoginPage;
