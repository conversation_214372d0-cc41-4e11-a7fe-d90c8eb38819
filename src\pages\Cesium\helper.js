import { isEmpty } from "@/utils/utils";

export function allClear(viewer){

    viewer.entities.removeAll();
    viewer.scene.primitives.removeAll();

    let gl = viewer.scene.context._originalGLContext;
    // 当canvas大小改变时，页面就会自动刷新
    gl.canvas.width = 1;
    gl.canvas.height = 1;
   
    // 销毁webgl上下文
    gl.getExtension("WEBGL_lose_context").loseContext();
    gl = null;
    // 销毁页面计时器
    // if(!isEmpty(viewer)){
    //     viewer.destroy();
    // }
   
}