// Map页面父组件样式
.mapPageContainer {
  width: 100%;
  height: calc(100vh - 56px);
  position: relative;

  // 工具箱定位
  .mapToolbox {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
  }

  // 多屏退出按钮定位
  .multiScreenExitButton {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1002;
  }

  // 多屏配置面板定位
  .multiScreenConfigPanel {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1003;
  }

  // 多屏模式容器
  .multiScreenContainer {
    width: 100%;
    height: 100%;
    position: relative;
    background: #f5f5f5;

    // 多屏地图布局
    .multiScreenMaps {
      display: grid;
      gap: 8px;
      width: 100%;
      height: 100%;
      padding: 8px;
      // 动态布局样式通过内联样式设置

      .multiScreenMapItem {
        position: relative;
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e8e8e8;

        .mapHeader {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(4px);
          padding: 8px 16px;
          border-bottom: 1px solid #e8e8e8;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }

        // 地图组件容器
        > div:last-child {
          width: 100%;
          height: 100%;
          padding-top: 40px; // 为header留出空间
        }
      }
    }

    // 多屏模式占位符样式（备用）
    .multiScreenPlaceholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 40px;
      text-align: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;

      h2 {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #fff;
      }

      p {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 12px;
        opacity: 0.9;
        max-width: 600px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 多时相对比组件定位
  .mapTemporalComparison {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1004;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .mapPageContainer {
    .mapToolbox,
    .multiScreenExitButton {
      top: 15px;
      right: 15px;
    }

    .multiScreenContainer {
      .multiScreenMaps {
        grid-template-columns: 1fr; // 移动端改为垂直布局
        grid-template-rows: 1fr 1fr;
        gap: 6px;
        padding: 6px;

        .multiScreenMapItem {
          .mapHeader {
            padding: 6px 12px;

            h4 {
              font-size: 13px;
            }
          }

          > div:last-child {
            padding-top: 36px; // 移动端header高度调整
          }
        }
      }

      .multiScreenPlaceholder {
        padding: 20px;

        h2 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
        }
      }
    }
  }
}
