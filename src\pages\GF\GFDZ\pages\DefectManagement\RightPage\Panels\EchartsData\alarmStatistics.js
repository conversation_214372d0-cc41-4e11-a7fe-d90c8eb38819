import * as echarts from 'echarts';
export default  {
  grid: {
    top:'5%',
    left: '0%',
    right:'4%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['热斑', '破裂/破损', '组件脱落', '二极管故障', '遮挡', '支架变形', '螺栓缺失'],
    axisLabel: {
      color: '#a9dcda', // 设置 x 轴刻度的颜色
      fontSize: 10, // 字体大小
      interval: 0, // 强制显示所有刻度
  },
  splitLine: {
    show: true, // 确保显示网格线
    lineStyle: {
        type: 'dashed', // 设置网格线为虚线
        color: '#798283', // 网格线颜色
        width: 0.5, // 虚线宽度
        lineDash: [5, 5] // 虚线的长度和间隔
    }
}
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      color: '#a9dcda', // 设置 y 轴刻度的颜色
      fontSize: 12, // 字体大小
      interval: 0, // 强制显示所有刻度
  },
  splitLine: {
    show: true, // 确保显示网格线
      lineStyle: {
          type: 'dashed', // 设置网格线为虚线
          color: '#798283', // 网格线颜色
          width: 0.5, // 虚线宽度
          lineDash: [5, 5] // 虚线的长度和间隔
      }
  }
  },
  series: [
    {
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'bar',
      barWidth: '30%', // 设置柱子的宽度，
      itemStyle: {
        // 使用渐变色
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#27dfa2' }, // 渐变开始颜色
          { offset: 1, color: '#005a4a' }  // 渐变结束颜色
        ])
      }
    }
  ]
  };