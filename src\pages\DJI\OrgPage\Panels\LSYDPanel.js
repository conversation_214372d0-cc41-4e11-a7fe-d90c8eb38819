import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';
import { useEffect, useState } from 'react';
import icon4 from '@/assets/icons/device.png';
import { BorderBox7 } from '@jiaminghi/data-view-react';
import { Badge, Progress, Row, Col, Button, Card, message, Spin, Timeline, Drawer, Descriptions, Modal } from 'antd';
import { getGuid, isIndividual } from '@/utils/helper';
import { getBodyH, isEmpty } from '@/utils/utils';
import { axiosApi } from '@/services/general';
import styles from './LSYDPanel.less';
import { useModel } from 'umi';
import DevicePage from '../../DevicePage';
import WayLineMap from '@/pages/DJI/WayLine/WayLineMap';
import { DownOutlined, UpOutlined, VideoCameraOutlined, LoadingOutlined } from '@ant-design/icons';
import LastPageButton from "@/components/LastPageButton";
import { CheckIfCanFly } from "@/pages/DJI/FlyToPage/helper";
import { HGet2 } from "@/utils/request";



const LSYDPanel = ({ data, onLSYDClick, onVideoClick, onShowRoute, fromSI }) => {
    const [loading, setLoading] = useState(false);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [dataSource, setDataSource] = useState([]);
    const { setPage } = useModel('pageModel');
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [detailItem, setDetailItem] = useState({});

    const [flyToModalVisible, setFlyToModalVisible] = useState(false);
    const { nearWRJlist, setNearWRJlist } = useModel('pageModel');
    const { DZZHPoint, setDZZHPoint } = useModel('pageModel');

    // 打开详情弹窗
    const openDetailModal = (item) => {
        setDetailModalVisible(true);
        setDetailItem(item);
    };

    // 处理点击头部 跳转和绘制逻辑
    const handleLSYDClick = (e, item) => {
        e.stopPropagation();

        if (onLSYDClick && item) {
            if (item.shape == null) {
                onLSYDClick(null)
                return;
            }
            // 创建坐标的深拷贝避免修改原始数据
            const reversedCoords = JSON.parse(JSON.stringify(item.shape.coordinates));

            // 递归反转坐标顺序
            const reverseCoordinateOrder = (arr) => {
                return arr.map(element => {
                    if (Array.isArray(element)) {
                        // 如果是坐标对且包含数字，则反转顺序
                        if (element.length >= 2 && typeof element[0] === 'number') {
                            return [element[1], element[0]];
                        }
                        return reverseCoordinateOrder(element);
                    }
                    return element;
                });
            };

            const adjustedCoordinates = reverseCoordinateOrder(reversedCoords);
            onLSYDClick(adjustedCoordinates);
        }
    };


    // 获取临时用地列表
    const getTempLandList = async () => {
        try {
            setLoading(true);
            const res = await axiosApi("/api/v1/Lsyd/GetList", "GET", null);

            if (res?.code === 1 && res.data) {
                setDataSource(res.data);
                console.log("@res.data", res.data);
            } else {
                throw new Error("获取临时用地数据失败");
            }

        } catch (err) {
            message.error(err.message);
        } finally {
            setLoading(false);
        }
    };

    const parseChineseDate = (dateStr) => {
        const [year, month, day] = dateStr.split(/[年月日]/).filter(Boolean).map(Number)
        return new Date(year, month - 1, day) // （JavaScript的Date对象月份从0开始）
    }

    const handleIsOverTime = (time) => {
        const jssj = parseChineseDate(time);
        const now = new Date();
        const threeMonthsLater = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        if (jssj < now) {
            return '已超时'
        }
        if (jssj <= threeMonthsLater) {
            return '即将超时'
        } else {
            return '未超时'
        }
    }

    const LSYDItem = ({ item }) => {
        const isOvertime = handleIsOverTime(item.jssj);

        return (
            <div className={`${styles.LSYDContainer}`}>
                <div
                    className={`${styles.LSYDItem} ${isOvertime === '已超时' ? styles.offline : ''}`}
                >
                    <span className={styles.LSYDIcon}
                        onClick={(e) => handleLSYDClick(e, item)} />
                    <span className={styles.xmmc} title={item.xmmc}
                        onClick={(e) => handleLSYDClick(e, item)}
                    >{item.xmmc}</span>
                    <span className={`${styles.LSYDisOverTime} ${isOvertime === '已超时' ? styles.OverTime : isOvertime === '即将超时' ? styles.SoonOvertime : ''} `}>
                        {isOvertime}
                    </span>
                    <span className={styles.flyToBtn}
                        onClick={() => {
                            setDetailItem(item);
                            // 如果它有坐标 则获取调用后端接口 获取附近的无人机列表
                            if (item.shape) {
                                getListByNearby(item.shape.coordinates[0][0]);
                            }
                        }}
                    >一键飞至
                    </span>
                    <span className={styles.sectionFooter}
                        onClick={() => openDetailModal(item)}
                    >详情
                    </span>

                </div>
            </div>
        )
    };

    // 获取附近的无人机列表
    const getListByNearby = async (geometry) => {
        const url = `/api/v1/Device/GetListByNearby?lat=${geometry[1]}&lng=${geometry[0]}`
        const res = await axiosApi(url, "GET", null)
        if (res?.data) {
            setNearWRJlist(res.data)
            //打开弹窗
            setFlyToModalVisible(true)
        } else {
            message.error("5公里范围内没有在线的可用机场")
        }
    };

    // 挂载时获取临时用地列表
    useEffect(() => {
        getTempLandList();
    }, []);

    const detailModal = (
        <Drawer
            title="详情"
            placement="left"
            width={500}
            open={detailModalVisible}
            onClose={() => setDetailModalVisible(false)}
        >
            <Descriptions column={1} bordered>
                <Descriptions.Item label="项目名称">{detailItem.xmmc}</Descriptions.Item>
                <Descriptions.Item label="用地位置">{detailItem.ydwz}</Descriptions.Item>
                <Descriptions.Item label="用地定位">{detailItem.yddw}</Descriptions.Item>
                <Descriptions.Item label="用地面积">{detailItem.ydmj}</Descriptions.Item>
                <Descriptions.Item label="占用耕地">{detailItem.zygd}</Descriptions.Item>
                <Descriptions.Item label="永久基本农田">{detailItem.yjjbnt}</Descriptions.Item>
                <Descriptions.Item label="开始时间">{detailItem.kssj}</Descriptions.Item>
                <Descriptions.Item label="结束时间">{detailItem.jssj}</Descriptions.Item>
                <Descriptions.Item label="批准文号">{detailItem.pzwh}</Descriptions.Item>
                <Descriptions.Item label="批准时间">{detailItem.pzsj}</Descriptions.Item>
                {/* <Descriptions.Item label="地理位置" span={2}>
                    示例地理位置信息
                </Descriptions.Item> */}
            </Descriptions>
        </Drawer>
    );

    const flyToModal = (
        <Modal
            title="附近可用无人机"
            open={flyToModalVisible}
            onCancel={() => setFlyToModalVisible(false)}
            footer={null}
            width={600}
            styles={{
                body: {
                    maxHeight: "600px",
                    overflowY: "auto",
                    padding: "16px 24px"
                },
            }}
        >
            <div style={{ padding: '16px 0' }}>
                {nearWRJlist.map(device => (
                    <div
                        key={device.ID}
                        className="modal-device-item"
                    >
                        <div className="device-content">
                            <img
                                src={require("@/assets/icons/device.png")}
                                alt="device"
                                className="device-image"
                            />
                            <div className="device-info">
                                <div style={{ marginBottom: 8 }}>
                                    <span>设备名称:</span>
                                    <span>{device.DName}</span>
                                </div>
                                <div style={{ marginBottom: 8 }}>
                                    <span>当前位置:</span>
                                    <span>{device.Location}</span>
                                </div>
                                <div>
                                    <span>距离(km):</span>
                                    <span>{device.Remark}</span>
                                </div>
                            </div>
                        </div>
                        <Button
                            type="primary"
                            onClick={(e) => {
                                e.stopPropagation(); // 阻止事件冒泡
                                // handelDeviceClick(device);
                                console.log('@@@确认起飞');
                            }}
                            style={{ marginTop: 8 }}
                        >
                            确认起飞
                        </Button>
                    </div>
                ))}
            </div>
        </Modal>
    );

    return (
        <>
            {detailModal}
            {flyToModal}
            <div style={{
                height: '100%', width: '500px', paddingTop: 20.0, paddingBottom: 5.0,
                background: 'linear-gradient(to left, rgb(10 19 28 / 80%) 0%, rgb(10 19 28 / 30%) 70%, rgb(10 19 28 / 0%) 100%)',
                position: 'relative'
            }}>
                <div style={{ height: 'calc(100% - 25px)', width: '400px', position: 'absolute', right: 0 }}>
                    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
                        <div className={styles.section + ' ' + styles.LSYDSection}>
                            <div className={styles.sectionTitle}>临时用地列表</div>
                            <div className={`${styles.sectionContent} ${styles.LSYDList} ${styles.wrapper}`}>
                                {dataSource.map(item => {
                                    return (
                                        <LSYDItem
                                            item={item}
                                            key={item.id}
                                        />
                                    )

                                })}
                            </div>
                        </div>
                    </BorderBox7>
                </div>
            </div>
        </>
    );
};

export default LSYDPanel;
