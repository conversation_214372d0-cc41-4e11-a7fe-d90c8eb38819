
var option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // Use axis to trigger tooltip
      type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
    }
  },
  grid: {
    top:'1%',
    left: '0%',
    right:'4%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      color: '#fff', // 设置 x 轴刻度的颜色
      fontStyle: 'italic', // 字体样式
      fontSize: 12, // 字体大小
  },
  splitLine: {
      show: true, // 确保显示网格线
      lineStyle: {
          type: 'dashed', // 设置网格线为虚线
          color: '#798283', // 网格线颜色
          width: 0.5, // 虚线宽度
          lineDash: [5, 5] // 虚线的长度和间隔
      }
  }
  },
  yAxis: {
    type: 'category',
    data: ['莫桑镇', '兴隆镇', '毛镇', '清水镇', '宁安镇', '水乡', '杨乡'],
  
    axisLabel: {
      color: '#fff', // 设置 x 轴刻度的颜色
      fontStyle: 'italic', // 字体样式
      fontSize: 12, // 字体大小
      interval: 0 // 强制显示所有刻度
  },
  splitLine: {
    show: true, // 确保显示网格线
      lineStyle: {
          type: 'dashed', // 设置网格线为虚线
          color: '#798283', // 网格线颜色
          width: 0.5, // 虚线宽度
          lineDash: [5, 5] // 虚线的长度和间隔
      }
  }
  },
  series: [

    {
      name: 'Video Ad',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [750, 712, 701, 854, 1110, 1230, 1310],
      itemStyle: {
      // 中心径向渐变
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
            { offset: 0, color: '#5193b3' }, // 0% 处的颜色
            { offset: 1, color: '#35a4d1' }  // 100% 处的颜色
        ],
        global: false // 缺省为 false
    }
    },
    },
    {
      name: 'Search Engine',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: [820, 832, 901, 934, 1290, 1330, 1320],
      itemStyle: {
        color: '#0cca82',
        borderRadius: [0, 25, 25, 0] // 设置圆角
    },
    }
  ]
};
export default option;
