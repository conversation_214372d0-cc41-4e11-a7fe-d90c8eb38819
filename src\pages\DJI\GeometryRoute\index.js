import { useState, useEffect, useRef } from 'react';
import { message, Select, InputNumber, Input, Checkbox, Button, Tooltip, Card } from 'antd';
import { Cesium } from "umi";
import { useModel } from 'umi';
import { axiosApi } from '@/services/general';
import { queryPage2 } from '@/utils/MyRoute';
import LastPageButton from "@/components/LastPageButton";
import useGeometryRouteHooks from './hooks/GeometryRouteHooks';
import useWaylineFileHooks from './hooks/waylineFileHooks';
import { CreatWaylineFile } from './creatGeomrtryWaylineFile';
import JSZip from "jszip";
import MapControl from '@/hooks/mapControl';

const BeltRoute = ({ wayLineInfo }) => {
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    let [viewerData, setviewerData] = useState(null)
    let [bottomHeight, setBottomHeight] = useState(520)
    let [topHeight, setTopHeight] = useState(550)
    let { BottomHeightChange, TopHeightChange, globalShootHeightChange, orthoCameraOverlapWChange, modelChange, cameraFlyTo } = useGeometryRouteHooks(creatPlacemarkList, creatPolygon, setviewer);
    let { waylineFileInfo, ChangeGlobalShootHeight, Changeoverlap, droneEnumValueChange, droneSubEnumValueChange, addPlacemarkList, PolygonChange } = useWaylineFileHooks(waylineChange, droneSubEnumValueOptionsChange);
    // 航线信息
    const [wayline, setWayline] = useState(null);
    // 航线名称
    const [waylineName, setWaylineName] = useState('新建航线');
    // 机库SN
    let [aircraftSN, setAircraftSN] = useState('')
    // 机库列表
    let [aircraftSNList, setAircraftSNList] = useState([])
    // 无人机机型子类型
    const [droneSubEnumValueOptions, setDroneSubEnumValueOptions] = useState([
        {
            value: 0,
            label: 'M4E',
        },
        {
            value: 1,
            label: 'M4T',
        }
    ]);
    let bottomHeightRef = useRef(null);
    let topHeightRef = useRef(null);
    let globalShootHeightRef = useRef(null);
    let orthoCameraOverlapHRef = useRef(null);
    let orthoCameraOverlapWRef = useRef(null);

    // 页面载入
    useEffect(() => {
        setWayline({ ...waylineFileInfo.current })
        axiosApi("/api/open/Device/GetAllList", "GET",).then((res) => {
            let newRes = []
            res.forEach((item, index) => {
                newRes.push({ value: item.SN, label: item.DName, Lat: item.Lat, Lng: item.Lng, Height: item.Height, key: item.ID })
            })
            setAircraftSN(newRes[0].value)
            setAircraftSNList([...newRes])
        })
    }, []);
    function setviewer(viewer) {
        setviewerData(viewer)
    }
    function waylineChange(wayline) {
        setWayline(wayline)
    }
    function droneSubEnumValueOptionsChange(val) {
        setDroneSubEnumValueOptions([...val])
    }
    function creatPlacemarkList(piontList, bearingList) {
        addPlacemarkList(piontList, bearingList)
    }
    function creatPolygon(val) {
        PolygonChange(val)
    }
    async function save() {
        if (waylineName.trim().length === 0) {
            message.error("航线名不能为空");
            return
        }
        let { wpmlString, kmlString } = CreatWaylineFile(wayline)
        // 创建 JSZip 实例
        const zip = new JSZip();
        // 创建 wpmz 文件夹
        const wpmzFolder = zip.folder('wpmz');
        // 添加文件到文件夹
        wpmzFolder.file('waylines.wpml', wpmlString);
        wpmzFolder.file('template.kml', kmlString);
        // 生成 zip 文件
        zip.generateAsync({ type: 'blob' }).then(file => {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("wName", waylineName);
            formData.append("wType", '5');
            formData.append("sn", aircraftSN);
            axiosApi(
                "/api/v1/WayLine/Upload",
                "POST",
                formData,
            ).then((res) => {
                if (res === 'ok') {
                    message.success("保存成功");
                    lastPage()
                }
            })
        })

    }
    return (
        <div style={{ height: '100%', background: '#F5F5FF', padding: 12.0 }}>
            <div style={{ height: '100%', width: '100%', position: 'relative' }} id="cesisss">
                <div style={{ position: 'absolute', width: 300, height: '100%', backgroundColor: '#001c1a', left: 0, top: 0, zIndex: 1, }}>
                    <Card title={<LastPageButton title="几何体航线" />}>
                        <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>航线名称</div>
                            <Input
                                style={{ width: '100%', }}
                                value={waylineName}
                                placeholder="请输入航线名称"
                                onChange={(e) => { setWaylineName(e.target.value) }}
                            >
                            </Input>
                        </div>
                        <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>选择机库</div>
                            <Select
                                style={{ width: '100%', }}
                                placeholder={'请选择机库'}
                                value={aircraftSN}
                                onChange={(value, option) => {
                                    cameraFlyTo(option.Lng, option.Lat)
                                    setAircraftSN(value)
                                }}
                                options={aircraftSNList}
                            />
                        </div>
                        <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>底部高度</div>
                            <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                <InputNumber
                                    ref={bottomHeightRef}
                                    min={10}
                                    max={1000}
                                    style={{ width: '100%', }}
                                    value={bottomHeight}
                                    onPressEnter={(e) => {
                                        if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 1000)) {
                                            return
                                        }
                                        setBottomHeight(e.target.value)
                                        BottomHeightChange(e.target.value)
                                        bottomHeightRef.current.blur()
                                    }}
                                    onStep={(e, info) => {
                                        setBottomHeight(e)
                                        BottomHeightChange(e)
                                    }}>
                                </InputNumber>
                            </Tooltip>
                        </div>
                        <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>顶部高度</div>
                            <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                <InputNumber
                                    ref={topHeightRef}
                                    min={10}
                                    max={1000}
                                    style={{ width: '100%', }}
                                    value={topHeight}
                                    onPressEnter={(e) => {
                                        if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 1000)) {
                                            return
                                        }
                                        setTopHeight(e.target.value)
                                        TopHeightChange(e.target.value)
                                        topHeightRef.current.blur()
                                    }}
                                    onStep={(e, info) => {
                                        setTopHeight(e)
                                        TopHeightChange(e)
                                    }}>
                                </InputNumber>
                            </Tooltip>
                        </div>
                        {wayline && <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>选择飞行器</div>
                            <Select
                                style={{ width: '100%' }}
                                value={wayline.missionConfig.droneInfo.droneEnumValue}
                                onChange={(e) => { droneEnumValueChange(e) }}
                                options={[
                                    {
                                        value: 77,
                                        label: 'Mavic 3 行业系列',
                                    },
                                    {
                                        value: 91,
                                        label: 'Matrice 3D系列',
                                    },
                                    {
                                        value: 99,
                                        label: 'Matrice 4 行业系列',
                                    },
                                    {
                                        value: 100,
                                        label: 'Matrice 4D 系列',
                                    },
                                ]}
                            />
                        </div>}
                        {wayline && <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>选择型号</div>
                            <Select style={{ width: '100%' }} value={wayline.missionConfig.droneInfo.droneSubEnumValue} optionLabelProp="label" onChange={(e) => {
                                droneSubEnumValueChange(e)
                                modelChange(e)
                            }}>
                                {droneSubEnumValueOptions.map((item, index) => { return <Select.Option value={item.value} label={item.label} key={item.value}>{item.label}</Select.Option> })}
                            </Select>
                        </div>}
                        {wayline && <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>相对被摄面距离</div>
                            <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                <InputNumber
                                    ref={globalShootHeightRef}
                                    min={10}
                                    max={1000}
                                    style={{ width: '100%', }}
                                    value={wayline.Folder.waylineCoordinateSysParam.globalShootHeight}
                                    onPressEnter={(e) => {
                                        if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 1000)) {
                                            return
                                        }
                                        globalShootHeightChange(e.target.value)
                                        ChangeGlobalShootHeight(e.target.value)
                                        globalShootHeightRef.current.blur()
                                    }}
                                    onStep={(e, info) => {
                                        globalShootHeightChange(e)
                                        ChangeGlobalShootHeight(e)
                                    }}>
                                </InputNumber>
                            </Tooltip>
                        </div>}
                        {wayline && <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>航向重叠率</div>
                            <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                <InputNumber
                                    ref={orthoCameraOverlapHRef}
                                    min={10}
                                    max={90}
                                    style={{ width: '100%', }}
                                    value={wayline.Folder.Placemark.overlap.orthoCameraOverlapH}
                                    onPressEnter={(e) => {
                                        if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 90)) {
                                            return
                                        }
                                        Changeoverlap(e.target.value, wayline.Folder.Placemark.overlap.orthoCameraOverlapW)
                                        orthoCameraOverlapHRef.current.blur()
                                    }}
                                    onStep={(e, info) => {
                                        Changeoverlap(e, wayline.Folder.Placemark.overlap.orthoCameraOverlapW)
                                    }}>
                                </InputNumber>
                            </Tooltip>
                        </div>}
                        {wayline && <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>旁向重叠率</div>
                            <Tooltip placement="bottom" title='输入完成后按回车确认'>
                                <InputNumber
                                    ref={orthoCameraOverlapWRef}
                                    min={10}
                                    max={90}
                                    style={{ width: '100%', }}
                                    value={wayline.Folder.Placemark.overlap.orthoCameraOverlapW}
                                    onPressEnter={(e) => {
                                        if ((e.target.value === '') || (e.target.value < 10) || (e.target.value > 90)) {
                                            return
                                        }
                                        Changeoverlap(wayline.Folder.Placemark.overlap.orthoCameraOverlapH, e.target.value)
                                        orthoCameraOverlapWChange(e.target.value)
                                        orthoCameraOverlapWRef.current.blur()
                                    }}
                                    onStep={(e, info) => {
                                        Changeoverlap(wayline.Folder.Placemark.overlap.orthoCameraOverlapH, e)
                                        orthoCameraOverlapWChange(e)
                                    }}>
                                </InputNumber>
                            </Tooltip>
                        </div>}
                        <div style={{ width: '100%', padding: '0 10px 0 10px' }}>
                            <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}><Button type="primary" onClick={() => { save() }}>保存</Button></div>
                        </div>
                    </Card>

                </div>
                <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
                    <MapControl viewerData={viewerData} />
                </div>
            </div>
        </div>
    )
};

export default BeltRoute;
