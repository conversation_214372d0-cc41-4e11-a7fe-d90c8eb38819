import React, { Fragment, useState, useEffect } from 'react';

import { Breadcrumb, Card, Input, Select, Tag, DatePicker, Descriptions, Row, Col, Button, Form, message, Table, Modal, Badge } from 'antd';

import { getBodyH, getDeviceName, getImgUrl, isEmpty } from '@/utils/utils';

import { Get2, Post, Post2, Post3 } from '@/services/general';

import AddButton from '@/components/AddButton';
import WayLineAddForm from './form_add';
import TableCols from './table';


import { useModel } from 'umi';
import { HGet2, HPost2 } from '@/utils/request';
import { ArrowLeftOutlined } from '@ant-design/icons';
import LastPageButton from '@/components/LastPageButton';
import dayjs from 'dayjs';
import MapJobAddForm from './task_select_panel';
import ZhengSheYuLanPage from './ZhengSheYuLan';


const { RangePicker } = DatePicker;
const { Description } = Descriptions;
const FormItem = Form.Item;


const MapJobListPage = (props) => {


  const [vList, setVList] = useState([]);
  const [sJC, setSjc] = useState('');
  const [xList, setXList] = useState([]);
  const [sDate, setSDate] = useState({});
  const [ifBusy,setIfBusy]=useState(true);
  const [ifBusy2,setIfBusy2]=useState(true);

  const { setPage, lastPage,setModal,setOpen } = useModel('pageModel')

  const ifPush = (e) => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')
    if (!isEmpty(sDate)) {
      t1 = dayjs(sDate[0]);
      t2 = dayjs(sDate[1]);
    }

    if (!isEmpty(sDate)) {
      const t3 = dayjs(e.CreateTime);
      if (t1.isAfter(t3) || (t2.isBefore(t3))) {
        return false;
      }
    }

    if (!isEmpty(sJC)) {
      if (e.State != sJC) {
        return false;
      }
    }
    return true;

  }

  useEffect(() => {
    const xx = () => {
      const xL = []
      // 
      vList.forEach(e => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
      setXList([...xL]);
    }
    xx();

  }, [sJC, sDate]);



  useEffect(() => {
    const getLineData = async () => {
      let pst = await Get2('/api/v1/MapJob/GetList', {});
      if(isEmpty(pst)) pst=[];
      setVList([...pst]);
      setXList([...pst]);
    };
    const getIfBusy=async()=>{
      const p1 = await Get2('/deal/Map2DStatus', {});
    //  const p2 = await Get2('/deal/Map3DStatus', {});

      setIfBusy(p1)
  //    setIfBusy2(p2)

    }
    getLineData();
    getIfBusy();
  }, []);


  const refrush = async () => {
    const p1 = await Get2('/deal/Map2DStatus', {});
    setIfBusy(p1)
    let pst = await HGet2('/api/v1/MapJob/GetList', {});
    if(isEmpty(pst)) pst=[];
    setVList([...pst]);
    setXList([...pst]);
  };

  const fhBtn = <Button icon={<ArrowLeftOutlined />} onClick={lastPage}>返回</Button>

  const showMap = (record) => {
    setPage(<Card title={fhBtn}>{WayLineMap(getBodyH(180), record)}</Card>)
  };


  const getWaySelect = (wayList, getLabel) => {

    const list = []
    wayList.forEach(e => {
      list.push(<Select.Option key={e} data={e}>{getLabel(e)}</Select.Option>)
    });
    // console.log('CronAddForm', list);

    return list;
  }


  const onClick=(item)=>{
    //setModal(<div style={{height:500,width:900}}><ZhengSheYuLanPage  jID={item.JobID}></ZhengSheYuLanPage></div>);
   // setOpen(true);
    setPage( <Card title={<LastPageButton title={'返回'}></LastPageButton>}><ZhengSheYuLanPage style={{height:getBodyH(56),width:'100%'}} jID={item.JobID}></ZhengSheYuLanPage></Card>)
}


  const getZT=(s1)=>{
      if(s1==0) return "任务未开始";
      if(s1==1) return "下载原视频";
      if(s1==2) return "地图重建中";
      if(s1==3) return "上传视频中";
      if(s1==4) return"任务已完成";
      return "";
  }
  const getExr = () => {

    const dList = []


    vList.forEach(e => {

      if (!dList.includes(e.State)) {
        dList.push(e.State);
      }

    });

    return <Row>

      <Col style={{ marginLeft: 6.0 }}> 
       <div style={{marginRight:12.0,paddingTop:4.0}}> {ifBusy?<Badge status="processing" text='服务器繁忙'></Badge> :<Badge status="success"  text='服务器空闲'></Badge>}</div>
      </Col>
      {/* <Col style={{ marginLeft: 6.0 }}> 
       <div style={{marginRight:12.0,paddingTop:4.0}}> {ifBusy2?<Badge status="processing" text='3D服务器繁忙'></Badge> :<Badge status="success"  text='3D服务器空闲'></Badge>}</div>
      </Col> */}
      <Col style={{ marginLeft: 6.0 }}>   <Select allowClear={true} style={{ width: 200 }} onClear={() => setSjc('')}
        placeholder={'任务状态'}
        onSelect={(e) => setSjc(e)}>
        {getWaySelect(dList, (e) => getZT(e))}
      </Select></Col>

      <Col style={{ marginLeft: 6.0 }}> <RangePicker onChange={(e) => setSDate(e)} /></Col>
      <Col style={{ marginLeft: 24.0 }}><AddButton  onClick={()=>setPage(<MapJobAddForm />)}>创建任务</AddButton></Col>
      <Col style={{ marginLeft: 24.0 }}><AddButton  onClick={()=>refrush()}>刷新任务</AddButton></Col>
   
    </Row>
  }


  return (

    <div style={{ margin: 0, height: getBodyH(56), background: '#F5F5FF' }}>

      <Card title={<LastPageButton title='地图重建任务' />} bordered={false} extra={getExr()} >
        <div>
          {isEmpty(vList) ? <div /> : <Table pagination={{ pageSize: 8 }}
            bordered dataSource={xList} columns={TableCols(refrush, onClick, setPage)} size='small' />}
        </div>
      </Card>


    </div>
  )
};

export default MapJobListPage;
