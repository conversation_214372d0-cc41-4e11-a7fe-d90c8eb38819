import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Descriptions, DatePicker, Radio, Select, Row, Col, Button, Modal, message, Table, Upload, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { getImgUrl, isEmpty } from '@/utils/utils';

import locale from 'antd/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { HGet2, HPost2 } from '@/utils/request';
import { useModel } from 'umi';
import TableCols  from './table_task';
import LastPageButton from '@/components/LastPageButton';
import {getVideoDuration,downLoadClick} from './help';
import {getPStrArea} from '@/pages/Maps/helper.js';
import AddButton from '@/components/AddButton';
import LoadPanel from '@/components/IfShowPanel/load_panel';
import { timeFormat } from '@/utils/helper';

const FlightTaskFileAddDescriptions = ({refrush}) => {

  const [fdata,setFData]=useState({});
  const [loading, setLoading] = useState(false);
  const [tList, setTList] = useState([]);


  const {setPage,lastPage}=useModel('pageModel');

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);


  const getImgList=(mList,hzm)=>{
    if(isEmpty(hzm)){
      hzm=exr;
    } 

    const list=[];
    if(isEmpty(mList))
      return list;
    mList.forEach(e => {
        if(e.FileName.indexOf(hzm) != -1){
          list.push(e)
        }
     });
    return list;
  }

  const getData3=async(fID)=>{

    const data=tList.find(p=>p.TaskID==fID);

    const mList=await  HGet2(`/api/v1/Media/GetListByTaskId?id=${fID}`)
    if(isEmpty(data)) return {FlyTime:0, RecordTime:0,FImgCount:0,FVideoCount:0};
    if(isEmpty(data.FlyTM)) data.FlyTM=0;

   // const xx=getPStrArea(data.Remark)
    const L1=getImgList(mList,".jpeg");
    const L2=getImgList(mList,".mp4");
    let recordTime=0;
    recordTime=await getRecordTime(L2);


    return {FlyTime:data.FlyTM / 60, RecordTime:(recordTime/60),FImgCount:L1.length,FVideoCount:L2.length}

}

const getRecordTime=async(L2)=>{
  let recordTime=0;
  for (let i = 0; i < L2.length; i++) {
    const x1=await getVideoDuration(getImgUrl(L2[i].ObjectName))
    recordTime= recordTime+x1;
  }
  return recordTime;
}

  const getFData=async()=>{
      if(isEmpty(rowSelection)){
        message.error("请先选择要提取的飞行记录！");
        return;
      }
      const d3={FlyTime:0, RecordTime:0,FImgCount:0,FVideoCount:0,TaskID:''}
      setLoading(true);
      for(let i=0;i<selectedRowKeys.length;i++){
          const dd=await getData3(selectedRowKeys[i]);
          d3.FlyTime+=dd.FlyTime;
          d3.RecordTime+=dd.RecordTime;
          d3.FImgCount+=dd.FImgCount;
          d3.FVideoCount+=dd.FVideoCount;
          d3.TaskID=d3.TaskID+selectedRowKeys[i]+","
        
      }
      let fT1=dayjs().format("YYYY-MM-DD");
      let title=""
      if(selectedRowKeys.length>0){
        const data=tList.find(p=>p.TaskID==selectedRowKeys[0]);
        fT1=dayjs(data['CreateTime']).format("YYYY-MM-DD");
        title=data['TaskContent']
      }
    //  setFData({...d3});
    setLoading(false);
    setFData({...fdata, ...{FName:title,FType:'外飞',UsedNet:((d3.FlyTime+d3.RecordTime)*0.6).toFixed(1), TaskID: d3.TaskID,FTime:fT1, FlyTime:(d3.FlyTime).toFixed(0),Media:'视频:'+d3.FVideoCount+"段,照片:"+d3.FImgCount+"张", RecordTime:d3.RecordTime.toFixed(0),ImgCount:d3.FImgCount,VideoCount:d3.FVideoCount}})

  }


  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  useEffect( () => {
    const yy =async()=>{
      let xx= await HGet2(`/api/v1/Task/GetAllList`)
      xx.forEach(e => {
        e.key=e.TaskID
      });
      setLoading(false);
      if(isEmpty(xx)) xx=[];
      setTList(xx);
    }
    yy();
  }, []);

 // const [dt, setDt] = useState(0)
 // const [tm, setTM] = useState({})
  //const [way, setWay] = useState({})
 // const [name, setName] = useState("")

  const onSave =async (e) => {

  //  const user=JSON.parse( localStorage.getItem('user'));
   const xx=await  HPost2('/api/v1/FlightTaskFile/Add',fdata);
    if(isEmpty(xx.err)){
      message.info("创建成功！")
    }
    
    refrush();
    lastPage();
  };

  const getCost=(n,v1)=>{
      if(n==0) return 1500;
      if(v1<=1) return 3000;
      return Math.ceil(v1-1)*1000+3000;
  }

 const getPanel=(data)=>{
    if(loading){
      return <Spin size="large"></Spin>
    }

    return <Descriptions title={'外飞任务信息'}  labelStyle={{paddingLeft:12.0,width:160}}  bordered column={2} extra={<AddButton style={{marginRight:48.0}} onClick={onSave}>保存</AddButton>}>

    <Descriptions.Item label="单位">
      <Input
        value={data.FDepartment}
        onChange={(e)=>{setFData({...data,FDepartment:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="时间" >
      <Input
        value={data.FTime}
        onChange={(e)=>{setFData({...data,FTime:e.target.value})}}>
      </Input>
    </Descriptions.Item>

   

    <Descriptions.Item label="飞行任务" span={2}>
      <Input
        value={data.FName}
        onChange={(e)=>{setFData({...data,FName:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="相关函告" span={2}>
      <Input
        value={data.FPaper}
        onChange={(e)=>{setFData({...data,FPaper:e.target.value})}}>
      </Input>
    </Descriptions.Item>

    <Descriptions.Item label="飞行时长(分钟)">
    <InputNumber
        style={{width:'100%'}}
        value={data.FlyTime}
        onChange={(e)=>{
          if(!isEmpty(e)){
          const g1=((e+Number(data.RecordTime))*0.6).toFixed(1)
          setFData({...data,FlyTime:e.toFixed(0),UsedNet:g1})}
          }}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="录制时长(分钟)">
    <InputNumber
        style={{width:'100%'}}
        value={data.RecordTime}
        onChange={(e)=>{
          if(!isEmpty(e)){
          const g1=((e+Number(data.FlyTime))*0.6).toFixed(1)
            setFData({...data,RecordTime:e.toFixed(0),UsedNet:g1})}
          }}>
      </InputNumber>
    </Descriptions.Item>


    <Descriptions.Item label="消耗流量(G)">
    <InputNumber
        style={{width:'100%'}}
        value={data.UsedNet}
        onChange={(e)=>{setFData({...data,UsedNet:e.toFixed(0)})}}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="飞行面积(平方公里)">
      <InputNumber
        style={{width:'100%'}}
        value={data.FlyArea}
        onChange={(e)=>{

          const c1=getCost(data.VideoCount, e);
          setFData({...data,FlyArea:e,FlyCost:c1})
          
          }}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="预计费用(元)">
    <InputNumber
        style={{width:'100%'}}
        value={data.FlyCost}
        onChange={(e)=>{setFData({...data,FlyCost:e})}}>
      </InputNumber>
    </Descriptions.Item>

    <Descriptions.Item label="影像(视频、图片)">
    <Input
        style={{width:'100%'}}
        value={data.Media}
        onChange={(e)=>{setFData({...data,Media:e.target.value})}}>
      </Input>
    </Descriptions.Item>
   

    <Descriptions.Item label="备注">
      <Input
        defaultValue={data.Remark}
        onChange={(e)=>{setFData({...data,Remark:e.target.value})}}>
      </Input>
    </Descriptions.Item>

  </Descriptions>}

  return  <Card title={<LastPageButton title="添加外飞任务" />} bordered={false}   > <Row>
    <Col span={11}> {isEmpty(tList)?<div/>: <Card title={'选择飞行记录'} bordered={false} extra={<AddButton onClick={()=>getFData()}>提取数据</AddButton>}  > <Table  rowSelection={rowSelection} style={{height:250}} 
            pagination={{pageSize:8,showSizeChanger:false}}
            bordered dataSource={tList} columns={TableCols()} size='small' /></Card>}</Col>
    <Col style={{paddingLeft:24.0}} span={12}>{getPanel(fdata)}</Col>
  </Row></Card>
}



export default FlightTaskFileAddDescriptions;