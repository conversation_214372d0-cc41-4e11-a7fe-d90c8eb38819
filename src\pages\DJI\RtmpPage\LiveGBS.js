import flvjs from "flv.js";
import Srs from "@/components/WebRtcPlayer/srs.sdk";
import { useEffect, useRef, useState } from "react";
import { getRtmpHttpUrl, getRtmpWebrtcUrl } from "@/utils/config";
import VideoLoading from "@/components/VideoLoading/VideoLoading";

const MediaViewer = ({ SN, play = true }) => {
  const videoRef = useRef(null);
  const flvPlayerRef = useRef(null);
  const [loading, setLoading] = useState(true);
  /*   const url =
    "http://112.44.103.230:10000/sms/" +
    SN +
    "/flv/hls/34020000001110000001_34020000001310000017.flv";
  console.log("FLVPlayer", url); */

  useEffect(() => {
    const URL =
      "http://112.44.103.230:10000/sms/34020000002020000001/flv/hls/34020000001110000" +
      SN +
      "_34020000001310000" +
      SN +
      ".flv";
      console.log("WebRTC测试ing", URL);
    // const URL = getRtmpHttpUrl() + SN + ".flv"; // flvC地址

    if (URL.endsWith(".flv")) {
      initializeFlvPlayer(URL);
    } else if (URL.startsWith("webrtc://")) {
      initializeWebRTC(URL);
    }

    return cleanup;
  }, [SN, play]);

  const initializeWebRTC = async (URL) => {
    //WebRTC 格式播放器,可能无限制引用组件数量
    const rtcPlayer = new Srs.SrsRtcPlayerAsync();
    if (play) {
      if (videoRef.current) {
        try {
          await rtcPlayer.play(URL);
          if (rtcPlayer.stream) {
            videoRef.current.srcObject = rtcPlayer.stream;
            await videoRef.current.play();
            setLoading(false);
          }
        } catch (err) {
          handleError(err, "WebRTC");
        }
      }
    }
  };

  const initializeFlvPlayer = (URL) => {
    //flv格式播放器，镜头转动画面会停滞,可能只能播放六个
    if (flvjs.isSupported()) {
      flvPlayerRef.current = flvjs.createPlayer({
        type: "flv",
        url: URL,
        isLive: true,
        hasAudio: false,
        hasVideo: true,
        enableStashBuffer: false,
        cors: true,
      });

      flvPlayerRef.current.attachMediaElement(videoRef.current);
      flvPlayerRef.current.load();
      if (play) {
        flvPlayerRef.current
          .play()
          .then(() => {
            setLoading(false);
          })
          .catch((err) => handleError(err, "FLV"));
      }
    } else {
      console.error("FLV.js is not supported in this browser.");
    }
  };

  const handleError = (err, type) => {
    console.error(`Error playing ${type} video:`, err);
    setLoading(false);
  };

  const cleanup = () => {
    if (flvPlayerRef.current) {
      flvPlayerRef.current.unload();
      flvPlayerRef.current.detachMediaElement();
      flvPlayerRef.current.destroy();
      flvPlayerRef.current = null;
    }
  };

  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      {loading && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 10,
          }}
        >
          <VideoLoading />
        </div>
      )}
      <video
        ref={videoRef}
        muted
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          display: loading ? "none" : "block",
        }}
      />
    </div>
  );
};

export default MediaViewer;
