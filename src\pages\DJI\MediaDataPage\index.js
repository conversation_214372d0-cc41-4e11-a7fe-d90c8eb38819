import { Clone, downloadFile, getBodyH, getImgUrl, isEmpty } from '@/utils/utils';

import { useEffect, useState } from 'react';
import { Card,List,Row,Col,Button,Checkbox, Spin} from 'antd';

import { HGet2 } from '@/utils/request';
import MediaListPanel from './MediaListPanel';
import MediaInfoPanel from './MediaInfoPanel';
import LoadPanel from '@/components/IfShowPanel/load_panel';



const MediaDataPage=({doNotShowLastButton})=>{

  const [mList,setMList]=useState([])
  const [sList,setSList]=useState([])
  const [ifLoad,setIfLoad]=useState(true);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const xx=async ()=>{
    const yy=await  HGet2(`/api/v1/Media/GetAllList`)
    const yy2=getImgList(yy);
    setMList(yy2);
    setIfLoad(false);
  }
  useEffect(() => {
    xx();
  }, []);

  const getImgList=(xL)=>{
    // 
     const list=[];
     if(isEmpty(xL))
       return list;
     xL.forEach(e => {
         if(e.FileName.indexOf('jpeg') != -1){
           list.push(e)
         }
      });
     return list;
   }

  const IfSelected=(item)=>{
    const xx=sList.indexOf(item.ID)

    return xx>-1;
  }

  const downLoadClick=()=>{

    mList.forEach(e => {
      if(IfSelected(e)){
        downloadFile(getImgUrl(e.ObjectName),e.FileName)
      }
    });
  }

const selectClick=(item)=>{


    const xx=sList.indexOf(item.ID)
    if (xx===-1){
      sList.push(item.ID)

    }else{
      delete sList[xx]

    }
    const yy=Clone(sList)
      setSList(yy)

}
  if(ifLoad) return <LoadPanel></LoadPanel>
  return  <MediaListPanel doNotShowLastButton={doNotShowLastButton} mList={mList} refrush={xx}></MediaListPanel>
}

export default MediaDataPage;
