/* navigationPage.css */

/* 页面容器样式 */
.navigation-page-container {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 设置视口高度 */
    margin: 0;
  }
  
  /* 上方信息区域样式 */
  .top-title-section {
    text-align: center;
    padding: 10px;
    background: url('@/assets/topTitle.png');
    background-size: cover;
    font-size: 22px;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-weight: bolder;
    color: #e3f5fb;
    white-space: nowrap;
  }
  
  /* 卡片容器样式 */
  .card-container {
    flex: 1; /* 占满剩余空间 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: url('@/assets/mlbg.png');
    background-size: cover;
  }
  
  /* 系统列表容器样式 */
  .thumbnail-list-container {
    display: flex;
    width: 100%;
    height: 100%;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;
    gap: 40px;
  }
  
  /* 单个系统缩略图容器样式 */
  .thumbnail-item {
    width: auto;
    text-align: center;
  }
  
  /* 系统缩略图样式 */
  .thumbnail-image {
    width: 500px;
  }
  
  /* 系统标题样式 */
  .thumbnail-title {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
  }
  