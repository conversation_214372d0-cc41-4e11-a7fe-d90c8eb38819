import React, { useState } from "react";
import styles from "./RightPage2.less";
import HeadTitle from "@/pages/GT/components/HeadTitle/HeadTitle";
import { Input, Space, ConfigProvider, Select } from "antd";
import { SearchOutlined } from "@ant-design/icons";
const { Search } = Input;
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import RightTable from "@/pages/GT/GTPages/RightPage/Panels/RightTable";
import MapComponent from "@/pages/GT/components/MapComponent/MapComponent";
const LeftPage2 = () => {
  return (
    <div className={styles.RightPage2}>
      <div>
          <RightTable></RightTable>
      </div>
    </div>
  );
};

export default LeftPage2;
