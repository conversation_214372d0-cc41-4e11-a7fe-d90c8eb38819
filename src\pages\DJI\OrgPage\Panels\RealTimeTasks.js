
import BlockTitle from '../../../../components/BlockPanel/BlockTitle';
import { BorderBox7 } from '@jiaminghi/data-view-react';
import {useEffect,useRef,useState} from 'react';
import { useModel} from 'umi';
import { HGet2 } from "@/utils/request";
import '@/pages/DJI/OrgPage/Panels/RealTimeTasks.css'
import DevicePage from "@/pages/DJI/DevicePage/index";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { getBodyH, isEmpty } from "@/utils/utils";

const RealTimeTasks = ({deviceList}) => {
  
  const [dataList,setDataList] = useState([])
  const [deviceLists,setDeviceLists] = useState([])
  const { setPage } = useModel('pageModel')
  const [ifLoad, setIfLoad] = useState(true);
 
  useEffect(()=>{
    const getData = async () =>{
      const res = await HGet2(`/api/v1/Task/GetListTop10`);
      if(res && res.length > 0){
        setDataList(res)
      } 
    } 
    if(!deviceList) {
      setDeviceLists(localStorage.getItem('device'))
    }else{
      setDeviceLists([...deviceList])
    }
    getData()
  },[])

let intervalTimer, timeoutTimer
const domRef = useRef()
useEffect(() => {
  if(domRef.current) {
      autoScroll()
      domRef.current.onmouseenter = () => {
          window.clearInterval(intervalTimer)
          window.clearTimeout(timeoutTimer)
      }
      domRef.current.onmouseleave = () => {
          autoScroll()
      }
  }
}, [domRef])
const autoScroll = () => {
  if(timeoutTimer) {
      window.clearTimeout(timeoutTimer)
  }
  timeoutTimer = setTimeout(() => {
      const clientH = domRef.current && domRef.current.clientHeight || 0
      const scrollH = domRef.current && domRef.current.scrollHeight || 0
      if(intervalTimer) {
          window.clearInterval(intervalTimer)
      }
      if(scrollH == clientH) return
      intervalTimer = setInterval(() => {
          if(domRef.current) {
              const scrollTop = domRef.current.scrollTop
              const distance = scrollH - clientH
              if(scrollTop >= distance) {
                  domRef.current.scrollTop = -1
              }
              domRef.current.scrollTop += 1
          }
      }, 40)
  }, 2800)
}
const formatDate = (date) =>{
  return date.slice(0,date.indexOf('T')) + ' ' + date.slice(date.indexOf('T')+1,date.indexOf('.'))
}
const fortmatState = (state) =>{
  if(state == 1){
    return <div className='state-end'>已完成</div>
  }else if(state == 0){
    return <div className='state-self'>正在执行</div>
  }else{
    return <div style={{color:'#10d7ec'}}>待执行</div>
  }
}
function toPage(item){
  for(let value of deviceLists){
    if(item.DeviceSN === value.SN){
      localStorage.setItem('device',JSON.stringify(value))
      setPage(<DevicePage device={value}/>)
    }
  }
}
const formatDevice = (item) =>{
  for(let value of deviceLists){
    if(item.DeviceSN === value.SN){
      return value.DName
    }
  }
}
const getItem2 = () => {
    return <div ref={domRef} className='swiper' style={{maxHeight:getBodyH(146)}}>
           {
            dataList.map((item,key)=>{2
             return(
              <div key={key} className='swiper-box' onClick={() =>toPage(item)}>
                   <div className='state'>
                      <div style={{color:'#ccc'}}>{formatDate(item.CreateTime)}</div>
                      {fortmatState(item.TaskState)}
                  </div>
                  <div style={{margin:'20px 0',}}>
                    {<span className='state-text' style={{background:item.TaskState===0?'#f9b403':(item.TaskState===1?'#81ce27':'#10d7ec')}}>状态</span>}
                    &nbsp;&nbsp;&nbsp;
                    <span style={{color:'#fff',marginRight:'10px'}}>{item.FlightLineName}</span>
                    <span>
                      <span style={{fontSize:'12px',color:'#feed9d'}}>
                      {`『${formatDevice(item)}』`}
                      </span>
                      ✈
                      </span>
                    </div>
               <div className='line'></div>
              </div>
             )
            }
            )
           }
        </div>
  }

  return <div  style={{ width: '100%', marginTop: 16.0}}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
      <BlockTitle title="实时任务"></BlockTitle> 
      <div id='side' style={{ width: '100%',paddingTop:'20px'}}>
        {getItem2()}
      </div>
    </BorderBox7>
  </div>
}
export default RealTimeTasks;