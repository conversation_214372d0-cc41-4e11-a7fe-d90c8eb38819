import { isEmpty } from "@/utils/utils";

import { Card, Space, Descriptions, Modal } from 'antd';
//import BaseMap from "@/pages/Maps/BaseMap";
import { useModel } from "umi";


import { getBaseColor, getGuid, getLocation } from "@/utils/helper";


const ModeCodeJson = { "0": "空闲中", "1": "现场调试", "2": "远程调试", "3": "固件升级中", "4": "作业中" }
const CoverStateJson = { "0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常" }

const CoverRainJson = ["无雨","小雨","中雨","大雨"];

const OsdPanel = () => {

  const { drc } = useModel('drcModel');



  const getItem = (label, val, dw) => {
    return <Descriptions.Item key={getGuid()} labelStyle={{ fontSize: 12.0, color: 'white' }} contentStyle={{ fontSize: 12.0, color: 'white' }} label={label}>{val + dw}</Descriptions.Item>
  }

  const getPanel = (list) => {
    return <Descriptions style={{ fontSize: 12.0, color: 'white' }} column={2}>
      {list}
    </Descriptions>
  }

  const panel2 = (data) => {

    const list = [];
    if(isEmpty(data)) return;
    list.push(getItem("经度", data.longitude.toFixed(3), ""));
    list.push(getItem("纬度", data.latitude.toFixed(3), ""));
    list.push(getItem("高度", data.height.toFixed(1), ""));
    list.push(getItem("相对高度", data.height.toFixed(1), ""));
    list.push(getItem("x速度", data.speed_x.toFixed(1), ""));
    list.push(getItem("y速度", data.speed_y.toFixed(1), ""));
    list.push(getItem("z速度", data.speed_z.toFixed(1), ""));
    list.push(getItem("云台pitch角", data.gimbal_pitch.toFixed(1), ""));
    list.push(getItem("云台roll角", data.gimbal_roll.toFixed(1), ""));
    list.push(getItem("云台yaw角", data.gimbal_yaw.toFixed(1), ""));
    return getPanel(list);
  }


  

  return <Card bordered={false} size="small" title='实时姿态'  headStyle={{ color: 'white' }} style={{ background: getBaseColor(), height: '100%', width: '100%' }}>
     {panel2(drc)}

  </Card>
}

export default OsdPanel;
