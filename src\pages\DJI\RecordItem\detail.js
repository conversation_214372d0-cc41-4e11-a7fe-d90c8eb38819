import { getBodyH,getImgUrl } from '@/utils/utils';
import {Row,Col,Card,Image, Descriptions} from 'antd';

const DangerDetailPage=({danger})=>{
    

    const h1=getBodyH(190)

   const dPanel=()=>{
    return <div style={{margin:8.0}}><Descriptions title={danger.Title} column={2}>
        <Descriptions.Item label="事件类型">{danger.DangerType}</Descriptions.Item>
        <Descriptions.Item label="紧急程度">{danger.Level}</Descriptions.Item>
        <Descriptions.Item label="事件来源">{'AI识别'}</Descriptions.Item>
        <Descriptions.Item label="事件状态">{danger.State==2?'已处理':'处理中'}</Descriptions.Item>
        {/* <Descriptions.Item label="主要内容">{danger.Content}</Descriptions.Item> */}
    </Descriptions></div>
}

    return <div>
    <Row>
        <Col span={10}>
            <Card bordered={false} style={{ width: '100%', height: h1, marginLeft: 8.0 }} title={dPanel()}>
                <Image style={{ width: '100%'}}
                src={getImgUrl(danger.ImgObjectName)}/>
            </Card>
        </Col>
        <Col span={14}>
            <div style={{marginLeft:36.0}}>

            </div>
        </Col>


    </Row>


</div>


}

export default DangerDetailPage;
