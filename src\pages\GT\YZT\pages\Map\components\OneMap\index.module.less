.mapContainer {
  width: 100%;
  height: calc(100vh - 56px);
  position: relative;
}

@left-sidebar-expanded-width: 360px;
@left-sidebar-collapsed-offset: 40px;
@right-sidebar-expanded-width: 360px;
@right-sidebar-collapsed-offset: 40px;
@tree-panel-spacing: 15px; // 与 sidebar 或页面边缘的间距

.treePanelContainer {
  position: absolute;
  top: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
  transition: left 0.35s ease-in-out;

  &.leftSidebarExpanded {
    // 当 LeftSidebar 展开时，目录树位于 LeftSidebar 右侧
    left: calc(@left-sidebar-expanded-width + @tree-panel-spacing);
  }

  &.leftSidebarCollapsed {
    // 当 LeftSidebar 收起时，目录树向左移动
    left: calc(@left-sidebar-collapsed-offset + @tree-panel-spacing);
  }
}

.treeToggleButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  color: #333;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  transition: all 0.3s;
  padding: 0;
  text-align: center;

  span {
    margin-top: 4px;
    font-size: 13px;
    line-height: 1.2;
    white-space: nowrap;
  }

  &:hover,
  &:focus {
    background: #f0f7ff;
    border-color: #a0cfff;
    box-shadow: inset 0 0 0 1px #a0cfff, 0 2px 12px rgba(0,0,0,0.15);
  }

  &.expanded {
    background: #f0f7ff;
    border-color: #a0cfff;
    box-shadow: inset 0 0 0 1px #a0cfff, 0 2px 12px rgba(0,0,0,0.15);
  }

  .toggleIcon {
    font-size: 28px;
    transition: transform 0.2s ease-in-out;
    margin-bottom: 2px;
    color: #457379;
  }
}

.treePanelYzt {
  width: 300px;
  min-height: 300px;
  max-height: 60vh;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  margin-left: -308px;
  opacity: 0;
  visibility: hidden;

  &.expanded {
    margin-left: 0;
    opacity: 1;
    visibility: visible;
  }

  .fixedHeader {
    padding: 8px 16px 0;
    background-color: #fff;
    z-index: 1001;
    flex-shrink: 0;
  }

  .scrollableContent {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px 16px;
      min-height: 0;
      max-height: 40vh;
      
      &::-webkit-scrollbar {
          width: 6px;
      }
      &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
      }
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  :global(.ant-tree) {
      color: #333 !important;
      background: transparent !important;

      :global(.ant-tree-node-content-wrapper:hover) {
        background-color: rgba(0, 0, 0, 0.04);
      }

      :global(.ant-tree-node-selected) {
        background-color: #e6f7ff !important;
      }

      :global(.ant-tree-draggable-icon) {
        display: none;
      }
    }
}

.viewToggle {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;

  .viewToggleButton {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    position: relative;
    transition: all 0.3s;

    &.active {
      color: #1890ff;
      background: #f2f1f1;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -12px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #1890ff;
      }
    }

    &:hover:not(.active) {
      color: #40a9ff;
    }
  }
}

.mapView {
  width: 100%;
  height: 100%;
  position: relative;
}

.topRightControlPanel {
  position: absolute;
  top: 20px;
  right: 235px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
  z-index: 1000;
  transition: right 0.35s ease-in-out;
}

.mapControlPanel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;



  .toggleButtonYzt {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 600; // 稍微加粗
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    letter-spacing: 0.5px; // 增加字母间距提升可读性

    // 专业级过渡动画
    transition:
      all 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 确保动画流畅
    will-change: transform, box-shadow, background-color;
    backface-visibility: hidden;

    // Light theme
    &.lightTheme {
      background-color: #fff;
      color: #333;

      &:hover {
        background: #f0f7ff;
        color: #1890ff;
      }
    }

    // Dark theme - 使用新的主题颜色
    &.darkTheme {
      background-color: #1D2834; // 新的深色背景
      color: #7EE0D4; // 新的文字颜色
      position: relative;
      overflow: hidden;

      // 添加微妙的边框和阴影
      border: 1px solid rgba(126, 224, 212, 0.2);
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(126, 224, 212, 0.1);

      // 添加背景渐变增加深度
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(126, 224, 212, 0.05) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &:hover {
        color: #9FF5EA; // 更亮的青绿色
        background-color: rgba(126, 224, 212, 0.08);
        border-color: rgba(126, 224, 212, 0.4);
        transform: translateY(-2px) scale(1.02); // 微妙的上浮和放大

        // 多层阴影创造悬浮效果
        box-shadow:
          0 6px 20px rgba(126, 224, 212, 0.2),
          0 3px 10px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(126, 224, 212, 0.2);

        &::before {
          opacity: 1;
        }

        // 添加发光效果
        &::after {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg,
            rgba(126, 224, 212, 0.3) 0%,
            rgba(126, 224, 212, 0.1) 50%,
            rgba(126, 224, 212, 0.3) 100%);
          border-radius: 10px;
          z-index: -1;
          opacity: 0;
          animation: glow-pulse 2s ease-in-out infinite;
        }
      }

      &:active {
        transform: translateY(-1px) scale(1.01);
        transition: transform 0.1s ease-out;
      }
    }

    &.active {
      background: #1890ff;
      color: #fff;
      border-color: #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }
}

@basemap-toggle-width: 40px;
@basemap-panel-to-toggle-spacing: 10px; // 面板和按钮之间的间距
@unified-controls-spacing: 12px; // 统一控制面板内部间距

// 统一控制面板容器
.unifiedControlsContainer {
  position: absolute;
  bottom: 70px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: @unified-controls-spacing;
  transition: right 0.35s ease-in-out;

  // 根据侧边栏状态调整位置
  &.rightSidebarExpanded {
    right: calc(@right-sidebar-expanded-width + @tree-panel-spacing);
  }

  &.rightSidebarCollapsed {
    right: calc(@right-sidebar-collapsed-offset + @tree-panel-spacing);
  }
}

// 独立的底图面板容器
.basemapPanelContainer {
  position: absolute;
  bottom: 70px; // 与统一控制面板容器相同的底部位置
  z-index: 999; // 略低于统一控制面板容器
  transition: right 0.35s ease-in-out;

  // 根据侧边栏状态调整位置，与底图按钮右侧对齐，留出间距
  &.rightSidebarExpanded {
    right: calc(@right-sidebar-expanded-width + @tree-panel-spacing + @basemap-toggle-width + @basemap-panel-to-toggle-spacing);
  }

  &.rightSidebarCollapsed {
    right: calc(@right-sidebar-collapsed-offset + @tree-panel-spacing + @basemap-toggle-width + @basemap-panel-to-toggle-spacing);
  }
}

.basemapPanel {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  max-height: 250px;
  overflow-y: auto;
  min-width: 100px;
  
  // 滑入动画
  &.slideIn {
    animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .noBasemapIndicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    color: #888;
    text-align: center;
    padding: 15px;

    svg {
      margin-bottom: 5px;
    }
  }

  .basemapList {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
  }

  .basemapItem {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    background-color: #fff;
    transition: all 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);

    &:hover {
      border-color: #91d5ff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    &.selected {
      border-color: #1890ff;
      background-color: #e6f7ff;

      .basemapLabel {
        color: #1890ff;
        font-weight: 500;
      }
    }

    .basemapThumbnailContainer {
      position: relative;
      width: 100%;
      margin-bottom: 8px;

      .basemapThumbnail {
        display: block;
        width: 100%;
        height: 60px;
        object-fit: cover;
        border-radius: 2px;
        border: 1px solid #f0f0f0;
      }

      .basemapSelectedIndicator {
        position: absolute;
        top: 4px;
        right: 4px;
        background-color: #1890ff;
        color: #fff;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        box-shadow: 0 0 0 2px #fff;
      }
    }

    .basemapLabel {
      font-size: 12px;
      color: #555;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
  }
}

// 底图切换按钮
.basemapToggle {
  cursor: pointer;
  box-sizing: border-box;
  border-radius: 8px; // 改为方形圆角
  height: @basemap-toggle-width;
  width: @basemap-toggle-width;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // Light theme
  &.lightTheme {
    background-color: #fff;
    border: 2px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #f5f5f5;
      border-color: #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
      transform: translateY(-1px);
    }
  }

  // Dark theme
  &.darkTheme {
    background-color: #1D2834;
    border: 2px solid rgba(126, 224, 212, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    &:hover {
      background-color: rgba(126, 224, 212, 0.1);
      border-color: rgba(126, 224, 212, 0.6);
      box-shadow: 0 4px 12px rgba(126, 224, 212, 0.2);
      transform: translateY(-1px);
    }
  }

  img {
    width: 100%;
    height: 100%;
    border-radius: 6px; // 稍小的圆角适配方形按钮
    object-fit: cover;
  }
}

.layerListContainer {
  background-color: #fff !important;
  color: rgba(0, 0, 0, 0.85) !important;

  h3 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .layerList {
    .layerItem {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.85) !important;

      .layerName {
        flex: 1;
        color: rgba(0, 0, 0, 0.85) !important;
      }

      .layerControls {
        display: flex;
        gap: 8px;

        button {
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          background: #fff;
          color: rgba(0, 0, 0, 0.85) !important;
          cursor: pointer;

          &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
          }

          &.hidden {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .emptyMessage {
    text-align: center;
    padding: 20px 0;
    color: #999 !important;
  }
}

// 滑入动画关键帧
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// 专业动画关键帧
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}
