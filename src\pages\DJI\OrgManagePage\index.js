import { useState, useEffect } from "react";
import { Card, Table } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2 } from "@/services/general";
import OrgInfoAddForm from "./form_add";
import TableCols from "./table";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import AddButton from "@/components/AddButton";
import { getGuid } from "@/utils/helper";
import AdminDiv from "@/components/AdminDiv";

const OrgInfoListPage = ({doNotShowLastButton}) => {
  const [OrgInfoList, setOrgInfoList] = useState([]);
  const { setModal, setOpen } = useModel("pageModel");

  //接口获取表单信息
  const getOrgInfoData = async () => {
    let pst = await Get2("/api/v1/OrgInfo/GetAllList", {});
    if (isEmpty(pst)) pst = [];
    setOrgInfoList(pst);
  };

  useEffect(() => {
    getOrgInfoData();
  }, []);

  const refrush = async () => {
    getOrgInfoData();
    setOpen(false);
  };

  const getExr = (
    <AdminDiv>
      <AddButton
        onClick={() => {
          setModal(
            <OrgInfoAddForm key={getGuid()} org={{}} refrush={refrush} />
          );
          setOpen(true);
        }}
      >
        新建子组织
      </AddButton>
    </AdminDiv>
  );

  return (
    <div style={{ margin: 0, height: getBodyH(56)}}>
      <Card
        title={ doNotShowLastButton ? '组织管理' : <LastPageButton title="组织管理"/> }
        bordered={false}
        extra={getExr}
      >
        <div>
          {isEmpty(OrgInfoList) ? (
            <div />
          ) : (
            <Table
              pagination={false}
              bordered
              dataSource={OrgInfoList}
              columns={TableCols(refrush, (e) => {
                setModal(
                  <OrgInfoAddForm
                    key={getGuid()}
                    refrush={refrush}
                    org={e}
                  ></OrgInfoAddForm>
                );
                setOpen(true);
              })}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default OrgInfoListPage;
