import { getBodyW2, isEmpty } from "@/utils/utils";
import { useEffect } from "react";
import { useModel,useSearchParams } from "umi";
import useDeviceStore from "@/stores/deviceStore";
import GuangFuPage from './GuangFuHome';
import JiaoTongPage from './JiaoTongHome';
import DevicePage from "../DevicePage";
import { queryPage2 } from "@/utils/MyRoute";


const OrgPage = () => {
    const { deviceList, fetchDeviceList } = useDeviceStore();
    const {setTitle}=useModel('pageModel')
    const [searchParams, setSearchParams] = useSearchParams();
    const xxx=searchParams.get('type')
    const d1=localStorage.getItem('device');


    useEffect(() => {
        // 初始化时获取设备数据
        fetchDeviceList();
    }, [fetchDeviceList]);

    // 更新localStorage
    useEffect(() => {
        if(deviceList.length > 0) {
            localStorage.setItem('device', JSON.stringify(deviceList[0]));
            const data = {};
            deviceList.forEach(e => {
                data[e.SN] = e;
            });
            localStorage.setItem('deviceList', JSON.stringify(data));
        }
    }, [deviceList]);
    

    const pTitle=localStorage.getItem('PageIndexTitle')
    if(!isEmpty(pTitle)){
        return queryPage2(pTitle);
    }

    if(xxx=="guangfu"){
        setTitle('光伏电站无人机智慧巡检系统');
        return <GuangFuPage />
    }

    if(xxx=="jiaotong"){
        setTitle('仁沐新高速无人机智慧巡检系统');
        //setTitle('紫坪铺水库无人机智慧巡检系统');
        return <JiaoTongPage />
    }

    if(xxx=="device"){
        if(!isEmpty(d1)){
            setTitle('紫坪铺水库无人机智慧巡检系统');
            const  device=JSON.parse( localStorage.getItem('device'))
            return <DevicePage device={device} />
         }
    }

    if(xxx=="shuiku"){
        if(!isEmpty(d1)){
            setTitle('紫坪铺水库无人机智慧巡检系统');
            const  device=JSON.parse( localStorage.getItem('device'))
            return <DevicePage device={device} />
         }
    }

    const ww=getBodyW2(0);
    if(ww>1000){
        setTitle('无人机智慧巡检与应急管理系统');
    }else{
        setTitle('无人机智慧巡检系统');
    }

    return <JiaoTongPage />
}

export default OrgPage;