# 操作日志 - REFACTOR

## 📋 基本信息
- **操作时间**: 2024-12-19 19:00:00
- **操作类型**: REFACTOR
- **关联任务**: 修复全景拍照功能无限循环问题
- **影响范围**: src/models/eventModel.jsx, src/models/panoramaModel.jsx

## 🎯 操作目标
解决多个模型重复监听同一个MQTT主题导致的重复消息处理问题，优化系统架构，实现统一的事件处理机制。

## 🔄 具体操作
### 修改文件
1. **文件路径**: `src/models/eventModel.jsx`
   - **修改类型**: 功能增强
   - **主要变更**: 添加回调机制支持全景拍照事件分发

2. **文件路径**: `src/models/panoramaModel.jsx`
   - **修改类型**: 架构重构
   - **主要变更**: 移除独立MQTT连接，改为使用eventModel回调机制

### 代码变更详情

#### eventModel.jsx 变更
```javascript
// 变更前
import { useState, useRef } from "react";

const EventMqttConn = (sn) => { ... };
const updateVal = (t1, m1) => { ... };

// 变更后
import { useState, useRef, useCallback } from "react";

// 修复依赖关系
const updateVal = useCallback((t1, m1) => {
  // ... 处理逻辑
}, [setHms, setCloudAuth, setOtaData]);

const EventMqttConn = useCallback((sn) => {
  // ... MQTT连接逻辑
}, [updateVal, setHms, setCloudAuth]);

// 新增回调机制
const panoramaCallbacks = useRef(new Set());

const registerPanoramaCallback = useCallback((callback) => {
  panoramaCallbacks.current.add(callback);
  return () => {
    panoramaCallbacks.current.delete(callback);
  };
}, []);

// 在全景拍照事件处理中通知回调
if(xx.method=="camera_photo_take_progress"){
  console.log('全景拍照进度事件',xx.data)
  panoramaCallbacks.current.forEach(callback => {
    try {
      callback(xx.data);
    } catch (error) {
      console.error('全景拍照回调执行失败:', error);
    }
  });
}
```

#### panoramaModel.jsx 变更
```javascript
// 变更前
import { GetMqttClient } from "@/utils/websocket";
const mqttC = useRef({});

const PanoramaMqttConn = useCallback((sn) => {
  mqttC.current = GetMqttClient();
  mqttC.current.on("message", (topic, message) => {
    updateVal(topic, message);
  });
  mqttC.current.subscribe("thing/product/" + sn + "/events");
}, []);

// 变更后
const { registerPanoramaCallback } = useModel('eventModel');

const PanoramaMqttConn = useCallback((sn) => {
  console.log('注册全景拍照事件回调, 设备SN:', sn);
  // 通过eventModel的回调机制处理事件
}, []);

// 注册回调
useEffect(() => {
  if (registerPanoramaCallback) {
    const unregister = registerPanoramaCallback(handlePhotoProgress);
    return () => {
      if (unregister) {
        unregister();
      }
    };
  }
}, [registerPanoramaCallback]);
```

## 💭 决策原因
### 技术考量
- **避免重复连接**: 多个模型监听同一MQTT主题导致资源浪费和重复处理
- **统一事件管理**: eventModel作为唯一的MQTT事件处理中心，提高系统可维护性
- **解耦设计**: panoramaModel不再直接依赖MQTT连接，通过回调机制获取事件
- **依赖关系修复**: 使用useCallback正确声明函数依赖，避免闭包陷阱和过时引用

### 业务逻辑
- **性能优化**: 减少重复的MQTT连接和消息处理
- **架构清晰**: 明确的职责分离，eventModel负责事件接收，panoramaModel负责业务处理
- **扩展性**: 其他模型也可以通过类似的回调机制订阅特定事件

## 🧪 验证方法
- [ ] 检查控制台是否还有重复的HMS日志
- [ ] 验证全景拍照事件是否正常处理
- [ ] 确认只有一个MQTT连接在工作
- [ ] 测试全景拍照功能是否正常
- [ ] 检查页面性能是否有改善

## ⚠️ 风险评估
### 潜在风险
- **回调机制**: 需要确保回调函数正确注册和注销
- **事件丢失**: 如果eventModel未正确初始化，可能导致事件丢失
- **依赖关系**: panoramaModel现在依赖eventModel的正确工作

### 缓解措施
- **错误处理**: 在回调执行中添加try-catch保护
- **日志记录**: 添加详细的注册/注销日志便于调试
- **向后兼容**: 保留PanoramaMqttConn接口，避免破坏现有调用

## 📚 参考资料
- [React useCallback 官方文档](https://react.dev/reference/react/useCallback)
- [观察者模式设计原则](https://refactoring.guru/design-patterns/observer)
- 项目规则文档: `.trae/rules/project_rules.md`

## 🔄 后续计划
- 监控系统运行状态，确保事件处理正常
- 考虑为其他类型的事件也实现类似的回调机制
- 优化eventModel的性能，支持更多事件类型

## 📝 复盘总结
### 成功点
- **架构优化**: 成功实现了统一的事件处理架构
- **问题解决**: 彻底解决了重复MQTT连接的问题
- **代码清理**: 移除了不必要的重复代码和依赖

### 改进点
- **文档完善**: 需要更新架构文档说明新的事件处理机制
- **测试覆盖**: 需要添加针对回调机制的单元测试

### 经验教训
- **系统设计**: 在设计多模块系统时要考虑资源共享和避免重复
- **事件驱动**: 回调机制是实现松耦合的有效方式
- **渐进式重构**: 保持接口兼容性有助于平滑过渡