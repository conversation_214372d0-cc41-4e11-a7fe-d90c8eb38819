import React, { useEffect, useMemo, useState } from "react";
import { Circle, Polygon, Tooltip } from "react-leaflet";
import { axiosApi } from "@/services/general";
import { convertPListWgs } from "../helper";
import { wgs84Togcj02 } from "../gps_helper";

export default function NoFlyArea({ needsConversion }) {
  const [FlyArea, setFlyArea] = useState(null); // 禁飞区请求数据
  let device = JSON.parse(localStorage.getItem("device"));

  useEffect(() => {
    getFlyArea();
  }, [needsConversion]);

  const getFlyArea = async () => {
    if (!device) {
      return;
    }
    await axiosApi(`/api/v1/FlyArea/GetCurrent`, "GET", { SN: device.SN })
      .then((res) => {
        // console.log("getFlyArea", res);
        if (res.code === 1) {
          setFlyArea(res.data.features);
        }
      })
      .catch((error) => {});
  };

  const elements = useMemo(() => {
    if (!FlyArea) {
      return null;
    }

    const results = [];
    const formatColor = {
      nfz: "red",
      dfence: "blue",
    };
    const formatText = {
      nfz: "禁飞区",
      dfence: "电子围栏",
    };

    const swapCoordinates = (coords) => {
      // 处理 [[lon, lat], [lon, lat], ...]
      if (Array.isArray(coords) && Array.isArray(coords[0]) && coords[0].length === 2) {
        return coords.map((pair) => [pair[1], pair[0]]);
      }
      
      return coords;
    };

    FlyArea.forEach((item) => {
      let originalCoordinates = item.geometry?.coordinates;
      let processedCoordinatesForDisplay;
      const color = formatColor[item.geofence_type] || "gray"; // 默认颜色

      // 检查原始坐标数据是否存在
      if (!originalCoordinates) {
        return;
      }

      if (item.geometry.type === "Polygon") {
        let polygonPoints = swapCoordinates(originalCoordinates[0]);
        if (needsConversion && polygonPoints && polygonPoints.length > 0) {
          polygonPoints = convertPListWgs(polygonPoints);
        }
        processedCoordinatesForDisplay = polygonPoints;
        
        if (processedCoordinatesForDisplay) {
          results.push(
            <Polygon
              key={item.id}
              positions={processedCoordinatesForDisplay}
              color={color}
              weight={2}
              dashArray="5,5" // 虚线
            >
              <Tooltip>
                <span style={{ color: color }}>
                  {formatText[item.geofence_type]}
                </span>
              </Tooltip>
            </Polygon>
          );
        }
      } else if (item.geometry.type === "Point") {
        let pointCoords = originalCoordinates;
        if (needsConversion && pointCoords && pointCoords.length === 2) {
          pointCoords = wgs84Togcj02(pointCoords[0], pointCoords[1]);
        }
        processedCoordinatesForDisplay = pointCoords;

        if (processedCoordinatesForDisplay) {
          results.push(
            <Circle
              key={item.id}
              center={processedCoordinatesForDisplay}
              radius={item.properties.radius}
              color={color}
            >
              <Tooltip>
                <span style={{ color: color }}>
                  {formatText[item.geofence_type]}
                </span>
              </Tooltip>
            </Circle>
          );
        }
      } else {
        return null;
      }
    });

    return results;
  }, [FlyArea]);

  return elements; // 返回包含所有 React 元素的数组
}
