import { HGet2 } from "@/utils/request";
import {Dropdown,But<PERSON>} from "antd";
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
import {CheckIfCanFly} from '@/pages/DJI/FlyToPage/helper';
import { isEmpty } from "@/utils/utils";
//import WaylinePanel from "../WayLine/WaylinePanel";


const TakeOff=async (sn)=>{
  const pb1=await CheckIfCanFly(device.SN);
  if(!pb1) return;
  HGet2("/api/v1/FlyTo/TakeOff?sn="+sn)
}

const items =(sn,onOpen,doCMD)=>{ return [

 
    {
      key: '1-1',
      label: (  <a onClick={()=>doCMD("debug_mode_open")}>开启调试模式</a>),
    },
    {
        key: '1-2',
        label: (  <a onClick={()=>doCMD("debug_mode_close")}>关闭调试模式</a>),
      },
      {
        key: '1-3',
        label: (  <a onClick={()=>doCMD("device_reboot")}>机场重启</a>),
      },

      {
        key: '1-4',
        label: (  <a onClick={()=>doCMD("drone_open")}>飞行器开机</a>),
      },
      {
        key: '1-5',
        label: (  <a onClick={()=>doCMD("drone_close")}>飞行器关机</a>),
      },
      {
        key: '1-6',
        label: (  <a onClick={()=>doCMD("cover_open")}>打开舱盖</a>),
      },
      
      {
        key: '1-7',
        label: (  <a onClick={()=>doCMD("cover_close")}>关闭舱盖</a>),
      },
      {
        key: '1-8',
        label: (  <a onClick={()=>doCMD("putter_open")}>推杆展开</a>),
      },
      
      {
        key: '1-9',
        label: (  <a onClick={()=>doCMD("putter_close")}>推杆闭合</a>),
      },
      
      {
        key: '1-10',
        label: (  <a onClick={()=>doCMD("device_format")}>机场数据格式化</a>),
      },

     
  ]};

  const DeBugButton=(props)=>{
    const {setModal,setOpen}=useModel('pageModel')
    const {DoCMD}=useModel('cmdModel');
    let col1=props.color;
    let label=props.label;

    if(isEmpty(col1)) col1='white';
    if(isEmpty(label)) label='';

    const {sn}=props;
    const doCMD=(cmd)=>{
      DoCMD(sn,cmd,null);
    }
    return   <Dropdown
       menu={{
         items:items(sn,()=>{
          // setModal(<WaylinePanel />); setOpen(true)
        },doCMD),
       }}
       placement="bottomRight"
       arrow
    
       style={{
         }}
     >
       <Button  type="text"> 调试指令 <DownOutlined style={{color:col1}}/></Button>
     </Dropdown>
   }

export default DeBugButton;
