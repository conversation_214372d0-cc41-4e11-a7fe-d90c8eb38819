import React, { useEffect, useState } from 'react';
import styles from '../index.less';
import { DownOutlined, UpOutlined, LoadingOutlined } from '@ant-design/icons';
import { axiosApi } from "@/services/general";
import { HGet2 } from '@/utils/request';
import { isEmpty } from '@/utils/utils';
import dayjs from 'dayjs';
import { useModel } from "umi";
import DevicePage from "@/pages/DJI/DevicePage";
import { Button, Card, message, Spin } from 'antd';
import { getGuid } from '@/utils/helper';
import { getBodyH } from '@/utils/utils';
import WayLineMap from '@/pages/DJI/WayLine/WayLineMap';
import LastPageButton from "@/components/LastPageButton";
import { CheckIfCanFly } from "@/pages/DJI/FlyToPage/helper";



const StatItem = ({ label, value, unit, className }) => (
  <div className={`${styles.statItem} ${className || ''}`}>
    <div className={styles.statContent}>
      <span className={styles.statLabel}>{label}</span>
      <div className={styles.statValueContainer}>
        <span className={styles.statValue}>{value}</span>
        {unit && <span className={styles.statUnit}>{unit}</span>}
      </div>
    </div>
  </div>
);

// 圆弧百分比组件 - 左右布局
const CircularProgress = ({ online, total, label, className }) => {
  const percentage = total > 0 ? (online / total) * 100 : 0;
  const radius = 36;
  const strokeWidth = 4;
  const normalizedRadius = radius - strokeWidth * 1.5;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={`${styles.airportStatItem} ${className || ''}`}>
      <div className={styles.circularProgress}>
        <svg
          height={radius * 2}
          width={radius * 2}
          className={styles.circularProgressSvg}
        >
          {/* 背景圆弧 */}
          <circle
            stroke="#2a3f5f"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          {/* 进度圆弧 */}
          <circle
            stroke="#1abc9c"
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            style={{ strokeDashoffset }}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
            className={styles.progressCircle}
          />
        </svg>
        <div className={styles.circularProgressText}>
          <div className={styles.onlineCount}>{online}</div>
        </div>
      </div>
      <div className={styles.airportStatInfo}>
        <div className={styles.airportStatLabel}>{label}</div>
        <div className={styles.airportStatValue}>
          <span style={{ color: '#71DBB7', fontSize: '22px' }}>{online}</span>
          <span>/{total}</span>
        </div>
      </div>
    </div>
  );
};

const AirportItem = ({ name, status, location, isFlying, isOnline, deviceItem, onVideoClick, onAirportNameClick }) => {
  const { page, setPage } = useModel("pageModel");

  const getStatusText = (statusCode) => {
    const statusMap = {
      0: '空闲',
      1: '现场调试',
      2: '远程调试',
      3: '固件升级中',
      4: '作业中',
    };
    return statusMap[statusCode] || '空闲';
  };

  const getStatusColor = (statusCode) => {
    if (statusCode === 0) return '#1ABC9C'; // 空闲状态使用#1ABC9C
    if (statusCode === 4) return 'orange';  // 作业中使用orange
    return '';
  };

  const handleNameClick = (e, device) => {
    e.stopPropagation();
    // 将点击事件和设备信息传递给父组件
    if (onAirportNameClick && device) {
      if (device.OsdData && device.OsdData.latitude && device.OsdData.longitude) {
        onAirportNameClick({
          lat: device.OsdData.latitude,
          lng: device.OsdData.longitude,
          height: device.OsdData.height,
          name: device.DName,
          device: device
        });
      } else if (device.Lat && device.Lng) {
        // 兼容不同的数据格式
        onAirportNameClick({
          lat: device.Lat,
          lng: device.Lng,
          height: device.Height,
          name: device.DName,
          device: device
        });
      }
    }
  }

  const handleLocationClick = (e) => {
    // console.log('deviceItem', e)
    localStorage.setItem("device", JSON.stringify(e));
    setPage(<DevicePage device={e} fromSI={true} />);
  };

  // 处理视频图标点击，显示视频面板
  const handleVideoClick = (device) => {
    console.log('显示视频面板:', device);
    // 调用父组件传递的回调函数
    if (onVideoClick) {
      onVideoClick(device);
    }
  };

  return (
    <div className={`${styles.airportContainer} ${isOnline ? styles.online : styles.offline}`}>
      <div
        className={`${styles.airportItem}`}
      >
        <span className={styles.airportName} title={name} onClick={(e) => handleNameClick(e, deviceItem)}>{name}</span>
        <span className={`${styles.airportOnline} ${!isOnline ? styles.offline : ''}`} title={isOnline ? '在线' : '离线'}>
          {isOnline ? '在线' : '离线'}
        </span>
        <span
          className={`${styles.airportStatus} ${isFlying ? styles.flying : ''}`}
          title={getStatusText(status)}
          style={{ color: getStatusColor(status) }}
        >
          {getStatusText(status)}
        </span>
        <span className={`${styles.airportLocation} ${isOnline ? styles.online : styles.offline}`}
          onClick={() => handleLocationClick(deviceItem)}
          title="进入驾驶舱"
          style={{ cursor: 'pointer' }} />
        <span className={`${styles.videoIcon} ${isOnline ? styles.online : styles.offline}`}
          onClick={() => handleVideoClick(deviceItem)}
          title="打开摄像头"
          style={{ cursor: 'pointer' }} />
      </div>
    </div>
  );
};

const calculateStatistic = (xList, dL, field, scale = 1) => {
  let total = 0;
  dL.forEach(e => {
    const vL = xList.filter(p => p.DeviceSN === e.SN);
    let v1 = 0;
    vL.forEach(x => {
      v1 = v1 + x[field];
    });
    total = total + v1;
  });
  return total * scale;
};

const LeftSidebar = ({ collapsed, deviceList, deviceLoading, onRefresh, onVideoClick, onAirportNameClick }) => {

  const getDeviceStats = (bindCodes) => {
    const devices = deviceList.filter(d =>
      Array.isArray(bindCodes)
        ? bindCodes.includes(d.BindCode)
        : d.BindCode === bindCodes
    );
    return {
      online: devices.filter(d => d.IfOnLine).length,
      total: devices.length
    };
  };

  const sidebarWidth = 420; // Fixed expanded width
  const sidebarPadding = 15; // Fixed padding

  let para1 = {
    sDate: [dayjs('2025/1/1'), dayjs()],
    t1: dayjs().startOf('year'),
    t2: dayjs(),
  }

  const [params, setParams] = useState(para1);
  const [xList, setXList] = useState([]);

  const ifPush = (e) => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')

    if (!isEmpty(params.t1)) {
      t1 = dayjs(params.t1);
    }
    if (!isEmpty(params.t2)) {
      t2 = dayjs(params.t2);
    }


    const t3 = dayjs(e.CreateTime);
    if (t1.isAfter(t3) || (t2.isBefore(t3))) {
      return false;
    }


    return true;
  }

  const getXL = (data) => {
    const xL = []
    data.forEach(e => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    return xL;
  }

  useEffect(() => {
    const yy = async () => {
      let xx = await HGet2(`/api/v1/Task/GetAllList`)
      if (isEmpty(xx)) xx = [];

      if (!isEmpty(xx.err)) {
        message.error(xx.err);
        xx = [];
      }

      const xL = getXL(xx);
      setXList([...xL]);
    }

    yy();
  }, []);

  useEffect(() => {
    // Set CSS variable to expanded width for toggle button positioning
    document.documentElement.style.setProperty('--left-sidebar-width', `${sidebarWidth}px`);
  }, []); // Empty dependency array, as sidebarWidth is now constant

  return (
    <div
      className={`${styles.sidebar} ${styles.leftSidebar} ${collapsed ? styles.isCollapsed : ''}`}
      style={{ width: `${sidebarWidth}px` }} // 移除overflow auto，使用CSS控制
    >
      {true && (
        <>
          <div className={styles.section}>
            <div className={styles.sectionTitle}><span className={styles.titleText}>飞行统计</span></div>
            <div className={styles.sectionContent + ' ' + styles.flightStats}>
              <StatItem
                label="飞行架次"
                value={calculateStatistic(xList, deviceList, 'TaskState')}
                unit="架次"
                className={styles.flightCountStat}
              />
              <StatItem
                label="飞行里程"
                value={calculateStatistic(xList, deviceList, 'Distance').toFixed(1)}
                unit="公里"
                className={styles.flightDistanceStat}
              />
              <StatItem
                label="飞行时长"
                value={calculateStatistic(xList, deviceList, 'FlyTM', 1 / 60 / 60).toFixed(1)}
                unit="小时"
                className={styles.flightTimeStat}
              />
            </div>
          </div>

          <div className={styles.section}>
            <div className={styles.sectionTitle}><span className={styles.titleText}>机场统计</span></div>
            <div className={`${styles.sectionContent} ${styles.airportStats}`}>
              <CircularProgress
                online={(() => {
                  const stats = getDeviceStats('qwe');
                  return stats.online;
                })()}
                total={(() => {
                  const stats = getDeviceStats('qwe');
                  return stats.total;
                })()}
                label="机场数"
                className={styles.airportOnlineStat}
              />
              <CircularProgress
                online={(() => {
                  const stats = getDeviceStats(['qwe', 'pilot']);
                  return stats.online;
                })()}
                total={(() => {
                  const stats = getDeviceStats(['qwe', 'pilot']);
                  return stats.total;
                })()}
                label="无人机数"
                className={styles.droneOnlineStat}
              />
            </div>
          </div>

          <div className={styles.section + ' ' + styles.airportSection}>
            <div className={styles.sectionTitle}>
              <span className={styles.titleText}>机场列表</span>
              {deviceLoading && <span style={{fontSize: '12px', color: '#999', marginLeft: '8px'}}>加载中...</span>}
            </div>
            <div className={`${styles.sectionContent} ${styles.airportList}`}>
              {deviceList.map(item => {
                return (
                  <AirportItem
                    key={item.ID}
                    name={item.DName}
                    status={item?.OsdData?.mode_code}
                    location={[item.Lat, item.Lng, item.Height]}
                    isFlying={item?.OsdData?.is_flying}
                    isOnline={item?.IfOnLine}
                    deviceItem={item}
                    onVideoClick={onVideoClick}
                    onAirportNameClick={onAirportNameClick}
                  />
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LeftSidebar;
