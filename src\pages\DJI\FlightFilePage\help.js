import { getImgUrl } from '@/utils/utils';
import {  getGuid, timeFormat } from '@/utils/helper';


export function getVideoDuration(url) {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.src = url;
      video.addEventListener('loadedmetadata', () => {
        resolve(video.duration);
        video.remove();
      });
      video.addEventListener('error', () => {
        resolve(null);
        video.remove();
      });
      document.body.appendChild(video);
    });
  }

 export const downLoadClick=(xL)=>{
    xL.forEach(e => {
        const url = getImgUrl(e.ObjectName) + "?attname=" + e.FileName;
        let iframe = document.createElement('iframe');
        iframe.id = getGuid();
        iframe.style.display = "none";
        document.body.appendChild(iframe);
        iframe.src = url;
    });
  }
