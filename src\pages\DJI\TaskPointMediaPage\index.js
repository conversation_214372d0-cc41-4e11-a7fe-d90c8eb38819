import React, { Fragment, useState, useEffect } from 'react';

import { <PERSON>readcrumb, Card, Input, Tag, List, Select, Radio, Descriptions, Row, Col, Button, Form, message, Table, Modal, Image } from 'antd';

import { getBodyH, getImgUrl, isEmpty } from '@/utils/utils';

import { Get2 } from '@/services/general';


import MediaPanel from '@/pages/DJI/MediaDataPage/MediaListPanel';
import { useModel } from 'umi';
import { ArrowLeftOutlined } from '@ant-design/icons';
import LastPageButton from '@/components/LastPageButton';



const TaskPointPage = (props) => {


    const [chartData, setChartData] = useState([]);
    const [wayLine, setWayLine] = useState({});
    const [wayPoint, setWayPoint] = useState({});
    const [mList, setMList] = useState([]);

    const { setPage, lastPage } = useModel('pageModel')

    useEffect(() => {

        const getLineData = async () => {
            const pst = await Get2('/api/v1/WayLine/GetAllList', {});
            setChartData(pst);
        };
        getLineData();
    }, []);

    const padWithZeros = (str, length) => {
        
        while (str.length < length) {
            str = '0' + str;
        }
        return '_' + str + '_';
    }


        const getMData = async (nn) => {
            if (isEmpty(wayLine)) return;
            const pst = await Get2('/api/v1/Media/GetListByPoint?id=' + wayLine + "&p1=" + padWithZeros(nn.toString(), 4), {});
            setMList(pst);
        };




    const getWaySelect = (wayList) => {
        const list = []
        if(isEmpty(wayList)) return list;
        wayList.forEach(e => {
            list.push(<Select.Option key={e.WanLineId} data={e} value={e.WanLineId}>{e.WayLineName}</Select.Option>)
        });

        return list;
    }

    const getPointSelect = () => {
        const list = []
        if (isEmpty(wayLine)) return list;
        // 
        const w1 = chartData.find(p => p.WanLineId == wayLine);
        for (let i = 1; i <= w1.Source; i++) {
            list.push(<Select.Option value={i}>{"航点" + i}</Select.Option>)
        }

        return list;
    }


    const exr = () => {
        return <div style={{ width: 600.0 }}><Form layout={'inline'}  >
            <Form.Item label="选择航线">
                <Select
                    style={{ width: 200 }}
                    onSelect={(e) => setWayLine(e)}>
                    {getWaySelect(chartData)}
                </Select>
            </Form.Item>

            <Form.Item style={{ marginLeft: 24.0, width: 200 }} label="选择航点">
                <Select
                    onSelect={(e) => {setWayPoint(e);getMData(e);}}>
                    {getPointSelect()}
                </Select>
            </Form.Item>

        </Form></div>
    }




    return (

        <div style={{ margin: 0, height: getBodyH(56), background: '#F5F5FF' }}>


            <Card title={<LastPageButton title='航点照片' />} bordered={false} extra={exr()} >
                <div>
                    <List
                        grid={{ gutter: 16, column: 5 }}
                        dataSource={mList}
                        pagination={{ pageSize: 10 }}
                        style={{ height: getBodyH(200) }}
                        renderItem={item => (
                            <List.Item>
                                <Image style={{ width: '100%', height: '100%' }} src={getImgUrl(item.ObjectName)}
                                />
                                <div>{item.FileName}</div>
                            </List.Item>)}></List>
                </div>
            </Card>


        </div>
    )
};

export default TaskPointPage;
