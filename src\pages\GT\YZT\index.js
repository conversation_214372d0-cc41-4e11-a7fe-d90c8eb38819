
// import MyHead from "../components/MyHead_YZT";
import MyHead from "../components/MyHead";
import commonStyle from "../style/common.less";
import "@/pages/GT/style/antd-common.less";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import { queryPage } from "@/utils/MyRoute";
import { useState,useEffect } from "react";
import { useModel } from "umi";
import SITheme from '@/pages/SI/style/theme';


function App() {
  const { page, setPage, lastPage, listPage } = useModel("pageModel");
  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };
  const headList = [
    { label: "", key: "一张图资源管理系统" },
  ];

  return (
    <div
      className={commonStyle.gt_back_white}
      style={{ position: "relative", overflow: "hidden", height: "100vh" }}
    >
      <MyHead
        headTitle={"一张图资源管理系统"}
        headList={headList}
        handlePageChange={handlePageChange}
      ></MyHead>
      <div style={{ height: "calc(100% - 56px)", overflow: "hidden" }}>
        <ConfigProvider locale={locale} theme={SITheme}>
          {page}
        </ConfigProvider>
      </div>
      {/* <MyFoot /> */}
    </div>
  );
}

export default App;
