.dynamicMonitorContainer {
  height: calc(100vh - 50px);
  width: 100vw;
  position: relative;
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
}

.mainContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  &>div:first-child {
    flex-grow: 1;

    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #f0f2f5;
}

.sidebarToggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 50px;
  // background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  // justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: background-color 0.3s ease;

  &.leftToggle {
    left: 0;
    border-radius: 4px 0 0 4px;
    justify-content: flex-end;
  }

  &.rightToggle {
    right: 0;
    border-radius: 0 4px 4px 0;
    justify-content: flex-start;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.toggleLine {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 65vh;
  z-index: 1001;

  &.leftLine {
    left: 12px;
  }

  &.rightLine {
    right: 12px;
  }
}

.toggleIcon {
  // width: 16px;
  // height: 16px;
  // position: absolute;
  // top: 50%;
  // transform: translateY(-50%);
  // width: 2px;
  // height: 75vh;
  // z-index: 1001;

  transition: transform 0.3s ease-in-out;
}

.leftToggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) translateX(-2px) rotate(180deg);
  left: 0px;
  z-index: 1001;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.expandedIconLeft {
  transform: translateY(-50%) translateX(-13px);
}

.rightToggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) translateX(1px);
  right: 0px;
  z-index: 1001;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.expandedIconRight {
  transform: translateY(-50%) translateX(12px) rotate(180deg);
}

.collapsedIcon {
  .leftToggle & {
    transform: rotate(180deg);
  }

  .rightToggle & {
    transform: rotate(0deg);
  }
}

.sidebar {
  position: absolute;
  top: 0;
  height: 100%;
  padding: 15px 20px 45px;
  box-sizing: border-box;
  overflow: hidden; // 移除整体滚动
  transition: transform 0.35s ease-in-out;
  z-index: 999;
  display: flex;
  flex-direction: column;

  .section {
    border-radius: 4px;
    width: 97%;
    margin-bottom: 15px;

    .sectionTitle {
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      background-image: url('../assets/image/title_bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 6px 15px;
      border-radius: 4px 4px 0 0;
      // border: 1px solid #1abc9c;
      display: flex;
      align-items: center;
      position: relative;

      >.anticon {
        margin-right: 8px;
      }

      .titleText {
        position: relative;
        top: -15px;
      }
    }

    .sectionContent {
      font-size: 14px;
      padding-top: 10px;
    }
  }
}

.leftSidebar {
  left: 0;
  // background: linear-gradient(to right, rgb(0 0 0), rgb(59 66 73 / 0.9), rgb(0 0 0 / 0));
  transform: translateX(0);

  &.isCollapsed {
    transform: translateX(-100% - 25px);
  }
}

.statItem {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-image: url('../assets/image/list-bottom.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 4px 100%;
  margin-bottom: 10px;

  &:last-child {
    border-bottom: none;
  }

  .statContent {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .statLabel {
    color: rgba(255, 255, 255, 0.996);
    font-size: 14px;
  }

  .statValueContainer {
    display: flex;
    align-items: baseline;
  }

  .statValue {
    color: rgba(114, 196, 181, 0.996);
    font-weight: bold;
    font-size: 20px;
  }

  .statUnit {
    color: rgba(217, 241, 253, 0.996);
    margin-left: 4px;
    font-size: 12px;
  }
}

// 飞行统计背景图片样式
.flightCountStat {
  background-image: url('../assets/image/低空监控/飞行架次.png'), url('../assets/image/list-bottom.png');
  background-size: 100% 100%, 100% auto;
  background-repeat: no-repeat, no-repeat;
  background-position: center, 4px 100%;

  .statLabel {
    margin-left: 50px;
  }
}

.flightDistanceStat {
  background-image: url('../assets/image/低空监控/飞行里程.png'), url('../assets/image/list-bottom.png');
  background-size: 100% 100%, 100% auto;
  background-repeat: no-repeat, no-repeat;
  background-position: center, 4px 100%;

  .statLabel {
    margin-left: 50px;
  }
}

.flightTimeStat {
  background-image: url('../assets/image/低空监控/飞行时长.png'), url('../assets/image/list-bottom.png');
  background-size: 100% 100%, 100% auto;
  background-repeat: no-repeat, no-repeat;
  background-position: center, 4px 100%;

  .statLabel {
    margin-left: 50px;
  }
}

.flightStats {
  height: auto;
  min-height: 14vh;
}

.airportSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex子项收缩

  .sectionContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 允许flex子项收缩
  }
}

.routesList {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0;
  max-height: 300px; // 子项的height
  overflow-y: auto;
  background-color: rgba(12, 74, 100, 0.3);
}

.routeItem {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 15px;
  transition: background 0.3s;
  position: relative;
}

.routeItem:hover {
  background: rgba(0, 0, 0, 0.2);
}

.routeInfo {
  width: 100%;
}

.routeHeader {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 8px;
}

.routeIcon {
  background-image: url('../assets/image/airportItem.svg');
  background-size: 80% auto;
  background-repeat: no-repeat;
  background-position: center;
  width: 20px;
  height: 20px;
  filter: hue-rotate(180deg);
  margin-right: 8px;
  display: inline-block;
}

.routeName {
  color: white;
  font-size: 14px;
  font-weight: bold;
  flex: 1;
}

.routeBtns {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  gap: 8px;
}

.viewRouteBtn {
  background: #1890ff;
  border-color: #1890ff;
}

.executeBtn {
  background: #52c41a;
  border-color: #52c41a;
}

.routeDetails {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.routeDescRow,
.routeTimeRow,
.routeStatusRow {
  display: flex;
  align-items: flex-start;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.routeLabel {
  width: 70px;
  color: rgba(255, 255, 255, 0.6);
}

.routeDesc,
.routeTime,
.routeStatusValue {
  flex: 1;
}

.noRoutes {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.airportStats {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  height: auto;
  min-height: 16vh;
}

// 机场统计项 - 左右布局
.airportStatItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  padding: 10px;
  background-image: url('../assets/image/list-bottom.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 4px 100%;
  flex: 1;

  .circularProgress {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .circularProgressSvg {
      transform: rotate(-90deg);
    }

    .progressCircle {
      transition: stroke-dashoffset 0.5s ease-in-out;
    }

    .circularProgressText {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;

      .onlineCount {
        font-size: 24px;
        font-weight: bold;
        color: #71DBB7;
        line-height: 1;
      }
    }
  }

  .airportStatInfo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    text-align: left;
    flex: 1;

    .airportStatLabel {
      font-size: 14px;
      color: rgba(165, 216, 252, 0.996);
      line-height: 1.2;
    }

    .airportStatValue {
      font-size: 18px;
      font-weight: bold;
      color: rgba(217, 241, 253, 0.996);
      line-height: 1.2;
    }
  }
}

// 机场统计背景图片样式
.airportOnlineStat {
  background-image: url('../assets/image/低空监控/机场统计在线数(1).png'), url('../assets/image/list-bottom.png');
  background-size: contain, 100% auto;
  background-repeat: no-repeat, no-repeat;
  background-position: center, 4px 100%;
  padding-left: 0px;
}

.droneOnlineStat {
  background-image: url('../assets/image/低空监控/机场统计在线数(1).png'), url('../assets/image/list-bottom.png');
  background-size: contain, 100% auto;
  background-repeat: no-repeat, no-repeat;
  background-position: center, 4px 100%;
  padding-left: 0px;
}

.airportList {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; // 允许flex子项收缩

  .airportContainer {
    position: relative;

    // 机场容器背景 - 根据在线状态显示不同背景
    &.online::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('../assets/image/低空监控/机场在线.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: calc(50% - 20px) center;
      z-index: -1;
    }

    &.offline::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('../assets/image/低空监控/机场离线.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: calc(50% - 20px) center;
      z-index: -1;
    }
  }

  .airportItem {
    display: grid;
    grid-template-columns: minmax(90px, 180px) 40px 40px 30px 30px 30px;
    column-gap: 3px;
    align-items: center;
    padding: 0px 15px;
    color: white;
    cursor: pointer;
    position: relative;
    height: 50px;

    .airportName {
      flex-grow: 1;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
      margin-left: 40px;
      margin-bottom: 10px;
      grid-column: 1;

      &:hover {
        cursor: pointer;
        color: #4dabd9;
      }
    }

    .airportLocation {
      background-size: 100% auto;
      background-repeat: no-repeat;
      background-position: 0 100%;
      width: 24px;
      height: 20px;
      justify-self: center;
      grid-column: 5;
      margin-bottom: 10px;

      // 根据在线状态显示不同图标
      &.online {
        background-image: url('../assets/image/低空监控/起飞在线.png');
      }

      &.offline {
        background-image: url('../assets/image/低空监控/起飞离线.png');
      }

      &:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.3s ease;
      }
    }


    .airportOnline {
      color: #52c41a;
      background: rgba(82, 196, 26, 0.2);
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
      justify-self: center;
      grid-column: 3;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      margin-bottom: 10px;
    }

    /* 离线状态的颜色 */
    .offline {
      color: #b9b5b6 !important;
    }

    .airportOnline:empty {
      display: none;
    }

    .airportStatus {
      color: white;
      background: rgba(24, 144, 255, 0.2);
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
      justify-self: center;
      grid-column: 4;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 10px;
      // width: 100%;
    }

    .airportStatus.flying {
      color: #faad14;
      background: rgba(250, 173, 20, 0.2);
    }

    .videoIcon {
      background-size: 100% auto;
      background-repeat: no-repeat;
      background-position: 0 100%;
      width: 24px;
      height: 24px;
      justify-self: center;
      grid-column: 6;
      margin-bottom: 10px;

      // 根据在线状态显示不同图标
      &.online {
        background-image: url('../assets/image/低空监控/摄像头在线.png');
      }

      &.offline {
        background-image: url('../assets/image/低空监控/摄像头离线.png');
      }

      &:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.3s ease;
      }
    }
  }
}

// .rightContainer {
//   width: 500px;
//   height: 100vh;
//   right: 0;
//   position: absolute;
//   background: linear-gradient(to left, rgb(10 19 28 / 80%) 0%, rgb(10 19 28 / 30%) 70%, rgb(10 19 28 / 0%) 100%);
//   z-index: 1001;
// }
.rightSidebar {
  right: 0;
  // background: linear-gradient(to left, rgb(0 0 0), rgb(59 66 73 / 0.9), rgb(0 0 0 / 0));
  transform: translateX(0);
  // padding: 15px 10px 15px;

  &.isCollapsed {
    transform: translateX(100% + 25px);
  }
}

.smallStatItem {
  .statLabel {
    font-size: 0.85em;
    margin-bottom: 4px;
  }

  .valueContainer {
    display: flex;
    align-items: baseline;
  }

  .statValue {
    font-size: 1.5em;
  }

  .statUnit {
    font-size: 0.8em;
    margin-left: 3px;
  }
}

// 新的巡飞统计样式
.flightStatsContainer {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  padding: 15px 0;
  height: auto;
  min-height: 14vh;
}

.flightStatCard {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background: linear-gradient(135deg, rgba(15, 82, 110, 0.85) 0%, rgba(8, 45, 65, 0.9) 100%);
  border: 1px solid rgba(26, 188, 156, 0.3);
  border-radius: 8px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #1abc9c, #16a085);
  }
}

.flightStatIcon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    filter: brightness(0) saturate(100%) invert(64%) sepia(98%) saturate(1151%) hue-rotate(160deg) brightness(91%) contrast(101%);
  }
}

.flightStatValue {
  font-size: 20px;
  font-weight: bold;
  color: #1abc9c;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
}

.flightStatUnit {
  font-size: 12px;
  color: #a0a0a0;
  margin-bottom: 4px;
  text-align: center;
}

.flightStatLabel {
  font-size: 14px;
  color: rgba(165, 216, 252, 0.996);
  text-align: center;
  line-height: 1.2;
}

// 巡飞统计背景图片样式
.patrolDaysStat {
  background-image: url('../assets/image/低空监控/累计巡飞天数.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding-top: 70px;
}

.patrolTimesStat {
  background-image: url('../assets/image/低空监控/累计巡飞次数.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding-top: 70px;
}

.patrolMileageStat {
  background-image: url('../assets/image/低空监控/累计巡飞里程.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding-top: 70px;
}

// 影像统计样式 - 背景图片已应用到饼状图内圆

.newImageStatsContainer {
  display: flex;
  // gap: 20px;
  align-items: center;
  padding: 15px 0;
  height: auto;
  min-height: 20vh;
}

.pieChartSection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
  padding: 10px;
}

.pieChartContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 140px;
  height: 140px;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}

/* 动画效果 */
.pieChartContainer svg path {
  transition: opacity 0.2s ease-in-out;
}

.pieChartCenter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.pieChartTotal {
  font-size: 22px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  text-shadow:
    0 1px 3px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(26, 188, 156, 0.4);
  letter-spacing: 0.5px;
}

.pieChartLabel {
  font-size: 13px;
  color: #ffffff;
  line-height: 1;
  margin-top: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  font-weight: 500;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  text-align: center;
  background: rgba(40, 60, 80, 0.3);
  border-radius: 50%;
  border: 1px solid rgba(26, 188, 156, 0.2);
}

.imageStatsLegend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.imageStatItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, rgba(15, 82, 110, 0.6) 0%, rgba(8, 45, 65, 0.7) 100%);
  border: 1px solid rgba(26, 188, 156, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(20, 95, 125, 0.7) 0%, rgba(12, 55, 80, 0.8) 100%);
    border-color: rgba(26, 188, 156, 0.4);
  }
}

.imageStatColor {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 10px;
  flex-shrink: 0;
}

.imageStatLabel {
  flex: 1;
  font-size: 13px;
  color: rgba(165, 216, 252, 0.996);
}

.imageStatValue {
  font-size: 14px;
  font-weight: bold;
  color: rgba(217, 241, 253, 0.996);
}

.imageStatsContainer {
  display: flex;
  gap: 15px;
  align-items: center;
  height: 24vh;

  .imageTotalStats {
    background-image: url('../assets/image/box-top.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    // background-color: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    min-width: 100px;
    text-align: center;

    .totalFigure {
      font-size: 28px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 5px;
    }

    .totalLabel {
      font-size: 13px;
      color: #a0a0a0;
      margin-bottom: 8px;
    }

    .imageIconPlaceholder {
      background-image: url('../assets/image/circle-bottom.png');
      background-size: 100% auto;
      background-repeat: no-repeat;
      background-position: 0 100%;
      font-size: 30px;
      color: #409eff;
      opacity: 0.7;
      width: 100%;
      height: 90px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      .imageIcon {
        background-image: url('../assets/image/imageIcon.svg');
        background-size: 100% auto;
        background-repeat: no-repeat;
        background-position: 0 100%;
        width: 37px;
        height: 31px;
      }
    }
  }

  .imageBreakdown {
    flex-grow: 1;

    .statItem {
      padding: 6px 18px;

      .statLabel {
        min-width: 60px;
      }

      .valueContainer {
        justify-content: flex-end;
      }
    }
  }
}

.missionSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex子项收缩

  .sectionContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 允许flex子项收缩
  }
}

.missionList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 0;
  min-height: 0; // 允许flex子项收缩

  .missionItem {
    background: linear-gradient(135deg, rgba(15, 82, 110, 0.85) 0%, rgba(8, 45, 65, 0.9) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(26, 188, 156, 0.3);
    border-left: 4px solid #1abc9c;
    border-radius: 8px;
    padding: 4px 18px;
    color: white;
    cursor: pointer;
    position: relative;
    min-height: 35px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    // 悬停效果
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
      border-color: rgba(26, 188, 156, 0.6);
      background: linear-gradient(135deg, rgba(20, 95, 125, 0.9) 0%, rgba(12, 55, 80, 0.95) 100%);

      .missionName {
        color: #4dd0e1;
      }

      .missionDetail {
        background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
        transform: scale(1.05);
      }
    }

    // 单行布局：事件名称、时间信息、详情按钮
    .missionMainRow {
      display: grid;
      grid-template-columns: 1fr auto auto;
      gap: 12px;
      align-items: center;
      min-height: 25px; /* 减少行高 */
    }

    .missionName {
      font-size: 14px;
      font-weight: 400;
      color: #e8f4fd;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      transition: color 0.3s ease;
      line-height: 1; /* 调整行高 */

      &:hover {
        color: #4dd0e1;
      }
    }

    .missionTime {
      color: rgba(255, 255, 255, 0.5); /* 弱化显示 */
      font-size: 11px;
      font-weight: 400;
      letter-spacing: 0.3px;
      white-space: nowrap;
      line-height: 1; /* 调整行高 */
    }

    .missionDetail {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 4px 8px;
      background: linear-gradient(135deg, rgba(26, 188, 156, 0.8) 0%, rgba(22, 160, 133, 0.9) 100%);
      border: 1px solid rgba(26, 188, 156, 0.4);
      border-radius: 20px;
      color: #ffffff;
      font-size: 12px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      min-width: 50px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      line-height: 1; /* 调整行高 */

      &:hover {
        background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(26, 188, 156, 0.4);
      }

      &:active {
        transform: scale(0.98);
      }
    }

    // 状态相关的样式变化
    &.urgent {
      border-left-color: #e74c3c;

      .missionStatus {
        background: linear-gradient(135deg, rgba(231, 76, 60, 0.8) 0%, rgba(192, 57, 43, 0.9) 100%);
        border-color: rgba(231, 76, 60, 0.4);
      }
    }

    &.warning {
      border-left-color: #f39c12;

      .missionStatus {
        background: linear-gradient(135deg, rgba(243, 156, 18, 0.8) 0%, rgba(211, 84, 0, 0.9) 100%);
        border-color: rgba(243, 156, 18, 0.4);
      }
    }

    &.normal {
      border-left-color: #27ae60;

      .missionStatus {
        background: linear-gradient(135deg, rgba(39, 174, 96, 0.8) 0%, rgba(34, 153, 84, 0.9) 100%);
        border-color: rgba(39, 174, 96, 0.4);
      }
    }

    // 加载状态
    &.loading {
      opacity: 0.7;
      pointer-events: none;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 20px;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid #1abc9c;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        transform: translateY(-50%);
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.mapControlsContainer {
  position: absolute;
  top: 20px;
  right: calc(var(--right-sidebar-width, 400px) + 20px);
  // transform: translateX(calc(-100% - 20px));
  display: flex;
  flex-direction: column;
  // gap: 0;
  // background-color: rgba(12, 29, 49, 0.85);
  // backdrop-filter: blur(5px);
  // border-radius: 4px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  z-index: 1002;
  transition:
    right 0.3s ease,
    transform 0.3s ease;

  .mapControlItem {
    // padding: 8px 10px;
    width: 60px;
    height: 60px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // color: #e0e0e0;
    font-size: 12px;
    min-width: 50px;
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-image: url('../assets/image/circle-button.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: 0 100%;

    &:last-child {
      border-bottom: none;
    }

    span:first-child {
      // Icon
      // font-size: 18px;
      margin-bottom: 2px;
      color: #1abc9c;
    }

    // &:hover {
    //   background-color: rgba(25, 44, 70, 0.9);
    //   color: #fff;
    //   span:first-child {
    //     color: #fff;
    //   }
    // }
  }

  .mapControlGroupFirst {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .mapControlGroupLast {
    border-bottom-left-radius: 4px;
  }

  &.mapControlGroupLast {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &:not(:last-child) {
    border-right: 1px solid #e8e8e8;
  }
}

.rightSidebarCollapsed {
  .mapControlsContainer {
    right: 20px;
    // transform: translateX(0);
  }
}

// 滚动条样式修改
// ::-webkit-scrollbar {
//   width: 5px;
// }

// ::-webkit-scrollbar-thumb {
//   background-color: rgba(255, 255, 255, 0.2);
//   border-radius: 2px;
// }

// ::-webkit-scrollbar-track {
//   background-color: rgba(255, 255, 255, 0.1);
//   border-radius: 2px;
// }