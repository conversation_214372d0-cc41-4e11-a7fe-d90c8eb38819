import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { Card, List, Checkbox, Select, DatePicker, Pagination } from 'antd';
import { useModel, history } from 'umi';
import { getVideoUrl, getImgUrl, getBodyH } from '@/utils/utils';
import LastPageButton from '@/components/LastPageButton';
import { timeFormat22 } from '@/utils/helper';
import dayjs from 'dayjs';
import DownLoadAndDel from '@/pages/DJI/MediaDataPage/DownLoadAndDel';
import useConfigStore from "@/stores/configStore";

const { RangePicker } = DatePicker;

// 视频Item组件
const MediaItem = React.memo(({ item, checkBoxChange, onItemClick, setModal, setOpen, doNotShowLastButton }) => {
  const [isVisible, setIsVisible] = useState(false);
  const itemRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(entry.target); // 加载后停止观察
          }
        });
      },
      { threshold: 0.2 } // 元素20%可见时触发
    );

    if (itemRef.current) {
      observer.observe(itemRef.current);
    }

    return () => {
      if (itemRef.current) {
        observer.unobserve(itemRef.current);
      }
    };
  }, [item.ID]);

  const onClick = useCallback((item) => {
    if (doNotShowLastButton) {
      history.push("/gt/WRJ");
      localStorage.setItem('showBackButton', 'true');
    }
    setModal(
      <div style={{ height: 420, width: '100%', color: "#fc9a03",position: 'relative' }} key={item.ID}>
        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center",width:"100%", position: 'absolute', top: 15.0,padding: '0 10px' }}>
          <div style={{color:"#000"}}>
            {item.WayLineNM + "-" + item.HangDianIndex}
          </div>
          <div>{timeFormat22(item.CreatedTime)}</div>
        </div>
        <video id={item.ID} height={'100%'} width={'100%'} controls>
          <source src={getVideoUrl(item)} type="video/mp4" />
        </video>
      </div>
    );
    setOpen(true);
  }, []);


  return (
    <div style={{ cursor: 'pointer' }} ref={itemRef}>
      {isVisible && (
        <>
          <img
            style={{ width: '100%', cursor: 'pointer', height: '100%', borderRadius: 5.0 }}
            src={getImgUrl(item.SLTImg)}
            onClick={() => onClick(item)}
            alt={item.FileName}
          />
          <div style={{ margin: 4.0, cursor: 'pointer', fontWeight: 'bold', height: 36.0, color: 'rgba(0,0,0,0.7)' }}>
            <span><Checkbox key={item.ID} onChange={(e) => checkBoxChange(e, item)} style={{ margin: '0 15px 0 -5px' }}></Checkbox></span>
            <span>{item.WayLineNM + "-" + item.HangDianIndex}</span> <span style={{ float: 'right' }}>{item.Size}</span>
          </div>
          <div style={{ position: 'absolute', cursor: 'pointer', top: 2, right: 25, color: '#fff' }}>{timeFormat22(item.CreatedTime)}</div>
        </>
      )}
    </div>
  );
});


const MediaListPanel = ({ mList, refrush, doNotShowLastButton }) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [sList, setSList] = useState([]);
  const [sWay, setSWay] = useState('');
  const [sDate, setSDate] = useState({});
  const { setPage, lastPage, setModal, open, setOpen } = useModel("pageModel");
  const [checkBoxList, setCheckBoxList] = useState([]);


  // LocalStorage分页缓存
  let page1 = localStorage.getItem('MediaListPage') || 1;
  page1 = Number(page1);

  const handleTableChange = (page) => {
    setTableCurrent(page);
  };

  //过滤函数
  const ifPush = useCallback((e) => {
    if (sWay && e.WayLineNM !== sWay) {
      return false;
    }

    if (sDate && sDate[0] && sDate[1]) {
      const t1 = dayjs(sDate[0]);
      const t2 = dayjs(sDate[1]);
      const t3 = dayjs(e.CreatedTime);
      if (t1.isAfter(t3) || t2.isBefore(t3)) {
        return false;
      }
    }
    return true;
  }, [sWay, sDate]);

  useEffect(() => {
    const filteredList = mList.filter(ifPush);
    setSList(filteredList);
  }, [mList, ifPush, refrush]); // 依赖 mList 和 ifPush


  //CheckBox状态
  const checkBoxChange = (e, item) => {
    setCheckBoxList(prevList => {
      if (!e) {
        return [];
      }
      const checked = e.target.checked;
      if (checked && !prevList.find(i => i.ID === item.ID)) {
        return [...prevList, item];
      }
      return prevList.filter(i => i.ID !== item.ID);
    });
  };



  //Select选择航线
  const onWayLineSelected = useCallback((value) => {
    setSWay(value);
  }, []);

  //时间选择
  const onDateSelected = useCallback((dates) => {
    setSDate(dates);
  }, []);

  //航线数据转换
  const wayList = useMemo(() => {
    const uniqueWays = new Set(mList.map(e => e.WayLineNM));
    return Array.from(uniqueWays).map(way => (
      <Select.Option key={way} value={way}>{way}</Select.Option>
    ));
  }, [mList]);

  //选择组件
  const extraContent = useMemo(() => {
    return (
      <div>
        <span>
          <Select
            allowClear
            style={{ width: 200 }}
            placeholder="选择航线"
            onSelect={onWayLineSelected}
            onClear={() => setSWay('')}
          >
            {wayList}
          </Select>
        </span>
        <span style={{ marginLeft: 12.0 }}>
          <RangePicker onChange={onDateSelected} />
        </span>
        <span>
          <DownLoadAndDel
            selectImgList={mList}
            checkBoxList={checkBoxList}
            refrush={refrush}
            checkBoxChange={checkBoxChange}
          />
        </span>
      </div>
    );
  }, [wayList, onWayLineSelected, onDateSelected, mList, checkBoxList, refrush, checkBoxChange]);


  return (
    <div>
      <Card
        title={doNotShowLastButton ? '航拍影像' : <LastPageButton title="航拍视频" />} style={{ height: '100%' }} extra={extraContent}>
        <List
          grid={{ gutter: 35, column: 5 }}
          dataSource={sList}
          pagination={{
            defaultCurrent: page1,
            showSizeChanger: true,
            showQuickJumper: true,
            current: tableCurrent,
            onChange: (e) => {
              handleTableChange(e);
              localStorage.setItem('MediaListPage', e);
            },
            style: { marginTop: "-40px" }
          }}
          style={{
            maxHeight: 'calc(100vh - 156px)',
            overflow: 'none auto',
            msOverflowStyle: 'none',
          }}
          renderItem={item => (
            <List.Item key={item.ID}>
              <MediaItem item={item} checkBoxChange={checkBoxChange} setModal={setModal} setOpen={setOpen} doNotShowLastButton={doNotShowLastButton} />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default MediaListPanel;
