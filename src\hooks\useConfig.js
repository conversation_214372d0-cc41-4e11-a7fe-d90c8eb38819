// src/hooks/useConfig.js
import { useConfigStore } from '../stores/configStore'

export const useConfig = () => {
  const config = useConfigStore((state) => state.config)
  
  return {
    // MQTT 配置
    mqtt: {
      wsUrl: config.mqtt.wsUrl,
      tcpAddress: config.mqtt.tcpAddress
    },
    // RTMP 配置
    rtmp: {
      webrtcUrl: config.rtmp.webrtcUrl,
      httpUrl: config.rtmp.httpUrl,
      shareUrl: config.rtmp.shareUrl
    },
    // OSS 配置
    oss: {
      url: config.oss.url,
      bucket: config.oss.bucket
    },
    // 应用配置
    app: {
      title: config.app.title,
      logo: config.app.logo,
      backgroundImage: config.app.backgroundImage
    },
    // 获取完整配置
    getFullConfig: () => config
  }
}