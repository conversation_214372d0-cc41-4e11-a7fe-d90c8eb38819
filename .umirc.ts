import { defineConfig } from "umi";
const CompressionPlugin = require("compression-webpack-plugin");
const env = process.env.ENV_MODE;

function chainWebpackFn(config: any) {
  config.optimization.splitChunks({
    chunks: "all",
    minSize: 30000, // 最小拆包阈值（30KB）
    minChunks: 1,
    cacheGroups: {
      libs: {
        name: "chunk-libs",
        test: /[\\/]node_modules[\\/]/,
        priority: 10,
        chunks: "initial", // only package third parties that are initially dependent
      },
      echarts: {
        name: "echarts",
        test: /[\\/]node_modules[\\/](echarts)[\\/]/,
        priority: 20,
      },
      fileViewer: {
        name: "file-viewer",
        test: /[\\/]node_modules[\\/](react-file-viewer|pdfjs-dist|mammoth)[\\/]/,
        priority: 20,
      },
      AgoraRTC: {
        name: "AgoraRTC",
        test: /[\\/]node_modules[\\/](agora-rtc-sdk-ng)[\\/]/,
        priority: 20,
      },
      commons: {
        // split async commons chunk
        name: "chunk-async-commons",
        minChunks: 2,
        priority: 40,
        chunks: "async",
      },
    },
  });
  // 开启Gzip
  if (env === "production") {
    config.plugin("compression").use(CompressionPlugin, [
      {
        test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i, // 匹配文件
        threshold: 10240, // 超过 10KB 才压缩
        minRatio: 0.8,
        deleteOriginalAssets: false, // 是否删除源文件（不建议）
      },
    ]);
  }
}

const chainWebpack = env === "production" ? chainWebpackFn : undefined;

export default defineConfig({
  routes: [
    {
      path: "/",
      redirect: "SI/login",
      exact: true,
      routes: [
        { path: "/", component: "@/pages/index.js" },
        { path: "/index", component: "@/pages/index.js" },
        { path: "/app", component: "@/pages/App/apppage.js", layout: false },
        { path: "/login", component: "@/pages/DJI/LoginPage/index.jsx" },
        {
          path: "/map3d",
          component: "@/pages/Cesium/OrgMap.js",
          layout: false,
        },
        {
          path: "/event",
          component: "@/pages/DJI/DangerPage/detail2.js",
          layout: false,
        },
        {
          path: "/eventList",
          component: "@/pages/DJI/DangerPage/index2.js",
          layout: false,
        },
        {
          path: "/device",
          component: "@/pages/DJI/DRCPage/index.js",
          layout: false,
        },
      ],
    },
    {
      path: "/HOME",
      routes: [
        {
          path: "/HOME",
          redirect: "/HOME/login",
          layout: false,
        },
        {
          path: "index",
          component: "@/pages/HOME/index",
          layout: false,
        },
        {
          path:"login",
          // component: "@/pages/GT/GTPages/Login/index", 
          component: "@/pages/HOME/LoginPage/index", 
          layout: false,
        },
      ],
    },
    {
      path: "/GT",
      routes: [
        {
          path: "home",
          component: "@/pages/GT/GTPages/DroneManage",
          layout: false,
        },
        {
          path: "DZZH",
          component: "@/pages/GT/DZZH/index",
          layout: false,
        },
        {
          path: "GDBH",
          component: "@/pages/GT/GDBH/index",
          layout: false,
        },
        {
          path: "LSYD",
          component: "@/pages/GT/LSYD/index",
          layout: false,
        },
        {
          path: "YZT",
          component: "@/pages/GT/YZT/index",
          layout: false,
        },
        {
          path: "WRJ",
          component: "@/pages/GT/WRJ/index",
          layout: false,
        },
        {
          path: "ZZCH",
          component: "@/pages/GT/ZZCH/index",
          layout: false,
        },
        {
          path: "YWGL",
          component: "@/pages/GT/YWGL/index",
          layout: false,
        },
        {
          path: "DZJC",
          component: "@/pages/GT/DZJC/index",
          layout: false,
        },
      ],
    },
    {
      path: "/GF",
      routes: [
        {
          path: "GFDZ",
          component: "@/pages/GF/GFDZ/index",
          layout: false,
        },
        {
          path: "DZZH",
          component: "@/pages/GF/GFDZ/index",
          layout: false,
        },
        {
          path: "WRJ",
          component: "@/pages/GF/WRJ/index",
          layout: false,
        },
      ],
    },
    {
      path: "/DJ",
      routes: [
        {
          path: "login",
          component: "@/pages/DJI/LoginPage/index.jsx",
          layout: false,
        },
        { path: "index", component: "@/pages/index.js", layout: false },
        { path: "app", component: "@/pages/App/apppage.js", layout: false },
        {
          path: "map3d",
          component: "@/pages/Cesium/OrgMap.js",
          layout: false,
        },
        {
          path: "event",
          component: "@/pages/DJI/DangerPage/detail2.js",
          layout: false,
        },
        {
          path: "eventList",
          component: "@/pages/DJI/DangerPage/index2.js",
          layout: false,
        },
        {
          path: "device",
          component: "@/pages/DJI/DRCPage/index.js",
          layout: false,
        },
        {
          path: "ModelingTask",
          component: "@/pages/Others/ModelingTask/index.js",
          layout: false,
        },
      ],
    },
    {
      path: "/rtmpShare",
      component: "@/pages/Others/RtmpShare/index",
      layout: false,
    },
    {
        path: "/SI",
        component: '@/pages/SI/layouts/index.js',
        routes: [
          { path: "login", component: "@/pages/SI/login.js", layout: false },
          { path: "index", component: "@/pages/SI/DynamicMonitor/index.js", layout: false },
          { path: "controlCenter", component: "@/pages/SI/ControlCenter/index.js", layout: false },
          { path: "ai", component: "@/pages/SI/AIAlgorithm/index.js", layout: false },
          { path: "patrol", component: "@/pages/SI/FlyApp/index.js", layout: false },
          { path: "system", component: "@/pages/GT/YWGL/index.js", layout: false },
        ],
    },
    // {
    //   path: "/index2",
    //   routes:[
    //     {
    //       path:"/index2",
    //       component: "@/pages/GT/GTPages/Login/index.js",
    //       layout: false
    //     },
    //     { path: "/home", component: "@/pages/GT/GTPages/NavigationPage/index.js" },
    //   ]
    // },

    // { path: "/docs", component: "docs" },
  ],
  history: { type: "hash" },
  jsMinifier: "terser",
  // 浏览器IE兼容

  npmClient: "pnpm",
  proxy: {
    // DJI 禁飞区API代理
    "/dji-api": {
      'target': 'http://8.137.54.85:11601/',
      // target: "https://flysafe-api.dji.com",
      changeOrigin: true,
      // pathRewrite: {
      //   "^/dji-api": "/api/qep/geo/feedback/areas/in_rectangle"
      // },
      // headers: {
      //   "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      //   "Referer": "https://www.dji.com/",
      //   "Accept": "application/json, text/plain, */*",
      //   "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
      // },
      // secure: true,
      // timeout: 15000,
    },
    "/api": {
      // 'target': 'http://8.137.54.85:11501/', // 测试环境
      'target': 'http://8.137.54.85:12411/', // 联调开发
      changeOrigin: true,
    },
    "/ai": {
      target: "http://112.44.103.230:24221/",
      changeOrigin: true,
    },
    "/geoserver": {
      target: "http://8.137.54.85:11501/",
      changeOrigin: true,
    },
    "/media/": {
      target:
        "https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/",
      changeOrigin: true,
    },
  },

  plugins: [
    "@umijs/plugins/dist/antd",
    "@umijs/plugins/dist/model",
    "umi-cesium-plugin",
  ],
  cesium: {
    accessToken:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3ZWYyYWYyZi05YmQxLTQzODQtYTIyZi1mMTg2NTAxZGY4NGIiLCJpZCI6MTgzNTU5LCJpYXQiOjE3MDIyMTA3NDZ9.ngQ_4Jd-HsbK_MpofsFs9lUnpRcYCdOcObRVqoOS56U",
  },
  antd: {},
  model: {},
  hash: true,

  //mfsu:false,
  codeSplitting: {
    jsStrategy: "granularChunks", // 更细粒度拆包
  },
  chainWebpack,
});
