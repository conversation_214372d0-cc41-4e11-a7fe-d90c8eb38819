# 需求：

- 点击全景拍照按钮后，切换到全景拍照模式，然后开始执行全景拍照
- 执行全景拍照时，在页面上显示一层类似于灰色遮罩，在遮罩上显示拍摄进度条（可以是环状进度条等形式），进度条下方显示提示文本：‘正在全景拍照中，请等得拍照完成，再执行其它操作...’（从专业UI/UX角度设计）
- 执行完成后，退出全景拍照模式


# 相关代码文件：

src\pages\DJI\DRCPage\Panels\CameraPanel.js
src\pages\DJI\DevicePage\Panels\CameraPanel.js
src\pages\DJI\HmsPage\cmdName.json
src\models\cmdModel.jsx
src/models/eventModel.jsx
及其他涉及‘全景拍照’的文件

# 相关url及官方文档内容：

title: "上云API"
source: "https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/topic-definition.html#topic-%E6%80%BB%E8%A7%88"

## Topic 总览

为了方便设备云平台区分不同属性的处理策略，将设备上报物模型属性分为 osd 和 state 两类，分别用不同的 topic 上报。

- osd：设备定频上报的属性，对应 pushmode = 0。
- state：设备事件性上报的物模型属性，对应 pushmode=1。

> **说明：** 下表中的 *{gateway\_sn}* 表示网关设备的 SN，*{device\_sn}* 表示该物模型属性的所属设备的 SN 。

| Topic Name | 发送者 -> 订阅者 | Message | 说明 |
| --- | --- | --- | --- |
| thing/product/*{device\_sn}*/osd | 设备 > 云平台 | [osd message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#osd-message-struct) | 设备端定频向云平台推送的设备属性（properties），   具体内容范围参见物模型内容 |
| thing/product/*{device\_sn}*/state | 设备 > 云平台 | [state message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#state-message-struct) | 设备端按需上报向云平台推送的设备属性（properties），   具体内容范围参见物模型内容 |
| thing/product/*{gateway\_sn}*/services | 云平台 -> 设备 | [services message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#services-message-struct) | 云平台向设备发送的服务（具体service identifier 见物模型内容）。 |
| thing/product/*{gateway\_sn}*/services\_reply | 设备 > 云平台 | [services\_reply message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#services_reply-message-struct) | 设备对 service 的回复、处理结果 |
| thing/product/*{gateway\_sn}*/events | 设备 > 云平台 | [events message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#events-message-struct) | 设备端向云平台发送的，需要关注和处理的事件。   比如SD满了，飞机解禁禁飞区等信息（事件范围参见物模型内容） |
| thing/product/*{gateway\_sn}*/events\_reply | 云平台 -> 设备 | [events\_reply message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#events_reply-message-struct) | 云平台对设备事件的回复、处理结果 |
| thing/product/*{gateway\_sn}*/requests | 设备 > 云平台 | [requests message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#requests-message-struct) | 设备端向云平台发送请求，为了获取一些信息，比如上传的临时凭证 |
| thing/product/*{gateway\_sn}*/requests\_reply | 云平台 -> 设备 | [requests\_reply message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#requests_reply-message-struct) | 云平台对设备请求的回复 |
| sys/product/*{gateway\_sn}*/status | 设备 > 云平台 | [status message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#status-message-struct) | 设备上下线、更新拓扑 |
| sys/product/*{gateway\_sn}*/status\_reply | 云平台 -> 设备 | [status\_reply message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#status_reply-message-struct) | 平台响应 |
| thing/product/*{gateway\_sn}*/property/set | 云平台 -> 设备 | [property set message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#property-set-message-struct) | 设备属性设置。设备属性是否可以被修改，在设备属性章节通过“accessMode”标识符号判断，accessMode = rw 表示可被读写。 |
| thing/product/*{gateway\_sn}*/property/set\_reply | 设备 > 云平台 | [property set\_reply message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#property-set-reply-message-struct) | 设备属性设置的响应 |
| thing/product/*{gateway\_sn}*/drc/up | 设备 > 云平台 | [DRC message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#drc-up-message-struct) | DRC 协议上行 |
| thing/product/*{gateway\_sn}*/drc/down | 云平台 > 设备 | [DRC message struct](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#drc-down-message-struct) | DRC 协议下行 |

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#%E5%85%AC%E5%85%B1%E5%AD%97%E6%AE%B5%E8%A7%A3%E9%87%8A) 公共字段解释

| Column | Name | Type | Description |
| --- | --- | --- | --- |
| tid | 事务uuid | text | 事务（Transaction）的 UUID：表征一次简单的消息通信，如：增/删/改/查，云台控制等，可以是：   1\. 数据上报请求+数据上报响应   2\. 握手认证请求+响应+ack   3.报警事件单向通知等，解决事务多并发和消息匹配的问题 |
| bid | 业务uuid | text | 业务（Business）的 UUID：有些功能不是一次通信就能完成的，包含持续一段时间内的所有交互。   业务通常由多个原子事务组成，且持续时间较长;   例如点播/下载/回放；解决业务多并发和重复请求的问题，便于所有模块的状态机管理。 |
| timestamp | 毫秒时间戳 | int | 消息的发送时间 |
| gateway | 网关设备的序列号 | text | 发送该消息的网关设备的序列号 |
| data | 消息内容 | object | 消息内容 |

## services 结构示例

*topic*: thing/product/*{gateway\_sn}*/services

```json
{
    "tid":"6a7bfe89-c386-4043-b600-b518e10096cc",
    "bid":"42a19f36-5117-4520-bd13-fd61d818d52e",
    "timestamp": 1598411295123,
    "gateway":"sn",
    "method": "some_method",
    "data": {}
}
```

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#services-reply-%E7%BB%93%E6%9E%84%E7%A4%BA%E4%BE%8B) services\_reply 结构示例

### [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#data%E4%B8%AD%E5%BF%85%E5%A1%AB%E5%AD%97%E6%AE%B5%E8%A7%A3%E9%87%8A) data中必填字段解释

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 设备响应的结果码 | int |  | 非 0 代表错误 |
| output | 设备消息内容 | struct |  | 设备响应服务端命令的消息内容 |

```json
{
    "tid":"6a7bfe89-c386-4043-b600-b518e10096cc",
    "bid":"42a19f36-5117-4520-bd13-fd61d818d52e",
    "timestamp": 1598411295123,
    "gateway":"sn",
    "method": "some_method",
    "data": {
        "result": 0, 
    	"output": {}
    }  
}
```

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#events-%E7%BB%93%E6%9E%84%E7%A4%BA%E4%BE%8B) events 结构示例

*topic*: thing/product/*{gateway\_sn}*/events

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| need\_reply | 服务端是否需要答复 | int |  | 服务端收到设备的events事件上报消息后，跟进need\_reply来判断是否给予收到答复;0代表不需要，1代表需要 |

```json
{
    "tid":"6a7bfe89-c386-4043-b600-b518e10096cc",
    "bid":"42a19f36-5117-4520-bd13-fd61d818d52e",
    "timestamp": 1598411295123,
    "need_reply": 0,
    "gateway":"sn",
    "method": "some_method",
    "data": {}
}
```

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#events-reply-%E7%BB%93%E6%9E%84%E7%A4%BA%E4%BE%8B) events\_reply 结构示例

*topic*: thing/product/*{gateway\_sn}*/events\_reply

### [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/#data%E4%B8%AD%E5%BF%85%E5%A1%AB%E5%AD%97%E6%AE%B5%E8%A7%A3%E9%87%8A-1) data中必填字段解释

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 服务端响应的结果码 | int |  | 非 0 代表错误 |

```json
{
    "tid":"6a7bfe89-c386-4043-b600-b518e10096cc",
    "bid":"42a19f36-5117-4520-bd13-fd61d818d52e",
    "timestamp": 1598411295123,
    "gateway":"sn",
    "method": "some_method",
    "data": {
        "result": 0
    }
}
```

title: "Event | 上云API"

source: "https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/rc-pro/drc.html"

## [上报拍照进度](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/rc-pro/drc.html#%E4%B8%8A%E6%8A%A5%E6%8B%8D%E7%85%A7%E8%BF%9B%E5%BA%A6)

当拍照行为需持续进行时会通过该事件上报进度。目前仅支持：全景拍照模式

**Topic:** thing/product/*{gateway\_sn}*/events

**Direction:** up

**Method:** camera\_photo\_take\_progress

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| output | 输出 | struct |  |  |
| »status | 拍照状态 | enum\_string | {"fail":"失败","in\_progress":"执行中","ok":"完成"} |  |
| »progress | 进度 | struct |  |  |
| »»current\_step | 执行步骤 | enum\_int | {"3000":"全景图拍摄未开始或者已结束","3002":"全景图正在拍摄","3005":"全景图合成中"} |  |
| »»percent | 进度值 | int | {"max":"100","min":"0","step":"1"} |  |
| »ext | 扩展内容 | struct |  |  |
| »»camera\_mode | 当前相机模式 | enum\_int | {"3":"全景拍照"} |  |
| result | 返回码 | int |  | 非 0 代表错误 |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"output": {
			"ext": {
				"camera_mode": 3
			},
			"progress": {
				"current_step": 0,
				"percent": 100
			},
			"status": "ok"
		},
		"result": 0
	},
	"need_reply": 1,
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_take_progress"
}
```

## 负载控制—切换相机模式

**Topic:** thing/product/*{gateway\_sn}*/services

**Direction:** down

**Method:** camera\_mode\_switch

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text |  | 相机枚举值。非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |
| camera\_mode | 相机模式 | enum\_int | {"0":"拍照","1":"录像","2":"智能低光","3":"全景拍照"} |  |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"camera_mode": 0,
		"payload_index": "39-0-7"
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_mode_switch"
}
```

**Topic:** thing/product/*{gateway\_sn}*/services\_reply

**Direction:** up

**Method:** camera\_mode\_switch

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int |  | 非 0 代表错误 |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"result": 0
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_mode_switch"
}
```

## [负载控制—停止拍照](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/rc-pro/drc.html#%E8%B4%9F%E8%BD%BD%E6%8E%A7%E5%88%B6%E2%80%94%E5%81%9C%E6%AD%A2%E6%8B%8D%E7%85%A7)

停止拍照指令，目前仅支持全景拍照模式

**Topic:** thing/product/*{gateway\_sn}*/services

**Direction:** down

**Method:** camera\_photo\_stop

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text |  | 相机枚举值。非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"payload_index": "39-0-7"
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_stop"
}
```

**Topic:** thing/product/*{gateway\_sn}*/services\_reply

**Direction:** up

**Method:** camera\_photo\_stop

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int |  | 非 0 代表错误 |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"result": 0
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_stop"
}
```

## [负载控制—开始拍照](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/rc-pro/#%E8%B4%9F%E8%BD%BD%E6%8E%A7%E5%88%B6%E2%80%94%E5%BC%80%E5%A7%8B%E6%8B%8D%E7%85%A7)

**Topic:** thing/product/*{gateway\_sn}*/services

**Direction:** down

**Method:** camera\_photo\_take

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text |  | 非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"payload_index": "39-0-7"
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_take"
}
```

**Topic:** thing/product/*{gateway\_sn}*/services\_reply

**Direction:** up

**Method:** camera\_photo\_take

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int |  | 非 0 代表错误 |
| status | 任务状态 | enum\_string | {"in\_progress":"执行中"} | 当全景拍照或其他持续性拍照行为时会上报状态信息，表达后续会有持续的进度事件上报，详细内容请查看 camera\_photo\_take\_progress 事件 |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"output": {
			"status": "in_progress"
		},
		"result": 0
	},
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_take"
}
```

---

## dji-rc-plus-2

title: "Event | 上云API"

source: "https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/dji-rc-plus-2/drc.html#%E4%B8%8A%E6%8A%A5%E6%8B%8D%E7%85%A7%E8%BF%9B%E5%BA%A6"

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/dji-rc-plus-2/#%E4%B8%8A%E6%8A%A5%E6%8B%8D%E7%85%A7%E8%BF%9B%E5%BA%A6) 上报拍照进度

当拍照行为需持续进行时会通过该事件上报进度。目前仅支持：全景拍照模式

**Topic:** thing/product/*{gateway\_sn}*/events

**Direction:** up

**Method:** camera\_photo\_take\_progress

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| output | 输出 | struct |  |  |
| »status | 拍照状态 | enum\_string | {"fail":"失败","in\_progress":"执行中","ok":"完成"} |  |
| »progress | 进度 | struct |  |  |
| »»current\_step | 执行步骤 | enum\_int | {"3000":"全景图拍摄未开始或者已结束","3002":"全景图正在拍摄","3005":"全景图合成中"} |  |
| »»percent | 进度值 | int | {"max":"100","min":"0","step":"1"} |  |
| »ext | 扩展内容 | struct |  |  |
| »»camera\_mode | 当前相机模式 | enum\_int | {"3":"全景拍照"} |  |
| result | 返回码 | int | {"max":"","min":"","step":"","unit\_name":null} | 非 0 代表错误 |

**Example:**

```json
{
	"bid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"data": {
		"output": {
			"ext": {
				"camera_mode": 3
			},
			"progress": {
				"current_step": 0,
				"percent": 100
			},
			"status": "ok"
		},
		"result": 0
	},
	"need_reply": 1,
	"tid": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxx",
	"timestamp": 1654070968655,
	"method": "camera_photo_take_progress"
}
```

## 负载控制—切换相机模式

seq 是递增的序号，保证指令顺序执行，与 data 同级

**Topic:** thing/product/*{gateway\_sn}*/drc/down

**Direction:** down

**Method:** drc\_camera\_mode\_switch

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text | {"length":""} | 相机枚举值。非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |
| camera\_mode | 模式 | enum\_int | {"0":"拍照","1":"录像","2":"智能低光","3":"全景拍照","4":"定时拍"} |  |

**Example:**

```json
{
	"data": {
		"camera_mode": 2,
		"payload_index": "89-0-0"
	},
	"method": "drc_camera_mode_switch",
	"seq": 1
}
```

**Topic:** thing/product/*{gateway\_sn}*/drc/up

**Direction:** up

**Method:** drc\_camera\_mode\_switch

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int | {"max":"","min":"","step":"","unit\_name":null} | 非 0 代表错误 |

**Example:**

```json
{
	"data": {
		"result": 0
	},
	"method": "drc_camera_mode_switch",
	"seq": 1
}
```

## 负载控制—停止拍照

停止拍照指令，目前仅支持全景拍照模式

**Topic:** thing/product/*{gateway\_sn}*/drc/down

**Direction:** down

**Method:** drc\_camera\_photo\_stop

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text | {"length":""} | 相机枚举值。非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |

**Example:**

```json
{
	"data": {
		"payload_index": "89-0-0"
	},
	"method": "drc_camera_photo_stop",
	"seq": 1
}
```

**Topic:** thing/product/*{gateway\_sn}*/drc/up

**Direction:** up

**Method:** drc\_camera\_photo\_stop

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int | {"max":"","min":"","step":"","unit\_name":null} | 非 0 代表错误 |

**Example:**

```json
{
	"data": {
		"result": 0
	},
	"method": "drc_camera_photo_stop",
	"seq": 1
}
```

## [#](https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/pilot-to-cloud/mqtt/dji-rc-plus-2/#%E8%B4%9F%E8%BD%BD%E6%8E%A7%E5%88%B6%E2%80%94%E5%BC%80%E5%A7%8B%E6%8B%8D%E7%85%A7) 负载控制—开始拍照

**Topic:** thing/product/*{gateway\_sn}*/drc/down

**Direction:** down

**Method:** drc\_camera\_photo\_take

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| payload\_index | 相机枚举 | text | {"length":""} | 非标准的 device\_mode\_key，格式为 {type-subtype-gimbalindex}，可以参考[产品支持open in new window](https://developer.dji.com/doc/cloud-api-tutorial/cn/overview/product-support.html) |

**Example:**

```json
{
	"data": {
		"payload_index": "89-0-0"
	},
	"method": "drc_camera_photo_take",
	"seq": 1
}
```

**Topic:** thing/product/*{gateway\_sn}*/drc/up

**Direction:** up

**Method:** drc\_camera\_photo\_take

**Data:**

| Column | Name | Type | constraint | Description |
| --- | --- | --- | --- | --- |
| result | 返回码 | int | {"max":"","min":"","step":"","unit\_name":null} | 非 0 代表错误 |
| status | 任务状态 | enum\_string | {"in\_progress":"执行中"} | 当全景拍照或其他持续性拍照行为时会上报状态信息，表达后续会有持续的进度事件上报，详细内容请查看 camera\_photo\_take\_progress 事件 |

**Example:**

```json
{
	"data": {
		"result": 0
	},
	"method": "drc_camera_photo_take",
	"seq": 1
}
```
