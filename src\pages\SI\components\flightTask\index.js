import { <PERSON><PERSON>, Card, Col, Form, Row, Select, Space, Table } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import ContentTitle from "./contentTitle/ContentTile"
import { axiosApi } from "@/services/general";
import TaskDetails from "./details/index"
import { useLocation, useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import { DynamicDataTable } from '@/pages/SI/components/Common';
import CommonCard from '@/components/CommonCard';


const taskStatus = [
    {value:9,label:"全部"},
    {value:0,label:"未执行"},
    {value:1,label:"执行中"},
    {value:2,label:"执行成功"},
    {value:-1,label:"执行失败"},
]



const index = ({doNotShowLastButton}) => {
    const routeP = useLocation()


    const [planOptions,setPlanOptions] = useState([])
    const [airLineOptions,setAirLineOptions] = useState([])
    const [tableData,setTableData] = useState([])
    const [pageData,setPageData] = useState({
      pageSize:10,
      page:1
    })
    const [total,setTotal] = useState(0)
    const [form] = Form.useForm()
    const [searchData,setSearchData] = useState({
      status:9,
      surveyTaskID:null,
      wayLineID:null
    })


    const { setPage, page } = useModel('pageModel');
      

    const columns = [
    {
      title: "巡检计划描述",
      dataIndex: "surveyTaskDesc",
    },
    {
      title: "应用场景",
      dataIndex: "surveyTaskTypeName",
    },
    {
      title: "巡检计划内容",
      dataIndex: "surveyTaskContent",
    },
    {
      title: "飞行任务名称",
      dataIndex: "flightTaskName",
    },
    {
      title: "航线名称",
      dataIndex: "waylineName",
    },
    {
      title: "状态",
      dataIndex: "status",
      render:(value)=>{
        if(value==0){
            return <span>未执行</span>
        }else if(value==1){
          return <span style={{color:"#1C50A8"}}>执行中</span>
        }else if(value==2){
          return <span style={{color:"#16a085"}}>执行成功</span>
        }else if(value==-1){
          return <span style={{color:"red"}}>执行失败</span>
        }
        return ""
      }
    },
    {
      title: "计划执行时间",
      dataIndex: "execTime",
      width: 200,
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      width: 200,
    },
    {
      title: "执行反馈",
      dataIndex: "execFeedback",
    },
    {
      title: "备注",
      dataIndex: "remark",
    },
    {
      title:"操作",
      dataIndex:"flightTaskID",
      render:(_,row)=>{
        return <Button type="link" style={{cursor:row.status==2?"pointer":"not-allowed"}}>
          <span style={{pointerEvents:row.status==2?"":"none"}} onClick={()=>{
          // 设置当前页面
          setPage(<TaskDetails baseInfo={row}></TaskDetails>);
        }}>详情</span>
        </Button>
      }
    }
]

    useEffect(()=>{
        getPlanOptions()
        getAirlineOptions()
    },[])

    useEffect(()=>{
      onSearch()
    },[pageData,searchData])

    // 获取所属计划下拉
    const getPlanOptions =async ()=>{
        try {
            const res = await axiosApi('/api/v1/surveytask/GetAllList', 'GET', null);
            if(res.data){
                setPlanOptions(res.data)
            }
        } catch (error) {   
            
        }
    }

    // 获取航线名称下拉
    const getAirlineOptions =async ()=>{
        try {
            const res = await axiosApi('/api/v1/WayLine/GetAllList', 'GET', null);
            setAirLineOptions(res)
            
        } catch (error) {
            
        }
    }

    // 重置
    const resetValues = ()=>{
        form.setFieldsValue({
            status:null,
            surveyTaskID:null,
            wayLineID:null
        })
    }

    // 查询表格数据
    const onSearch = async ()=>{
      const arr = routeP.pathname.split('/')
      // console.log('routeP',routeP,arr)

        try {
            const res = await axiosApi('/api/v1/FlightTask/GetFlightTaskList', 'GET', {
                ...searchData,
                ...pageData,
                tableName:arr[arr.length-1]==="controlCenter"?"":arr[arr.length-1]
            });
            if(res.data?.list){
              setTableData(res.data.list)
              setTotal(res.data.pagination.total)
            }
        } catch (error) {
            console.log('err',error)
        }
    }


    const onPageChange = (page,pageSize)=>{
      setPageData({page,pageSize})
    }

    const onFinish = (values)=>{
      setSearchData({...values})
    }



  return (
    <CommonCard title={doNotShowLastButton ? "巡飞任务" : <LastPageButton title="巡飞任务" />}>
        {/* <ContentTitle>巡飞任务</ContentTitle> */}
        <Form onFinish={onFinish} form={form} labelCol={{style:{width:80}}} style={{margin:"20px"}}>
            <Row gutter={50}>
                <Col span={6}>
                    <Form.Item label="所属计划" name="surveyTaskID">
                        <Select options={planOptions} fieldNames={{
                            value:"TaskID",label:"TaskDesc"
                        }} allowClear></Select>
                    </Form.Item>
                </Col>
                 <Col span={6}>
                    <Form.Item label="任务状态" name="status">
                        <Select options={taskStatus} allowClear></Select>
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="航线名称" name="wayLineID">
                        <Select options={airLineOptions} fieldNames={{
                            value:"WanLineId",label:"WayLineName"
                        }} allowClear></Select>
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item>
                      <Space>
                          <Button type='primary' htmlType='submit'>查询</Button>
                          <Button onClick={resetValues}>重置</Button>
                      </Space>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
        <DynamicDataTable    
          autoHeight={true}
          pagination={{
          defaultPageSize: pageData.pageSize,
          defaultCurrent: pageData.page,
          showQuickJumper: true,
          showSizeChanger: true,
          total:total,
          onChange:onPageChange,
        }}
        dataSource={tableData} 
        columns={columns} 
        rowKey="flightTaskID"
        />
    </CommonCard>
  )
}

export default index