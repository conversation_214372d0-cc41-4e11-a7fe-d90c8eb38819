
import { isEmpty } from "@/utils/utils";
import { useState, useRef } from "react";
import { GetMqttClient } from "@/utils/websocket";
import { useModel } from "umi";

export default function droneModel() {

    const [fj, setFj] = useState({});
    const fjData=useRef({})
    const mqttC=useRef({})
    const fjSN=useRef('')
    const {setGps2} =useModel('gpsModel');

    const DroneMqttConn = async (sn) => {
        if (!isEmpty(mqttC.current)) {
          mqttC.current.end();
        }
        
       mqttC.current = await GetMqttClient()
       mqttC.current.on("message", (topic, message) => {
           updateVal(topic,message);
        });
        if (!isEmpty(mqttC.current)) {
          mqttC.current.unsubscribe('#');
          mqttC.current.subscribe("thing/product/"+sn+"/osd");
        }
       
       
    }

    const updateVal=(t1,m1)=>{
        const xx=JSON.parse( m1);
        const data=xx.data;
        console.log('fj',t1,xx);
        setFj(xx)
        fjData.current=xx.data;
        if(data.latitude>0){
           setGps2([data.latitude,data.longitude,data.height])
        }
    }

    const NewDroneConn = (device) => {
        setFj({})
        fjSN.current=device.SN2;
        fjData.current={};
        setGps2([device.Lat,device.Lng,device.Height])
        DroneMqttConn(device.SN2);
    }


    
    return {fj,fjSN, fjData,setFj, DroneMqttConn ,NewDroneConn};
};