
import { isEmpty } from "@/utils/utils";
import { useModel } from "umi";
import {getGrayStyle} from './helper';
import { color } from "echarts";
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";

import styles from './CameraPanel2.less'

const CameraPanel2=()=>{
    const w1=150
    const h1=50
    const {fj}=useModel('droneModel');
    const {DoCMD,DoCMD2}=useModel('cmdModel');
    const  device = JSON.parse(localStorage.getItem('device'));
    const {cameraJT,setCameraJT}=useModel('rtmpModel');

    const CameraJT = (z) => {
        const data = {
            "video_id": `${device.SN2}/${device.Camera2}/normal-0`,
            "video_type": z
        }
        DoCMD(device.SN, "live_lens_change", data)
        setCameraJT(z);
        // if(z=='ir'){
        //     setTimeout(CameraIRType, 1000);
        // }
       // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
    }

    const CameraIRType=()=> {
   
        let data={}
        data[device.Camera2]={"thermal_current_palette_style": 6}
        let s = {}
        s.bid = getGuid();
        s.tid = getGuid();
       // s.gateway = sn
        s.data = data
        s.timestamp = dayjs().valueOf();
        DoCMD2(`thing/product/${device.SN}/property/set`, s)  
    }


    const changeCamera=(v)=>{
       const data= {
            "camera_mode": v,
            "payload_index": device.Camera2,
       }
       DoCMD(device.SN,'camera_mode_switch',data);
    }

    if(isEmpty(fj)) return;
    //const fSty={userSelect:'none', opacity:0.6,cursor:'pointer',  fontFamily:'MiSan', textAlign:'center',fontSize:14.0,color:'white',fontWeight:'bold',paddingTop:8.0}
     return <div className={styles.camerasBar} >
        <div className={styles.camerasBarItem}  onClick={()=>CameraJT('wide')}>广角</div>
        <div className={styles.camerasBarItem}  onClick={()=>CameraJT('zoom')}>变焦</div>
        <div className={styles.camerasBarItem}  onClick={()=>CameraJT('ir')}>红外</div>

     </div> 
    
}

export default CameraPanel2;

<div ></div> 