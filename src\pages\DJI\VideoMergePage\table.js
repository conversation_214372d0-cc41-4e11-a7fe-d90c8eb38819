import { Space, Tag, message,Modal, Badge} from 'antd';
import { downloadFile, getDeviceName, isEmpty } from '@/utils/utils';
import './table.css';
import { timeFormat, timeFormat2 } from '@/utils/helper';
import { getImgUrl } from '@/utils/utils';
import { HGet2 } from '@/utils/request';
import { Post2 } from '@/services/general';
import WayLineEditPage from './form_edit';
import DevicePage from '../DevicePage';
import dayjs from 'dayjs';



const getTableTitle=(title)=>{return  <div style={{fontWeight: 'bold', textAlign: 'center' }}>  {title}</div>}

const { confirm } = Modal;

const toFly=async(record,refrush)=>{
 // console.log('toFly')
 // const {newPList}=useModel('gpsModel')
  if(isEmpty(record)) return ;
  
  const rr=await Post2('/deal2/VideoMerge',record);
  message.info(rr);
  refrush();

}


const deleteWayline=async(record,refrush)=>{

  const xx=await Post2("/api/v1/VideoMerge/Delete",record);

  //console.log('deleteWayline',xx)
  if (!isEmpty(xx.err)){
    message.info("错误："+xx.err)
  }else{
    message.info("删除成功！")
    refrush();
  }
}

const showDeleteConfirm = (record,refrush) => {
  confirm({
    title: '删除任务',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该任务吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteWayline(record,refrush);
    },
    onCancel() {
     // console.log('Cancel');
    },
  });
};

const  download=(url, title = "", artist = "")=> {
  const eleLink = document.createElement("a"); // 新建A标签
  eleLink.href =  url; // 下载的路径
  eleLink.download = `${title} - ${artist}`; // 设置下载的属性，可以为空
  eleLink.style.display = "none";
  document.body.appendChild(eleLink);
  eleLink.click(); // 触发点击事件
  document.body.removeChild(eleLink);
}

const getZT=(s1)=>{
  if(s1==0) return <Badge status="warning" text="任务未开始" />;
  if(s1==1) return <Badge status="processing" text="下载原视频" />;
  if(s1==2) return <Badge status="processing" text="视频拼接中" />;
  if(s1==3) return  <Badge status="processing" text="视频上传中" />;
  if(s1==4) return <Badge status="success" text="任务已完成" />;
  return "";
}

const TableCols =(refrush,showMap,setPage)=>{return [
  
          {
            title: getTableTitle('任务名称'),
            dataIndex: 'VName',
            key: 'VName',
            align:'center',
           // width:200,
          },
         
          {
            title: getTableTitle('任务进度'),
            dataIndex: 'State',
            key: 'State',
            align:'center',
            render: (record) => (
               getZT( record)
             )
          //  width:200,
          },
          {
             title: getTableTitle('创建用户'),
            dataIndex: 'CreateUser',
            key: 'CreateUser',
            align:'center',
            render: (e) => (
            
              e.length<2? '管理员':e
            )
         //   width:200,
          },

          {
            title: getTableTitle('创建时间'),
           dataIndex: 'CreateTime',
           key: 'CreateTime',
           align:'center',
           render: (e) => (
            dayjs(e).format('YYYY-MM-DD HH:mm')
           )
        //   width:200,
         },

        {
             title: getTableTitle('操作'),
            align:'center',
            width:360,
            render: (record) => (
              <Space size="middle">
                {record.State==0? <Tag><a onClick={()=>toFly(record,refrush)}>开始任务</a></Tag>:null}
                {record.State==4?<Tag><a onClick={()=>showMap(record)}>播放</a></Tag>:null}
                {/* {record.State==4?<Tag><a onClick={()=>download(getImgUrl(record.OutFileObject))}>下载</a></Tag>:null} */}
                {/* <Tag><a>编辑</a></Tag> */}
                <Tag><WayLineEditPage refrush={refrush} w1={record}></WayLineEditPage></Tag>
                <Tag><a onClick={()=>showDeleteConfirm(record,refrush)}>删除任务</a></Tag>
              </Space>)
          }];
        }



export default TableCols;
