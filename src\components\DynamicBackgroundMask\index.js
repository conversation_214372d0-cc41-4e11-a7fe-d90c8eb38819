import React from 'react';
import styles from './index.module.less';

/**
 * 动态监控背景遮罩组件
 * 用于为页面添加 dynamicBackgroundMask.png 背景图片遮罩效果
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.className - 额外的样式类名
 * @param {Object} props.style - 自定义内联样式
 * @param {React.ReactNode} props.children - 子组件（如果需要在遮罩层上显示内容）
 * @returns {React.ReactElement} 背景遮罩组件
 */
const DynamicBackgroundMask = ({ className = '', style = {}, children, ...restProps }) => {
  return (
    <div 
      className={`${styles.backgroundMask} ${className}`}
      style={style}
      {...restProps}
    >
      {children}
    </div>
  );
};

export default DynamicBackgroundMask; 