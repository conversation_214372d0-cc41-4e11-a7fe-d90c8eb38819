/**
 * DJI 禁飞区 API 服务
 * 提供禁飞区数据获取、缓存管理等功能
 */

import { formatBounds, expandBounds } from '@/utils/mapBoundsHelper';

// DJI API 基础配置
const DJI_API_BASE_URL = 'https://flysafe-api.dji.com/api/qep/geo/feedback/areas/in_rectangle';
const PROXY_API_BASE_URL = '/dji-api/api/qep/geo/feedback/areas/in_rectangle'; // 代理API路径
const DEFAULT_PARAMS = {
  zones_mode: 'flysafe_website',
  drone: 'dji-mavic-3',
  level: '0,1,2,3,7,8,10'
};

// 配置选项
const API_CONFIG = {
  useProxy: true,        // 是否使用代理
  timeout: 10000,        // 请求超时时间
  retryCount: 3,         // 重试次数
  retryDelay: 1000       // 重试延迟
};

// 缓存配置
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟
const MAX_CACHE_SIZE = 50; // 最大缓存条目数

// 内存缓存
const cache = new Map();

/**
 * 获取禁飞区数据
 * @param {Object} bounds - 地图边界 { ltlat, ltlng, rblat, rblng }
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 禁飞区数据
 */
export async function fetchNFZData(bounds, options = {}) {
  if (!bounds) {
    throw new Error('NFZService: bounds parameter is required');
  }

  // 扩展边界范围以提供缓存覆盖
  const expandedBounds = expandBounds(bounds, 0.2);
  const cacheKey = generateCacheKey(expandedBounds, options);

  // 检查缓存
  const cachedData = getFromCache(cacheKey);
  if (cachedData) {
    console.log('NFZService: Using cached data');
    return filterDataByBounds(cachedData, bounds);
  }

  try {
    const data = await fetchFromAPI(expandedBounds, options);
    
    // 缓存数据
    setToCache(cacheKey, data);
    
    // 返回过滤后的数据
    return filterDataByBounds(data, bounds);
  } catch (error) {
    console.error('NFZService: Error fetching NFZ data:', error);
    throw error;
  }
}

/**
 * 从 DJI API 获取数据
 * @param {Object} bounds - 地图边界
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} API 响应数据
 */
async function fetchFromAPI(bounds, options = {}) {
  const params = {
    ...DEFAULT_PARAMS,
    ...options,
    ltlat: bounds.ltlat,
    ltlng: bounds.ltlng,
    rblat: bounds.rblat,
    rblng: bounds.rblng
  };

  // 尝试多种方法获取数据
  const methods = [
    () => fetchWithProxy(params),           // 方法1: 使用代理
    // () => fetchWithCORS(params),           // 方法2: 直接请求（带CORS处理）
    // () => fetchWithJSONP(params)           // 方法3: JSONP（如果支持）
  ];

  let lastError = null;

  for (const method of methods) {
    try {
      const data = await method();
      if (data && data.code === 0) {
        return data;
      }
    } catch (error) {
      console.warn('NFZService: Method failed, trying next:', error.message, error.data);
      lastError = error;
    }
  }

  throw lastError || new Error('NFZService: All request methods failed');
}

/**
 * 通过代理服务器获取数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API 响应数据
 */
async function fetchWithProxy(params) {
  const url = new URL(PROXY_API_BASE_URL, window.location.origin);
  Object.keys(params).forEach(key => {
    url.searchParams.append(key, params[key]);
  });

  const response = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Proxy request failed with status ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();

  // 检查API响应格式
  if (data.code !== 0) {
    // 构建错误信息，优先使用data字段中的详细错误信息
    let errorMessage = data.message?.english || data.message?.chinese || 'Unknown error';
    if (data.data && typeof data.data === 'string') {
      errorMessage = data.data;
    }
    throw new Error(`API returned error code ${data.code}: ${errorMessage}`);
  }

  return data;
}

/**
 * 直接请求DJI API（处理CORS）
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API 响应数据
 */
async function fetchWithCORS(params) {
  const url = new URL(DJI_API_BASE_URL);
  Object.keys(params).forEach(key => {
    url.searchParams.append(key, params[key]);
  });

  const response = await fetch(url.toString(), {
    method: 'GET',
    mode: 'cors',  // 明确指定CORS模式
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json',
      'Origin': window.location.origin,
      'Referer': window.location.href
    },
    credentials: 'omit'  // 不发送凭据
  });

  if (!response.ok) {
    throw new Error(`CORS request failed with status ${response.status}`);
  }

  const data = await response.json();

  if (data.code !== 0) {
    // 构建错误信息，优先使用data字段中的详细错误信息
    let errorMessage = data.message?.english || data.message?.chinese || 'Unknown error';
    if (data.data && typeof data.data === 'string') {
      errorMessage = data.data;
    }
    throw new Error(`API returned error code ${data.code}: ${errorMessage}`);
  }

  return data;
}

/**
 * 使用JSONP方式获取数据（备用方案）
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} API 响应数据
 */
async function fetchWithJSONP(params) {
  return new Promise((resolve, reject) => {
    const callbackName = `nfzCallback_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const script = document.createElement('script');
    const url = new URL(DJI_API_BASE_URL);

    // 添加参数
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });
    url.searchParams.append('callback', callbackName);

    // 设置全局回调函数
    window[callbackName] = (data) => {
      cleanup();
      resolve(data);
    };

    // 清理函数
    const cleanup = () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
      delete window[callbackName];
    };

    // 错误处理
    script.onerror = () => {
      cleanup();
      reject(new Error('JSONP request failed'));
    };

    // 超时处理
    setTimeout(() => {
      cleanup();
      reject(new Error('JSONP request timeout'));
    }, API_CONFIG.timeout);

    script.src = url.toString();
    document.head.appendChild(script);
  });
}

/**
 * 根据边界过滤数据
 * @param {Object} data - 原始数据
 * @param {Object} bounds - 目标边界
 * @returns {Object} 过滤后的数据
 */
function filterDataByBounds(data, bounds) {
  if (!data.data || !data.data.areas) {
    return data;
  }

  // 简单的边界过滤（可以根据需要优化）
  const filteredAreas = data.data.areas.filter(area => {
    // 检查圆形区域
    if (area.shape === 2 && area.lat && area.lng) {
      return area.lat >= bounds.rblat && 
             area.lat <= bounds.ltlat && 
             area.lng >= bounds.ltlng && 
             area.lng <= bounds.rblng;
    }
    
    // 检查多边形区域
    if (area.sub_areas && area.sub_areas.length > 0) {
      return area.sub_areas.some(subArea => {
        if (subArea.polygon_points && subArea.polygon_points.length > 0) {
          return subArea.polygon_points[0].some(point => {
            return point[1] >= bounds.rblat && 
                   point[1] <= bounds.ltlat && 
                   point[0] >= bounds.ltlng && 
                   point[0] <= bounds.rblng;
          });
        }
        return false;
      });
    }
    
    return true; // 保留无法判断的区域
  });

  return {
    ...data,
    data: {
      ...data.data,
      areas: filteredAreas
    }
  };
}

/**
 * 生成缓存键
 * @param {Object} bounds - 地图边界
 * @param {Object} options - 可选参数
 * @returns {string} 缓存键
 */
function generateCacheKey(bounds, options) {
  const boundsStr = formatBounds(bounds, 4); // 降低精度以提高缓存命中率
  const optionsStr = JSON.stringify(options);
  return `${boundsStr}_${optionsStr}`;
}

/**
 * 从缓存获取数据
 * @param {string} key - 缓存键
 * @returns {Object|null} 缓存的数据或null
 */
function getFromCache(key) {
  const cached = cache.get(key);
  if (!cached) return null;

  // 检查是否过期
  if (Date.now() - cached.timestamp > CACHE_DURATION) {
    cache.delete(key);
    return null;
  }

  return cached.data;
}

/**
 * 设置缓存数据
 * @param {string} key - 缓存键
 * @param {Object} data - 要缓存的数据
 */
function setToCache(key, data) {
  // 清理过期缓存
  cleanExpiredCache();
  
  // 如果缓存已满，删除最旧的条目
  if (cache.size >= MAX_CACHE_SIZE) {
    const firstKey = cache.keys().next().value;
    cache.delete(firstKey);
  }

  cache.set(key, {
    data: data,
    timestamp: Date.now()
  });
}

/**
 * 清理过期缓存
 */
function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      cache.delete(key);
    }
  }
}

/**
 * 清空所有缓存
 */
export function clearCache() {
  cache.clear();
  console.log('NFZService: Cache cleared');
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
export function getCacheStats() {
  return {
    size: cache.size,
    maxSize: MAX_CACHE_SIZE,
    duration: CACHE_DURATION,
    keys: Array.from(cache.keys())
  };
}

/**
 * 预加载指定区域的禁飞区数据
 * @param {Object} bounds - 地图边界
 * @param {Object} options - 可选参数
 * @returns {Promise<void>}
 */
export async function preloadNFZData(bounds, options = {}) {
  try {
    await fetchNFZData(bounds, options);
    console.log('NFZService: Data preloaded successfully');
  } catch (error) {
    console.warn('NFZService: Preload failed:', error);
  }
}

/**
 * 批量获取多个区域的禁飞区数据
 * @param {Array<Object>} boundsList - 边界数组
 * @param {Object} options - 可选参数
 * @returns {Promise<Array<Object>>} 数据数组
 */
export async function fetchMultipleNFZData(boundsList, options = {}) {
  const promises = boundsList.map(bounds => 
    fetchNFZData(bounds, options).catch(error => {
      console.warn('NFZService: Failed to fetch data for bounds:', bounds, error);
      return null;
    })
  );

  return Promise.all(promises);
}

export default {
  fetchNFZData,
  clearCache,
  getCacheStats,
  preloadNFZData,
  fetchMultipleNFZData
};
