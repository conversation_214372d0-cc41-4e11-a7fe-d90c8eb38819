import { Get2 } from '@/services/general';



export function NewAliOssClientByToken (KeyId,KeySecret,stsToken) {
    const OSS = require('ali-oss');
    const x1=getOssConfig();
   // 
    const client = new OSS({
        // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
        region: 'oss-cn-chengdu',
        // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
        accessKeyId: KeyId,
        accessKeySecret: KeySecret,
        // 从STS服务获取的安全令牌（SecurityToken）。
        stsToken: stsToken,
        refreshSTSToken: async () => {
            // 向您搭建的STS服务获取临时访问凭证。
            const info = await Get2('/api/v1/File/AliToken', {});
            return {
                accessKeyId: info.AccessKeyId,
                accessKeySecret: info.AccessKeySecret,
                stsToken: info.SecurityToken
            }
        },
        // 刷新临时访问凭证的时间间隔，单位为毫秒。
        refreshSTSTokenInterval: 3000000,
        // 填写Bucket名称。
        bucket: x1.bucket
    });
    return client;
}

export async function NewAliOssClient () {
 
  
 const info = await Get2('/api/v1/File/AliToken', {});

 
return NewAliOssClientByToken(info.AccessKeyId,info.AccessKeySecret,info.SecurityToken);
 
}

export async function OssPutFile (file,object) {
  
   // 
    const client=await NewAliOssClient();
    // 
    //client.put(object, file,{});
    const options = {
        // 获取分片上传进度、断点和返回值。
        progress: (p, cpt, res) => {
          console.log('progress',p);
        },
        // 设置并发上传的分片数量。
        parallel: 4,
        // 设置分片大小。默认值为1 MB，最小值为100 KB。
        partSize: 1024 * 1024,
        // headers,
        // 自定义元数据，通过HeadObject接口可以获取Object的元数据。
        //meta: { year: 2020, people: "test" },
      //  mime: "text/plain",
      };

    const res = await client.multipartUpload(object, file,{...options});
    return res;  

}