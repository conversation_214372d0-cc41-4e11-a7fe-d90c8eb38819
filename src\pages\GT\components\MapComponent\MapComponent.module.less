.MapComponent {
    position: absolute;
    top: 20px;
    // right: calc(100vh - 130px);
    left:-90px;
    z-index: 99999!important;
    cursor: pointer;
    user-select: none;
}

.MapComponent_content_item_box {
    position: relative;
}
.MapComponent_content_item {
    width: 70px;
    height: 70px;
    background: url(../../assets/image/u429.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: rgba(79, 244, 255);
    text-align: center;
    line-height: 60px;
    font-size: 13px;
    border-radius: 50%;
}

.MapComponent_content_item_child_box {
    visibility: hidden;
    width: 0;
    top: 12px;
    right: 70px;
    position: absolute;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    border-radius: 6px;
    transition: all ease-in 0.2s;
    overflow: hidden;
    padding:0;
}
.child_box_active{
    padding:6px;
    width: auto;
    background: rgba(#0c0f1d, 0.7);
    visibility: visible;
}
.MapComponent_content_item_child {
    width: 35px;
    height: 35px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin: 0px 10px;
    border: 1px solid #1ab598;
    color: #1ab598;
    background: #1c2342;
    font-size: 12px;
    text-align: center;
    writing-mode: horizontal-tb; /* 确保文字水平排列 */
    text-orientation: mixed; /* 适用于多行文本 */
}
.MapComponent_content_item_child2{
    border-radius: 3px;
    width: auto;
    height: auto;
    padding:7px;
    font-size: 13px;
}

.MapComponent_content_item_child:hover{
    background-color: #80ffff;
}
.distance_tooltip {
    background-color: rgba(253, 95, 96, 0.8);
    color: white;
    padding: 5px;
    border-radius: 5px;
    font-size: 12px;
}
