import { queryPage, queryPage2 } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect, useMemo } from 'react';
import {
  Card, Table, Button, Modal, Form, Input, Select, List, Pagination,
  DatePicker, Checkbox, InputNumber, Radio, Upload, message, Spin, Splitter, Tooltip, Steps, Tabs, Descriptions, Tag
} from 'antd';
import LastPageButton from '@/components/LastPageButton';
import TableCols from './table';
import { axiosApi } from "@/services/general";
import { getDeviceName, WayLineTypeToString } from "@/utils/utils";
import { Get2, Post2 } from '@/services/general';
import { timeFormat } from "@/utils/helper";
import AIModelSelector from '@/pages/GT/components/RecordsPage/AImodelSelector';
import DynamicDataTable from '@/pages/SI/components/Common/Table/index'

// 目标识别页面
const TargetRecognition = ({ taskType }) => {
  const { setPage, currentPage, pageData } = useModel("pageModel");
  const [dataSource, setDataSource] = useState([]);
  const [currentTablePage, setCurrentTablePage] = useState(1);
  const [loading, setLoading] = useState(false); // 目标识别表格加载状态
  const [loading2, setLoading2] = useState(false); // 飞行记录列表加载状态
  const [loading3, setLoading3] = useState(false) // 步骤弹窗的加载状态
  const [modalVisible, setModalVisible] = useState(false);
  const [showWaylineModal, setShowWaylineModal] = useState(false);
  const [isDJPage, setIsDJPage] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [activeTabKey, setActiveTabKey] = useState('1'); // 记录第一步的标签页状态
  const [selectedWayLine, setSelectedWayLine] = useState(); // 选择的航线
  const [filesUploaded, setFilesUploaded] = useState(false); // 是否上传了图片
  const [selectedFiles, setSelectedFiles] = useState([]); // 已上传的图片
  const [pendingFiles, setPendingFiles] = useState([]); // 等待上传的文件
  const [WayLineList, setWayLineList] = useState([]); // 飞行记录列表
  const [WayLineListOptions, setWayLineListOptions] = useState([]); // 飞行记录列表选项
  const [flightRecordPagination, setFlightRecordPagination] = useState({
    current: 1,
    pageSize: 10,
  }); // 飞行记录分页状态
  const [searchFlightRecord, setSearchFlightRecord] = useState(''); // 搜索筛选的飞行记录
  const [selectedModel, setSelectedModel] = useState(); // 选择的模型
  const [resource, setResource] = useState(1); // 识别资源类型 (1: 飞行记录, 2: 图片)
  const [taskName, setTaskName] = useState(''); // 任务名称
  const AppType = localStorage.getItem('currentPage') || ''; // 获取当前的行业类型
  const [pageSize, setPageSize] = useState(10);


  const taskTypeMap = {
    '目标识别': 1,
    '语义分割': 2,
    '变化检测': 3
  };

  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };
  const handlePageChange2 = (page) => {
    setPage(queryPage2(page));
  };

  const getDefaultData = async () => {
    try {
      setLoading(true);
      // const res = await axiosApi(`/api/v1/AITask/GetList?taskType=目标识别`, 'GET', null);

      const res = await axiosApi(`/api/v1/AITaskInstance/GetList`, 'GET', {
        taskType: taskType,
      });

      // 空数据处理
      if (res.data) {
        // 筛选行业类型
        // 如果没有选择行业类型 则不筛选
        if (AppType == '') {
          setDataSource(res.data);
        }
        else {
          const fillteredData = res.data.filter(item => item.AppType === AppType);
          setDataSource(fillteredData);
        }
        setLoading(false);
      } else {
        setDataSource([]);
        console.log('无数据或者获取数据失败');
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  }

  const openModal = () => {
    setModalVisible(true);
    getAllList(); // 获取飞行记录列表
  }


  useEffect(() => {
    // 如果pageData为空，说明是从DJ页面跳转过来的，需要设置isDJPage为true
    if (!pageData) {
      setIsDJPage(true);
    }
  }, [pageData]);

  const handleBatchUpload = async () => {
    // 提取原始文件对象
    const rawFiles = pendingFiles.map(file => file.originFileObj || file);

    if (taskType === '变化检测' && rawFiles.length < 2) {
      message.warning("变化检测需要至少上传2个文件！");
      return;
    }

    if (rawFiles.length === 0) {
      message.warning("请选择要上传的文件！");
      return;
    }
    const formData = new FormData();
    rawFiles.forEach(file => {
      formData.append('images', file);
    });
    try {
      setLoading3(true);
      const token = localStorage.getItem('token');
      const res = await axiosApi(
        "/api/v1/upload/UploadImages",
        "POST",
        formData,
        {
          headers: {
            'auth': token,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': '*',
            "Content-Type": "multipart/form-data",
          },
        }
      );
      setLoading3(false);
      message.success("上传成功！");
      console.log('上传成功', res);
      setSelectedFiles(res.data);
      setPendingFiles([]); // 清空待上传文件列表
      setFilesUploaded(false); // 标记未上传文件
      setResource(2); // 识别资源类型为图片
      setCurrentStep(1); // 切换到第二步
    } catch (error) {
      console.log('上传失败', error);
      message.error("上传失败! ");
      setLoading3(false);
    }
  }

  const reset = () => {
    setSelectedWayLine(null);
    setFilesUploaded(false);
    setSelectedFiles([]);
    setPendingFiles([]);
    setSelectedModel();
    setTaskName('');
    setModalVisible(false)
    setCurrentStep(0)
    setActiveTabKey('1')
  };

  // 处理提交
  const handleSubmit = async () => {
    const data = {
      taskName: taskName, // 任务名称
      resource: resource, // 识别来源 1 飞行记录 2 自己上传
      files: '', // 上传的文件地址
      funcID: selectedModel.funcID, // 算法ID
      resourceCategory: selectedModel.category, // 算法类别 （图片/视频）
      pyFunc: selectedModel.pyFunc, // 算法参数
      flightTaskID: '', // 飞行记录ID
      taskType: taskTypeMap[taskType], // 识别类型 1 目标识别 2 语义分割，3 变化检测
      appType: AppType, // 行业类型 （耕地保护、自主测绘、临时用地.....）
    }

    if (resource === 1) {
      // 识别类型为飞行记录
      data.flightTaskID = selectedWayLine.FlightLineId;

    } else if (resource === 2) {
      // 识别类型为自己上传
      data.files = selectedFiles.images.map(f => f.url);
    }

    console.log('即将提交的数据data', data);

    try {
      setLoading3(true)
      const res = await axiosApi('/api/v1/AIAlgorithmTask/Add', 'POST', data);
      if (res.code === 0) {
        message.error(res.msg);
      } else if (res.code === 1) {
        message.success('提交成功');
      }
      setLoading3(false)
    } catch (error) {
      console.log('提交失败', error);
      message.error("提交失败! ");
      setLoading3(false)
    }
    setModalVisible(false);
    reset();
    // 重新获取一遍数据
    getDefaultData();
  }



  // 飞行记录弹窗过滤逻辑
  const filteredWaylines = useMemo(() =>
    WayLineListOptions.filter(item => {
      if (!searchFlightRecord) return true;
      const searchText = searchFlightRecord.toLowerCase();
      const formattedTime = timeFormat(item.TaskBeginTime).toLowerCase();

      return (
        item.label.toLowerCase().includes(searchText) ||
        (item.airport || '').toLowerCase().includes(searchText) ||
        formattedTime.includes(searchText)
      );
    }),
    [WayLineListOptions, searchFlightRecord]
  );

  // 步骤条相关
  const steps = [
    {
      title: '第一步',
      content: (
        <div style={{ marginTop: 16 }}>
          <Tabs
            activeKey={activeTabKey}
            onChange={(activeKey) => {
              setActiveTabKey(activeKey);
              // 切换标签时重置所有状态
              setSelectedWayLine(null);
              setFilesUploaded(false);
            }}
            items={[
              {
                key: '1',
                label: '图片上传',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{
                      maxHeight: '30vh',
                      overflowY: 'auto',
                      paddingRight: 8
                    }}>
                      <Upload.Dragger
                        name="file"
                        multiple={true}
                        fileList={pendingFiles}
                        beforeUpload={(file) => {
                          const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                          if (!isValidType) {
                            message.error('仅支持jpg/png格式文件！');
                            return false;
                          }
                          return false;
                        }}
                        onChange={({ file, fileList }) => {
                          // 转换文件对象格式
                          const formattedList = fileList.map(f => ({
                            ...f,
                            uid: f.uid,
                            name: f.name,
                            status: f.status
                          }));
                          setPendingFiles(formattedList);

                          // 根据任务类型设置上传状态
                          if (taskType === '变化检测') {
                            setFilesUploaded(formattedList.length >= 2);
                          } else {
                            setFilesUploaded(formattedList.length > 0);
                          }
                        }}
                        onRemove={(file) => {
                          const newFiles = pendingFiles.filter(f => f.uid !== file.uid);
                          setPendingFiles(newFiles);
                          setFilesUploaded(newFiles.length > 0);
                          return true;
                        }}
                      >
                        <p className="ant-upload-text">点击或拖拽上传图片</p>
                        <p className="ant-upload-hint">支持jpg/png格式</p>
                      </Upload.Dragger>
                    </div>
                    <Button
                      type="primary"
                      onClick={() => handleBatchUpload()}
                      disabled={
                        taskType === '变化检测'
                          ? pendingFiles.length < 2  // 变化检测需要至少2个文件
                          : !filesUploaded           // 其他类型只需要有文件
                      }
                      style={{ alignSelf: 'flex-end' }}
                    >
                      下一步
                    </Button>
                  </div>
                ),
              },
              ...(taskType === '目标识别' ? [{
                key: '2',
                label: '飞行记录',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <label style={{ whiteSpace: 'nowrap' }}>飞行记录：&nbsp;</label>
                      <Input
                        placeholder="请选择飞行记录"
                        readOnly
                        value={selectedWayLine?.FlightLineName}
                        onClick={() => setShowWaylineModal(true)}
                        style={{ cursor: 'pointer' }}
                      />
                      <Button
                        type="link"
                        onClick={() => setShowWaylineModal(true)}
                        style={{ marginLeft: 8 }}
                      >
                        选择记录
                      </Button>
                    </div>

                    {/* 选择飞行记录弹窗 */}
                    <Modal
                      title="选择飞行记录"
                      open={showWaylineModal}
                      onCancel={() => setShowWaylineModal(false)}
                      footer={null}
                      width={800}
                    >
                      <div style={{ display: 'flex', flexDirection: 'column', gap: 16, height: '70vh' }}>
                        <Input
                          placeholder="搜索飞行记录（名称/机场/时间）"
                          allowClear
                          onChange={(e) => {
                            setSearchFlightRecord(e.target.value);
                            setFlightRecordPagination(prev => ({ ...prev, current: 1 })); // 搜索时重置到第一页
                          }}
                        />

                        <div style={{ flex: 1, overflowY: 'auto' }}>
                          <List
                            dataSource={filteredWaylines.slice(
                              (flightRecordPagination.current - 1) * flightRecordPagination.pageSize,
                              flightRecordPagination.current * flightRecordPagination.pageSize
                            )}
                            renderItem={(item) => (
                              <List.Item
                                onClick={() => {
                                  setSelectedWayLine(item.flightLineData);
                                  setShowWaylineModal(false);
                                }}
                                style={{
                                  cursor: 'pointer',
                                  padding: 12,
                                  borderBottom: '1px solid #f0f0f0',
                                  transition: 'all 0.3s',
                                  boxShadow: 'none',
                                  background: 'transparent'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.5)';
                                  e.currentTarget.style.transform = 'translateY(-2px)';
                                  e.currentTarget.style.background = '#ffffff0a';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.boxShadow = 'none';
                                  e.currentTarget.style.transform = 'translateY(0)';
                                  e.currentTarget.style.background = 'transparent';
                                }}
                              >
                                <List.Item.Meta
                                  title={item.label}
                                  description={
                                    <>
                                      <div>类型：{item.FlightLineType}</div>
                                      <div>所属机场：{item.airport}</div>
                                      <div>开始时间：{timeFormat(item.TaskBeginTime)}</div>
                                    </>
                                  }
                                />
                              </List.Item>
                            )}
                          />
                        </div>
                        <div style={{ marginTop: 16, alignSelf: 'flex-end' }}>
                          <Pagination
                            current={flightRecordPagination.current}
                            pageSize={flightRecordPagination.pageSize}
                            total={filteredWaylines.length}
                            onChange={(page, pageSize) => {
                              setFlightRecordPagination({
                                current: page,
                                pageSize: pageSize
                              });
                            }}
                            showSizeChanger
                            showQuickJumper
                            pageSizeOptions={['10', '20', '50']}
                          />
                        </div>
                      </div>
                    </Modal>

                    <Button
                      type="primary"
                      onClick={() => {
                        setCurrentStep(1);
                        setResource(1);
                      }}
                      disabled={!selectedWayLine}
                      style={{ alignSelf: 'flex-end' }}
                    >
                      下一步
                    </Button>
                  </div>
                ),
              }] : [{
                key: '2',
                label: '正射影像',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <label style={{ whiteSpace: 'nowrap' }}>正射影像: &nbsp;</label>
                      <Button
                        type="primary"
                        onClick={() => {
                          console.log('@选择正射影像@');
                        }}
                      >
                        选择正射影像
                      </Button>
                    </div>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurrentStep(1);
                        setResource(1);
                      }}
                      disabled={true}
                      style={{ alignSelf: 'flex-end' }}
                    >
                      下一步
                    </Button>
                  </div>
                ),
              }
              ])
            ]}
          />
        </div>
      ),
    },
    {
      title: '第二步',
      content: (
        <div style={{ marginTop: 16 }}>
          <Tabs
            defaultActiveKey="1"
            onChange={(activeKey) => {
            }}
            items={[
              {
                key: '1',
                label: '选择模型',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{
                      maxHeight: '40vh',
                      overflowY: 'auto',
                      scrollbarWidth: 'none',
                    }}>
                      <label style={{ whiteSpace: 'nowrap', marginBottom: '8px' }}>任务名称</label>
                      <Input
                        placeholder="请输入任务名称"
                        value={taskName}
                        onChange={(e) => setTaskName(e.target.value)}
                        style={{ flex: 1 }}
                      />
                      <AIModelSelector
                        type={taskType}
                        selectedModels={selectedModel}
                        onChange={({ selectedModels, }) => {
                          setSelectedModel(selectedModels || null);
                        }}
                        isSingle={true} // 单选模式
                      />
                    </div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 8,
                      marginTop: 16
                    }}>
                      <Button
                        type="primary"
                        onClick={() => {
                          setCurrentStep(0);
                          setActiveTabKey(resource === 1 ? '2' : '1'); // 这行代码似乎没有必要
                        }}
                      >
                        上一步
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => setCurrentStep(2)}
                        disabled={!selectedModel || !taskName}
                      >
                        下一步
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          ></Tabs>
        </div>
      ),
    },
    {
      title: '第三步',
      content: (
        <div style={{
          marginTop: 16,
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                key: '1',
                label: '确认信息',
                children: (
                  <div style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    height: '33vh',
                    gap: 16
                  }}>
                    <div style={{
                      flex: 1,
                      overflowY: 'auto',
                      paddingRight: 8,
                    }}>
                      <Descriptions
                        bordered
                        column={2}
                        layout="vertical"
                      >
                        {/* <Descriptions.Item label="上传的图片">
                          {selectedFiles?.images ? (
                            selectedFiles.images.map(img => img.filename).join(', ')
                          ) : '无'}
                        </Descriptions.Item>
                        <Descriptions.Item label="飞行记录">
                          {selectedWayLine?.FlightLineName || '未选择'}
                        </Descriptions.Item> */}
                        {resource === 2 && selectedFiles?.images && (
                          <Descriptions.Item label="上传的图片">
                            {selectedFiles.images.map(img => img.filename).join(', ')}
                          </Descriptions.Item>
                        )}
                        {resource === 1 && selectedWayLine?.FlightLineName && (
                          <Descriptions.Item label="飞行记录">
                            {selectedWayLine.FlightLineName}
                          </Descriptions.Item>
                        )}
                        <Descriptions.Item label="任务名称">
                          {taskName == '' ? '---' : taskName}
                        </Descriptions.Item>
                        <Descriptions.Item label="AI算法">
                          {selectedModel ? (<>
                            {/* <lable>资源类型:</lable>&nbsp;&nbsp; */}
                            <Tag
                              color={selectedModel.category === '视频' ? 'blue' : selectedModel.category === '图片' ? 'gold' : undefined}
                            >
                              {selectedModel.category}
                            </Tag>
                            <span>
                              {/* <lable>算法名称:</lable>&nbsp;&nbsp; */}
                              {selectedModel.sceneFunc}
                            </span>
                          </>) : '未选择'}
                        </Descriptions.Item>
                      </Descriptions>
                    </div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 8,
                      marginTop: 16
                    }}>
                      <Button
                        type="primary"
                        onClick={() => {
                          setCurrentStep(1);
                        }}
                      >
                        上一步
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => handleSubmit()}
                      >
                        确认提交
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </div>
      ),
    }
  ];


  // 获取飞行列表
  const getAllList = async () => {
    try {
      setLoading2(true);
      const res = await axiosApi('/api/v1/Task/GetAllList', 'GET', null);
      setWayLineList(res)
      setWayLineListOptions(res.map(item => ({
        flightLineData: item, // 飞行记录数据
        label: item.FlightLineName,
        value: item.FlightLineId,
        FlightLineType: WayLineTypeToString(item.FlightLineType),
        airport: getDeviceName(item.DeviceSN),
        TaskBeginTime: item.TaskBeginTime,
        key: item.ID, // 这里一定要设置唯一key 否则会导致渲染丢失
      }))
      )
      setLoading2(false);
    }
    catch (error) {
      console.log(error);
    }
  };

  const changeStep = value => {
    //点击步骤条的时候 重置所有状态
    setSelectedWayLine(null); // 选择的航线
    setFilesUploaded(false); // 是否上传了图片
    setSelectedFiles([]); // 已上传的图片
    setPendingFiles([]); // 等待上传的文件
    setSelectedModel(null); // 选择的模型
    setTaskName(''); // 任务名称

    setCurrentStep(value);
  };

  useEffect(() => {
    getDefaultData(); // 获取数据列表
  }, [taskType])

  const items = steps.map((item) => ({ key: item.title, title: item.title }));

  const exr = <div>
    <Button type="primary" onClick={() => { console.log('刷新'); getDefaultData() }} style={{ marginRight: 8 }}>刷新</Button>
    <Button type="primary" onClick={() => { console.log('新增'); openModal(); }}>新增</Button>
  </div>
  return (
    <div className='blackBackground'>
      <Card
        title={taskType}
        extra={exr}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: 'hidden',
          padding: 0,
        }}
      >
        {/* <Table
          // 自定义分页
          pagination={{
            pageSize: 15,
            showSizeChanger: false,
            current: currentTablePage,
            total: dataSource.length,
            showTotal: (total) => (
              <span className="custom-pagination-text">
                共 {Math.ceil(total / 15)} 条 第 {currentTablePage}/{Math.ceil(total / 15)} 页
              </span>
            ),
            onChange: (page) => setCurrentTablePage(page),
            className: "custom-pagination-container"
          }}
          rowKey={(record) => record.ID}
          loading={loading}
          bordered
          dataSource={dataSource}
          columns={TableCols(handlePageChange, isDJPage, handlePageChange2, getDefaultData)}
          size='small'
          scroll={{
            scrollToFirstRowOnChange: true,
            y: `calc(100vh - 310px)`,
            // y:600,  
          }}
        /> */}
        <DynamicDataTable
          rowKey={(record) => record.ID}
          loading={loading}
          bordered
          dataSource={dataSource}
          columns={TableCols(handlePageChange, handlePageChange2, getDefaultData, taskType)}
          pagination={{
            pageSize: pageSize,
            showSizeChanger: true,
            current: currentTablePage,
            total: dataSource.length,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrentTablePage(1);
            },
            onChange: (page) => setCurrentTablePage(page),
            className: "custom-pagination-container",
            locale: {
              items_per_page: "条/页",
              jump_to: "跳至",
              page: "页",
            },
            showTotal: (total) => (
              <span className="custom-pagination-text">
                共 {total} 条
              </span>
            ),
            showQuickJumper: true,
          }}
          scroll={{
            scrollToFirstRowOnChange: true,
            y: `calc(100vh - 310px)`,
            // y:600,  
          }}
        />
        <Modal
          title="新建任务"
          open={modalVisible}
          onCancel={() => {
            // 重置所有状态
            reset();
          }}
          footer={null}
          width="60vw"
          styles={{
            body: {
              maxHeight: '70vh',
              display: 'flex',
              flexDirection: 'column',
              padding: 24
            }
          }}
        >
          <Spin spinning={loading3}>
            <Steps
              current={currentStep}
              items={items}
            // onChange={changeStep} // 允许点击步骤条切换步骤
            />
            <div>{steps[currentStep].content}</div>
          </Spin>
        </Modal>
      </Card>
    </div>

  );
};
export default TargetRecognition;