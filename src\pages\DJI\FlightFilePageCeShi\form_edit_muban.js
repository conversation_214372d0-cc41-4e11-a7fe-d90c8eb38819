import { Input, Checkbox, TreeSelect, Cascader, Slider, ColorPicker, InputNumber, Switch, Card, Tag, Descriptions, DatePicker, Radio, Select, Row, Col, Button, Modal, message, Table, Upload } from 'antd';

import  { useState, useEffect } from 'react';
import {  isEmpty } from '@/utils/utils';


//import 'dayjs/locale/zh-cn';
import {  HPost2 } from '@/utils/request';
import { useModel } from 'umi';

import LastPageButton from '@/components/LastPageButton';

import AddButton from '@/components/AddButton';


const EditForm = ({ data0, refrush }) => {

  const [fdata, setFData] = useState(data0);
  const { setPage, lastPage } = useModel('pageModel');

  const onSave = async (e) => {

    //  const user=JSON.parse( localStorage.getItem('user'));
    const xx = await HPost2('/api/v1/{dname}/Update', fdata);
    if (isEmpty(xx.err)) {
      message.info("创建成功！")
    }

    refrush();
    lastPage();
  };



  const getDataItem=(title,field,span)=>{
    const data=fdata;
    if(isEmpty(span)) span=1;
   return <Descriptions.Item label={title} span={span}>
    <Input
      value={data[field]}
      onChange={(e) => {const d2={...data};d2[field]=e.target.value; setFData(d2) }}>
    </Input>
  </Descriptions.Item>
  }

  const getPanel = (data) => {

    const list=[]
    // list.push(getInputItem('单位','FDepartment'));
    {colstr}
    return  <Descriptions title={'数据'} labelStyle={{ paddingLeft: 12.0, width: 160 }} bordered column={2} extra={<AddButton style={{ marginRight: 48.0 }} onClick={onSave}>保存</AddButton>}>{list}</Descriptions>
  }

  return <Card title={<LastPageButton title={data0['ID']>0?"数据编辑":"数据新建"} />} bordered={false}   > <Row>
    <Col span={4}></Col>
    <Col style={{ paddingLeft: 24.0 }} span={16}>{getPanel(fdata)}</Col>

  </Row></Card>
}



export default EditForm;