
import { createRoot } from 'react-dom/client';
import { Button, Space } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

// 分页展示组件
const FeatureViewer = ({ features }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const total = features.length;

  // 处理翻页
  const handlePageChange = (step) => {
    setCurrentIndex((prev) => {
      const newIndex = prev + step;
      if (newIndex < 0) return total - 1;
      if (newIndex >= total) return 0;
      return newIndex;
    });
  };

  return (
    <div style={{
      width: '500px',
      maxHeight: '400px',
      padding: '12px',
      backgroundColor: '#fff',
      borderRadius: '4px'
    }}>
      {/* 分页控制器 */}
      <div style={{ 
        marginBottom: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span>
          第 {currentIndex + 1} 条 / 共 {total} 条
        </span>
        <Space>
          <Button 
            size="small" 
            icon={<LeftOutlined />}
            onClick={() => handlePageChange(-1)}
          />
          <Button
            size="small"
            icon={<RightOutlined />}
            onClick={() => handlePageChange(1)}
          />
        </Space>
      </div>

      {/* 内容区域 后期再修改 */}
      <div style={{ 
        maxHeight: '320px',
        overflow: 'auto',
        border: '1px solid #f0f0f0',
        borderRadius: '4px',
        padding: '8px'
      }}>
        {/* 暂时将数据用JSON.stringify展示 */}
        <pre style={{
          margin: 0,
          fontSize: '13px',
          lineHeight: 1.6,
          whiteSpace: 'pre-wrap',
          fontFamily: 'monospace'
        }}>
          {JSON.stringify(features[currentIndex].properties, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// 弹窗创建方法
export const createFeaturePopup = (data) => {
  if (!data?.features?.length) return null;

  const container = L.DomUtil.create('div');
  const root = createRoot(container);
  
  root.render(<FeatureViewer features={data.features} />);
  
  return container;
};