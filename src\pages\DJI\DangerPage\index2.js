import React, { Fragment, useState, useEffect } from 'react';

import { <PERSON>readcrumb,Card,List, Input, Tag, DatePicker,Descriptions, Row, Col, Badge,Button, Form ,message, Table, Modal,Meta} from 'antd';

import { getImgUrl,getBodyH, isEmpty } from '@/utils/utils';

import { Get2, Post2 } from '@/services/general';
import {timeFormat} from '@/utils/helper';

import DangerAddForm from './form_add';
import TableCols from './table';

import DangerDetailPage from './detail';
import { useModel,history } from 'umi';
import LastPageButton from '@/components/LastPageButton';


const DangerListPage = (props) => {

  const [wayList, setWayList] = useState({});
  // const [open, setOpen] = useState(false);
  const [DangerList, setDangerList] = useState({});
  const {setModal,setOpen,setPage,lastPage}=useModel('pageModel')

  const dateFormat="YYYY/MM/DD";


  useEffect(() => {

   
    const getDangerData = async () => {
      const id=localStorage.getItem('orgId');
      const  pst= await Get2( '/api/v2/Danger/GetAllList?p1='+id,{});
   //   if(isEmpty(pst)) return;
      
      setDangerList(pst);
    };

    getDangerData();

  },[]);


  
  const showMap = (values) => {
    
    setPage(<Card title={<LastPageButton title='事件详情' />} extra={<Button onClick={()=>lastPage()}>返回</Button>}><DangerDetailPage danger2={values}></DangerDetailPage></Card>)
    
  };

  const refrush = async () => {
    setOpen(false);
    const  pst= await Get2( '/api/v1/Danger/GetAllList',{});
  //  if(isEmpty(pst)) return;
    setDangerList(pst);
   
  };



//   const exr= <div><Button type="primary" onClick={()=>setCanSee(true)}>航线上传</Button> <Modal  title={null} footer={null} onOk={null} visible={canSee}  onCancel={()=>setCanSee(false)}>
//             <WayLineAddForm/>
// </Modal></div>
const getChuLiState=(x)=>{
  if (x >= 1)
    return <Badge color={'green'} text="已处理" />
  return <Badge color={'orange'} text="处理中" />
}


  const exr= <Button type="primary" onClick={()=>{setModal(<DangerAddForm  wayList={wayList} refrush={refrush}/>); setOpen(true)}}>手动添加</Button>

  {/* <DangerAddForm  data={refrush}/> */}
  // if(isEmpty(DangerList))return <div></div>
  return (
    
    <div style={{margin:0,height:getBodyH(56),background:'#F5F5FF'}}>
      {/* <div><BreadTitle /></div> */}

   
      <div>
      {isEmpty(DangerList)?<div/>:   <List
   // grid={{ gutter: 16, column: 1 }}
    pagination={{ pageSize: 5,}}

    dataSource={DangerList}
    renderItem={(item) => (
      <List.Item>
        <Card title={item.Title}
        onClick={()=>history.push('/event?id='+item.ID)}
        extra={getChuLiState(item.State)}
        cover={<div >  <img style={{padding:12.0}} width={'100%'} src={getImgUrl(item.ImgObjectName)}/> <div style={{marginLeft:24.0}}>{ timeFormat(item.CreateTM)}</div> </div>
      }
        >
         
        </Card>
      </List.Item>
    )}
  />
      
      }
      </div>

    

    </div>
 )
};

export default DangerListPage;
