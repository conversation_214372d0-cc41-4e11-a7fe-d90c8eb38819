import React, { useEffect, useRef, useState, forwardRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { message } from 'antd';
import { throttle, debounce } from 'lodash';
import { MAP_CONFIG } from './config';
import * as esri from 'esri-leaflet/dist/esri-leaflet.js';
import MapInfoPanel from '@/pages/GT/components/MapControls/MapInfoPanel';
import MapCompass from '@/pages/GT/components/MapControls/MapCompass';
import MapScale from '@/pages/GT/components/MapControls/MapScale';
import MapMeasure from '@/pages/GT/components/MapControls/MapMeasure';

const LeafletMap = forwardRef((props, ref) => {
  const {
    service,
    initZoom,
    initCenter,
    onSwitchTo3D,
    showMapInfoPanel,
    showMapScale,
    showMeasureBtn,
    showMapCompass,
    isLeftSidebarCollapsed,
    isRightSidebarCollapsed,
    customCompassStyle = {}, // 接收外部传入的指南针自定义样式
  } = props;
  // 添加状态来存储鼠标位置和缩放级别
  const [mousePosition, setMousePosition] = useState({ lat: 0, lng: 0 });
  const [zoomLevel, setZoomLevel] = useState(initZoom);
  const mapContainer = useRef(null);
  const mapRef = useRef(null);
  const [layers, setLayers] = useState([]);

  // 添加图层函数
  const addLayer = (serviceConfig) => {
    if (!mapRef.current || !serviceConfig) return null;

    try {
      let layer;
      const { url, type, zIndex, ...options } = serviceConfig;

      switch (type) {
        // ArcGIS服务
        case 'ArcGISTiledMapServiceLayer':
          if (!esri || !esri.tiledMapLayer) {
            console.error('esri-leaflet tiledMapLayer 未加载');
            throw new Error('esri-leaflet tiledMapLayer 未加载');
          }
          const haveXYZ = url.includes('{z}/{y}/{x}');
          if (!haveXYZ) {
            layer = esri.tiledMapLayer({
              url,
              ...options
            });
          } else {
            layer = L.tileLayer(url, {
              subdomains: options.subdomains || ['0', '1', '2', '3', '4', '5', '6', '7'],
              ...options
            });
          }
          break;

        case 'ArcGISDynamicMapServiceLayer':
          console.log('创建动态地图图层:', url, options);
          if (!esri || !esri.dynamicMapLayer) {
            console.error('esri-leaflet dynamicMapLayer 未加载');
            throw new Error('esri-leaflet dynamicMapLayer 未加载');
          }
          layer = esri.dynamicMapLayer({
            url,
            ...options
          });
          break;

        case 'ArcGISImageMapServiceLayer':
          if (!esri || !esri.imageMapLayer) {
            console.error('esri-leaflet imageMapLayer 未加载');
            throw new Error('esri-leaflet imageMapLayer 未加载');
          }
          layer = esri.imageMapLayer({
            url,
            ...options
          });
          break;

        case 'ArcGISFeatureMapServiceLayer':
          if (!esri || !esri.featureLayer) {
            console.error('esri-leaflet featureLayer 未加载');
            throw new Error('esri-leaflet featureLayer 未加载');
          }
          layer = esri.featureLayer({
            url,
            ...options
          });
          break;

        // OGC服务
        case 'WmsServiceLayer':
          // 解析WMS URL中的参数
          const wmsUrl = url.split('?')[0];
          const urlParams = new URLSearchParams(url.split('?')[1]);

          layer = L.tileLayer.wms(wmsUrl, {
            layers: urlParams.get('layers'),
            version: urlParams.get('version') || '1.1.0',
            format: urlParams.get('format') || options.format || 'image/png',
            transparent: true,
            opacity: options.opacity || 1,
            ...options
          });
          break;

        case 'WmtsServiceLayer':
          // 解析WMTS URL中的参数
          const wmtsParams = new URLSearchParams(url.split('?')[1]);

          layer = L.tileLayer(url, {
            subdomains: wmtsParams.get('subdomains') || ['0', '1', '2', '3', '4', '5', '6', '7'],
            tk: wmtsParams.get('tk') || MAP_CONFIG.TIANDITU_KEY,
            ...MAP_CONFIG.SERVICE_PARAMS,
            ...options
          });
          break;

        // 天地图服务
        case 'TiandituVecLayer':
        case 'TiandituImgLayer':
        case 'TiandituCvaLayer':
          const tiandituSubdomains = ['0', '1', '2', '3', '4', '5', '6', '7'];
          const hasToken = url.includes('tk=');
          let tiandituUrl = hasToken ? url : `${url}${url.includes('?') ? '&' : '?'}tk=${MAP_CONFIG.TIANDITU_KEY}`;
          layer = L.tileLayer(tiandituUrl, {
            subdomains: tiandituSubdomains,
            tk: url.split('tk') || MAP_CONFIG.TIANDITU_KEY,
            ...options
          });
          break;

        // 其他网络地图服务
        case 'WebTileLayer':
          layer = L.tileLayer(url, {
            ...MAP_CONFIG.SERVICE_PARAMS,
            ...options
          });
          break;

        case 'VectorTileLayer':
          layer = L.vectorGrid.protobuf(url, {
            ...options
          });
          break;

        // 正射影像
        case 'TmsServiceLayer':
          // console.log('创建正射影像图层:', url, options);
          const { id, accessInfo, expandParam } = options;
          const expandParamArr = JSON.parse(expandParam).expandParam;
          let paramsObj = {};
          if (expandParamArr && expandParamArr.length > 0) {
            // 使用 reduce 将数组转换为对象
            paramsObj = expandParamArr.reduce((acc, item) => {
              acc[item.paramKey] = item.paramValue;
              return acc;
            }, {});
          }

          const lat = parseFloat(paramsObj.lat) || parseFloat(paramsObj['纬度']) || 30.06346956642108;
          const long = parseFloat(paramsObj.long) || parseFloat(paramsObj['经度']) || 105.31706203454723;
          const zoom = parseInt(paramsObj.zoom, 10) || parseFloat(paramsObj['缩放层级']) || 16;

          mapRef.current.setView(
            [lat, long], zoom
          );

          layer = L.tileLayer(url, {
            opacity: 1.0,
            tms: true,
            ...MAP_CONFIG.SERVICE_PARAMS,
            ...options
          });
          break;

        case 'Cesium3DTileService':
        case 'CesiumTerrainService':
          message.warning(`二维场景下不支持该服务类型，即将前往三维场景查看`);
          if (onSwitchTo3D) {
            onSwitchTo3D(serviceConfig);
          }
          break;

        default:
          message.warning(`不支持的服务类型: ${type}`);
          return null;
      }

      // 添加图层到地图
      if (layer) {
        layer.setZIndex(zIndex);
        mapRef.current.addLayer(layer);
        console.log('图层添加成功:', layer);

        // 创建图层对象并添加到状态中
        const layerObj = {
          id: serviceConfig.id, // ID
          config: serviceConfig,
          instance: layer,
          zIndex: zIndex,
        };

        setLayers(prevLayers => [layerObj, ...prevLayers].sort((a, b) => b.instance.options.zIndex - a.instance.options.zIndex));
        return layerObj;
      }
    } catch (error) {
      console.error('加载服务失败:', error);
      message.error('加载服务失败: ' + error.message);
    }

    return null;
  };

  // 移除图层函数
  const removeLayer = (layerId) => {
    const layerToRemove = layers.find(layer => layer.id === layerId);

    if (layerToRemove && mapRef.current) {
      mapRef.current.removeLayer(layerToRemove.instance);
      setLayers(prevLayers => prevLayers.filter(layer => layer.id !== layerId));
      return true;
    }

    return false;
  };

  // 上移图层函数
  const moveLayerUp = (layerId) => {
    setLayers(prevLayers => {
      const index = prevLayers.findIndex(layer => layer.id === layerId);
      if (index <= 0) return prevLayers; // 已经是最上层或不存在

      const newLayers = [...prevLayers];
      const layerToMove = newLayers[index];
      const layerAbove = newLayers[index - 1];

      // 1. 交换数组中的位置 (用于 UI 列表顺序)
      [newLayers[index], newLayers[index - 1]] = [layerAbove, layerToMove];

      // 2. 交换 Leaflet 图层实例的 zIndex
      if (mapRef.current && layerToMove.instance?.setZIndex && layerAbove.instance?.setZIndex) {
        const zIndexMove = layerToMove.instance.options.zIndex;
        const zIndexAbove = layerAbove.instance.options.zIndex;

        if (zIndexMove !== undefined && zIndexAbove !== undefined) {
          try {
            layerToMove.instance.setZIndex(zIndexAbove);
            layerAbove.instance.setZIndex(zIndexMove);
            // 更新 newLayers 数组中对象的 zIndex 记录 (如果你的状态依赖这个)
            layerToMove.zIndex = zIndexAbove;
            layerAbove.zIndex = zIndexMove;
          } catch (error) {
            console.error("设置 zIndex 时出错:", error);
          }
        } else {
          console.warn("无法获取要交换图层的 zIndex:", layerToMove, layerAbove);
        }
      } else {
        console.warn("地图实例或图层实例/setZIndex方法不存在，无法交换 zIndex");
      }

      return newLayers; // 返回更新后的数组
    });
  };

  // 下移图层函数
  const moveLayerDown = (layerId) => {
    setLayers(prevLayers => {
      const index = prevLayers.findIndex(layer => layer.id === layerId);
      if (index === -1 || index >= prevLayers.length - 1) return prevLayers; // 已经是最下层或不存在

      const newLayers = [...prevLayers];
      const layerToMove = newLayers[index];
      const layerBelow = newLayers[index + 1];

      // 1. 交换数组中的位置 (用于 UI 列表顺序)
      [newLayers[index], newLayers[index + 1]] = [layerBelow, layerToMove];

      // 2. 交换 Leaflet 图层实例的 zIndex
      if (mapRef.current && layerToMove.instance?.setZIndex && layerBelow.instance?.setZIndex) {
        const zIndexMove = layerToMove.instance.options.zIndex;
        const zIndexBelow = layerBelow.instance.options.zIndex;

        if (zIndexMove !== undefined && zIndexBelow !== undefined) {
          try {
            layerToMove.instance.setZIndex(zIndexBelow);
            layerBelow.instance.setZIndex(zIndexMove);
            // 更新 newLayers 数组中对象的 zIndex 记录
            layerToMove.zIndex = zIndexBelow;
            layerBelow.zIndex = zIndexMove;
          } catch (error) {
            console.error("设置 zIndex 时出错:", error);
          }
        } else {
          console.warn("无法获取要交换图层的 zIndex:", layerToMove, layerBelow);
        }
      } else {
        console.warn("地图实例或图层实例/setZIndex方法不存在，无法交换 zIndex");
      }
      return newLayers; // 返回更新后的数组
    });
  };

  // 设置图层显示/隐藏状态
  const setLayerVisibility = (layerId, visible) => {
    const layer = layers.find(layer => layer.id === layerId);
    console.log('设置图层可见性:', layers);
    if (layer && layer.instance) {
      if (visible) {
        mapRef.current.addLayer(layer.instance);
      } else {
        console.log('隐藏图层:', layer.instance);
        mapRef.current.removeLayer(layer.instance);
      }
      // 更新图层状态scaleControl
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId
            ? { ...l, visible }
            : l
        )
      );
      return true;
    }
    return false;
  };

  // 移除所有图层函数
  const removeAllLayers = () => {
    return new Promise((resolve) => {
      if (mapRef.current && layers.length > 0) {
        console.log('Removing all layers from 2D map:', layers);
        layers.forEach(layer => {
          try {
            mapRef.current.removeLayer(layer.instance);
          } catch (error) {
            console.warn(`Error removing layer ${layer.id} from 2D map:`, error);
          }
        });
        setLayers([]); // 清空内部状态
        console.log('All layers removed from 2D map state.');
      }
      resolve(); // 无论如何都解析，表示操作完成
    });
  };

  // 添加marker
  const addMarker = (marker) => {
    let markerObj = null;
    if (mapRef.current) {
      markerObj = marker.addTo(mapRef.current);
    }
    return markerObj;
  };

  // 移除marker
  const removeMarker = (marker) => {
    if (mapRef.current) {
      marker.remove();
    }
  }

  // 获取所有layer，包括marker
  const getAllLayers = () => {
    if (mapRef.current) {
      return mapRef.current._layers;
    }
    return {};
  };

  const zoomIn = React.useCallback(() => {
    if (mapRef.current) {
      mapRef.current.zoomIn()
    }
  }, [])

  const zoomOut = React.useCallback(() => {
    if (mapRef.current) {
      mapRef.current.zoomOut()
    }
  }, [])

  const setZoom = React.useCallback((level) => {
    if (mapRef.current) {
      mapRef.current.setZoom(level)
    }
  }, [])

  const setCenter = React.useCallback((center) => {
    if (mapRef.current) {
      mapRef.current.setView(center)
    }
  }, [])

  const backToDefault = React.useCallback(() => {
    if (mapRef.current) {
      mapRef.current.setView(
        initCenter,
        initZoom
      );
    }
  }, [])

  // 坐标查询相关状态和方法
  const [coordinateMarkers, setCoordinateMarkers] = useState([]) // 存储坐标标记
  const [isPickingCoordinate, setIsPickingCoordinate] = useState(false) // 是否处于拾取模式
  const [pickedCoordinate, setPickedCoordinate] = useState(null) // 拾取到的坐标

  // 定位到指定坐标
  const locationToCoordinate = React.useCallback((coordinate) => {
    if (!mapRef.current) return

    const { longitude, latitude } = coordinate
    const latlng = [latitude, longitude]

    // 定位到坐标
    mapRef.current.setView(latlng, 16)

    // 创建标记
    const marker = L.marker(latlng, {
      icon: L.divIcon({
        className: 'coordinate-location-marker',
        html: `<div style="
          width: 20px;
          height: 20px;
          background: #ff4d4f;
          border: 2px solid #fff;
          border-radius: 50%;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          position: relative;
        ">
          <div style="
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 8px solid #ff4d4f;
          "></div>
        </div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      })
    }).addTo(mapRef.current)

    // 添加弹窗显示坐标信息
    marker.bindPopup(`
      <div style="text-align: center; font-family: monospace;">
        <strong>定位坐标</strong><br/>
        经度: ${longitude.toFixed(6)}°<br/>
        纬度: ${latitude.toFixed(6)}°
      </div>
    `).openPopup()

    // 保存标记到状态
    setCoordinateMarkers(prev => [...prev, marker])
  }, [])

  // 开始/停止拾取坐标
  const togglePickCoordinate = React.useCallback(() => {
    if (!mapRef.current) return

    setIsPickingCoordinate(prev => {
      const newState = !prev

      if (newState) {
        // 开始拾取模式
        mapRef.current.getContainer().style.cursor = 'crosshair'
        mapRef.current.on('click', handleMapClick)
      } else {
        // 停止拾取模式
        mapRef.current.off('click', handleMapClick)
        mapRef.current.getContainer().style.cursor = ''
      }

      return newState
    })
  }, [])

  // 处理地图点击事件（拾取坐标）
  const handleMapClick = React.useCallback((e) => {
    // 直接使用当前鼠标位置信息（与MapInfoPanel一致）
    const { lat, lng } = e.latlng
    const coordinate = {
      latitude: parseFloat(lat.toFixed(6)),
      longitude: parseFloat(lng.toFixed(6))
    }

    // 设置拾取到的坐标
    setPickedCoordinate(coordinate)

    // 创建拾取标记
    const marker = L.marker([lat, lng], {
      icon: L.divIcon({
        className: 'coordinate-pick-marker',
        html: `<div style="
          width: 16px;
          height: 16px;
          background: #52c41a;
          border: 2px solid #fff;
          border-radius: 50%;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        "></div>`,
        iconSize: [16, 16],
        iconAnchor: [8, 8]
      })
    }).addTo(mapRef.current)

    // 添加弹窗显示坐标信息
    marker.bindPopup(`
      <div style="text-align: center; font-family: monospace;">
        <strong>拾取坐标</strong><br/>
        经度: ${lng.toFixed(6)}°<br/>
        纬度: ${lat.toFixed(6)}°
      </div>
    `).openPopup()

    // 保存标记到状态
    setCoordinateMarkers(prev => [...prev, marker])

    // 停止拾取模式
    setIsPickingCoordinate(false)
    if (mapRef.current) {
      mapRef.current.off('click', handleMapClick)
      mapRef.current.getContainer().style.cursor = ''
    }
  }, [])

  // 清除所有坐标标记
  const clearCoordinateMarkers = React.useCallback(() => {
    if (!mapRef.current) return

    coordinateMarkers.forEach(marker => {
      mapRef.current.removeLayer(marker)
    })
    setCoordinateMarkers([])
    setPickedCoordinate(null)
  }, [coordinateMarkers])

  React.useImperativeHandle(
    ref,
    () => ({
      addLayer,
      removeLayer,
      removeAllLayers,
      moveLayerUp,
      moveLayerDown,
      addMarker,
      removeMarker,
      getLayers: () => layers,
      getMap: () => mapRef.current,
      getAllLayers,
      setLayerVisibility,
      zoomIn,
      zoomOut,
      setZoom,
      setCenter,
      backToDefault,
      // 坐标查询相关方法
      locationToCoordinate,
      togglePickCoordinate,
      clearCoordinateMarkers,
      getPickedCoordinate: () => pickedCoordinate,
      getIsPickingCoordinate: () => isPickingCoordinate,
    }),
    [layers,
      addLayer, removeLayer, removeAllLayers, moveLayerUp, moveLayerDown, setLayerVisibility,
      addMarker, removeMarker, zoomIn, zoomOut, setZoom, setCenter, backToDefault,
      locationToCoordinate, togglePickCoordinate, clearCoordinateMarkers, pickedCoordinate, isPickingCoordinate
    ]
  );

  useEffect(() => {
    if (!mapContainer.current) return;

    // 检查esri-leaflet是否正确加载
    if (!esri) {
      console.error('esri-leaflet 未加载');
      message.error('地图服务组件加载失败');
      return;
    }

    // 初始化Leaflet地图
    const map = L.map(mapContainer.current, {
      center: initCenter,
      zoom: initZoom,
      zoomControl: false,
      attributionControl: false,
    });



    // // 添加比例尺控件
    // const scaleControl = L.control.scale({
    //   maxWidth: 200,
    //   metric: true,
    //   imperial: false,
    //   position: 'bottomleft',
    //   updateWhenIdle: true
    // });

    // // 确保比例尺控件正确添加和显示
    // try {
    //   scaleControl.addTo(map);
    // } catch (error) {
    //   console.error('添加比例尺控件失败:', error);
    // }

    // // 强制触发地图更新以确保比例尺显示
    // setTimeout(() => {
    //   if (map) {
    //     map.invalidateSize();
    //   }
    // }, 100);

    // 添加鼠标移动事件监听（使用节流）
    const throttledMouseMove = throttle((e) => {
      setMousePosition({
        lat: e.latlng.lat.toFixed(6),
        lng: e.latlng.lng.toFixed(6)
      });
    }, 100);
    map.on('mousemove', throttledMouseMove);

    // 立即触发一次鼠标位置更新
    const center = map.getCenter();
    throttledMouseMove({ latlng: center });

    // 添加缩放事件监听（使用防抖）
    map.on('zoomend', debounce(() => {
      setZoomLevel(map.getZoom());
    }, 200));

    // 保存map引用以便清理
    mapRef.current = map;

    // 添加默认底图
    const satellite =
      "https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
    // let dt_bz2 =
    // "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    // "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    // "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    // "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";
    // L.tileLayer(satellite).addTo(map);
    // const wmsUrl = "/geoserver/bdzl/wms"
    // L.tileLayer.wms(wmsUrl, {
    //   layers: "bdzl:dzzhd",
    //   version: "1.1.0",
    //   format: "image/png",
    //   transparent: true,
    //   opacity: 0.8,
    //   attribution: null,
    // }).addTo(map);

    // const e = {
    //   Url: 'https://e48e14d9-068b-42e1-8d46-b9e0befd2e70.oss-cn-chengdu.aliyuncs.com/filezip2/mapDT_8b_202403/{z}/{x}/{y}.png',
    //   MapType: 1,
    //   MaxZoom: 18,
    //   MinZoom: 1,
    // };
    // L.tileLayer(e.Url, {
    //   tms: e.MapType === 0 ? false : true,
    //   maxZoom: e.MaxZoom,
    //   minZoom: e.MinZoom,
    //   opacity: 1.0,
    //   attribution: "",
    // }).addTo(map);

    // 如果有服务配置，添加图层
    if (service) {
      addLayer(service);
    }

    return () => {
      // 清理Leaflet实例
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [service]);

  return (
    <div
      ref={mapContainer}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative'
      }}>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'absolute',
        bottom: '20px',
        left: '20px',
        gap: '15px',
        zIndex: 1000
      }}>
        {showMapInfoPanel && <MapInfoPanel
          mousePosition={mousePosition}
          mapType="2D"
          scaleInfo={zoomLevel}
        />}
        {showMapScale && mapRef.current && <MapScale viewer={mapRef.current} />}
      </div>
      {showMapCompass && mapRef.current && <MapCompass viewer={mapRef.current} isRightSidebarCollapsed={isRightSidebarCollapsed} customCompassStyle={customCompassStyle} />}
      {showMeasureBtn && mapRef.current && <MapMeasure viewer={mapRef.current}/>}
    </div>
  );
});

export default LeafletMap;