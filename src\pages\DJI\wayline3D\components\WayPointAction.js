import React from 'react';
import {  Slider, InputNumber, Checkbox, Switch } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';


function WaypointList({ wayline, seleteIndex, actionIndex, deleteWaypointAction, editWaypointAction, setAirplaneModelheading, setAirplaneModelPitch }) {
    function actionActuatorFuncToString(actionActuatorFunc) {
        if (actionActuatorFunc === 'takePhoto') {
            return '拍照'
        } else if (actionActuatorFunc === 'startRecord') {
            return '开始录像'
        } else if (actionActuatorFunc === 'stopRecord') {
            return '结束录像'
        } else if (actionActuatorFunc === 'zoom') {
            return '变焦'
        } else if (actionActuatorFunc === 'rotateYaw') {
            return '偏航角'
        } else if (actionActuatorFunc === 'gimbalRotate') {
            return '俯仰角'
        } else if (actionActuatorFunc === 'hover') {
            return '悬停'
        } else if (actionActuatorFunc === 'panoShot') {
            return '全景拍照'
        }
    }
    return <div style={{ height: '100%', width: '100%' }}>
        {seleteIndex !== null && actionIndex !== null && <div style={{ padding: '0 20px', width: '100%', height: 50, borderBottom: '1px solid #8d8d8d', borderTop: '1px solid #8d8d8d', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>{actionActuatorFuncToString(wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc)}</div>
            <div style={{ width: '20%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>{seleteIndex + 1}-{actionIndex + 1}</div>
            <div onClick={() => { deleteWaypointAction(seleteIndex, actionIndex) }} style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}><DeleteOutlined /></div>
        </div>}
        {seleteIndex !== null && actionIndex !== null && <div style={{ width: '100%', padding: 20 }}>
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'takePhoto' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <Checkbox.Group
                    disabled={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex}
                    value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex}
                    onChange={(e) => { editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', e) }}>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'visable'}
                        value={'visable'}
                        style={{ fontSize: '14px', marginRight: '10px' }}>可见光</Checkbox>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'ir'}
                        value={'ir'}
                        style={{ fontSize: '14px', }}>红外光</Checkbox>
                </Checkbox.Group>
                <div >
                    跟随航线：<Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex} onChange={(e) => {
                        editWaypointAction(seleteIndex, actionIndex, 'useGlobalPayloadLensIndex', e ? 1 : 0)
                        editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', wayline.Folder.payloadParam.imageFormat)
                    }} />
                </div>
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'startRecord' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <Checkbox.Group
                    disabled={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex}
                    value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex}
                    onChange={(e) => { editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', e) }}>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'visable'}
                        value={'visable'}
                        style={{ fontSize: '14px', marginRight: '10px' }}>可见光</Checkbox>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'ir'}
                        value={'ir'}
                        style={{ fontSize: '14px', }}>红外光</Checkbox>
                </Checkbox.Group>
                <div >
                    跟随航线：<Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex} onChange={(e) => {
                        editWaypointAction(seleteIndex, actionIndex, 'useGlobalPayloadLensIndex', e ? 1 : 0)
                        editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', wayline.Folder.payloadParam.imageFormat)
                    }} />
                </div>
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'stopRecord' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <Checkbox.Group
                    disabled={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex}
                    value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex}
                    onChange={(e) => { editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', e) }}>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'visable'}
                        value={'visable'}
                        style={{ fontSize: '14px', marginRight: '10px' }}>可见光</Checkbox>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'ir'}
                        value={'ir'}
                        style={{ fontSize: '14px', }}>红外光</Checkbox>
                </Checkbox.Group>
                <div >
                    跟随航线：<Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex} onChange={(e) => {
                        editWaypointAction(seleteIndex, actionIndex, 'useGlobalPayloadLensIndex', e ? 1 : 0)
                        editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', wayline.Folder.payloadParam.imageFormat)
                    }} />
                </div>
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'zoom' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <div style={{ width: '20%', }} >变焦焦距：</div>
                <Slider min={2} max={224} step={2} style={{ width: '80%', }} value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.focalLength} onChange={(e) => { editWaypointAction(seleteIndex, actionIndex, 'focalLength', e) }} />
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'rotateYaw' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <div style={{ width: '20%', }} >偏航角：</div>
                <Slider min={-180} max={180} value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.aircraftHeading} onChange={(e) => {
                    editWaypointAction(seleteIndex, actionIndex, 'aircraftHeading', e)
                    setAirplaneModelheading(e)
                }} style={{ width: '80%' }} />
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'gimbalRotate' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <div style={{ width: '20%', }} >俯仰角：</div>
                <Slider min={-90} max={35} value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.gimbalPitchRotateAngle} onChange={(e) => {
                    editWaypointAction(seleteIndex, actionIndex, 'gimbalPitchRotateAngle', e)
                    setAirplaneModelPitch(e)
                }} style={{ width: '80%' }} />
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'hover' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <div style={{ width: '20%', }} >悬停时间：</div>
                <InputNumber min={0} value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.hoverTime} onPressEnter={(e) => { editWaypointAction(seleteIndex, actionIndex, 'hoverTime', Number(e.target.value)) }} style={{ width: '80%' }} />
            </div>}
            {wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFunc === 'panoShot' && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                <Checkbox.Group
                    disabled={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex}
                    value={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex}
                    onChange={(e) => { editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', e) }}>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'visable'}
                        value={'visable'}
                        style={{ fontSize: '14px', marginRight: '10px' }}>可见光</Checkbox>
                    <Checkbox
                        disabled={wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex.length === 1 && wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.payloadLensIndex[0] === 'ir'}
                        value={'ir'}
                        style={{ fontSize: '14px', }}>红外光</Checkbox>
                </Checkbox.Group>
                <div >
                    跟随航线：<Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].actionGroup.actionList[actionIndex].actionActuatorFuncParam.useGlobalPayloadLensIndex} onChange={(e) => {
                        editWaypointAction(seleteIndex, actionIndex, 'useGlobalPayloadLensIndex', e ? 1 : 0)
                        editWaypointAction(seleteIndex, actionIndex, 'payloadLensIndex', wayline.Folder.payloadParam.imageFormat)
                    }} />
                </div>
            </div>}
        </div>}
    </div>
}
export default WaypointList;