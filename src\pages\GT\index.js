import { Modal, ConfigProvider } from "antd";

import { useModel } from "umi";
import locale from "antd/locale/zh_CN";

import "dayjs/locale/zh-cn";
import { useEffect } from "react";

import { getBodyH, isEmpty } from "@/utils/utils";
import TopMenu from "@/components/TopMenu4";
import { Get2 } from "@/services/general";
import { JCStart } from "@/pages/DJI/DRCPage/Panels/RtmpChange";
import useConfigStore from "@/stores/configStore";
import MyMenu from "@/pages/GT/components/MyMenu";
import MyHead from "@/pages/GT/components/MyHead";
import commonStyle from "@/pages/common.less";
export default function HomePage() {
  const { page, modal, open, setOpen, lastPage } = useModel("pageModel");
  const initConfig = useConfigStore((state) => state.initConfig);

  useEffect(() => {
    // const StartJCZB = async () => {
    //     await HGet2('/api/v1/RtmpSource/Close1')
    //     HGet2('/api/v1/RtmpSource/Start3')
    // };
    initConfig();
    const startJC = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      JCStart(device);
    };

    startJC();

    const CloseRtmp = () => {
      const device = JSON.parse(localStorage.getItem("device"));
      if (isEmpty(device)) return;
      Get2(
        `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
      );
      // Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
    };

    window.addEventListener("beforeunload", (event) => {
      CloseRtmp();
    });

    document.body.addEventListener(
      "touchmove",
      function (e) {
        e.preventDefault();
      },
      { passive: false }
    );

    document.body.addEventListener("popstate", function (event) {
      lastPage();
      // e.stopPropagation();
    });
  }, []);

  return (
    <div className="gt-page">
      <div
        style={{ width: "100%", overflow: "hidden", height: "100vh" }}
        className={commonStyle.gt_back}
      >
        <ConfigProvider locale={locale}>
          {/* <TopMenu></TopMenu> */}
          <MyHead></MyHead>
          <div
            style={{
              width: "100%",
              height: "100%",
              display: "flex",
              background:"#001529",
            }}
          >
            <MyMenu></MyMenu>
            <div style={{ width: "100%", height: getBodyH(56) }}>
              {page}
              <Modal
                title={null}
                footer={null}
                onOk={null}
                style={{ paddingBottom: 72.0 }}
                open={open}
                onCancel={() => setOpen(false)}
              >
                {modal}
              </Modal>
            </div>
          </div>
        </ConfigProvider>
      </div>
    </div>
    
  );
}
