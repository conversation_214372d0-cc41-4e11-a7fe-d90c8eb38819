.videoPanel {
  position: absolute;
  top: 20px;
  left: 10px;
  width: 750px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

// 右侧定位样式
.videoPanelRight {
  left: auto;
  right: 10px;
}

.videoHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(8, 231, 203, 0.6);
  color: white;
}

.videoHeader button {
  color: white;
}

.title {
  font-weight: 500;
  font-size: 14px;
}

.btnPanel {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 4px;
  background-color: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.btnPanel button {
  color: white;
  font-size: 12px;
  padding: 2px 8px;
}

.videoContainer {
  height: 240px;
  width: 100%;
  background-color: #000;
}

.pipContainer {
  // height: 100%;
  min-height: 420px;
  width: 100%;
  background-color: #000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
} 