import { arrIndex } from "./helper";
import DJRoutes from "./DJRoute";

function findRouteByTitle(routes, title) {
  for (const route of routes) {
    if (route.title === title) {
      return route;
    }
    if (route.routes && route.routes.length > 0) {
      const found = findRouteByTitle(route.routes, title);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

export function queryPage(params) {
  const result = findRouteByTitle(DJRoutes, params);
  if (result) {
    return result || null;
  }
  return null;
}

// export function queryPage(params) {
//   let i = arrIndex(DJRoutes, "title", params);
//   if (i === null) {
//     if(DJRoutes[i].routes && DJRoutes[i].routes && DJRoutes[i].routes.length > 0){
//       i = arrIndex(DJRoutes[i].routes, "title", params);
//       if (i) {
//         return DJRoutes[i].routes[i].children;
//       }
//     }
//   }
//   return DJRoutes[i];
// }

export function queryPage2(params) {
  const i = arrIndex(DJRoutes, "title", params);
  if (i === null) {
    return <div />;
  }
  localStorage.setItem("DJRouterIndex", JSON.stringify(i));
  return DJRoutes[i].children;
}

export function queryPageByPath(params) {
  const i = arrIndex(DJRoutes, "key", params);
  if (i === null) {
    return null;
  }
  return DJRoutes[i];
}
