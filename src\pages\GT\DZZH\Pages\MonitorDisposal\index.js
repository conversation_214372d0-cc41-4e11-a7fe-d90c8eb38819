import { useEffect, useState } from "react";
import LeafletMap from "@/pages/GF/GFDZ/pages/Maps/LeafletMap";
import LeftPage from "@/pages/GF/GFDZ/pages/RealTimeMonitoring/LeftPage/LeftPage";
import RightPage from "@/pages/GT/DZZH/Pages/MonitorDisposal/RightPage/RightPage";
import MyModal from "@/pages/GT/components/MyModal";
import LeftBox from "@/pages/GT/components/LeftBox";
import RightBox from "@/pages/GT/components/RightBox";
import { Select, Space, ConfigProvider } from "antd";
import { axiosApi } from '@/services/general';

export default function DefectManagement() {
  const [isMyModalOpen, setIsMyModalOpen] = useState(true);
  const [isMyModalOpen2, setIsMyModalOpen2] = useState(true);
  const [planOptions, setPlanOptions] = useState([])

  const closeMyModal = () => {
    setIsMyModalOpen(false);
  };

  const handleChange = value => {
    console.log(`Selected: ${value}`);
    getGetByTaskId(value);
  };
  useEffect(() => {
    getPlanOptions();
  }, [])
  
  const getGetByTaskId = async (taskId) => {
    let res = await axiosApi(`/api/v1/Wayline/GetByTaskId?ID=${taskId}`, "GET", null);
    console.log("??????", res);
  }
  const getPlanOptions = async () => {
    const res = await axiosApi('/api/v1/surveytask/GetAllList', 'GET', null);
    console.log("/surveytask/GetAllList", res);
    if (res.data) {
      setPlanOptions(res.data)
    }
  }
  let options1 = planOptions?.map((item, index) => ({ label: item.TaskDesc, value: item.TaskID }))
  let selectArr = [
    {
      placeholder: "区域",
      options:options1
    },
    {
      placeholder: "时间范围",
      options: [
        {
          value: "区域",
          label: "区域",
        },
        {
          value: "lucy",
          label: "Lucy",
        },
      ],
    },
    {
      placeholder: "缺陷类型",
      options: [
        {
          value: "区域",
          label: "区域",
        },
        {
          value: "lucy",
          label: "Lucy",
        },
      ],
    },
    // {
    //   placeholder: "处理状态",
    //   options: [
    //     {
    //       value: "区域",
    //       label: "区域",
    //     },
    //     {
    //       value: "lucy",
    //       label: "Lucy",
    //     },
    //   ],
    // },
  ];
  return (
    <div>
      <div style={{ position: "absolute", zIndex: 1, width: "100vw" }}>
        <LeafletMap />
      </div>
      <RightBox child={<RightPage />} />

      <div
        style={{
          display: "flex",
          justifyContent: "flex-start",
          alignItems: "center",
          gap: 30,
          position: "absolute",
          left: 10,
          top: 10,
          zIndex: 1,
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 30,
            padding: "10px",
            border: "1px solid #1abc9c",
            borderRadius: "5px",
            fontSize: "14px",
            background: "rgba(52, 49, 51, 0.8)",
          }}
        >
          <div>缺陷统计：</div>
          <div style={{ color: "#ccc" }}>
            今日新增:{" "}
            <span style={{ textDecoration: "underline", color: "#fff" }}>
              12
            </span>
          </div>
          <div style={{ color: "#ccc" }}>
            待处理:{" "}
            <span style={{ textDecoration: "underline", color: "red" }}>
              28
            </span>
          </div>
          <div style={{ color: "#ccc" }}>
            已修复:{" "}
            <span style={{ textDecoration: "underline", color: "#3bb19c" }}>
              156
            </span>
          </div>
        </div>
          {selectArr.map((item, index) => {
            return (
              <Select
                key={index}
                placeholder={item.placeholder}
                style={{
                  width: 120,
                }}
                onChange={handleChange}
                options={item.options}
              />
            );
          })}
      </div>
      <MyModal
        title={"001无人机"}
        status={"组件过热"}
        content={
          <div>
            <div>无人机编号:UAV-0025</div>
            <div>‌组件编号‌：YX-202503-25-36-001</div>
            <div>实时发电效率‌：98.2%（理论值：150W）</div>
            <div>热成像温度‌：42.5℃（环境温度：25℃）</div>
            <div>
              故障记录：2025/03/19{" "}
              <span style={{ textDecoration: "underline", color: "red" }}>
                遮挡故障（待处理）
              </span>
            </div>
            <div>最近巡检时间：2025/03/19 10:15:0</div>
          </div>
        }
        isOpen={isMyModalOpen2}
        onClose={() => {
          setIsMyModalOpen2(false);
        }}
        isCenterBtn={"无人机核查"}
      />
    </div>
  );
}
