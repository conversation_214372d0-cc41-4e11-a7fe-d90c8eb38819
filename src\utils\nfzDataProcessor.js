/**
 * 禁飞区数据处理器
 * 处理DJI API返回的复杂数据结构，标准化为统一格式
 */

/**
 * 禁飞区级别对应的颜色和透明度配置
 */
const NFZ_LEVEL_CONFIG = {
  '#DE4329': { color: '#DE4329', opacity: 0.5, name: '禁飞区' },
  '#979797': { color: '#979797', opacity: 0.5, name: '限高区' },
  '#1088F2': { color: '#1088F2', opacity: 0.5, name: '授权区' },
  '#E8D483': { color: '#E8D483', opacity: 0.5, name: '警示区' },
  '#EE8815': { color: '#EE8815', opacity: 0.5, name: '加强警示区' },
  '#6EC4D3': { color: '#6EC4D3', opacity: 0.5, name: '法规限制区' },
  '#4BC04B': { color: '#4BC04B', opacity: 0.5, name: '法规适飞区' },
  '#377A26': { color: '#377A26', opacity: 0.5, name: '风景示范区' }
};

/**
 * 禁飞区形状类型
 */
const NFZ_SHAPE_TYPES = {
  1: 'polygon',  // 多边形
  2: 'circle'    // 圆形
};

/**
 * 处理DJI API返回的禁飞区数据
 * @param {Object} apiResponse - DJI API响应数据
 * @returns {Array} 标准化的禁飞区数据数组
 */
export function processNFZData(apiResponse) {
  if (!apiResponse || !apiResponse.data || !apiResponse.data.areas) {
    console.warn('NFZDataProcessor: Invalid API response data');
    return [];
  }

  const areas = apiResponse.data.areas;
  const processedAreas = [];

  areas.forEach((area, index) => {
    try {
      const processedArea = processArea(area, index);
      if (processedArea) {
        processedAreas.push(processedArea);
      }
    } catch (error) {
      console.error('NFZDataProcessor: Error processing area:', area, error);
    }
  });

  return processedAreas;
}

/**
 * 处理单个禁飞区域
 * @param {Object} area - 原始区域数据
 * @param {number} index - 区域索引
 * @returns {Object|null} 处理后的区域数据
 */
function processArea(area, index) {
  const baseInfo = {
    id: area.area_id || `nfz_${index}`,
    name: area.name || `禁飞区 ${index + 1}`,
    type: area.type || 0,
    level: area.level || 0,
    color: area.color || '#DE4329',
    description: area.description || '',
    country: area.country || '',
    city: area.city || '',
    address: area.address || '',
    url: area.url || '',
    beginAt: area.begin_at || 0,
    endAt: area.end_at || 0,
    dataSource: area.data_source || '',
    height: area.height || 0,
    originalData: area,
  };

  // 获取级别配置
  const levelConfig = NFZ_LEVEL_CONFIG[baseInfo.color] || NFZ_LEVEL_CONFIG['#DE4329'];
  baseInfo.levelConfig = levelConfig;

  // 处理主区域几何形状
  const mainGeometry = processGeometry(area);
  if (mainGeometry) {
    return {
      ...baseInfo,
      geometry: mainGeometry,
      subAreas: processSubAreas(area.sub_areas || [], baseInfo)
    };
  }

  // 如果主区域没有几何形状，但有子区域，则处理子区域
  if (area.sub_areas && area.sub_areas.length > 0) {
    const subAreas = processSubAreas(area.sub_areas, baseInfo);
    if (subAreas.length > 0) {
      return {
        ...baseInfo,
        geometry: null,
        subAreas: subAreas
      };
    }
  }

  console.warn('NFZDataProcessor: Area has no valid geometry:', area);
  return null;
}

/**
 * 处理几何形状数据
 * @param {Object} area - 区域数据
 * @returns {Object|null} 几何形状对象
 */
function processGeometry(area) {
  // 处理圆形区域
  if (area.lat && area.lng && area.radius) {
    return {
      type: 'circle',
      center: [area.lng, area.lat],
      radius: area.radius,
      color: area.color || NFZ_LEVEL_CONFIG[area.color]?.color || '#DE4329',
      opacity: NFZ_LEVEL_CONFIG[area.color]?.opacity || 0.5
    };
  }

  // 处理多边形区域（通过polygon_points）
  if (area.polygon_points && area.polygon_points.length > 0) {
    const coordinates = processPolygonPoints(area.polygon_points);
    if (coordinates) {
      return {
        type: 'polygon',
        coordinates: coordinates,
        color: area.color || NFZ_LEVEL_CONFIG[area.color]?.color || '#DE4329',
        opacity: NFZ_LEVEL_CONFIG[area.color]?.opacity || 0.5
      };
    }
  }

  return null;
}

/**
 * 处理子区域数据
 * @param {Array} subAreas - 子区域数组
 * @param {Object} parentInfo - 父区域信息
 * @returns {Array} 处理后的子区域数组
 */
function processSubAreas(subAreas, parentInfo) {
  if (!Array.isArray(subAreas)) return [];

  return subAreas.map((subArea, index) => {
    const subInfo = {
      id: `${parentInfo.id}_sub_${index}`,
      name: `${parentInfo.name} - 子区域 ${index + 1}`,
      type: parentInfo.type,
      level: subArea.level || parentInfo.level,
      height: subArea.height || parentInfo.height,
      parentId: parentInfo.id,
      color: subArea.color || parentInfo.color
    };

    // 获取级别配置
    const levelConfig = NFZ_LEVEL_CONFIG[subInfo.color] || NFZ_LEVEL_CONFIG['#DE4329'];
    subInfo.levelConfig = levelConfig;

    // 处理子区域几何形状
    let geometry = null;

    // 圆形子区域
    if (subArea.shape === 2 && subArea.lat && subArea.lng && subArea.radius) {
      geometry = {
        type: 'circle',
        center: [subArea.lng, subArea.lat],
        radius: subArea.radius,
        color: subArea.color || levelConfig.color,
        opacity: levelConfig.opacity
      };
    }
    // 多边形子区域
    else if (subArea.polygon_points && subArea.polygon_points.length > 0) {
      const coordinates = processPolygonPoints(subArea.polygon_points);
      if (coordinates) {
        geometry = {
          type: 'polygon',
          coordinates: coordinates,
          color: subArea.color || levelConfig.color,
          opacity: levelConfig.opacity
        };
      }
    }

    return geometry ? { ...subInfo, geometry } : null;
  }).filter(Boolean);
}

/**
 * 处理多边形坐标点
 * @param {Array} polygonPoints - 多边形坐标点数组
 * @returns {Array|null} 处理后的坐标数组
 */
function processPolygonPoints(polygonPoints) {
  if (!Array.isArray(polygonPoints) || polygonPoints.length === 0) {
    return null;
  }

  try {
    // DJI API返回的格式通常是 [[[lng, lat], [lng, lat], ...]]
    const firstRing = polygonPoints[0];
    if (!Array.isArray(firstRing)) {
      return null;
    }

    // 验证坐标点格式
    const validCoordinates = firstRing.filter(point => {
      return Array.isArray(point) && 
             point.length >= 2 && 
             typeof point[0] === 'number' && 
             typeof point[1] === 'number' &&
             point[0] >= -180 && point[0] <= 180 &&
             point[1] >= -90 && point[1] <= 90;
    });

    if (validCoordinates.length < 3) {
      console.warn('NFZDataProcessor: Polygon has less than 3 valid points');
      return null;
    }

    // 确保多边形闭合
    const lastPoint = validCoordinates[validCoordinates.length - 1];
    const firstPoint = validCoordinates[0];
    if (lastPoint[0] !== firstPoint[0] || lastPoint[1] !== firstPoint[1]) {
      validCoordinates.push([firstPoint[0], firstPoint[1]]);
    }

    return [validCoordinates]; // GeoJSON格式需要数组包装
  } catch (error) {
    console.error('NFZDataProcessor: Error processing polygon points:', error);
    return null;
  }
}

/**
 * 按级别分组禁飞区数据
 * @param {Array} processedAreas - 处理后的区域数组
 * @returns {Object} 按级别分组的数据
 */
export function groupAreasByLevel(processedAreas) {
  const grouped = {};
  
  processedAreas.forEach(area => {
    const level = area.color;
    if (!grouped[level]) {
      grouped[level] = {
        level: level,
        config: NFZ_LEVEL_CONFIG[level] || NFZ_LEVEL_CONFIG['#DE4329'],
        areas: []
      };
    }
    grouped[level].areas.push(area);
    
    // 处理子区域
    if (area.subAreas && area.subAreas.length > 0) {
      area.subAreas.forEach(subArea => {
        const subLevel = subArea.color;
        if (!grouped[subLevel]) {
          grouped[subLevel] = {
            level: subLevel,
            config: NFZ_LEVEL_CONFIG[subLevel] || NFZ_LEVEL_CONFIG['#DE4329'],
            areas: []
          };
        }
        grouped[subLevel].areas.push(subArea);
      });
    }
  });

  return grouped;
}

/**
 * 过滤指定级别的禁飞区
 * @param {Array} processedAreas - 处理后的区域数组
 * @param {Array} levels - 要包含的级别数组
 * @returns {Array} 过滤后的区域数组
 */
export function filterAreasByLevel(processedAreas, levels) {
  if (!Array.isArray(levels)) return processedAreas;
  
  return processedAreas.filter(area => {
    // 检查主区域级别
    if (levels.includes(area.level)) return true;
    
    // 检查子区域级别
    if (area.subAreas && area.subAreas.length > 0) {
      return area.subAreas.some(subArea => levels.includes(subArea.level));
    }
    
    return false;
  });
}

/**
 * 获取禁飞区统计信息
 * @param {Array} processedAreas - 处理后的区域数组
 * @returns {Object} 统计信息
 */
export function getNFZStatistics(processedAreas) {
  const stats = {
    total: 0,
    byLevel: {},
    byType: { circle: 0, polygon: 0 },
    byShape: {}
  };

  processedAreas.forEach(area => {
    stats.total++;
    
    // 按级别统计
    const level = area.level;
    stats.byLevel[level] = (stats.byLevel[level] || 0) + 1;
    
    // 按类型统计
    if (area.geometry) {
      stats.byType[area.geometry.type] = (stats.byType[area.geometry.type] || 0) + 1;
    }
    
    // 统计子区域
    if (area.subAreas && area.subAreas.length > 0) {
      area.subAreas.forEach(subArea => {
        stats.total++;
        const subLevel = subArea.level;
        stats.byLevel[subLevel] = (stats.byLevel[subLevel] || 0) + 1;
        
        if (subArea.geometry) {
          stats.byType[subArea.geometry.type] = (stats.byType[subArea.geometry.type] || 0) + 1;
        }
      });
    }
  });

  return stats;
}

export default {
  processNFZData,
  groupAreasByLevel,
  filterAreasByLevel,
  getNFZStatistics,
  NFZ_LEVEL_CONFIG,
  NFZ_SHAPE_TYPES
};
