import { HGet2, HPost2 } from '@/utils/request';
import request from '@/utils/request2';
import axios from "axios";


export async function Get(params) {
  const { url } = params;
  return request(url, {
    method: 'GET',
  });
}

export async function Get2(url) {
  return HGet2(url);
  // return request(url, {
  //   method: 'GET',
  // });
}
// query
export async function Get3(params) {
  const { url, ...queryParams } = params;
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`${url}?${queryString}`, {
    method: 'GET',
  });
}

export async function GetList(params) {
  const { kbmc } = params;

  return request(`/api/${kbmc}/GetList`, {
    // return request(`/api/event/list/`, {
    method: 'GET',
    body: {
      ...params,
    },
  });
}

export async function Detail(params) {
  const { kbmc } = params;


  return request(`/api/${kbmc}/Detail`, {
    // return request(`/api/event/list/`, {
    method: 'GET',
    body: {
      ...params,
    },
  });
}

export async function GetByWhere(params) {
  const { kbmc, Where, obj } = params;


  return request(`/api/${kbmc}/GetByWhere`, {
    // return request(`/api/event/list/`, {
    method: 'POST',
    body: {
      Where,
      obj,
    },
  });
}



export async function GetCountByWhere(data) {
  const { kbmc, Where, obj } = data;


  return request(`/api/${kbmc}/GetCountByWhere`, {
    // return request(`/api/event/list/`, {
    method: 'POST',
    body: {
      Where,
      obj,
    },
  });
}




export function DownLoadFile(url) {
  return new Promise((resolve, reject) => {
    axios({
      url: url,
      method: "GET",
      responseType: "arraybuffer",
    })
      .then((data) => {
        resolve(data.data);
      })
      .catch((error) => {
        reject(error.toString());
      });
  });
};

export async function GetListByWhere(data) {
  const { kbmc, Where, obj, PageIndex, PageSize } = data;


  return request(`/api/${kbmc}/GetListByWhere`, {
    // return request(`/api/event/list/`, {
    method: 'POST',
    body: {
      Where,
      obj,
      PageIndex,
      PageSize,
    },
  });
}

export async function GetListBySpaceId(params) {
  const { kbmc, SpaceId } = params;

  return request(`/api/${kbmc}/GetListBySpaceId?SpaceId=${SpaceId}`, {
    // return request(`/api/event/list/`, {
    method: 'GET',
    body: {
      method: 'get',
    },
  });
}

export async function Add(params) {
  // return request(`/api/news/news_list`);
  const { kbmc } = params;
  // console.log('kbmc', kbmc, params);

  return request(`/api/${kbmc}/Add`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function Update(params) {
  // return request(`/api/news/news_list`);

  const { kbmc } = params;

  return request(`/api/${kbmc}/Update`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function Post(params) {
  // return request(`/api/news/news_list`);

  const { url } = params;
  console.log("url", params);
  return HPost2(url, { ...params });
  return request(url, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function Post2(url, params) {
  // return request(`/api/news/news_list`);
  return HPost2(url, { ...params });
  return request(url, {
    method: 'POST',
    body: {
      ...params
    },

  });
}

export async function Post3(url, params) {
  // return request(`/api/news/news_list`);
  return HPost2(url, params);
}


export async function Delete(params) {

  const { kbmc } = params;


  return request(`/api/${kbmc}/Delete`, {
    method: 'POST',
    body: {
      params,
    },
  });
}

// 下载图片
export function getImgFile(url) {
  return new Promise((resolve, reject) => {
    axios({
      url: `https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/${url}`,
      method: "GET",
      responseType: "arraybuffer",
    })
      .then((data) => {
        resolve(data.data);
      })
      .catch((error) => {
        reject(error.toString());
      });
  });
};
// export function axiosApi(url, method, data) {
//   const token = localStorage.getItem('token');
//   return new Promise((resolve, reject) => {
//     axios({
//       url: url,
//       method: method,
//       headers: {
//         'auth': token,
//         'Access-Control-Allow-Origin': '*',
//         'Access-Control-Allow-Methods': '*',
//         'Content-Type': 'application/json;charset=UTF-8',
//       },
//       ...(method === 'GET' ? { params: data } : { data }) 
//     })
//     .then((response) => {
//       resolve(response.data);
//     })
//     .catch((error) => {
//       reject(error.toString());
//     });
//   });
// };

export function axiosApi(url, method, data, config = {}) {  // 添加config参数 可以自定义headers
  const token = localStorage.getItem('token');
  return new Promise((resolve, reject) => {
    axios({
      url: url,
      method: method,
      headers: {
        'auth': token,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
        ...config.headers, // 合并传入的headers配置
      },
      ...(method === 'GET' ? { params: data } : { data }),
      ...config // 展开其他配置
    })
    .then((response) => {
      resolve(response.data);
    })
    .catch((error) => {
      reject(error.toString());
    });
  });
}

export const getMapDataBySN = async (device) => {
  // 通过机场SN获取地图正射影像，三维模型数据
  try {
    const res = await axiosApi("/api/v2/MapData/MapData2GetBySN", "GET", {
      sn: device?.SN,
      OrgCode: device?.OrgCode,
    });
    return res;
  } catch (error) {
    console.error("Error in getMapDataBySN:", error);
    throw error; 
  }
};
