/* 列表容器样式 */
.record-list-container {
  max-height: 70vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  border: none;
}

.record-list-container .ant-table-row {
  transition: all 0.3s;
}

.record-list-container .ant-table-row:hover {
  background-color: #ffffff0a !important;
}

.record-list-container .selected {
  outline: 2px solid #1890ff;
  background-color: #ffffff0a !important;
}


/* 分页容器 */
.record-pagination {
  padding: 16px 0;
  background-color: transparent;
  border: none;
  border-top: 0;
  border-radius: 0 0 4px 4px;
}

/* 日期选择器容器 */
.date-picker-group {
  padding: 0 0 16px 0;
  display: flex;
  gap: 8px;
  width: 100%;
}

.fly-table {
  height: 70vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.ai-table {
  height: 70vh;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}