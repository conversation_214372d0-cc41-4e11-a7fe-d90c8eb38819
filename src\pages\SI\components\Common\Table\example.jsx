import React, { useState } from 'react';
import { Space, Tag, Button, message, Card, Divider, Table } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import DynamicDataTable from './index';

const TableExample = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 模拟数据
  const dataSource = [
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '北京市朝阳区建国门外大街88号，这是一个很长的地址信息，用来测试省略号功能',
      status: 'active',
      tags: ['开发者', '技术'],
      email: 'zhang<PERSON>@example.com',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '上海市浦东新区陆家嘴金融贸易区',
      status: 'inactive',
      tags: ['设计师'],
      email: '<EMAIL>',
    },
    {
      key: '3',
      name: '王五',
      age: 35,
      address: '广州市天河区珠江新城CBD核心区域',
      status: 'active',
      tags: ['产品经理', '策划'],
      email: '<EMAIL>',
    },
    {
      key: '4',
      name: '赵六',
      age: 29,
      address: '深圳市南山区粤海街道科技园',
      status: 'active',
      tags: ['运营'],
      email: '<EMAIL>',
    },
    {
      key: '5',
      name: '钱七',
      age: 26,
      address: '杭州市西湖区文三路',
      status: 'inactive',
      tags: ['测试', '质量'],
      email: '<EMAIL>',
    },
  ];

  // 列配置 - 简洁明了
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      fixed: 'left',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      // ellipsis 会自动添加
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      // 长文本会自动显示省略号和 tooltip
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '不活跃'}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 200,
      render: (tags) => (
        <Space>
          {tags.map(tag => (
            <Tag key={tag} color="blue">{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => message.info(`查看 ${record.name}`)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => message.info(`编辑 ${record.name}`)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 工具栏配置 - 新的简化API
  const toolbar = {
    buttons: [
      {
        key: 'add',
        text: '新增用户',
        type: 'primary',
        icon: <PlusOutlined />,
        onClick: () => message.success('点击了新增按钮')
      },
      {
        key: 'edit',
        text: '编辑',
        icon: <EditOutlined />,
        needsSelection: true,
        selectionMessage: '请选择要编辑的用户',
        onClick: (keys) => message.success(`编辑 ${keys.length} 个用户`)
      },
      {
        key: 'delete',
        text: '批量删除',
        icon: <DeleteOutlined />,
        type: 'primary',
        danger: true,
        needsSelection: true,
        selectionMessage: '请选择要删除的用户',
        onClick: (keys) => message.success(`删除了 ${keys.length} 个用户`)
      }
    ],
    align: 'left'
  };

  // 选择配置 - 直接使用 Ant Design API
  const selection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ]
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>DynamicDataTable 优化版本示例</h1>
      <p style={{ marginBottom: '32px', color: '#666' }}>
        展示优化后的表格组件 - 代码量减少70%，API更简洁，性能更好
      </p>

      <Card title="1. 基础表格" style={{ marginBottom: '24px' }}>
        <p>最简单的用法，自动处理省略号和 tooltip</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Card title="2. 带工具栏和选择功能" style={{ marginBottom: '24px' }}>
        <p>新的 toolbar 和 selection 配置方式</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          toolbar={toolbar}
          selection={selection}
          scroll={{ x: 1000 }}
        />
        <div style={{ marginTop: '16px', color: '#666' }}>
          已选择: {selectedRowKeys.length} 项
        </div>
      </Card>

      <Card title="3. 紧凑型表格 + 边框" style={{ marginBottom: '24px' }}>
        <p>小尺寸表格，显示边框</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          size="small"
          bordered
          scroll={{ x: 1000 }}
        />
      </Card>

      <Card title="4. 带分页功能" style={{ marginBottom: '24px' }}>
        <p>启用分页，所有 Ant Design Table 属性都支持</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          pagination={{
            pageSize: 3,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Card title="5. 禁用斑马线条纹" style={{ marginBottom: '24px' }}>
        <p>设置 striped={false} 禁用斑马线</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          striped={false}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Card title="6. 工具栏对齐方式" style={{ marginBottom: '24px' }}>
        <p>工具栏右对齐，展示不同的对齐选项</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          toolbar={{
            ...toolbar,
            align: 'right'
          }}
          selection={selection}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Divider />

      <h2>API 对比</h2>
      <div style={{ background: '#fff', padding: '16px', borderRadius: '6px' }}>
        <h3>旧版本（复杂）：</h3>
        <pre style={{ background: '#f8f8f8', padding: '12px', borderRadius: '4px' }}>
{`<DynamicDataTable
  dataSource={data}
  columns={columns}
  showToolbar={true}
  toolbarButtons={buttons}
  toolbarAlign="left"
  showSelectionInfo={true}
  rowSelection={rowSelection}
  // ... 还需要 30+ 个其他 props
/>`}
        </pre>

        <h3 style={{ marginTop: '24px' }}>新版本（简洁）：</h3>
        <pre style={{ background: '#f0f9ff', padding: '12px', borderRadius: '4px' }}>
{`<DynamicDataTable
  dataSource={data}
  columns={columns}
  toolbar={{ buttons, align: 'left' }}
  selection={rowSelection}
  striped
  // 所有 Ant Design Table 属性都直接支持
/>`}
        </pre>
      </div>
    </div>
  );
};

export default TableExample; 