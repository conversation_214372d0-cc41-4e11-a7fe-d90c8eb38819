import React, { Fragment, useState, useEffect } from "react";

import {
  Breadcrumb,
  Select,
  DatePicker,
  Card,
  Input,
  Tag,
  Descriptions,
  Row,
  Col,
  Button,
  Form,
  message,
  Table,
  Modal,
} from "antd";

import {
  getBodyH,
  getDeviceName,
  isEmpty,
  WayLineTypeToString,
} from "@/utils/utils";
import CommonCard from '@/components/CommonCard';
import { DynamicDataTable } from '@/pages/SI/components/Common';

import WayLineAddForm from "./form_add";
import TableCols from "./table.js";
import useConfigStore from "@/stores/configStore";
import { HGet2 } from "@/utils/request";
import TaskMediaPanel from "./TaskMediaPanel2";
import { Get2 } from "@/services/general";
import { useModel } from "umi";
import MediaListPanel from "./../MediaDataPage/MediaListPanel";
import LastPageButton from "@/components/LastPageButton";
import dayjs from "dayjs";
import { FastBackwardFilled } from "@ant-design/icons";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import AddButton from "@/components/AddButton";

const { RangePicker } = DatePicker;

const TaskListPage = ({ doNotShowLastButton }) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  let para1 = {};
  const localPara = localStorage.getItem("PageParams");
  if (!isEmpty(localPara)) {
    para1 = JSON.parse(localPara);
  }

  const [ifLoad, setIfLoad] = useState(true);
  const [canSee2, setCanSee2] = useState(false);
  const [tList, setTList] = useState([]);
  const [index, setIndex] = useState({});
  const [mList, setMList] = useState({});

  const [params, setParams] = useState(para1);
  const [xList, setXList] = useState([]);
  const [ifNew, setIfNew] = useState(false);
  const { setPage, lastPage } = useModel("pageModel");
  const [pageSize, setPageSize] = useState(10); // 每页显示条数


  const handleTableChange = (current) => {
    setTableCurrent(current);
  };

  const ifPush = (e) => {
    let t1 = dayjs("1900/1/1");
    let t2 = dayjs("2900/1/1");
    if (!isEmpty(params.sDate)) {
      t1 = dayjs(params.sDate[0]);
      t2 = dayjs(params.sDate[1]);
    }

    if (!isEmpty(params.sWay)) {
      if (e.FlightLineName != params.sWay) {
        return false;
      }
    }
    if (!isEmpty(params.sDate)) {
      const t3 = dayjs(e.CreateTime);
      if (t1.isAfter(t3) || t2.isBefore(t3)) {
        return false;
      }
    }
    if (!isEmpty(params.sJC)) {
      if (e.DeviceSN != params.sJC) {
        return false;
      }
    }

    if (!isEmpty(params.sType)) {
      if (e.FlightLineType != params.sType) {
        return false;
      }
    }
    if (e.TaskState == 0) {
      return false;
    }
    return true;
  };

  const getXL = (data) => {
    const xL = [];
    data.forEach((e) => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    return xL;
  };

  useEffect(() => {
    const xx = () => {
      const xL = getXL(tList);
      setXList([...xL]);
    };
    localStorage.setItem("PageParams", JSON.stringify(params));
    xx();
  }, [params, ifNew]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const yy = async () => {
      let xx = await HGet2(`/api/v1/Task/GetAllList`);
      setIfLoad(false);
      if (isEmpty(xx)) xx = [];
      setTList(xx);
      const xL = getXL(xx);
      console.log([...xL]);

      setXList([...xL]);
    };

    yy();
  }, []);

  const refrush = async () => {
    const pst = await Get2("/api/v1/Task/GetAllList", {});
    if (isEmpty(pst)) return;

    setTList(pst);
    setIfNew(!ifNew);
  };

  const exr = (
    <div>
      <Button type="primary" onClick={() => refrush()}>
        刷新
      </Button>{" "}
    </div>
  );

  const hxzp = (list) => {
    return (
      <Modal
        title={null}
        footer={null}
        onOk={null}
        width={1000.0}
        height={600.0}
        open={canSee2}
        onCancel={() => setCanSee2(false)}
      >
        {isEmpty(list) ? <div></div> : TaskMediaPanel(mList)}
      </Modal>
    );
  };

  const onRow = (record) => {
    return {
      onClick: async () => {
        setIndex(record);
        const yy = await HGet2(
          `/api/v1/Media/GetListByTaskId?id=${record.TaskID}`
        );
        console.log("onRow", yy);
        setMList(yy);
      }, // 点击行
    };
  };

  const hClick = async (record) => {
    // console.log(canSee2)
    const yy = await HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`);
    // console.log('onRow',yy)
    if (isEmpty(yy)) {
      message.info("该航线未拍摄照片");
      return;
    }
    setMList(yy);
    setCanSee2(true);
  };

  const getWaySelect = (wayList, getLabel) => {
    const list = [];
    wayList.forEach((e) => {
      if (!getLabel(e)) return; //如果机场名称为空，则不显示
      list.push(
        <Select.Option key={e} data={e}>
          {getLabel(e)}
        </Select.Option>
      );
    });
    console.log("CronAddForm", list);

    return list;
  };

  const getExr = () => {
    let wList = [];
    let dList = [];
    let pList = [];

    tList.forEach((e) => {
      if (!wList.includes(e.FlightLineName)) {
        wList.push(e.FlightLineName);
      }
      if (!pList.includes(e.FlightLineType)) {
        pList.push(e.FlightLineType);
      }
      if (!dList.includes(e.DeviceSN)) {
        dList.push(e.DeviceSN);
      }
    });

    pList = pList.map((item) => WayLineTypeToString(item));

    return (
      <div>
        <span style={{ marginLeft: 6.0 }}>
          {" "}
          <Select
            defaultValue={params.sJC}
            allowClear={true}
            style={{ width: 200 }}
            onClear={() => setParams({ ...params, sJC: "" })}
            placeholder={"选择机场"}
            onSelect={(e) => setParams({ ...params, sJC: e })}
          >
            {getWaySelect(dList, (e) => getDeviceName(e))}
          </Select>
        </span>

        <span style={{ marginLeft: 6.0 }}>
          {" "}
          <Select
            allowClear={true}
            defaultValue={params.sType}
            style={{ width: 200 }}
            onClear={() => setParams({ ...params, sType: "" })}
            placeholder={"选择航线类型"}
            onSelect={(e) => setParams({ ...params, sType: e })}
          >
            {getWaySelect(pList, (e) => e)}
          </Select>
        </span>

        <span style={{ marginLeft: 6.0 }}>
          {" "}
          <Select
            allowClear={true}
            defaultValue={params.sWay}
            style={{ width: 200 }}
            onClear={() => setParams({ ...params, sWay: "" })}
            placeholder={"选择飞行航线"}
            onSelect={(e) => setParams({ ...params, sWay: e })}
          >
            {getWaySelect(wList, (e) => e)}
          </Select>
        </span>

        <span style={{ marginLeft: 6.0 }}>
          {" "}
          <RangePicker onChange={(e) => setParams({ ...params, sDate: e })} />
        </span>
        <span style={{ marginLeft: 6.0 }}>
          <AddButton type="primary" onClick={() => refrush()}>
            刷新
          </AddButton>{" "}
        </span>
      </div>
    );
  };

  if (ifLoad) return <LoadPanel></LoadPanel>;

  const { columns, rowClassName } = TableCols(hClick, refrush, setPage);

  return (
    <div style={{ margin: 0 ,height: getBodyH(56) }}>
      <CommonCard
        title={doNotShowLastButton ? '飞行记录' : <LastPageButton title="飞行记录"></LastPageButton>}
        bordered={false}
        extra={getExr()}
      >
        <div>
          {isEmpty(tList) ? (
            <div />
          ) : (
            <DynamicDataTable
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                current: tableCurrent,
                pageSize: pageSize,
                total: xList.length,
                onChange: (page, size) => {
                  setTableCurrent(page);
                  setPageSize(size); // 更新每页显示条数
                },
                onShowSizeChange: (current, size) => {
                  setPageSize(size);
                  setTableCurrent(1); // 重置到第一页
                },
              }}
              // 我不知道为什么 这个页面加一个autoHeight 就会缩到很短 有懂的人看到这里可以尝试改一下 别的页面都是加了这条的
              // autoHeight={true}
              scroll={{ y: 'calc(100vh - 330px)', x: "auto" }}
              dataSource={xList}
              columns={columns}
              rowKey={(record) => record.ID}
              rowClassName={rowClassName}
            />
          )}
        </div>

        {hxzp(mList)}
      </CommonCard>
    </div>
  );
};

export default TaskListPage;
