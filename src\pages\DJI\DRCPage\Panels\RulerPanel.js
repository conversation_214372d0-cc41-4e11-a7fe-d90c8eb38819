import rulerImg from "@/assets/drcImgs/ruler.png";
import youbiaoImg from "@/assets/drcImgs/youbiao.png";
import { isEmpty } from "@/utils/utils";
import { useEffect, useRef, useState } from "react";
import { useModel } from "umi";


const RulerPanel=()=>{
   const [t1,setT1]=useState(20)
   const [t2,setT2]=useState(20)
   const [val,setVal]=useState(2)

   const { drc,osdData } = useModel('drcModel');
   const {cameraJT}=useModel('rtmpModel');

   const pressed=useRef(0);
   const min=0
   const max=90
   const min2=-35
   const max2=90
   const count=useRef(0)

   const {DoCMD}=useModel('cmdModel');
   const device = JSON.parse(localStorage.getItem('device'))
   
   const PToV=(p)=>{
    return ((p-min)/(max-min))*(max2-min2)+min2
   }

   const VToP=(p)=>{
    return ((p-min2)/(max2-min2))*(max-min)+min
   }


   useEffect(()=>{
   // document.addEventListener("mousemove", MouseMove, false);
   // document.addEventListener("mouseup", MouseUp, false);

      const cc = document.getElementById('ruler1');

       if ("ontouchstart" in document.documentElement) {
           cc.addEventListener("touchstart", onTouchStart, false);
           document.addEventListener("touchmove", onTouchMove, false);
           document.addEventListener("touchend", onTouchEnd, false);
       }
       else {
           cc.addEventListener("mousedown", MouseDown, false);
           document.addEventListener("mousemove", MouseMove, false);
           document.addEventListener("mouseup", MouseUp, false);
       }
       if(!isEmpty(osdData)){
        const v2=VToP(-1*osdData.current.gimbal_pitch);
        setT1(v2);
       }
       
   },[])

   useEffect(()=>{
        const v2=VToP(-1*osdData.current.gimbal_pitch);
        setT2(v2);
    },[osdData.current.gimbal_pitch])

   const MouseDown=()=>{
      pressed.current=1
   }



  const fyjCMD2 = (v1) => {

    let v2 = -1 * osdData.current.gimbal_pitch;
    let v3=1
   // 
    if(cameraJT=="zoom") v3=5;

    if (Math.abs(v2 - v1) > 1 && count.current*(v2-v1)>=0) {
      fyjCMD((v2 - v1));
      count.current=v2-v1

      setTimeout(() => {
        fyjCMD2(v1);
      }, 100);
    }
  }

   const fyjCMD=(v)=>{
    const data = {
        "locked": false,
        "payload_index": device.Camera2,
        "pitch_speed": v,
        "yaw_speed": 0
    }
    DoCMD(device.SN, "camera_screen_drag",data);
   }


  const MouseLevel = () => {
    pressed.current = 0
  }

  const getY = (y) => {
    if (y < min) y = min;
    if (y > max) y = max;
    return y - min;
  }

  const MouseMove=(event)=>{
    if(pressed.current==1){
        const xy=getMovedXY(event.pageX,event.pageY);
         const v1=xy[1];
        setT1(v1);
        const v2=PToV(v1);
        setVal(v2.toFixed(0));
    }
   }

    const getMovedXY = (x0,y0) => {
        const canvas = document.getElementById('ruler1');
        let movedX = x0;
        let movedY = y0;
        // Manage offset
        if (canvas.offsetParent.tagName.toUpperCase() === "BODY") {
            movedX -= canvas.offsetLeft;
            movedY -= canvas.offsetTop;
        }
        else {
            // 
            movedX -= canvas.offsetParent.offsetLeft;
            movedY -= canvas.offsetParent.offsetParent.offsetTop;
        }
        movedY=movedY-40
      //  console.log('fyj',movedY);

        if(movedY<min) movedY=min;
        if(movedY>max) movedY=max;
        return [movedX, movedY];
    }

    const MouseUp = (event) => {
        if (pressed.current == 1) {
         // 
            const xy=getMovedXY(event.pageX,event.pageY);
            const v1=xy[1];

            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
            pressed.current = 0
            changeFYJ(v1);
           // console.log('ruler', v1 / (max - min), v1, event.clientY);
            // if (!isEmpty(callback)) {
            //     callback(v1);
            // }
        }
    }

    function onTouchStart(event)
    {
        pressed.current=1
    }

    function onTouchMove(event)
    {
        if(pressed.current === 1)
        {
           
            const xy=getMovedXY(event.changedTouches[0].pageX,event.changedTouches[0].pageY);
            const v1=xy[1];
            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
        }
    }

    function onTouchEnd(event)
    {
        if (pressed.current == 1) {
          //  console.log('fyj',event);
            const xy=getMovedXY(event.changedTouches[0].pageX,event.changedTouches[0].pageY);
            const v1=xy[1];
            setT1(v1);
            const v2=PToV(v1);
            setVal(v2.toFixed(0));
            pressed.current = 0
            changeFYJ(v1);
        }
        
    }

   const changeFYJ=(p)=>{
        const v1=PToV(p);
        count.current=0;
        fyjCMD2(v1);
   }
   
   const getImg=()=>{
      if(pressed.current==1){
       return   <img  draggable={false} style={{cursor:'pointer', position:'absolute',top:t1,left:0}} height={8} width={15} src={youbiaoImg} ></img>
      }else{
        return  <img  draggable={false} style={{cursor:'pointer', position:'absolute',top:t2,left:0}} height={8} width={15} src={youbiaoImg} ></img>
    
      }
   }

   return  <div id="ruler1" style={{ height:96,width:30,zIndex:1010}}   onMouseDown={MouseDown} onMouseLeave={MouseLevel} >


    <img draggable={false} height={96} width={10} style={{cursor:'pointer', position:'absolute',top:0,left:8}} src={rulerImg}></img>
    {/* <img  draggable={false} style={{cursor:'pointer', position:'absolute',top:t1,left:0}} height={8} width={15} src={youbiaoImg} ></img> */}
    {getImg()}
    </div>
}

export default RulerPanel;



