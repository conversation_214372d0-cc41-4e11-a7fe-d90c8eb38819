import React from 'react';

export interface DynamicBackgroundMaskProps {
  /**
   * 额外的样式类名
   */
  className?: string;
  
  /**
   * 自定义内联样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子组件（如果需要在遮罩层上显示内容）
   */
  children?: React.ReactNode;
  
  /**
   * 其他 HTML div 元素属性
   */
  [key: string]: any;
}

/**
 * 动态监控背景遮罩组件
 * 用于为页面添加 dynamicBackgroundMask.png 背景图片遮罩效果
 */
declare const DynamicBackgroundMask: React.FC<DynamicBackgroundMaskProps>;

export default DynamicBackgroundMask; 