.appLogin {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background: url('../../../assets/bj6.webp') no-repeat;
  background-size: 100% 100%;
  object-fit: cover;
}


.appLoginForm {
  width: 420px;
  padding: 40px;
  border-radius: 11px;
  background: #fff;

  .header {
    margin-bottom: 30px;

    .title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
    }
  }
}

.videoBackground {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.GlyLogo {
  position: fixed;
  left: 50%;
  top: 85%;
  transform: translate(-50%, -85%);
  margin-top: 5%;
  width: 37%;
  height: 80px;
  // background: url(../../../assets/CompentsLogo/Gly.png);
  background-size: 100% 100%;
}

.xxx {
  background-color: red;
}

.xxx2 {
  width: 500px;
}