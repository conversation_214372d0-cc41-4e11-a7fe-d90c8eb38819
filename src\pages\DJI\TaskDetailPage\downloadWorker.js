// // downloadWorker.js
import J<PERSON><PERSON><PERSON> from 'jszip';
import { DownLoadFile } from "@/services/general";
import { getDeviceName, getImgUrl } from "@/utils/utils";


self.onmessage = async (event) => {
  const { files, maxConcurrentRequests, chunkSize } = event.data;
  const zip = new JSZip();

  const downloadFileChunk = async (url, start, end) => {
    const response = await DownLoadFile(url, { start, end });

    if (response.size > 0) {
      return await response.blob();
    }
  
  };

  for (const file of files) {
    const url = getImgUrl(file.ObjectName);
    const totalSize = files.length;
    const extensionRegex = /(\.[a-z0-9]+)$/i;

    const activeDownloads = [];

    for (let offset = 0; offset < totalSize; offset += chunkSize) {
      const end = Math.min(offset + chunkSize - 1, totalSize - 1);
      const extension = file.FileName.match(extensionRegex)?.[1] || "";
      const fileName = `${file.WayLineNM}-航点${file.HangDianIndex}${extension}`;

      const task = downloadFileChunk(url, offset, end)
        .then(blob => zip.file(fileName, blob, { binary: true }))
        .catch(error => console.error(`分片下载失败: ${fileName} [${start}-${end}]`, error))
        .finally(() => {
          activeDownloads.splice(activeDownloads.indexOf(task), 1);
        });

      activeDownloads.push(task);
      if (activeDownloads.length >= maxConcurrentRequests) {
        await Promise.race(activeDownloads);
      }
    }

    await Promise.all(activeDownloads);
  }

  const content = await zip.generateAsync({ type: "blob", compression: "DEFLATE", compressionOptions: { level: 6 } });
  self.postMessage(content);
};

// 用于获取文件大小的助手函数
const getFileSize = async (url) => {
  const response = await fetch(url, { method: 'HEAD' });
  if (!response.ok) {
    throw new Error(`获取文件大小失败: ${response.status}`);
  }
  return parseInt(response.headers.get('Content-Length'), 10);
};




