import { LockOutlined, UserOutlined, SafetyOutlined } from "@ant-design/icons";
import { Form, Input, Checkbox, Button, message, Spin, Modal } from "antd";
import { useEffect, useState, useRef, useCallback } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { history, useModel, useSearchParams, useParams } from "umi";

import styles from "./index.less";
import { HGet2, HPost2 } from "@/utils/request";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Post2, Post3 } from "@/services/general";
import { SM4Util } from "sm4util";

const LoginPage = (props) => {
  const { userLogin = {}, submitting } = props;
  const [load, setIsload] = useState(false);
  const params = new URLSearchParams(window.location.search);
  const param1 = params.get("token");
  const [captchaValue, setCaptchaValue] = useState(""); //验证码

  //SM4加密
  const GetSM4 = (data) => {
    const sm4 = new SM4Util();
    const miStr1 = sm4.encryptDefault_ECB(data);
    return miStr1;
  };

  const submitData = (data) => {
    if (isEmpty(data)) return;
    if (!isEmpty(data.err)) {
      message.info(data.err);
      if (data.err.includes("睡眠")) {
        return;
      }
      if (data.err.includes("修改")) {
        localStorage.setItem("ID", data.ID);
        history.push("/ChangePwd");
        return;
      }
      return;
    }

    localStorage.setItem("token", data.token);
    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("orgId", data.user.OrgCode);
    localStorage.setItem("orgName", data.user.OrgName);

    if (window.location.href.indexOf("token") > 0) {
      // 如果当前页面的 URL 中包含 "token" 字符串，则删除 "token" 和 "json" 参数，并重新加载页面
      let url = new URL(window.location.href); // 通过当前页面的 URL 路径，生成URL对象
      url.searchParams.delete("token");
      url.searchParams.delete("json");
      window.location.href = url.href + "#/index";
    } else {
      history.push("/index"); // 如果当前页面的 URL 中不包含 "token" 参数，则使用 history.push 方法进行页面导航
    }
    console.log(data);
  };

  const handleSubmit = async (e) => {
    /* message.info(e);
    message.info(captchaValue); */
    // const data = await HGet2(
    //   "/api/v2/User/Login?userName=" + e.userName + "&password=" + e.password
    // );
    ;
    const data = await HPost2("/api/v2/User/Login?", {
      Id: e.userName,
      Password: /* e.password */ GetSM4(e.password),
      CaptchaID: String(captchaValue.id),
      CaptchaCode: String(e.Captcha),
    });
    console.log("login", data);
    submitData(data);
  };

  //获取验证码
  const GetCaptcha = async () => {
    const data = await HPost2("/api/v2/User/GetCaptcha");
    setCaptchaValue(data);
    ;
  };

  useEffect(() => {
    const getByToken = async () => {
      const params = new URLSearchParams(window.location.search);
      const p1 = params.get("token");
      const p2 = params.get("json");

      if (isEmpty(p1)) {
        setIsload(true);
        return;
      }
      const data = await Post3("/api/v2/User/Login2", p1);

      submitData(data);
      setIsload(true);
    };
    getByToken();
    GetCaptcha();
    localStorage.removeItem("PageIndexTitle");
  }, []);

  const fff = (
    <div className={styles.appLogin}>
      <div className={styles.appLoginForm} style={{ width: 420, height: 400 }}>
        <div style={{}}>
          <h2
            style={{ width: "100%", textAlign: "center", marginBottom: 12.0 }}
            className="title"
          >
            {"无人机智能巡检与应急管理系统"}
          </h2>
        </div>
        <div className="body">
          <Form
            name="basic-login"
            // form={loginForm}
            initialValues={{ remember: true, agreement: true }}
            onFinish={handleSubmit}
            autoComplete="off"
          >
            <Form.Item
              name="userName"
              rules={[{ required: true, message: "请输入用户名" }]}
            >
              <Input
                size="large"
                prefix={<UserOutlined className="site-form-item-icon" />}
                placeholder="账户"
              />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[{ required: true, message: "请输入密码" }]}
            >
              <Input.Password
                size="large"
                prefix={<LockOutlined className="site-form-item-icon" />}
                placeholder="密码"
              />
            </Form.Item>
            <Form.Item
              name="Captcha"
              rules={[{ required: true, message: "请输入验证码" }]}
              style={{ marginBottom: 16 }} // 添加底部间距以确保布局一致
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <Input
                  size="large"
                  prefix={<SafetyOutlined className="site-form-item-icon" />}
                  placeholder="验证码"
                  style={{ flex: 1 }} // 使输入框充满可用空间
                />
                <img onClick={GetCaptcha} src={captchaValue.image} />
              </div>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" block size="large">
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );

  if (!load) {
    return <LoadPanel></LoadPanel>;
  }

  return fff;
};

export default LoginPage;
