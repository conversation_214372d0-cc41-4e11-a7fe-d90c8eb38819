import { Input, InputNumber } from 'antd';
// 中文字段映射表
export const columnTranslation = {
    ID:'ID',
    OrgCode:'组织码',
    TaskID:'任务编号',
    TaskDesc:'任务描述',
    TaskType:'任务类型',
    State:'状态',
    TableName:'任务名称',
    CreateTM:'创建时间',


};

export const TableHelper = {
    
}


// 字段组件映射表
export const FIELD_COMPONENT_MAPPING = {
    Xmjj: 'textarea',
    State: 'number',
    // ...其他字段配置
  };

export const getFieldComponent = (fieldType) => {
    switch(fieldType) {
        case 'number':
            return <InputNumber style={{ width: '100%' }} />;
        case 'textarea':
            return <Input.TextArea rows={3} />;
        default:
            return <Input />;
    }
};
