import { HGet2, HPost2 } from '@/utils/request';
import request from '@/utils/request2';
import axios from "axios";

export function axiosApi(url, method, data) {
  const token = localStorage.getItem('token');
  return new Promise((resolve, reject) => {
    axios({
      url: `${url}`,
      method: method,
      headers: {
        'auth': token,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
      },
      ...(method === 'GET' ? { params: data } : { data })
    })
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error.toString());
      });
  });
}

export function axiosApi_form(url, method, data) {
  const token = localStorage.getItem('token');
  return new Promise((resolve, reject) => {
    axios({
      url: `${url}`,
      method: method,
      headers: {
        'auth': token,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      ...(method === 'GET' ? { params: data } : { data })
    })
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error.toString());
      });
  });
}

export function axiosApi_json(url, method, data) {
  const token = localStorage.getItem('token');
  return new Promise((resolve, reject) => {
    axios({
      url: `${url}`,
      method: method,
      headers: {
        'auth': token,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
        'Content-Type': 'application/json'
      },
      ...(method === 'GET' ? { params: data } : { data })
    })
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error.toString());
      });
  });
}

export default {
  
  //--------- 数据目录模块：增删查改图层 ------------
  // 数据目录查询
  queryDataDirectory: async (data) => {
    try {
      const res = await axiosApi("/api/v1/DataDirectory/QueryCatalog", "GET", data);
      console.log('data', data);
      // console.log("queryDataDirectory", res);
      return res.data;
    } catch (error) {
      console.error("Error in queryDataDirectory:", error);
      throw error;
    }
  },

  // 数据目录详情查询
  queryDetailsById: async (data) => {
    try {
      const res = await axiosApi('/api/v1/DataDirectory/QueryDetails', 'GET', {
        id: data
      });
      console.log('queryDetails', res);
      return res;
    } catch (error) {
      console.error('Error in queryDetails:', error);
      throw error;
    }
  },

  // 数据目录删除
  directoryDelete: async (data) => {
    try {
      const res = await axiosApi('/api/v1/DataDirectory/Delete', 'GET', {
        id: data
      });
      console.log('directoryDelete', res);
      return res;
    } catch (error) {
      console.error('Error in directoryDelete:', error);
      throw error;
    }
  },

  // 添加或修改数据目录
  addAndUpdate: async (data) => {
    try {
      console.log('data', data);
      const res = await axiosApi_form('/api/v1/DataDirectory/AddandUpdate', 'POST', data);
      console.log('addAndUpdate', res);
      return res;
    } catch (error) {
      console.error('Error in addAndUpdate:', error);
      throw error;
    }
  },

  // 数据目录，服务添加子图层
  addMapChildLayer: async (data) => {
    try {
      const res = await axiosApi('/api/v1/DataDirectory/SaveLayerInfos', 'POST', data);
      console.log('addMapChildLayer', res);
      return res;
    } catch (error) {
      console.error('Error in addMapChildLayer:', error);
      throw error;
    }
  },

  // 更新树节点顺序
  sortDirectoryTree: async (data) => {
    try {
      const res = await axiosApi_json('/api/v1/DataDirectory/SortUnitTree', 'POST', data);
      console.log('sortDirectoryTree', res);
      return res;
    } catch (error) {
      console.error('Error in sortDirectoryTree:', error);
      throw error;
    }
  },

  //--------- 地图基本配置模块：增删查改地图基本配置 ------------
  // 查询地图基本配置
  queryMapBaseConfig: async (data) => {
    try {
      const res = await axiosApi('/api/v1/MapConfigure/QueryMapBaseConfig', 'GET', data);
      // console.log('queryMapBaseConfig', res);
      return res;
    } catch (error) {
      console.error('Error in queryMapBaseConfig:', error);
      throw error;
    }
  },

  // 添加和修改地图基本配置
  updateMapBaseConfig: async (data) => {
    try {
      const res = await axiosApi_json('/api/v1/MapConfigure/AddOrUpdateMapBaseConfig', 'POST', data);
      console.log('addMapBaseConfig', res);
      return res;
    } catch (error) {
      console.error('Error in addMapBaseConfig:', error);
      throw error;
    }
  },

  // 删除地图基本配置
  deleteMapBaseConfig: async (data) => {
    try {
      const res = await axiosApi('/api/v1/MapConfigure/Delete', 'GET', data);
      console.log('deleteMapBaseConfig', res);
      return res;
    } catch (error) {
      console.error('Error in deleteMapBaseConfig:', error);
      throw error;
    }
  },

}