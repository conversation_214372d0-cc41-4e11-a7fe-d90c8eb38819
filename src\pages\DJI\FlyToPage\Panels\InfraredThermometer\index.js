import { useModel } from "umi";
import { useEffect, useRef, useState } from "react";
import TemperaturePoint from "@/pages/DJI/FlyToPage/Panels/InfraredThermometer/TemperaturePoint/TemperaturePoint";
import TemperatureArea from "@/pages/DJI/FlyToPage/Panels/InfraredThermometer/TemperatureArea/TemperatureArea";
import configStore from "@/stores/configStore";
export default function InfraredThermometer() {
  //红外测温组件
  const { DoCMD, temperatureMode, setTemperatureMode } = useModel("cmdModel");
  const { fjVideo } = useModel("stateModel");
  const { headerHeight } = configStore();
  let device = JSON.parse(localStorage.getItem("device") || "{}");

  const handleTemperature = (e, mode) => {
    // 负载控制—红外测温 模式 设置：0是关闭，1是点测温，2是区域测温
    setTemperatureMode(mode);
    let data = {
      payload_index: device.Camera2,
      mode: mode,
    };
    DoCMD(device.SN, "ir_metering_mode_set", data);
  };

  useEffect(() => {
    //无人机下线自动关闭测温
    if (fjVideo.video_type !== "ir") {
      handleTemperature(0);
    }
  }, [fjVideo]);

  const renderComponent = () => {
    //1为红外点测温，2为红外区域测温,0是关闭
    if (temperatureMode === 1) {
      return <TemperaturePoint />;
    }
    if (temperatureMode === 2) {
      return <TemperatureArea />;
    }
  };
  const renderApp = () => {
    if (!temperatureMode || temperatureMode === 0) {
      // 未选择模式或者模式为0时，不显示
      return null;
    } 
    else {
      return (
        <div 
        style={{
          position: "absolute",
          zIndex:3,
          top:0,
          left:"3.05%",
          width:"94%",
          height:"inherit",
        }}
        >{renderComponent()}</div>
      );
    }
  };
  return renderApp();
}
