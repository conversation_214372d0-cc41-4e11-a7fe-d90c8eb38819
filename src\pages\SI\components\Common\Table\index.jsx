import React, { useMemo, useRef, useState, useEffect } from 'react';
import { Table, Space, Button, message } from 'antd';
import './index.less';

// 工具栏组件 - 独立关注点
const TableToolbar = ({ buttons = [], selectedKeys = [], dataSource = [], style = {} }) => {
  if (!buttons.length) return null;

  const handleClick = (button) => {
    if (button.needsSelection && !selectedKeys.length) {
      message.warning(button.selectionMessage || '请选择要操作的数据');
      return;
    }
    button.onClick?.(selectedKeys, dataSource);
  };

  return (
    <Space style={{ marginBottom: 16, ...style }}>
      {buttons.map((btn, idx) => (
        <Button
          key={btn.key || idx}
          type={btn.type}
          icon={btn.icon}
          loading={btn.loading}
          disabled={btn.disabled || (btn.needsSelection && !selectedKeys.length)}
          onClick={() => handleClick(btn)}
        >
          {btn.text}
        </Button>
      ))}
    </Space>
  );
};

// 智能高度计算Hook
const useAutoHeight = (containerRef, autoHeight, pagination) => {
  const [tableHeight, setTableHeight] = useState(400);

  const calculateAutoHeight = () => {
    if (!autoHeight || !containerRef.current) return;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    
    // 获取容器的可用高度
    let availableHeight = containerRect.height;
    
    // 自动检测同级元素的高度（如表单、其他工具栏等）
    const parentElement = container.parentElement;
    if (parentElement) {
      let totalSiblingHeight = 0;
      
      // 遍历同级元素，计算它们的高度
      Array.from(parentElement.children).forEach(child => {
        if (child !== container) {
          const childRect = child.getBoundingClientRect();
          totalSiblingHeight += childRect.height;
          
          // 考虑margin
          const computedStyle = window.getComputedStyle(child);
          totalSiblingHeight += parseInt(computedStyle.marginTop) || 0;
          totalSiblingHeight += parseInt(computedStyle.marginBottom) || 0;
        }
      });
      
      // 从父容器高度中减去同级元素的高度
      const parentRect = parentElement.getBoundingClientRect();
      availableHeight = parentRect.height - totalSiblingHeight;
    }
    
    // 减去表格内部组件的高度
    
    // 减去工具栏高度（如果存在）
    const toolbarElement = container.querySelector('.ant-space');
    if (toolbarElement) {
      availableHeight -= toolbarElement.getBoundingClientRect().height + 16; // 16px是marginBottom
    }
    
    // 减去表头高度（约55px）
    availableHeight -= 55;
    
    // 减去分页器高度（如果存在）
    if (pagination !== false) {
      availableHeight -= 64;
    }
    
    // 减去一些内边距和边距
    availableHeight -= 20;
    
    // 设置最小高度
    const finalHeight = Math.max(availableHeight, 200);
    
    setTableHeight(finalHeight);
  };

  useEffect(() => {
    if (!autoHeight || !containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      calculateAutoHeight();
    });

    // 监听容器变化
    resizeObserver.observe(containerRef.current);
    
    // 监听父容器变化
    const parentElement = containerRef.current.parentElement;
    if (parentElement) {
      resizeObserver.observe(parentElement);
      
      // 监听同级元素变化
      Array.from(parentElement.children).forEach(child => {
        if (child !== containerRef.current) {
          resizeObserver.observe(child);
        }
      });
    }

    // 初始计算
    setTimeout(calculateAutoHeight, 100);

    return () => {
      resizeObserver.disconnect();
    };
  }, [autoHeight]);

  // 监听数据变化，重新计算高度
  useEffect(() => {
    if (autoHeight) {
      setTimeout(calculateAutoHeight, 100);
    }
  }, [autoHeight]);

  return autoHeight ? tableHeight : undefined;
};

// 精简的表格组件
const DynamicDataTable = ({
  // 核心属性
  dataSource = [],
  columns = [],
  loading = false,
  
  // 功能配置 - 使用对象分组相关配置
  toolbar = null, // { buttons: [], align: 'left' }
  selection = null, // 直接使用 Ant Design 的 rowSelection
  
  // 自适应高度配置
  autoHeight = false, // 是否启用自适应高度
  
  // 样式配置
  striped = true, // 斑马线条纹
  className = '',
  style = {},
  rowClassName = '',
  
  // 其他所有 Ant Design Table 原生属性
  ...tableProps
}) => {
  const containerRef = useRef(null);
  const calculatedHeight = useAutoHeight(containerRef, autoHeight, tableProps.pagination);

  // 处理列配置 - 利用 Ant Design 5.x 的 ellipsis 特性
  const processedColumns = useMemo(() => 
    columns.map(col => ({
      ...col,
      // Ant Design 5.x 支持 ellipsis: { showTitle: true } 自动显示 tooltip
      ellipsis: col.ellipsis ?? { showTitle: true }
    })), [columns]
  );

  // 行样式 - 简化斑马线实现
  const getRowClassName = (record, index) => {
    const classes = ['dynamic-table-row']; // 为所有行添加基础类名
    if (striped && index % 2 === 0) classes.push('dynamic-table-row-even'); // 偶数行添加条纹类名
    if (typeof rowClassName === 'function') {
      classes.push(rowClassName(record, index));
    } else if (rowClassName) {
      classes.push(rowClassName);
    }
    return classes.join(' ');
  };

  // 处理scroll属性
  const processedScroll = useMemo(() => {
    const originalScroll = tableProps.scroll || {};
    
    if (autoHeight && calculatedHeight !== undefined) {
      return {
        ...originalScroll,
        y: calculatedHeight
      };
    }
    
    return originalScroll;
  }, [autoHeight, calculatedHeight, tableProps.scroll]);

  return (
    <div 
      ref={containerRef}
      className={`dynamic-table-container ${autoHeight ? 'auto-height-enabled' : ''} ${className}`} 
      style={style}
    >
      {toolbar && (
        <TableToolbar
          buttons={toolbar.buttons}
          selectedKeys={selection?.selectedRowKeys || []}
          dataSource={dataSource}
          style={{ justifyContent: toolbar.align || 'left' }}
        />
      )}
      
      <Table
        dataSource={dataSource}
        columns={processedColumns}
        loading={loading}
        rowSelection={selection}
        rowClassName={getRowClassName}
        className="dynamic-table"
        {...tableProps}
        scroll={processedScroll}
      />
    </div>
  );
};

export default DynamicDataTable; 