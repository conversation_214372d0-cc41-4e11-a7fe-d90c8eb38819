export const serviceTypeList = [
  {
    value: "ArcGISTiledMapServiceLayer",
    label: "ArcGIS瓦片地图服务"
  }, {
    value: "ArcGISDynamicMapServiceLayer",
    label: "ArcGIS动态地图服务"
  }, {
    value: "ArcGISImageMapServiceLayer",
    label: "ArcGIS影像地图服务"
  }, {
    value: "ArcGISFeatureMapServiceLayer",
    label: "ArcGIS要素地图服务"
  }, {
    value: "WmsServiceLayer",
    label: "WMS地图服务"
  }, {
    value: "WmtsServiceLayer",
    label: "WMTS地图服务"
  }, {
    value: "WfsServiceLayer",
    label: "WFS地图服务"
  },{
    value: "WebTileLayer",
    label: "互联网地图服务"
  },{
    value: "VectorTileLayer",
    label: "矢量瓦片地图服务"
  },{
    value: "TiandituVecLayer",
    label: "天地图矢量地图服务"
  },{
    value: "TiandituImgLayer",
    label: "天地图卫星地图服务"
  },{
    value: "TiandituCvaLayer",
    label: "天地图注记地图服务"
  },{
    value: "TmsServiceLayer",
    label: "正射影像"
  },{
    value: "Cesium3DTileService",
    label: "Cesium三维瓦片服务"
  },{
    value: "CesiumTerrainService",
    label: "Cesium地形瓦片服务"
  },
]

export const serviceList3D = [
  'Cesium3DTileService', 
  'CesiumTerrainService',
]

export const childLayerTableColumn = [
  {
    type: 'selection',
    width: 45,
    align: 'center'
  },
  {
    title: "ID",
    key: "id",
    align: "center",
    width: 60
  },
  {
    title: "图层名称",
    key: "name",
    tooltip:true
  },
  {
    title: "图层类型",
    key: "type",
    width: 200,
    tooltip:true
  },
  {
    title: "几何类型",
    key: "geometryType",
    tooltip:true
  },
  {
    title: "拉伸字段",
    key: "stretchField",
    slot:'stretchField'
  },
  {
    title: "操作",
    key: "handle",
    slot:'action',
    width: 300,
    align: "center"
  }
]
