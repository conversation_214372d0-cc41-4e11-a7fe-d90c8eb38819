import { Get } from '@/services/general';
import request, { HGet2 } from '@/utils/request';
import dayjs from 'dayjs';



export function InfoCacheInit() {
    
  //  HGet2('/api/v2/ProXY/SCQXGridRain?v=24');
  //  let data = localStorage.getItem('info-If');
    // if(!isEmpty(data)){
    //      console.log("InfoCacheInit",data)
    //       return;
    // }

   // SetREngineer();
   // SetRDike();
   // SetWeakpoint();
   // SetSectionX();
   // SetOK();
}

function SetOK() {
    localStorage.setItem('info-If', true);
}


function isEmpty(obj) {

    if (!obj && obj !== 0 && obj !== '') {
        return true;
    }

    if (Array.prototype.isPrototypeOf(obj) && obj.length === 0) {
        return true;
    }

    if (Object.prototype.isPrototypeOf(obj) && Object.keys(obj).length === 0) {
        return true;
    }
    return false;
}



export function GetInfoValue(key,time) {

    let t1 = JSON.parse(localStorage.getItem(time));
    let t2 = moment.now();
    let du = t2 - t1;
    //localStorage.clear();
    let data = JSON.parse(localStorage.getItem(key));
    return JSON.parse(localStorage.getItem(key));
    // if ((du > 300000 * 600 || isEmpty(data) == true)) {
    //     return [];
    // } else {
    //     return JSON.parse(localStorage.getItem(key));
    // }
}


export function IfTimeOver(time) {

    let t1 = localStorage.getItem(time);
    if(isEmpty(t1)) return true;
    
    let t2 = dayjs().unix();
    let du = t2 - t1;
    console.log('IfTimeOver',t1,t2,du);
     if ((du > 60*30)) {
       return  true;
     } else {
        return false;
     }
}


async function SetRDike() {
    const f1 = await Get({ url: "/xpi/v1/RDike/GetList" }) ;
  
    console.log('SetRDike',f1);
    if(isEmpty(f1)) return;

    localStorage.setItem('info-RDike', JSON.stringify(f1));
    localStorage.setItem('info-RDike-time', moment.now());
}






  

  








  


  




  

