import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Tree, Input, Button, Tooltip } from 'antd'
import { 
  SearchOutlined, 
  GlobalOutlined, 
  FolderOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined,
  LeftOutlined,
  RightOutlined,
  UpOutlined,
  DownOutlined
} from '@ant-design/icons'
import styles from './index.module.less'

/**
 * 卷帘图层选择器组件
 * 用于在卷帘模式下选择左右两侧显示的图层
 */
const SplitLayerSelector = ({
  side = 'left', // 'left' | 'right'
  treeData = [],
  selectedLayers = [],
  onLayerSelect,
  onLayerToggle,
  visible = false,
  onClose,
  position = { top: 20, left: 20 },
  currentLayers = [] // 当前已加载的图层列表
}) => {
  const [searchValue, setSearchValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState([])
  const [autoExpandParent, setAutoExpandParent] = useState(true)
  const [isCollapsed, setIsCollapsed] = useState(false)

  // 搜索功能
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value
    setSearchValue(value)
    
    if (value) {
      // 搜索时自动展开所有匹配的父节点
      const expandedKeys = []
      const findMatchingKeys = (data, searchValue) => {
        data.forEach(item => {
          if (item.Name && item.Name.toLowerCase().includes(searchValue.toLowerCase())) {
            expandedKeys.push(item.Rid)
          }
          if (item.Children && item.Children.length > 0) {
            findMatchingKeys(item.Children, searchValue)
          }
        })
      }
      findMatchingKeys(treeData, value)
      setExpandedKeys(expandedKeys)
      setAutoExpandParent(true)
    }
  }, [treeData])

  // 过滤树数据
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData

    const filterTree = (data) => {
      return data.filter(item => {
        const matchesSearch = item.Name && item.Name.toLowerCase().includes(searchValue.toLowerCase())
        const hasMatchingChildren = item.Children && filterTree(item.Children).length > 0
        
        if (matchesSearch || hasMatchingChildren) {
          return {
            ...item,
            Children: item.Children ? filterTree(item.Children) : []
          }
        }
        return false
      }).filter(Boolean)
    }

    return filterTree(treeData)
  }, [treeData, searchValue])

  // 展开/收起处理
  const onExpand = useCallback((expandedKeys) => {
    setExpandedKeys(expandedKeys)
    setAutoExpandParent(false)
  }, [])

  // 图层选择处理
  const handleLayerCheck = useCallback((checkedKeys, { node }) => {
    if (node.Attribute?.servicePath) {
      const isChecked = checkedKeys.includes(node.Rid)
      onLayerSelect?.(node, isChecked, side)
    }
  }, [onLayerSelect, side])

  // 图层可见性切换
  const handleLayerVisibilityToggle = useCallback((node, visible) => {
    onLayerToggle?.(node, visible, side)
  }, [onLayerToggle, side])

  // 折叠/展开切换
  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed)
  }, [isCollapsed])

  // 自定义节点渲染
  const titleRender = useCallback((node) => {
    const isLayer = node.Attribute?.dataType === '2'
    const isSelected = selectedLayers.includes(node.Rid)
    
    return (
      <div className={styles.splitLayerNode}>
        <span className={styles.nodeContent}>
          {isLayer ? (
            <GlobalOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          ) : (
            <FolderOutlined style={{ marginRight: 8, color: '#faad14' }} />
          )}
          <span className={styles.nodeTitle}>{node.Name}</span>
        </span>

        {isLayer && (
          <div className={styles.nodeActions}>
            <Tooltip title={isSelected ? '从卷帘中移除' : `添加到${side === 'left' ? '左' : '右'}侧`}>
              <Button
                type="text"
                size="small"
                icon={isSelected ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  handleLayerVisibilityToggle(node, !isSelected)
                }}
              />
            </Tooltip>
          </div>
        )}
      </div>
    )
  }, [selectedLayers, side, handleLayerVisibilityToggle])

  if (!visible) return null

  const sideIcon = side === 'left' ? <LeftOutlined /> : <RightOutlined />
  const sideText = side === 'left' ? '左侧' : '右侧'

  return (
    <div
      className={`${styles.splitLayerSelector} ${side} ${isCollapsed ? styles.collapsed : ''}`}
      style={{
        top: position.top,
        [side]: position[side] || 20
      }}
    >
      <div className={styles.selectorHeader}>
        <div className={styles.headerTitle}>
          {sideIcon}
          <span>{sideText}图层选择</span>
        </div>
        <div className={styles.headerActions}>
          <Tooltip title={isCollapsed ? '展开' : '折叠'}>
            <Button
              type="text"
              size="small"
              onClick={handleToggleCollapse}
              className={styles.collapseBtn}
              icon={isCollapsed ? <DownOutlined /> : <UpOutlined />}
            />
          </Tooltip>
          <Button
            type="text"
            size="small"
            onClick={onClose}
            className={styles.closeBtn}
          >
            ×
          </Button>
        </div>
      </div>

      {!isCollapsed && (
        <div className={styles.selectorContent}>
          <Input
            placeholder="搜索图层"
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={handleSearchChange}
            className={styles.searchInput}
          />

          <div className={styles.treeContainer}>
            {searchValue && filteredTreeData.length === 0 ? (
              <div className={styles.noResults}>
                没有找到匹配的图层
              </div>
            ) : (
              <Tree
                checkable
                onExpand={onExpand}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                treeData={filteredTreeData}
                checkedKeys={selectedLayers}
                onCheck={handleLayerCheck}
                titleRender={titleRender}
                className={styles.splitLayerTree}
              />
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default SplitLayerSelector