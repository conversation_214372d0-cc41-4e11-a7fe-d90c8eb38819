import { useState, useEffect, useRef } from 'react';
import { Input, InputNumber, Checkbox, Radio, Select, Button, Tooltip, Switch,Slider } from 'antd';
import styles from './index.less';

function WayPointSetting({
    wayline,
    seleteIndex,
    PlacemarkListItemPositionChange,
    useGlobalHeightChange,
    PlacemarkListItemHeightChange,
    waypointUseGlobalSetting,
    waypointSpeedChange,
    waypointHeadingModeChange,
    waypointTurnModeChange
}) {
    let inputRef = useRef(null)


    return <div style={{ width: 420, borderRadius: 10, backgroundColor: '#001c1a', position: 'absolute', right: -420, top: 60, zIndex: 2, padding: '10px' }}>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px' }}>
            <div style={{ fontSize: '14px', }}>航点{seleteIndex + 1}</div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ fontSize: '14px', }}>经度</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Input disabled placeholder="请输入航点经度" value={wayline.Folder.PlacemarkList[seleteIndex].Point.coordinates.longitude} onPressEnter={(e) => { PlacemarkListItemPositionChange(seleteIndex, e.target.value, wayline.Folder.PlacemarkList[seleteIndex].Point.coordinates.latitude) }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ fontSize: '14px', }}>纬度</div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Input disabled placeholder="请输入航点纬度" value={wayline.Folder.PlacemarkList[seleteIndex].Point.coordinates.latitude} onPressEnter={(e) => { PlacemarkListItemPositionChange(seleteIndex, wayline.Folder.PlacemarkList[seleteIndex].Point.coordinates.longitude, e.target.value) }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ width: '100%', fontSize: '14px', display: 'flex', justifyContent: 'space-between', alignContent: 'center' }}>
                <div >高度</div>
                <div >
                    <Tooltip placement="top" title='是否使用全局航点高度'>
                        <Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].useGlobalHeight} onChange={(e) => { useGlobalHeightChange(seleteIndex, e ? 1 : 0) }} />
                    </Tooltip>
                </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Tooltip placement="bottom" title='输入完成后按回车确认'>
                    <InputNumber ref={inputRef} style={{ width: '100%' }} placeholder="请输入航点高度" disabled={wayline.Folder.PlacemarkList[seleteIndex].useGlobalHeight === 1} value={wayline.Folder.PlacemarkList[seleteIndex].height} onPressEnter={(e) => { PlacemarkListItemHeightChange(seleteIndex, Number(e.target.value)); inputRef.current.blur() }} />
                </Tooltip>
            </div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ width: '100%', fontSize: '14px', display: 'flex', justifyContent: 'space-between', alignContent: 'center' }}>
                <div >航点飞行速度</div>
                <div >
                    <Tooltip placement="top" title='是否使用全局航线速度'>
                        <Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].useGlobalSpeed} onChange={(e) => { waypointUseGlobalSetting(seleteIndex, 'useGlobalSpeed', e ? 1 : 0) }} />
                    </Tooltip>
                </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Slider min={1} max={15} disabled={wayline.Folder.PlacemarkList[seleteIndex].useGlobalSpeed === 1} value={wayline.Folder.PlacemarkList[seleteIndex].waypointSpeed} onChange={(e) => { waypointSpeedChange(seleteIndex, e) }} style={{ width: '100%' }} />
            </div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ width: '100%', fontSize: '14px', display: 'flex', justifyContent: 'space-between', alignContent: 'center' }}>
                <div >航点类型</div>
                <div >
                    <Tooltip placement="top" title='是否使用全局航点类型'>
                        <Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].useGlobalTurnParam} onChange={(e) => { waypointUseGlobalSetting(seleteIndex, 'useGlobalTurnParam', e ? 1 : 0) }} />
                    </Tooltip>
                </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    disabled={wayline.Folder.PlacemarkList[seleteIndex].useGlobalTurnParam === 1}
                    value={wayline.Folder.PlacemarkList[seleteIndex].waypointTurnParam.waypointTurnMode}
                    onChange={(e) => { waypointTurnModeChange(seleteIndex, e) }}
                    options={[
                        {
                            value: 'coordinateTurn',
                            label: '协调转弯，不过点，提前转弯',
                        },
                        {
                            value: 'toPointAndStopWithDiscontinuityCurvature',
                            label: '直线飞行，飞行器到点停',
                        },
                        {
                            value: 'toPointAndStopWithContinuityCurvature',
                            label: '曲线飞行，飞行器到点停',
                        },
                        {
                            value: 'toPointAndPassWithContinuityCurvature',
                            label: '曲线飞行，飞行器过点不停',
                        },
                    ]}
                />
            </div>
        </div>
        <div style={{ width: '100%', padding: '10px', backgroundColor: ' #232323', borderRadius: '10px', marginTop: '10px' }}>
            <div style={{ width: '100%', fontSize: '14px', display: 'flex', justifyContent: 'space-between', alignContent: 'center' }}>
                <div >飞行器偏航角模式</div>
                <div >
                    <Tooltip placement="top" title='是否使用全局偏航角模式'>
                        <Switch value={!!wayline.Folder.PlacemarkList[seleteIndex].useGlobalHeadingParam} onChange={(e) => { waypointUseGlobalSetting(seleteIndex, 'useGlobalHeadingParam', e ? 1 : 0) }} />
                    </Tooltip>
                </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', marginTop: '10px' }}>
                <Select
                    style={{ width: '100%' }}
                    disabled={wayline.Folder.PlacemarkList[seleteIndex].useGlobalHeadingParam === 1}
                    value={wayline.Folder.PlacemarkList[seleteIndex].waypointHeadingParam.waypointHeadingMode}
                    onChange={(e) => { waypointHeadingModeChange(seleteIndex, e) }}
                    options={[
                        {
                            value: 'followWayline',
                            label: '沿航线方向',
                        },
                        {
                            value: 'manually',
                            label: '手动控制',
                        },
                        {
                            value: 'fixed',
                            label: '锁定当前偏航角',
                        },
                        {
                            value: 'smoothTransition',
                            label: '自定义',
                        },
                    ]}
                />
            </div>
        </div>
    </div>
}
export default WayPointSetting;