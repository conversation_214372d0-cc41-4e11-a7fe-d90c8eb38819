import { Clone, downloadFile2, downloadFile, getBodyH, isEmpty, getImgUrl, getVideoSLTUrl } from '@/utils/utils';

import { useEffect, useState } from 'react';
import { Card, Table, Form, List, Row, Col, Button, Checkbox, Select,Radio , DatePicker, message, Input, Descriptions } from 'antd';

import { useModel } from 'umi';


import LastPageButton from '@/components/LastPageButton';
import { timeFormat2 } from '@/utils/helper';
import dayjs from 'dayjs';
import { HGet2 } from '@/utils/request';
import TableCols from './task_table';
import { DoubleLeftOutlined, DoubleRightOutlined, SearchOutlined } from '@ant-design/icons';
import { Post2 } from '@/services/general';
import AddButton from '@/components/AddButton';


const getImgUrl2 = (obj) => {
  return `/${obj}`
}

const { RangePicker } = DatePicker;

//const JobTypeList=[{nm:'正射影像',val:0},{nm:'三维地图',val:1}];
const JobTypeList=[{nm:'正射影像',val:0}];

const MapJobPanel = () => {

  console.log('MapJobPanel', mList)
  const [sList, setSList] = useState([]);
  const [xList, setXList] = useState([]);
  const [vName, setVName] = useState('111.mp4');
  const [sWay, setSWay] = useState('');
  const [sDate, setSDate] = useState({});
  const [mList, setMList] = useState([]);
  const [load, setIfLoad] = useState(true);
  const [wType, setWType] = useState(0);
  const [index1, setIndex1] = useState({})
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);



  const { setModal, setOpen, lastPage } = useModel("pageModel")
  // eslint-disable-next-line react-hooks/exhaustive-deps

 

  const ifPush = (e) => {

    if (!isEmpty(sWay)) {
      if (e.FlightLineName != sWay) {
        return false;
      }
    }
    if (!isEmpty(sDate)) {
      // 
      let t1 = dayjs(sDate[0]);
      let t2 = dayjs(sDate[1]);
      const t3 = dayjs(e.CreateTime);

      if (t1.isAfter(t3) || (t2.isBefore(t3))) {
        // console.log('time',t1.format('YYYY-MM-DD HH:mm:ss'),t2.format('YYYY-MM-DD HH:mm:ss'),t3.format('YYYY-MM-DD HH:mm:ss'));
        return false;
      }
    }
    return true;
  }


  useEffect(() => {
    const xx = async () => {
      let yy = await HGet2(`/api/v1/Task/GetListByType?p1=测绘航线`)
      if (isEmpty(yy)) yy = [];
      setMList(yy);
      const xL = []
      yy.forEach(e => {
        if (ifPush(e)) {
          e.key = e.ID;
          xL.push(e);
        }
      });
      setSList([...xL]);
      setIfLoad(false);
    }
    xx();
  }, []);

  useEffect(() => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')
    if (!isEmpty(sDate)) {
      t1 = dayjs(sDate[0]);
      t2 = dayjs(sDate[1]);
    }



    const xx = () => {
      const xL = []
      mList.forEach(e => {
        if (ifPush(e)) {
          e.key = e.ID;
          xL.push(e);
        }
      });
      setSList([...xL]);
      setIndex1({})
      //setIndex2({})
    }
    xx();
  }, [sWay, sDate]);




  const onVideoClick = (item) => {
    setModal(<div style={{ height: 350, width: '100%' }}>
      <div style={{ position: 'absolute', left: 20, top: 18.0, fontWeight: 'bold', height: 36.0, color: 'rgba(0,0,0,0.7)' }}>{item.FlightLineName + "-" + item.HangDianIndex} </div>
      <div style={{ position: 'absolute', cursor: 'pointer', top: 18.0, right: 48, fontWeight: 'bold', color: 'rgba(0,0,0,0.7)' }}>{timeFormat2(item.CreateTime)}</div>

      <video id={item.ID} key={item.ID} height={'100%'} width={'100%'} controls>
        <source src={getImgUrl(item.ObjectName)} type="video/mp4" />
      </video></div>);
    setOpen(true);
  }

  //

  const getWaySelect = (wayList) => {

    const list = []
    wayList.forEach(e => {
      list.push(<Select.Option key={e} data={e}>{e}</Select.Option>)
    });
    console.log('CronAddForm', list);

    return list;
  }

  const onWayLineSelected = (e) => {
    setSWay(e);
  }

  const onDateSelected = (e) => {
    // 
    setSDate(e);
  }

  const getExr = () => {
    const wList = []
    mList.forEach(e => {
      if (!wList.includes(e.FlightLineName)) {
        wList.push(e.FlightLineName);
      }
    });
    return <div>

      <span>      <Select allowClear={true} style={{ width: 200 }} onClear={() => setSWay('')}
        placeholder={'航线'}
        onSelect={onWayLineSelected}>
        {getWaySelect(wList)}
      </Select></span>

      <span style={{ marginLeft: 12.0 }}> <RangePicker onChange={onDateSelected} /></span>

    </div>
  }

  const StartMerge = async () => {
    if (isEmpty(index1)) {
      message.info('先选择需要创建地图的作业！')
      return;
    }


    const rr = await Post2('/api/v1/MapJob/Add', { JobName: vName,JobType:wType, TaskID:index1.TaskID });
    message.info(rr);
    lastPage();
  }


  const getTypeList = (xL) => {
    const list = []
    xL.map(p => {
      list.push(<Radio value={p.val}>{p.nm}</Radio>)
    })
    return list
  }

  const getExr2 = () => {
    const wList = []
    mList.forEach(e => {
      if (!wList.includes(e.FlightLineName)) {
        wList.push(e.FlightLineName);
      }
    });
    return  <Descriptions style={{margin:24.0}} title="创建地图任务" column={1}   >
       
    <Descriptions.Item label="任务名称"><Input style={{width:360}} onChange={(e) => { setVName(e.target.value) }} /></Descriptions.Item>
    <Descriptions.Item label="任务类型"><Radio.Group defaultValue={wType} onChange={(e) => { setWType(e.target.value) }} >
          {getTypeList(JobTypeList)}
        </Radio.Group></Descriptions.Item>
   
    <Descriptions.Item style={{marginTop:24.0}} >

      <AddButton  disabled={isEmpty( index1)} onClick={() => {
        StartMerge();
      }}>提交任务</AddButton>
    </Descriptions.Item>
  </Descriptions>

    
  }


  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
    const v1 = sList.find(p => p.ID == newSelectedRowKeys[0])
    setIndex1(v1);
  };

  const rowSelection = {
    selectedRowKeys,
    type: 'radio',
    onChange: onSelectChange,
  };




  return <Card title={<LastPageButton title='地图重建' />} style={{ height: getBodyH(56) }} >
<Row>
  <Col span={12}>
    <Card title='选择测绘作业' style={{ marginTop: 12.0 }} extra={getExr()}>
      <Table pagination={{ pageSize: 8 }}
        rowSelection={rowSelection}
        bordered dataSource={sList} columns={TableCols(onVideoClick)} size='small' />
    </Card></Col>

    <Col span={12}>
    {getExr2()}
    </Col>
</Row>
  </Card>
}

export default MapJobPanel;
