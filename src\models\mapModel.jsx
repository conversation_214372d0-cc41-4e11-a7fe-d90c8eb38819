
import { useEffect, useState, useCallback } from "react";
import { fetchBaseMapLayers } from '@/utils/map/baseMap'

// 全局缓存，避免重复请求
let globalBaseMapCache = null;
let globalBaseMapPromise = null;

export default function mapModel() {
    const [position,setPosition]=useState([])
    const [ifP,setIfP]=useState(true)
    const [ baseMapLayers, setBaseMapLayers] = useState([]);
    const [mapUrl,setMapUrl]=useState("https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}")
    const [ifDrawLine,setIfDrawLine]=useState(false)
    const [pList,setPList]=useState([])

    const [ifWayline,setIfWayLine]=useState(false)
    
    const fetchBaseMapData = useCallback(async () => {
        try {
            // 如果已有缓存，直接返回
            if (globalBaseMapCache) {
                console.log('使用缓存的底图数据');
                return globalBaseMapCache.rawBaseMapData;
            }

            // 如果正在请求中，等待现有请求完成
            if (globalBaseMapPromise) {
                console.log('等待现有底图请求完成');
                try {
                    const result = await globalBaseMapPromise;
                    return result.rawBaseMapData;
                } catch (error) {
                    // 如果等待的请求失败了，清除Promise并重新尝试
                    globalBaseMapPromise = null;
                    console.log('等待的请求失败，重新发起请求');
                }
            }

            // 创建新的请求 - 使用立即执行的异步函数来确保原子性
            if (!globalBaseMapPromise) {
                console.log('发起新的底图数据请求');
                globalBaseMapPromise = (async () => {
                    try {
                        const result = await fetchBaseMapLayers();
                        const {layerConfigs, rawBaseMapData} = result;

                        if(!layerConfigs) {
                            return {layerConfigs: [], rawBaseMapData: []};
                        }

                        // 缓存结果
                        globalBaseMapCache = {layerConfigs, rawBaseMapData};

                        return result;
                    } catch (error) {
                        console.error('底图数据请求失败:', error);
                        throw error;
                    }
                })();
            }

            const {layerConfigs, rawBaseMapData} = await globalBaseMapPromise;
            globalBaseMapPromise = null;

            if(!layerConfigs || layerConfigs.length === 0) {
                return [];
            }

            // 使用函数式更新确保使用最新状态
            setBaseMapLayers(prevLayers => {
                const hasChanged = JSON.stringify(layerConfigs) !== JSON.stringify(prevLayers)
                if (!hasChanged) return prevLayers

                return layerConfigs
            })

            // 只在初始化或当前url无效时更新mapUrl
            setMapUrl(prevUrl => {
                const urlExists = layerConfigs.some(layer => layer.tileUrl === prevUrl)
                if (layerConfigs.length > 0 && !urlExists) {
                    return layerConfigs[0].tileUrl
                }
                return prevUrl
            })

            // 返回原始底图数据供 OneMap 组件进行统一转换
            return rawBaseMapData
        } catch (error) {
            console.error('获取底图数据失败:', error)
            globalBaseMapPromise = null;
            return []
        }
    }, [])

    return {pList,setPList,position,ifWayline,setIfWayLine,setPosition,
        ifP,setIfP,ifDrawLine,setIfDrawLine,mapUrl,setMapUrl,
        baseMapLayers, setBaseMapLayers, fetchBaseMapData};
};