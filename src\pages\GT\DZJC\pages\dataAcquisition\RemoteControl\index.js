import {
  MqttAddress,
  MqttUrl,
  getBodyH,
  getBodyW,
  getBodyW2,
  isEmpty,
} from "@/utils/utils";
import { useEffect, useRef, useState } from "react";
import { But<PERSON>, message, Card, Tooltip } from "antd";
import styles from "../DevicePage/index.less";
import TopPanel from "@/pages/DJI/DRCPage/Panels/TopPanel";
import IfShowPanel from "@/components/IfShowPanel";
import GetPlayer from "@/pages/DJI/DevicePage/PlayerPanel";
import JoyStick from "@/pages/DJI/DRCPage/joy";
import { FJStart, FJStart2 } from "@/pages/DJI/DRCPage/Panels/RtmpChange";
import OsdPanel from "@/pages/DJI/DRCPage/Panels/YiBiaoPanel";
import MapPanel from "@/pages/DJI/DRCPage/Panels/mapPanel";
import { useModel,useLocation } from "umi";
import cvImg from "@/assets/drcImgs/cv3.png";
import homeImg from "@/assets/drcImgs/home.png";
import fcontinue from "@/assets/drcImgs/fcontinue.png";
import shuaxin from "@/assets/drcImgs/shuaxin.png";
import { CanCelToHomeIcon } from "@/components/icons/icons";
import fstop from "@/assets/drcImgs/fstop.png";
import stop from "@/assets/drcImgs/stop.png";
import BianJiaoPanel from "@/pages/DJI/DRCPage/Panels/BianJiaoPanel";
import BianJiaoPanel2 from "@/pages/DJI/DRCPage/Panels/BianJiaoPanel2";
import CameraPanel from "@/pages/DJI/DRCPage/Panels/CameraPanel";
import CameraPanel2 from "@/pages/DJI/DRCPage/Panels/CameraPanel2";
import { ToHome, WayLineRecory, cancelToHome } from "@/pages/DJI/FlyToPage/helper";
import KeyboardPanel from "@/pages/DJI/DRCPage/Panels/KeyBoradPanel";
import { PlusOutlined } from "@ant-design/icons";
import DRCXinTiao from "@/pages/DJI/DRCPage/Panels/DrcXinTiao";
import { getMqttTcpAddress } from "@/utils/config";
import { HGet2 } from "@/utils/request";

const RemoteControl = () => {
  const location = useLocation();
  let device = location.state.device;
  const [p1, setP1] = useState(<div />);
  const [ifY, setIfY] = useState(true);
  const { fj } = useModel("droneModel");
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  const { cameraJT } = useModel("rtmpModel");
  const { drcStatus } = useModel("eventModel");
  const { jcData } = useModel("dockModel");
  const { MqttConnect } = useModel("mqttModel");
      const [isDanBinPlus2, setIsDanbBinPlus2] = useState(device?.BindCode==='pilot'&&device?.Model==="RC_PLUS_2");

  const COrV = useRef(1);
  const seq = useRef(0);
  const drc = useRef({ x: 0, y: 0, h: 0, w: 0 });
  const col1 = "rgba(65, 65, 65, 0.8)";
  const col2 = "rgba(255, 182, 0, 1)";
  const top0 = getBodyH(170);

  // const sn="7CTDLCE00AC2J4";
  // const sn2="1581F6Q8D23CT00A5N49";
  if (isEmpty(device)) {
    device = JSON.parse(localStorage.getItem("device"));
  }

  const getJData = (v1, min, max, max2) => {
    //if (v2 == 80) return 0;
    let v2 = ((v1 - 40) / 80) * (max - min) + min;
    if (max2 > 0) {
      if (Math.abs(v2) > max2) v2 = (v2 * max2) / Math.abs(v2);
    }
    return v2;
  };

  const JoyHandle1 = (d) => {
    console.log('JoyHandle1',d)
    const v1 = getJData(d.xPosition, -12, 12, 17);
    const v2 = -1 * getJData(d.yPosition, -4, 5);
    drc.current = { ...drc.current, x: 1024, y: 1024, w: v1, h: v2 };
    sendData(v1==0?1024:v1>0?1324:724 , v2==0?1024:v2>0?1324:724 , 1024, 1024);
    // sendData(v1  , v2  , 0, 0);
  };
  const JoyHandle2 = (d) => {
        console.log('JoyHandle2',d)
    const v1 = -1 * getJData(d.yPosition, -20, 20, 17);
    const v2 = getJData(d.xPosition, -20, 20, 17);

    drc.current = { ...drc.current, x: v1, y: v2, w: 1024, h: 1024 };
    sendData(1024, 1024,v1==0?v1:v1>0?1324:724,  v2==0?v2:v2>0?1324:724);
    // sendData(0, 0,v1 ,  v2 );
  };
  const JoyHandle = (x, y, w, h) => {
        console.log('JoyHandle3')
    drc.current = { w, h, x, y };
    sendData(w, h, x, y);
  };

  const StopFly = () => {
    const data = {
      data: {},
      method: "drone_emergency_stop",
    };
    //console.log('joy1', seq.current, w)

    DoCMD2(`thing/product/${device.SN}/drc/down`, data);
    message.info("无人机急停!");
  };

  const CameraDrag = (x, y) => {
    const data = {
      locked: true,
      payload_index: device.Camera2,
      pitch_speed: x,
      yaw_speed: y,
    };
    DoCMD(device.SN, "camera_screen_drag", data);
    // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_screen_drag", data)
  };

  const GetDroneName = (x1) => {
    if (x1 == "0-91-0") return "Matrice 3D";
    if (x1 == "0-91-1") return "Matrice 3TD";
    if (x1 == "0-67-0") return "Matrice 30";
    if (x1 == "0-67-1") return "Matrice 30T";
    return x1;
  };

  const beginX = () => {
    seq.current = 0;
    const data = {
      hsi_frequency: 1,
      mqtt_broker: {
        address: getMqttTcpAddress(),
        client_id: device.SN2,
        enable_tls: false,
        expire_time: 1872744922,
        password: "",
        username: "",
      },
      osd_frequency: 10,
    };
    const data2 = {
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "drc_mode_enter", data);
    DoCMD(device.SN, "payload_authority_grab", data2);
  };

  useEffect(() => {
    MqttConnect(device);
    setP1(GetPlayer("100%", getBodyH(62), 8, device.SN));
    //setP1(<div style={{background:'blace',height:getBodyH(58),width:'100%'}}/>)
    FJStart(device);
    //setP2(<DJBaseMap device={device} sn={device.SN} h1={240} />);
    const nL1 = ["上升(Z)", "下降(C)", "左旋(Q)", "右旋(E)"];
    const nL2 = ["前进(W)", "后退(S)", "左移(A)", "右移(D)"];
    const Joy1 = new JoyStick(
      "joyDiv1",
      {
        internalFillColor: col1,
        internalStrokeColor: col2,
        externalStrokeColor: col1,
        nL: nL1,
      },
      JoyHandle1
    );
    const Joy2 = new JoyStick(
      "joyDiv2",
      {
        internalFillColor: col1,
        internalStrokeColor: col2,
        externalStrokeColor: col1,
        nL: nL2,
      },
      JoyHandle2
    );

    beginX();

    document.addEventListener(
      "touchmove",
      function (event) {
        // 阻止默认的下拉刷新行为
        event.preventDefault();
      },
      { passive: false }
    );
  }, []);

  if (drcStatus.current.drc_state == 0) {
    beginX();
  }

  // useEffect(()=>{
  //   if(drcStatus.current.drc_state==0){
  //     beginX();
  //   }
  // },[drcStatus.current]);

  const sendData = (w, w2, w3, w4) => {

  let data = {}
    if(isDanBinPlus2){
      data = {
        data: {
          throttle: w2,
          yaw: w,
          pitch: w3,
          roll: w4,
        },
        method: "stick_control",
      }
    }else{
      data = {
        data: {
          // h: w2,
          // seq: seq.current,
          // w: w,
          // x: w3,
          // y: w4,

          throttle: w2,
          yaw: w,
          pitch: w3,
          roll: w4,
          
        },
        // method: "drone_control",
        method:"stick_control",
        seq: seq.current,
      }
    }

    //console.log('joy1', seq.current, w)
     console.log('sendData1===',w, w2, w3, w4,data)

    DoCMD2(`thing/product/${device.SN}/drc/down`, data);
    seq.current = seq.current + 1;
  };

  const getBianJiao = () => {
    if (cameraJT == "wide") {
      return null;
    } else if (cameraJT == "zoom") {
      return <BianJiaoPanel />;
    } else if (cameraJT == "ir") {
      return <BianJiaoPanel2 />;
    } else {
      return null;
    }
  };

  const CameraRefrush = () => {
    const data = { payload_index: device.Camera2, reset_mode: COrV.current };
    DoCMD(device.SN, "gimbal_reset", data);
    if (COrV.current == 0) {
      COrV.current = 1;
    } else {
      COrV.current = 0;
    }
  };

  const WayLinePause = (sn) => {
    if (isEmpty(fj)) return;
    if (isEmpty(fj.data)) return;

    if (fj.data?.mode_code == 17) {
      DoCMD(device.SN, "fly_to_point_stop", {});
    }
    if (fj.data?.mode_code == 5) {
      HGet2("/api/v1/WayLine/Pause?sn=" + sn);
    }
  };

  const isCancelToHome = () => {
    if (fj.data?.mode_code == "9") {
      return (
        <Tooltip
          onClick={() => {
            cancelToHome(device.SN);
          }}
          placement="left"
          title="取消返航"
        >
          <div>
            <CanCelToHomeIcon
              width={"100%"}
              height={36}
              fillColor={"#eeeeeb"}
            />
          </div>
        </Tooltip>
      );
    }
    if (
      (fj.data?.mode_code > 2 && fj.data?.mode_code < 9) ||
      fj.data?.mode_code == 17
    ) {
      return (
        <Tooltip
          onClick={() => {
            ToHome(device.SN);
          }}
          placement="left"
          title="一键返航"
        >
          <img height={48} width={36} src={homeImg} />
        </Tooltip>
      );
    }
  };
  const cvDiv = (
    <div
      onClick={() => CameraRefrush()}
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: top0 + 80,
        left: 240,
        width: 48,
        height: 48,
      }}
    >
      <img height={36} width={24} src={cvImg} />
    </div>
  );
  const homeDiv = (
    <div
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: 250,
        left: 20,
        width: 36,
        userSelect: "none",
      }}
    >
      <div>{isCancelToHome()}</div>
   {isDanBinPlus2?null:   <div style={{ marginTop: 16.0, marginLeft: 4.0 }}>
        <Tooltip
          onClick={() => WayLinePause(device.SN)}
          placement="right"
          title="航线暂停"
        >
          <img height={24} width={24} src={fstop} />
        </Tooltip>
      </div>}
     {isDanBinPlus2?null: <div style={{ marginTop: 24.0, marginLeft: 4.0 }}>
        <Tooltip
          onClick={() => WayLineRecory(device.SN)}
          placement="right"
          title="航线继续"
        >
          <img height={22} width={22} src={fcontinue} />
        </Tooltip>
      </div>}
      <div style={{ marginTop: 24.0, marginLeft: 0 }}>
        <Tooltip
          onClick={async () => {
            const gateway_sn = device.SN;
            const topic = `thing/product/${gateway_sn}/drc/down`;
            const data = {
              data: {},
              method: "drone_emergency_stop",
            };
            DoCMD2(topic, data);
          }}
          placement="right"
          title="飞行急停"
        >
          <img height={32} width={32} src={stop} />
        </Tooltip>
      </div>
      <div style={{ marginTop: 24.0, marginLeft: 4.0 }}>
        <Tooltip onClick={() => beginX()} placement="right" title="刷新控制器">
          <img height={28} width={23} src={shuaxin} />
        </Tooltip>
      </div>
    </div>
  );
  const jianpanDiv = (
    <div
      onClick={() => ToHome(device.SN)}
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: 320,
        left: 20,
        width: 48,
        height: 48,
      }}
    >
      <Tooltip placement="right" title="一键返航">
        <img height={48} width={36} src={homeImg} />
      </Tooltip>
    </div>
  );

  const getHSZ = () => {
    if (isEmpty(jcData.current)) return null;
    if (isEmpty(jcData.current?.sub_device)) return null;
    if (
      GetDroneName(jcData.current?.sub_device?.device_model_key) ===
      "Matrice 3D"
    ) {
      return (
        <div
          style={{
            position: "fixed",
            left: "calc(49% - 20px)",
            top: "calc(51% - 20px)",
          }}
        >
          <PlusOutlined style={{ fontSize: "40px", color: "#ea0c0c" }} />
        </div>
      );
    }

    return (
      <div style={{ position: "fixed", left: "48.2%", top: "51%" }}>
        <PlusOutlined style={{ fontSize: "40px", color: "#ea0c0c" }} />
      </div>
    );
  };
  return (
    <div className={styles.IndexPageStyle} style={{ background: "black" }}>
      {/* {isEmpty(device) ? <div /> : <WebSocketDemo sn={device.SN} sn2={device.SN2} />} */}

      <DRCXinTiao device={device} DoCMD2={DoCMD2}></DRCXinTiao>
      <KeyboardPanel
        joyHandle={JoyHandle}
        CameraDrag={CameraDrag}
        StopFly={StopFly}
      />
      <div
        className="container"
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          background: "black",
        }}
      >
        <TopPanel />
        {p1}
        <MapPanel />
        {getBianJiao()}
        <CameraPanel />
        <CameraPanel2 />

        {cvDiv}
        {homeDiv}
        {IfShowPanel(
          0,
          0,
          getBodyH(190),
          getBodyW(430),
          null,
          null,
          <OsdPanel></OsdPanel>,
          true
        )}
        <div
          id="joyDiv1"
          style={{
            display: "block",
            height: 160,
            width: 160,
            position: "absolute",
            zIndex: 1000,
            top: top0,
            left: 50,
          }}
        />
        <div
          id="joyDiv2"
          style={{
            display: "block",
            height: 160,
            width: 160,
            position: "absolute",
            zIndex: 1000,
            top: top0,
            right: 50,
          }}
        />
        {getHSZ()}
      </div>
    </div>
  );
};

export default RemoteControl;
