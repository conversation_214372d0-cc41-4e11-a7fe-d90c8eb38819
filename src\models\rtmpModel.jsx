import { useState } from "react";

export default function rtmpModel() {
    const [ifFJRtmp,setIfFJRtmp]=useState(false)
    const [ifJCRtmp,setIfJCRtmp]=useState(false)
    const [ifCMDPanel,setIfCMDPanel]=useState(false)
    const [cmdPanel,setCMDPanel]=useState(<div/>)
    const [cameraJT,setCameraJT]=useState('wide')

    return {cameraJT,setCameraJT, ifFJRtmp,setIfFJRtmp, ifJCRtmp,setIfJCRtmp,ifCMDPanel,setIfCMDPanel,cmdPanel,setCMDPanel };
}