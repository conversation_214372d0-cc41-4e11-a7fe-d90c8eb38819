# SI (智能巡检 - Smart Inspection) 模块

本模块是“云端智行-无人机行业应用平台”中的核心功能区之一，主要负责智能巡检相关业务。

## 主要功能

*   **用户认证**: 提供用户登录 (`login.js`) 功能，支持账号密码登录及 Token 自动登录。
*   **应用主界面**: 模块的主入口 (`layouts/index.js`)，承载和导航至各个子功能模块。
*   **核心业务模块**:
    *   `DynamicMonitor/`: 动态监控或实时数据显示（主界面）。
    *   `ControlCenter/`: 控制中心或指挥调度相关功能。
    *   `AIAlgorithm/`: AI算法仓。
        *   提供一个专门的界面来管理和应用AI算法。
        *   该模块具有内部的二级导航结构，包含：“算法模型”、“任务管理”、“模型训练”。
        *   `Pages/AlgorithmModel/`: (对应“算法模型”视图) 展示可用的AI模型，显示模型的详细信息，如准确度、绑定状态和更新时间。
        *   `Pages/TaskManagement/`: 用于管理AI相关任务的界面。
        *   `Pages/ModelTraining/`: 用于AI模型训练流程的界面。
    *   `FlyApp/`: 行业应用。
*   **其他辅助功能**:
    *   包含通用的布局 (`layouts/`)、组件 (`components/`)、工具函数 (`utils/`) 、样式 (`style/`)、资源类 (`assets/`)。

## 技术栈

*   **前端框架**: React
*   **路由与构建**: UmiJS
*   **UI 组件库**: Ant Design
*   **样式**: Less, CSS Modules

## 目录结构

```
src/pages/SI/
├── DynamicMonitor/    # 动态监控相关模块
├── ControlCenter/     # 控制中心相关模块
├── AIAlgorithm/       # AI算法仓模块
│   ├── Pages/         # AI算法仓模块的各个子页面
│   │   ├── AlgorithmModel/  # “算法模型”页面 (包含 index.js, index.less)
│   │   ├── TaskManagement/  # “任务管理”页面
│   │   └── ModelTraining/   # “模型训练”页面
│   ├── index.js         # AI算法仓模块的主布局和内部路由逻辑
│   └── index.less       # AI算法仓模块主布局样式
├── FlyApp/            # 行业应用相关模块
├── assets/            # 静态资源 (图片、字体等)
├── components/        # 模块内通用组件
├── layouts/           # 模块主布局和导航中心 (layouts/index.js)
├── style/             # 模块级样式文件
├── utils/             # 模块内工具函数
├── login.js           # 登录页面逻辑
├── login.less         # 登录页面样式
└── README.md          # 本说明文档
```

## 注意事项

*   各子目录（如 `ControlCenter/` 等）的具体功能是基于初始版本描述，实际功能请参考其内部实现。