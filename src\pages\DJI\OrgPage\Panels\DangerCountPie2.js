
import { MainColor } from '@/utils/colorHelper';
import Chart from './../../../../utils/chart';
import BlockTitle from '../../../../components/BlockPanel/BlockTitle';
import { BorderBox7 } from '@jiaminghi/data-view-react';
import { Badge } from 'antd';


const option = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    // Try 'horizontal'
    orient: 'vertical',
    right: 20,
    top: '20%',
    textStyle: {
      color: 'white'
    }
  },
  backgroundColor: '',
  series: [
    {
      name: '事件统计',
      type: 'pie',
      radius: ['30%', '50%'],
      center: ["35%", "40%"],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold',
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 5, name: '设施损坏' },
        { value: 3, name: '烟雾告警' },
        { value: 120, name: '人员进入' },
        { value: 0, name: '病虫害' },
      ]
    }
  ]
};

const getItem = () => {
  return <div style={{ color: 'white', marginTop: 24.0, marginLeft: 48.0 }}>
    <span>
      <Badge color="green" text={''} />
    </span>
    <span style={{ fontSize: 14.0, marginLeft: 8.0 }}>
      已处理 520件
    </span>
    <span style={{ marginLeft: 36.0 }}>
      <Badge color="orange" text={''} />
    </span>
    <span style={{ fontSize: 14.0, marginLeft: 8.0 }}>
      处理中 3件
    </span>
  </div>
}
const xx = () => {
  return <div style={{ height: '45%', width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="事件处理统计"></BlockTitle>
      <div style={{ height: '240px', width: '100%'}}>
        {getItem()}
        <Chart option={option} />
      </div>
    </BorderBox7>

  </div>
}
export default xx;