import { queryPage } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Tabs, Card, Table, Form, Input, Select, Modal, DatePicker, message, Row, Col, Descriptions, Slider, InputNumber, Image, Spin } from 'antd';

import MediaDataPage from "@/pages/DJI/MediaDataPage/index";
import MediaDataPage2 from "@/pages/DJI/MediaDataPage/index2";
import Orthophoto from "@/pages/GT/ZZCH/Pages/VideoManagement/Pages/2DOrthophoto"

const VideoManagement = () => {
    const { setPage } = useModel("pageModel");
    const [currentTable, setCurrentTable] = useState('picture'); // 默认显示图片
    return (
        <div>
            <Card
                title={"影像管理"}
                styles={{
                    body: {
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        padding: 0,
                        overflow: "hidden",
                    },
                }}
            >
                <Tabs
                    activeKey={currentTable}
                    onChange={setCurrentTable}
                    tabBarStyle={{
                        marginBottom: 16,
                        marginLeft: 36,
                        display: 'flex',
                        alignItems: 'center'
                    }}
                >
                    <Tabs.TabPane tab="图片" key="picture">
                        <MediaDataPage doNotShowLastButton={true} />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="视频" key="video">
                        <MediaDataPage2 doNotShowLastButton={true} />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="正射影像" key="2DOrthophoto">
                        <Orthophoto />
                    </Tabs.TabPane>
                </Tabs>
            </Card>
        </div>
    );
};

export default VideoManagement;