//import {Modal,} from 'antd';
import {Form,Modal,InputNumber} from 'antd';
import { useState } from 'react';
const ChangeHeightPanel=({h1,flyTo})=>{
    const [height,setHeight]=useState(h1)
    const { confirm } = Modal;


    confirm({
      title: '调整高度',
      //icon: <ExclamationCircleFilled />,
      content: <InputNumber min={20} max={220} onChange={(e)=>setHeight(e)} value={height}/>,
      okText: '确定调整',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        flyTo(height);
      },
      onCancel() {
       // console.log('Cancel');
      },
    });
  };




export default ChangeHeightPanel;
