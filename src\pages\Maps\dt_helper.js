
import {
    Polyline, Tooltip,

    LayerGroup,
    LayersControl,GeoJSON

} from 'react-leaflet';


//import <PERSON>Yu from '../liuyu';


import { Location_Market2 } from './dt_market';
const { Overlay } = LayersControl




const DT_XHNL = () => {
   


const list = [];




list[0] =
<Overlay  name="水库位置" checked>
           
                
               
        {Location_Market2([31.033777,103.576870],"紫坪铺水库")}
 
    </Overlay>



return list;

}


export default DT_XHNL;


