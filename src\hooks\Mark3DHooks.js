import { useState, useEffect, useRef, } from 'react';
import { Cesium } from "umi";
import { GetCesiumViewer, Cartesian3_TO_Position, getTerrainDistance, GenerateTriangularMesh } from '@/utils/cesium_help';
import { message, InputNumber, Radio, Modal, Tooltip, Slider, Spin, Popover } from 'antd';
import { DeleteOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, LoadingOutlined } from '@ant-design/icons';

const useMark3DHooks = ({ idName }) => {
    const { confirm } = Modal;
    // 大地图viewer
    const viewer = useRef(null);
    // cesium事件
    let handlerPoint = useRef(null)
    // 标注方式
    let [edit_way, setEdit_way] = useState(null)
    // 标注方式
    let edit_wayCopy = useRef(null)
    let editWayList = [
        {
            key: 'point',
            label: '点'
        },
        {
            key: 'polyline',
            label: '线'
        },
        {
            key: 'polygon',
            label: '面'
        },
        {
            key: 'square',
            label: '方'
        }
    ]
    // 鼠标移动点位置
    let move_position = useRef([]);
    // 正在标注的线位置
    let edit_polyline_position = useRef([])
    // 正在标注的面位置
    let edit_polygon_position = useRef([])
    // 正在标注的填挖面位置
    let edit_rectangle_polygon_position = useRef([])
    // 填挖方基准面
    let rectangle_position = useRef([])
    // 实体集合
    let CesiumEntitys = useRef({
        positions: [],
        polyline_CesiumEntity: null,
        polygon_CesiumEntity: null,
        rectangle_CesiumEntity: null,
        GeometryInstanceList: null,
        triangleList: [],
        points: []
    });
    // 实体集合
    let CesiumEntitylist = useRef([]);
    // 实体索引
    let edit_index = useRef(null);
    // 实体索引
    let move_index = useRef(null);

    // 1.定义一个定时器, 保存上一次的定时器
    let timer = useRef(null)
    // 挖方基准面高度
    let [benchHeight, setBenchHeight] = useState(300)
    let benchHeightCopy = useRef(300)
    // 挖方采样精度
    const [precision, setPrecision] = useState(20);
    const precisionCopy = useRef(20);
    // 面积采样精度
    const [areaPrecision, setAreaPrecision] = useState(20);
    const areaPrecisionCopy = useRef(20);
    const [open, setOpen] = useState(false);

    useEffect(() => {
        viewer.current = GetCesiumViewer(idName)
        // viewer.current.scene.globe.depthTestAgainstTerrain = true;
        // viewer.current.scene.camera.setView({
        //     destination: Cesium.Cartesian3.fromDegrees(120, 30, 10000),
        //     orientation: {
        //         heading: 0,
        //         pitch: Cesium.Math.toRadians(-90),
        //         roll: 0,
        //     },
        // });
        // 添加cesium事件
        handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer.current.scene.canvas)
        LEFT_CLICK()
        MOUSE_MOVE()
        RIGHT_CLICK()
        WHEEL_ALT()
        return () => {
            destroy()
        };
    }, []);
    // 测量方式改变
    function edit_wayChange(text) {
        if (edit_wayCopy.current === text) {
            setEdit_way(null)
            edit_wayCopy.current = null
        } else {
            setEdit_way(text)
            edit_wayCopy.current = text
        }
    }
    //鼠标点击事件
    function LEFT_CLICK() {
        handlerPoint.current.setInputAction(function (e) {
            if (edit_wayCopy.current === null) {
                return
            }
            const cartesian = viewer.current.scene.pickPosition(e.position) //获取点击位置的地理坐标
            var pickedObject = viewer.current.scene.pick(e.position);
            if (!cartesian) {//underfind说明地图还没加载成功
                return
            }
            if (move_index.current !== null) {
                if (pickedObject.id.name === '填挖方标注面') {
                    if ((edit_wayCopy.current === 'polygon') && (edit_index.current !== null)) {
                        CesiumEntitylist.current[edit_index.current].polygon_CesiumEntity.polyline.material = Cesium.Color.fromCssColorString('#409EFF')
                        edit_index.current = null
                    }
                    setEdit_way('square')
                    edit_wayCopy.current = 'square'
                    setBenchHeight(CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.rectangle.height.getValue())
                    benchHeightCopy.current = CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.rectangle.height.getValue()
                    setPrecision(CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.precision)
                    precisionCopy.current = CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.precision
                    if (edit_index.current === move_index.current) {
                        CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.rectangle.material = Cesium.Color.WHITE.withAlpha(0.5)
                        edit_index.current = null
                        setOpen(false)
                    } else {
                        if (edit_index.current !== null) {
                            CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.rectangle.material = Cesium.Color.WHITE.withAlpha(0.5)
                        }
                        edit_index.current = move_index.current
                        CesiumEntitylist.current[move_index.current].rectangle_CesiumEntity.rectangle.material = Cesium.Color.RED.withAlpha(0.5)
                        setOpen(true)
                    }
                } else if (pickedObject.id.name === '标注面') {
                    if ((edit_wayCopy.current === 'square') && (edit_index.current !== null)) {
                        CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.rectangle.material = Cesium.Color.WHITE.withAlpha(0.5)
                        edit_index.current = null
                    }
                    setEdit_way('polygon')
                    edit_wayCopy.current = 'polygon'
                    setAreaPrecision(CesiumEntitylist.current[move_index.current].polygon_CesiumEntity.precision)
                    areaPrecisionCopy.current = CesiumEntitylist.current[move_index.current].polygon_CesiumEntity.precision
                    if (edit_index.current === move_index.current) {
                        CesiumEntitylist.current[move_index.current].polygon_CesiumEntity.polyline.material = Cesium.Color.fromCssColorString('#409EFF')
                        edit_index.current = null
                        setOpen(false)
                    } else {
                        if (edit_index.current !== null) {
                            CesiumEntitylist.current[edit_index.current].polygon_CesiumEntity.polyline.material = Cesium.Color.fromCssColorString('#409EFF')
                        }
                        edit_index.current = move_index.current
                        CesiumEntitylist.current[move_index.current].polygon_CesiumEntity.polyline.material = Cesium.Color.RED
                        setOpen(true)
                    }
                }
                return
            }
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
            let point = viewer.current.entities.add({
                name: `节点`,
                index: CesiumEntitylist.current.length,
                position: new Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                point: {
                    show: true,
                    pixelSize: 10,
                    color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
                    // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
            })
            CesiumEntitys.current.positions.push(longitude, latitude, height)
            CesiumEntitys.current.points.push(point)
            if (edit_wayCopy.current === 'point') {
                point.label = {
                    text: `经度:${longitude}\n纬度:${latitude}\n高度:${height.toFixed(0)}m`,
                    font: '8pt Source Han Sans CN',             //字体样式
                    fillColor: Cesium.Color.WHITE,                //字体颜色
                    backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                    showBackground: true,                         //是否显示背景颜色
                    style: Cesium.LabelStyle.FILL,                //label样式
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
                }
                CesiumEntitylist.current.push(CesiumEntitys.current)
                CesiumEntitys.current = {
                    positions: [],
                    polyline_CesiumEntity: null,
                    polygon_CesiumEntity: null,
                    rectangle_CesiumEntity: null,
                    GeometryInstanceList: null,
                    triangleList: [],
                    points: []
                }
            } else if (edit_wayCopy.current === 'polyline') {
                setOpen(true)
                edit_polyline_position.current.push(longitude, latitude, height)
                if (!CesiumEntitys.current.polyline_CesiumEntity) {
                    CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                        name: `临时标注线`,
                        polyline: {
                            show: true,
                            positions: new Cesium.CallbackProperty(() => {
                                return new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polyline_position.current, ...move_position.current]);
                            }, false),
                            // 宽度
                            width: 5,
                            // 线的颜色
                            material: Cesium.Color.fromCssColorString('#409EFF'),
                            clampToGround: true,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                        },
                    })
                }
            } else if (edit_wayCopy.current === 'polygon') {
                setOpen(true)
                edit_polygon_position.current.push(longitude, latitude, height)
                if (!CesiumEntitys.current.polygon_CesiumEntity) {
                    CesiumEntitys.current.polygon_CesiumEntity = viewer.current.entities.add({
                        name: `临时标注面`,
                        polyline: {
                            show: true,
                            positions: new Cesium.CallbackProperty(() => {
                                return new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polygon_position.current, ...move_position.current, edit_polygon_position.current[0], edit_polygon_position.current[1], edit_polygon_position.current[2]]);
                            }, false),
                            // 宽度
                            width: 5,
                            // 线的颜色
                            material: Cesium.Color.fromCssColorString('#409EFF'),
                            clampToGround: true,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                        },
                        polygon: {
                            show: true,
                            hierarchy: new Cesium.CallbackProperty(() => {
                                return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polygon_position.current, ...move_position.current]))
                            }, false),
                            // 宽度
                            width: 5,
                            // 线的颜色
                            material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.5),
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                        },
                    })
                }
            } else if (edit_wayCopy.current === 'square') {
                setOpen(true)
                edit_rectangle_polygon_position.current.push(longitude, latitude, height)
                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                rectangle_position.current.push(cartographic)
                if (!CesiumEntitys.current.rectangle_CesiumEntity) {
                    CesiumEntitys.current.rectangle_CesiumEntity = viewer.current.entities.add({
                        name: `临时填挖面`,
                        polyline: {
                            show: true,
                            positions: new Cesium.CallbackProperty(() => {
                                return new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_rectangle_polygon_position.current, ...move_position.current, edit_rectangle_polygon_position.current[0], edit_rectangle_polygon_position.current[1], edit_rectangle_polygon_position.current[2]]);
                            }, false),
                            // 宽度
                            width: 5,
                            // 线的颜色
                            material: Cesium.Color.fromCssColorString('#409EFF'),
                            clampToGround: true,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                        },
                        polygon: {
                            show: true,
                            hierarchy: new Cesium.CallbackProperty(() => {
                                return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_rectangle_polygon_position.current, ...move_position.current]))
                            }, false),
                            // 宽度
                            width: 5,
                            // 线的颜色
                            material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.5),
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                        },
                    })
                }
            }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    }
    //鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            const cartesian = viewer.current.scene.pickPosition(e.endPosition) //获取点击位置的地理坐标
            if (!cartesian) {//underfind说明地图还没加载成功
                return
            }
            var pickedObject = viewer.current.scene.pick(e.endPosition);
            if (pickedObject && pickedObject.id && pickedObject.id.name === '填挖方标注面') {
                move_index.current = pickedObject.id.index
                viewer.current._element.style.cursor = "pointer";
            } else if (pickedObject && pickedObject.id && pickedObject.id.name === '标注面') {
                move_index.current = pickedObject.id.index
                viewer.current._element.style.cursor = "pointer";
            } else {
                if (CesiumEntitylist.current[move_index.current]) {
                    move_index.current = null
                }
                viewer.current._element.style.cursor = "";
            }
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
            move_position.current = [longitude, latitude, height]
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    //右击事件
    function RIGHT_CLICK() {
        handlerPoint.current.setInputAction(async function (e) {
            if (edit_wayCopy.current === null) {
                return
            }
            if (edit_wayCopy.current === 'polyline') {
                if (CesiumEntitys.current.points.length < 2) {
                    message.error(`线段标注至少两个点`);
                    return
                }
                viewer.current.entities.remove(CesiumEntitys.current.polyline_CesiumEntity)
                CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                    name: `标注线`,
                    index: CesiumEntitylist.current.length,
                    polyline: {
                        show: true,
                        positions: new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polyline_position.current]),
                        // 宽度
                        width: 5,
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF'),
                        clampToGround: true
                    }
                })
                let length = edit_polyline_position.current.length
                var point1 = new Cesium.Cartesian3.fromDegrees(edit_polyline_position.current[0], edit_polyline_position.current[1], edit_polyline_position.current[2]); // 第一个点的坐标（x、y、z）
                var point2 = new Cesium.Cartesian3.fromDegrees(edit_polyline_position.current[length - 3], edit_polyline_position.current[length - 2], edit_polyline_position.current[length]); // 第二个点的坐标（x、y、z）
                var distance2 = Cesium.Cartesian3.distance(point1, point2);
                let height = edit_polyline_position.current[length - 1] - edit_polyline_position.current[2]
                console.log('直线距离', distance2);
                console.log('垂直距离', height);
                let res = getTerrainDistance(viewer.current, edit_polyline_position.current)
                edit_polyline_position.current = []
                CesiumEntitys.current.points[CesiumEntitys.current.points.length - 1].label = {
                    text: `距离:${res.toFixed(0)}m`,
                    font: '8pt Source Han Sans CN',             //字体样式
                    fillColor: Cesium.Color.WHITE,                //字体颜色
                    backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                    showBackground: true,                         //是否显示背景颜色
                    style: Cesium.LabelStyle.FILL,                //label样式
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
                }
            } else if (edit_wayCopy.current === 'polygon') {
                if (CesiumEntitys.current.points.length < 3) {
                    message.error(`多边形标注至少两个点`);
                    return
                }
                viewer.current.entities.remove(CesiumEntitys.current.polygon_CesiumEntity)
                CesiumEntitys.current.polygon_CesiumEntity = viewer.current.entities.add({
                    name: `标注面`,
                    index: CesiumEntitylist.current.length,
                    precision: areaPrecisionCopy.current,
                    polyline: {
                        show: true,
                        positions: new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polygon_position.current, edit_polygon_position.current[0], edit_polygon_position.current[1], edit_polygon_position.current[2]]),
                        // 宽度
                        width: 5,
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF'),
                        clampToGround: true,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                    polygon: {
                        show: true,
                        hierarchy: new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_polygon_position.current]),
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.5),
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                })
                let { center_position, surfaceArea, wf, tf, GeometryInstanceList, triangleList } = GenerateTriangularMesh(viewer.current, edit_polygon_position.current, 'polygon', 300, 20)
                CesiumEntitys.current.triangleList = triangleList.map((item, index) =>
                    viewer.current.entities.add({
                        name: "标注面",
                        precision: areaPrecisionCopy.current,
                        index: CesiumEntitylist.current.length,
                        polygon: {
                            hierarchy: item.position,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            perPositionHeight: true,
                            material: item.color,
                            // outline: true,
                            // outlineColor: Cesium.Color.WHITE,
                        }
                    })
                )
                edit_polygon_position.current = []
                CesiumEntitys.current.points[CesiumEntitys.current.points.length - 1].label = {
                    text: `面积:${surfaceArea.toFixed(0)}㎡`,
                    font: '8pt Source Han Sans CN',             //字体样式
                    fillColor: Cesium.Color.WHITE,                //字体颜色
                    backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                    showBackground: true,                         //是否显示背景颜色
                    style: Cesium.LabelStyle.FILL,                //label样式
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
                }
            } else if (edit_wayCopy.current === 'square') {
                if (CesiumEntitys.current.points.length < 3) {
                    message.error(`填挖方标注至少两个点`);
                    return
                }
                viewer.current.entities.remove(CesiumEntitys.current.rectangle_CesiumEntity)
                CesiumEntitys.current.rectangle_CesiumEntity = viewer.current.entities.add({
                    name: `填挖方标注面`,
                    index: CesiumEntitylist.current.length,
                    precision: precisionCopy.current,
                    rectangle: {
                        coordinates: Cesium.Rectangle.fromCartographicArray(rectangle_position.current),
                        material: Cesium.Color.WHITE.withAlpha(0.5),
                        height: benchHeightCopy.current,
                    },
                    polygon: {
                        show: true,
                        hierarchy: new Cesium.Cartesian3.fromDegreesArrayHeights([...edit_rectangle_polygon_position.current]),
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#ffffff').withAlpha(0.5),
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                })
                let { center_position, surfaceArea, wf, tf, GeometryInstanceList, triangleList } = GenerateTriangularMesh(viewer.current, edit_rectangle_polygon_position.current, 'square', benchHeightCopy.current, precisionCopy.current)
                CesiumEntitys.current.GeometryInstanceList = viewer.current.scene.primitives.add(new Cesium.Primitive({
                    geometryInstances: GeometryInstanceList,
                    appearance: new Cesium.PerInstanceColorAppearance({
                        translucent: true,
                        flat: true,
                    }),
                    asynchronous: false,
                }));
                CesiumEntitys.current.triangleList = triangleList.map((item, index) =>
                    viewer.current.entities.add({
                        name: "三角面",
                        polygon: {
                            hierarchy: item.position,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            perPositionHeight: true,
                            material: item.color,
                        }
                    })
                )
                edit_rectangle_polygon_position.current = []
                rectangle_position.current = []
                CesiumEntitys.current.points[CesiumEntitys.current.points.length - 1].label = {
                    text: `基准高度：${benchHeightCopy.current}米\n面积:${surfaceArea.toFixed(0)}㎡\n挖方:${wf.toFixed(0)}m³\n填方:${tf.toFixed(0)}m³`,
                    font: '8pt Source Han Sans CN',             //字体样式
                    fillColor: Cesium.Color.WHITE,                //字体颜色
                    backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                    showBackground: true,                         //是否显示背景颜色
                    style: Cesium.LabelStyle.FILL,                //label样式
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
                }
            }
            CesiumEntitylist.current.push(CesiumEntitys.current)
            CesiumEntitys.current = {
                positions: [],
                polyline_CesiumEntity: null,
                polygon_CesiumEntity: null,
                rectangle_CesiumEntity: null,
                GeometryInstanceList: null,
                triangleList: [],
                points: []
            }
            setOpen(false)
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    //按下ALT并滚动滚轮
    function WHEEL_ALT() {
        handlerPoint.current.setInputAction(function (e) {
            if (edit_index.current !== null) {
                viewer.current._element.style.cursor = "s-resize";
                let move_y = e / 100
                let height = CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.rectangle.height
                CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.rectangle.height = height + move_y
                CesiumEntitylist.current[edit_index.current].points[CesiumEntitylist.current[edit_index.current].points.length - 1].label.text.setValue(`基准高度：${height + move_y}米\n占地面积:--㎡\n挖方:--m³\n填方:--m³`)
                benchHeightChange((height + move_y))
            }
        }, Cesium.ScreenSpaceEventType.WHEEL, Cesium.KeyboardEventModifier.ALT);
    }
    // 销毁函数
    function destroy() {
        deletteAll()
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.WHEEL, Cesium.KeyboardEventModifier.ALT);
    }
    // 清空标注
    function deletteAll() {
        viewer.current.entities.removeAll();
        CesiumEntitylist.current.forEach((item, index) => item.GeometryInstanceList && viewer.current.scene.primitives.remove(item.GeometryInstanceList))
        CesiumEntitylist.current = []
    }
    // 标注控件
    function Mark3D() {
        return <div style={{ borderRadius: 3, backgroundColor: '#ffffff', width: 30, color: '#2b85e4', }} >
            {editWayList.map((item, index) => {
                return <div key={item.key} style={{ borderRadius: (index === 0) ? '3px 3px 0 0' : '0', boxSizing: 'border-box', borderBottom: '1px solid #dcdee2', cursor: 'pointer', height: 30, width: '100%', backgroundColor: edit_way === item.key ? '#2b85e4' : 'transparent', color: edit_way === item.key ? '#ffffff' : '#2b85e4', display: 'flex', justifyContent: 'center', alignItems: 'center' }} onClick={() => edit_wayChange(item.key)}>{item.label}</div>
            })}
            <div style={{ boxSizing: 'border-box', cursor: 'pointer', height: 30, width: '100%', color: '#2b85e4', display: 'flex', justifyContent: 'center', alignItems: 'center' }} onClick={() => deletteAll()}>
                <Tooltip placement="left" title={'清除所有标记'}>
                    <DeleteOutlined />
                </Tooltip>
            </div>
            <div style={{ boxSizing: 'border-box', cursor: 'pointer', height: 30, width: '100%', color: '#2b85e4', display: 'flex', justifyContent: 'center', alignItems: 'center' }} onClick={() => setOpen(!open)}>
                <Tooltip placement="left" title={'点击查看使用帮助'}>
                    <Popover content={
                        <div>
                            <p>1:鼠标左键开始绘制</p>
                            <p>2:鼠标右键结束绘制</p>
                            <p>3:点击填挖方平整面选中变红</p>
                            <p>4:平整面选中时可以调整参数</p>
                            <p>5:平整面选中时通过ALT+滚轮调整平整面高度</p>
                            <p>6:再次点击平整面取消选中</p>
                        </div>
                    } title="使用帮助" trigger="click" placement="left" open={open}>
                        <QuestionCircleOutlined />
                    </Popover>
                </Tooltip>
            </div>
            {/* {(edit_way === 'polygon') && <div style={{ borderRadius: 5, width: 280, padding: 10, position: 'absolute', top: 60, left: -290, backgroundColor: '#ffffff' }}>
                <div style={{ height: 40, width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Tooltip placement="bottom" title={'采样密度越高，计算结果越准确，但是计算时间也会越长'}>
                        <div style={{ cursor: 'pointer', fontSize: 13 }}><QuestionCircleOutlined />采样密度:</div>
                    </Tooltip>
                    <Radio.Group options={[
                        { label: '低', value: 20 },
                        { label: '中', value: 40 },
                        { label: '高', value: 60 },
                    ]} onChange={(e) => {
                        areaPrecisionChange(e.target.value)
                    }} value={areaPrecision} />
                </div>
            </div>} */}
            {(edit_way === 'square') && <div style={{ borderRadius: 5, width: 280, padding: 10, position: 'absolute', top: open ? 330 : 90, left: -290, backgroundColor: '#ffffff' }}>
                <div style={{ height: 40, width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Tooltip placement="bottom" title={'以此高度为平面进行填挖方计算(WGS84高度)'}>
                        <div style={{ cursor: 'pointer', fontSize: 13 }}><QuestionCircleOutlined />平整面高度:</div>
                    </Tooltip>
                    <Tooltip placement="top" title={'输入完成后按回车确认'}>
                        <InputNumber
                            style={{ width: 150, height: 30 }}
                            precision={2}
                            addonAfter={'米'}
                            value={benchHeight}
                            onPressEnter={(e) => {
                                benchHeightChange(Number(e.target.value))
                            }}>
                        </InputNumber>
                    </Tooltip>
                </div>
                <div style={{ height: 40, width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Tooltip placement="bottom" title={'采样密度越高，计算结果越准确，但是计算时间也会越长'}>
                        <div style={{ cursor: 'pointer', fontSize: 13 }}><QuestionCircleOutlined />采样密度:</div>
                    </Tooltip>
                    <Radio.Group options={[
                        { label: '低', value: 20 },
                        { label: '中', value: 40 },
                        { label: '高', value: 60 },
                    ]} onChange={(e) => {
                        precisionChange(e.target.value)
                    }} value={precision} />
                </div>
            </div>}
        </div>
    }
    // 获取viewer
    function getViewer() {
        return viewer.current
    }
    // 基准面高度改变
    function benchHeightChange(val) {
        setBenchHeight(val)
        benchHeightCopy.current = val
        edit_index.current !== null && computeVolume(false)
    }
    // 采样精度变化
    function precisionChange(val) {
        if ((edit_index.current !== null)) {
            confirm({
                icon: <ExclamationCircleOutlined />,
                content: '改变采样精度需要重新计算并渲染，这可能会导致浏览器卡顿一段时间...',
                onOk() {
                    setPrecision(val)
                    precisionCopy.current = val
                    computeVolume(true)
                    CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.precision.setValue(val)
                },
                onCancel() {
                    console.log('Cancel');
                },
            });
        } else {
            setPrecision(val)
            precisionCopy.current = val
        }
    }
    // 计算体积
    function computeVolume(Refresh) {
        // 取消上一次的定时器
        if (timer.current !== null) {
            clearTimeout(timer.current)
        }
        // 延迟执行
        timer.current = setTimeout(() => {
            let { center_position, surfaceArea, wf, tf, GeometryInstanceList, triangleList } = GenerateTriangularMesh(viewer.current, CesiumEntitylist.current[edit_index.current].positions, 'square', benchHeightCopy.current, precisionCopy.current)
            viewer.current.scene.primitives.remove(CesiumEntitylist.current[edit_index.current].GeometryInstanceList)
            CesiumEntitylist.current[edit_index.current].GeometryInstanceList = viewer.current.scene.primitives.add(new Cesium.Primitive({
                geometryInstances: GeometryInstanceList,
                appearance: new Cesium.PerInstanceColorAppearance({
                    translucent: true,
                    flat: true,
                }),
                asynchronous: false,
            }));
            if (Refresh) {
                CesiumEntitylist.current[edit_index.current].triangleList.forEach((item, index) => {
                    viewer.current.entities.remove(item)
                })
                CesiumEntitylist.current[edit_index.current].triangleList = triangleList.map((item, index) => {
                    viewer.current.entities.add({
                        name: "三角面",
                        polygon: {
                            hierarchy: item.position,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                            perPositionHeight: true,
                            material: item.color,
                        }
                    })
                })
            }
            CesiumEntitylist.current[edit_index.current].rectangle_CesiumEntity.rectangle.height = benchHeightCopy.current
            CesiumEntitylist.current[edit_index.current].points[CesiumEntitylist.current[edit_index.current].points.length - 1].label.text.setValue(`基准高度：${benchHeightCopy.current}米\n面积:${surfaceArea.toFixed(0)}㎡\n挖方:${wf.toFixed(0)}m³\n填方:${tf.toFixed(0)}m³`)
            timer.current = null
        }, 2000)
    }
    // 面积采样精度变化
    function areaPrecisionChange(val) {
        if (edit_index.current !== null) {
            confirm({
                icon: <ExclamationCircleOutlined />,
                content: '改变采样精度需要重新计算并渲染，这可能会导致浏览器卡顿一段时间...',
                onOk() {
                    setAreaPrecision(val)
                    areaPrecisionCopy.current = val
                    computeArea()
                },
                onCancel() {
                    console.log('Cancel');
                },
            });
        } else {
            setAreaPrecision(val)
            areaPrecisionCopy.current = val
        }
    }
    // 计算面积
    function computeArea() {
        // 取消上一次的定时器
        if (timer.current) clearTimeout(timer.current)
        // 延迟执行
        timer.current = setTimeout(() => {
            let { center_position, surfaceArea, wf, tf, GeometryInstanceList, triangleList } = GenerateTriangularMesh(viewer.current, CesiumEntitylist.current[edit_index.current].positions, 'polygon', 300, areaPrecisionCopy.current)
            CesiumEntitylist.current[edit_index.current].triangleList.forEach((item, index) => {
                viewer.current.entities.remove(item)
            })
            CesiumEntitylist.current[edit_index.current].triangleList = triangleList.map((item, index) =>
                viewer.current.entities.add({
                    name: "标注面",
                    precision: areaPrecisionCopy.current,
                    index: edit_index.current,
                    polygon: {
                        hierarchy: item.position,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        perPositionHeight: true,
                        material: item.color,
                    }
                })
            )
            CesiumEntitylist.current[edit_index.current].points[CesiumEntitylist.current[edit_index.current].points.length - 1].label.text.setValue(`面积:${surfaceArea.toFixed(0)}㎡`)
            timer.current = null
        }, 2000)
    }
    return {
        Mark3D,
        getViewer
    }
};

export default useMark3DHooks;