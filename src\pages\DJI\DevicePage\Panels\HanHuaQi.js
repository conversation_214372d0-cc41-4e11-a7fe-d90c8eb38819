import { Input, Button, message, Slider, Select } from "antd";
import { useEffect, useState, useRef } from "react";
import { axiosApi } from "@/services/general";
import { DeleteOutlined } from "@ant-design/icons";

const HanHuaPage = ({ device, setOpen }) => {
  const { TextArea } = Input;
  const { Option } = Select;
  const [seleteTextItem, setSeleteTextItem] = useState(null);
  const [seleteAudioItem, setSeleteAudioItem] = useState(null);
  const [record, setRecord] = useState(true);
  const [commonWordsList, setCommonWordsList] = useState([]);
  const [megaphone, setMegaphone] = useState({
    sn: device.SN,
    audio: null,//音频文件
    input_type: 1,//嘁话输入类型
    source: "",// 文件地址或文本内容
    remark: '',
    play_mode: 0,//{"0":"单次播放","1":"循环播放(单曲)"}
    play_volume: 50,//音量，默认50
    play_go_on: -1,//持续时间循环播放默认为-1,一直播放，可传持续时间<秒>
  })
  // 使用 useRef 来保存 AudioContext 实例，方便在不同函数中访问和修改
  const audioContextRef = useRef(null);
  // 保存媒体流对象，用于管理音频输入
  const mediaStreamRef = useRef(null);
  // 保存录音处理器节点，用于处理音频数据
  const recorderRef = useRef(null);
  // 记录录音开始的时间戳
  const startTimeRef = useRef(null);
  // 保存定时器的 ID，用于后续清除定时器
  const timerIntervalRef = useRef(null);
  // 使用 useState 管理录音时长状态
  const [recordingDuration, setRecordingDuration] = useState(0);
  // 管理录制的音频数据数组
  const [audioData, setAudioData] = useState([]);
  // 控制是否显示音频预览界面
  const [showPreview, setShowPreview] = useState(false);
  // 管理音频预览的 URL
  const [audioPreviewUrl, setAudioPreviewUrl] = useState('');
  // 管理 PCM 文件下载的 URL
  const [pcmDownloadUrl, setPcmDownloadUrl] = useState('');
  // 保存音频预览播放器的引用
  const audioPreviewRef = useRef(null);
  useEffect(() => {
    getSourceList()
  }, []);
  // 获取常用词列表
  function getSourceList() {
    axiosApi(`/api/v1/PSDK/SourceList`, "GET", {}).then((res) => {
      res.data && setCommonWordsList(res.data)
    })
  }
  function formatter(value) {
    return `${value === -1 ? '一直播放' : secondsToMinutes(value)}`
  }
  function secondsToMinutes(seconds) {
    // 转为对应的 时, 分, 秒
    let m = parseInt(seconds / 60 % 60)
    let s = parseInt(seconds % 60)
    // 拼接字符串
    let timestr = `${m === 0 ? '' : m + '分'}${s === 0 ? '' : s + '秒'}`;
    // 将字符串返回
    return timestr;
  }
  // 喊话
  function Shout() {
    if (megaphone.input_type === 1 && megaphone.source.length === 0) {
      message.error(`请输入喊话内容`);
      return
    }
    if (megaphone.input_type === 2 && megaphone.source.length === 0) {
      message.error(`请选择音频文件`);
      return
    }
    axiosApi(`/api/v1/PSDK/PushSource`, "POST", megaphone).then((res) => {
      if (res.code === 1) {
        setMegaphone({
          sn: device.SN,
          input_type: 1,//嘁话输入类型
          source: "",// 文件地址或文本内容
          remark: '',
          play_mode: 0,//{"0":"单次播放","1":"循环播放(单曲)"}
          play_volume: 50,//音量，默认50
          play_go_on: -1,//持续时间循环播放默认为-1,一直播放，可传持续时间<秒>
        })
        resetRecording()
        setOpen(false);
        setSeleteTextItem(null)
        setSeleteAudioItem(null)
      }
    })
  }
  // 取消喊话
  function cancelShout() {
    axiosApi(`/api/v1/PSDK/StopSource`, "GET", { sn: device.SN }).then((res) => {
    })
  }
  // 添加常用词
  function addWords() {
    if (megaphone.input_type === 1 && megaphone.source.length === 0) {
      message.error(`请输入喊话内容`);
      return
    }
    if (megaphone.input_type === 2 && audioData.length === 0) {
      message.error(`请录制音频`);
      return
    }
    if (megaphone.input_type === 2 && megaphone.remark.length === 0) {
      message.error(`请输入音频标记`);
      return
    }
    let formData = new FormData();
    formData.append('org_code', JSON.parse(localStorage.getItem('device')).OrgCode);
    formData.append('input_type', megaphone.input_type);
    formData.append('remark', megaphone.remark);
    if (megaphone.input_type === 1) {
      formData.append('source', megaphone.source);
    } else if (megaphone.input_type === 2) {
      // 合并录制的音频数据
      const pcmData = mergePCMData(audioData);
      // 创建 PCM 格式的 Blob 对象
      const pcmBlob = createPCMBlob(pcmData);
      // 生成文件名
      const fileName = `voice_recording_16khz_16bit_${Date.now()}.pcm`;
      // 将 PCM 文件添加到 FormData 中
      formData.append('audio', pcmBlob, fileName);
    }
    axiosApi(`/api/v1/PSDK/AddSourve`, "POST", formData).then((res) => {
      if (res.code === 1) {
        getSourceList()
        setMegaphone({
          sn: device.SN,
          input_type: megaphone.input_type,//嘁话输入类型
          source: "",// 文件地址或文本内容
          remark: '',
          play_mode: 0,//{"0":"单次播放","1":"循环播放(单曲)"}
          play_volume: 50,//音量，默认50
          play_go_on: -1,//持续时间循环播放默认为-1,一直播放，可传持续时间<秒>
        })
        resetRecording()
      }
    })
  }
  // 删除常用词
  function deleteWords(id) {
    axiosApi(`/api/v1/PSDK/DeleteSourve?id=${id}`, "DELETE", {}).then((res) => {
      if (res.code === 1) {
        setSeleteTextItem(null)
        setSeleteAudioItem(null)
        getSourceList()
      }
    })
  }

  // 更新录音时长的函数
  const updateTimer = () => {
    // 计算当前录音时长，精确到小数点后一位
    const currentDuration = ((Date.now() - startTimeRef.current) / 1000).toFixed(1);
    // 更新录音时长状态
    setRecordingDuration(parseFloat(currentDuration));
  };

  // 开始录音的函数
  const startRecording = async (state) => {
    try {
      // 创建一个新的 AudioContext 实例，设置采样率为 16000Hz
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 16000
      });

      // 请求访问用户的音频设备，设置音频参数
      mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          sampleSize: 16,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      // 获取实际的采样率
      const actualSampleRate = audioContextRef.current.sampleRate;
      console.log(`请求采样率: 16000Hz, 实际采样率: ${actualSampleRate}Hz`);

      // 创建一个媒体流源节点，将媒体流连接到该节点
      const source = audioContextRef.current.createMediaStreamSource(mediaStreamRef.current);
      // 创建一个脚本处理器节点，用于处理音频数据
      const scriptNode = audioContextRef.current.createScriptProcessor(4096, 1, 1);

      // 将媒体流源节点连接到脚本处理器节点
      source.connect(scriptNode);
      // 将脚本处理器节点连接到音频输出目的地
      scriptNode.connect(audioContextRef.current.destination);

      // 清空之前录制的音频数据
      setAudioData([]);
      // 记录录音开始的时间戳
      startTimeRef.current = Date.now();

      // 当有新的音频数据输入时，将其添加到音频数据数组中
      scriptNode.onaudioprocess = (e) => {
        const inputBuffer = e.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        setAudioData(prevData => [...prevData, new Float32Array(inputData)]);
      };

      // 保存脚本处理器节点，方便后续操作
      recorderRef.current = scriptNode;
      // 隐藏音频预览界面
      setShowPreview(false);

      // 启动定时器，每 100 毫秒更新一次录音时长
      timerIntervalRef.current = setInterval(updateTimer, 100);

      setRecord(!state)
    } catch (err) {
      // 若启动录音过程中出现错误，打印错误信息
      console.error('无法开始录音:', err);
    }
  };

  // 创建 PCM 格式的 Blob 对象的函数
  const createPCMBlob = (pcmData) => {
    // 获取实际的采样率
    const actualSampleRate = audioContextRef.current.sampleRate;
    // 目标采样率为 16000Hz
    const targetSampleRate = 16000;

    let resampledData;
    // 如果实际采样率与目标采样率不同，进行重采样
    if (actualSampleRate !== targetSampleRate) {
      console.log(`进行重采样: 从 ${actualSampleRate}Hz 到 ${targetSampleRate}Hz`);

      const ratio = actualSampleRate / targetSampleRate;
      const newLength = Math.floor(pcmData.length / ratio);
      resampledData = new Float32Array(newLength);

      for (let i = 0; i < newLength; i++) {
        const exactPos = i * ratio;
        const index1 = Math.floor(exactPos);
        const index2 = Math.min(index1 + 1, pcmData.length - 1);
        const fraction = exactPos - index1;

        resampledData[i] = pcmData[index1] * (1 - fraction) + pcmData[index2] * fraction;
      }
    } else {
      // 若采样率相同，直接使用原始数据
      resampledData = pcmData;
    }

    console.log(`原始采样数: ${pcmData.length}, 重采样后数量: ${resampledData.length}`);

    // 创建一个 Int16Array 来存储重采样后的数据
    const buffer = new Int16Array(resampledData.length);

    // 音量增益系数
    const volumeBoost = 1.2;

    // 对重采样后的数据进行处理，将其转换为 16 位有符号整数
    for (let i = 0; i < resampledData.length; i++) {
      let sample = Math.max(-1, Math.min(1, resampledData[i] * volumeBoost));

      if (Math.abs(sample) < 0.01) {
        sample = 0;
      }

      buffer[i] = Math.round(sample * 32767);
    }

    console.log(`PCM文件生成完成，大小: ${buffer.length * 2} 字节`);
    // 创建一个 Blob 对象，包含处理后的音频数据
    return new Blob([buffer], { type: 'audio/pcm; sampleRate=16000; bitsPerSample=16' });
  };

  // 创建 WAV 格式的 Blob 对象的函数
  const createWavFile = (pcmData) => {
    // 获取实际的采样率
    const actualSampleRate = audioContextRef.current.sampleRate;
    // 目标采样率为 16000Hz
    const targetSampleRate = 16000;

    let resampledData;
    // 如果实际采样率与目标采样率不同，进行重采样
    if (actualSampleRate !== targetSampleRate) {
      const ratio = actualSampleRate / targetSampleRate;
      const newLength = Math.floor(pcmData.length / ratio);
      resampledData = new Float32Array(newLength);

      for (let i = 0; i < newLength; i++) {
        const exactPos = i * ratio;
        const index1 = Math.floor(exactPos);
        const index2 = Math.min(index1 + 1, pcmData.length - 1);
        const fraction = exactPos - index1;

        resampledData[i] = pcmData[index1] * (1 - fraction) + pcmData[index2] * fraction;
      }
    } else {
      // 若采样率相同，直接使用原始数据
      resampledData = pcmData;
    }

    // 音频通道数为 1（单声道）
    const numChannels = 1;
    // 采样率为 16000Hz
    const sampleRate = 16000;
    // 每个样本的位数为 16 位
    const bitsPerSample = 16;
    // 每个块的对齐字节数
    const blockAlign = numChannels * (bitsPerSample / 8);
    // 每秒的字节率
    const byteRate = sampleRate * blockAlign;

    // 计算音频数据的大小
    const dataSize = resampledData.length * 2;
    // 创建一个 ArrayBuffer 来存储 WAV 文件的头部和音频数据
    const buffer = new ArrayBuffer(44 + dataSize);
    // 创建一个 DataView 对象，用于操作 ArrayBuffer
    const view = new DataView(buffer);

    // 写入 WAV 文件的头部信息
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);

    // 音量增益系数
    const volumeBoost = 1.2;

    // 将重采样后的数据转换为 16 位有符号整数，并写入到 ArrayBuffer 中
    for (let i = 0; i < resampledData.length; i++) {
      let sample = Math.max(-1, Math.min(1, resampledData[i] * volumeBoost));

      if (Math.abs(sample) < 0.01) {
        sample = 0;
      }

      const pcm16 = Math.round(sample * 32767);
      view.setInt16(44 + i * 2, pcm16, true);
    }

    console.log(`WAV预览文件生成完成，大小: ${buffer.byteLength} 字节`);
    // 创建一个 Blob 对象，包含处理后的音频数据和 WAV 文件头部
    return new Blob([buffer], { type: 'audio/wav' });
  };

  // 向 DataView 中写入字符串的函数
  const writeString = (view, offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  // 停止录音的函数
  const stopRecording = (state) => {
    // 如果存在录音处理器节点，断开其连接
    if (recorderRef.current) {
      recorderRef.current.disconnect();
      // 停止媒体流中的所有轨道
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
      }
    }
    // 清除定时器
    clearInterval(timerIntervalRef.current);

    // 合并录制的音频数据
    const pcmData = mergePCMData(audioData);
    // 创建 PCM 格式的 Blob 对象
    const pcmBlob = createPCMBlob(pcmData);
    // 创建 WAV 格式的 Blob 对象
    const wavBlob = createWavFile(pcmData);

    // 显示音频预览界面
    showPreviewfu(wavBlob, pcmBlob);

    setRecord(!state)
  };

  // 合并多个音频数据缓冲区的函数
  const mergePCMData = (buffers) => {
    let totalLength = 0;
    // 计算所有缓冲区的总长度
    buffers.forEach(buffer => totalLength += buffer.length);

    // 创建一个新的 Float32Array 来存储合并后的数据
    const result = new Float32Array(totalLength);
    let offset = 0;
    // 将每个缓冲区的数据复制到新的数组中
    buffers.forEach(buffer => {
      result.set(buffer, offset);
      offset += buffer.length;
    });
    return result;
  };

  // 显示音频预览界面的函数
  const showPreviewfu = (wavBlob, pcmBlob) => {
    // 创建音频预览的 URL
    const audioPreviewUrl = URL.createObjectURL(wavBlob);
    // 创建 PCM 文件下载的 URL
    const pcmDownloadUrl = URL.createObjectURL(pcmBlob);

    // 更新音频预览 URL 状态
    setAudioPreviewUrl(audioPreviewUrl);
    // 更新 PCM 文件下载 URL 状态
    setPcmDownloadUrl(pcmDownloadUrl);
    // 显示音频预览界面
    setShowPreview(true);
  };

  // 重置录音状态的函数
  const resetRecording = () => {
    // 清空录制的音频数据
    setAudioData([]);
    // 重置录音时长为 0
    setRecordingDuration(0);
    // 隐藏音频预览界面
    setShowPreview(false);
    // 清空音频预览 URL
    setAudioPreviewUrl('');
    // 清空 PCM 文件下载 URL
    setPcmDownloadUrl('');
    // 清空音频预览播放器的源
    if (audioPreviewRef.current) {
      audioPreviewRef.current.src = '';
    }
  };

  // 将 PCM 数据转换为 Base64 编码的函数
  const pcmToBase64 = (pcmData) => {
    // 创建一个 Int16Array 来存储转换后的数据
    const int16Data = new Int16Array(pcmData.length);
    for (let i = 0; i < pcmData.length; i++) {
      int16Data[i] = pcmData[i] * 0x7FFF;
    }

    // 获取 Int16Array 的缓冲区
    const buffer = int16Data.buffer;
    // 创建一个 Uint8Array 来存储缓冲区的数据
    const bytes = new Uint8Array(buffer);
    let binary = '';
    // 将每个字节转换为字符，并拼接成二进制字符串
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    // 将二进制字符串转换为 Base64 编码
    return window.btoa(binary);
  };

  return (
    <div>
      <div style={{ fontSize: 16.0, fontWeight: "bold", marginLeft: 4.0, }}>无人机喊话</div>
      <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
        <div style={{ width: '20%', }}>
          音量：
        </div>
        <div style={{ width: '80%', }}>
          <Slider value={megaphone.play_volume} min={0} max={100} onChange={(e) => setMegaphone({ ...megaphone, play_volume: e })} />
        </div>
      </div>
      <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px 0 0 0' }}>
        <div style={{ width: '50%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <div style={{ width: '40%', }}>
            播放模式：
          </div>
          <div style={{ width: '60%', }}>
            <Select
              style={{ width: '100%', }}
              value={megaphone.play_mode}
              placeholder={'选择播放模式'}
              onChange={(value, option) => {
                setMegaphone({
                  ...megaphone,
                  play_mode: value,
                })

              }}
              options={[
                {
                  label: '单次播放',
                  value: 0,
                },
                {
                  label: '循环播放',
                  value: 1,
                },
              ]}
            />
          </div>
        </div>
        <div style={{ width: '50%', padding: '0 0 0 10px', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <div style={{ width: '40%', }}>
            音源类型：
          </div>
          <div style={{ width: '60%', }}>
            <Select
              style={{ width: '100%', }}
              value={megaphone.input_type}
              placeholder={'选择音源类型'}
              onChange={(value, option) => {
                setMegaphone({
                  ...megaphone, input_type: value,
                  source: "",// 文件地址或文本内容
                  remark: '',
                 })
                setSeleteTextItem(null)
                setSeleteAudioItem(null)
              }}
              options={[
                {
                  label: '文本',
                  value: 1,
                },
                {
                  label: '音频 ',
                  value: 2,
                },
              ]}
            />
          </div>
        </div>
      </div>
      {megaphone.play_mode === 1 && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px 0 0 0' }}>
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <div style={{ width: '20%', }}>循环时间：</div>
          <div style={{ width: '80%', }}>
            <Slider value={megaphone.play_go_on} tooltip={{ formatter }} min={-1} max={900} onChange={(e) => setMegaphone({ ...megaphone, play_go_on: e })} marks={{
              '-1': '无限',
              '180': '3分钟',
              '360': '6分钟',
              '540': '9分钟',
              '720': '12分钟',
              '900': '15分钟',
            }} />
          </div>
        </div>
      </div>}
      {megaphone.input_type === 1 && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px 0 0 0' }}>
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <div style={{ width: '20%', }}>{megaphone.input_type === 1 ? '常用词' : '音频'}：</div>
          <div style={{ width: '80%', }}>
            <Select value={seleteTextItem} style={{ width: '100%', }} placeholder={`选择${megaphone.input_type === 1 ? '常用词' : '音频'}`}>
              {commonWordsList.map((item, index) => {
                if (item.input_type === 1) {
                  return <Option key={item.id}>
                    <div style={{ width: '100%', height: 20, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div style={{ width: '92%', overflow: 'hidden' }} onClick={() => {
                        setMegaphone({ ...megaphone, source: item.source, remark: item.remark })
                        setSeleteTextItem(item.source)
                      }}>{item.remark}</div>
                      <div style={{ width: '5%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#e1e6ed', color: 'red' }} onClick={() => { deleteWords(item.id) }}>
                        <DeleteOutlined />
                      </div>
                    </div>
                  </Option>
                }
              })}
            </Select>
          </div>
        </div>
      </div>}
      {megaphone.input_type === 2 && <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px 0 0 0' }}>
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <div style={{ width: '20%', }}>{megaphone.input_type === 1 ? '常用词' : '音频'}：</div>
          <div style={{ width: '80%', }}>
            <Select value={seleteAudioItem} style={{ width: '100%', }} placeholder={`选择${megaphone.input_type === 1 ? '常用词' : '音频'}`}>
              {commonWordsList.map((item, index) => {
                if (item.input_type === 2) {
                  return <Option key={item.id}>
                    <div style={{ width: '100%', height: 20, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div style={{ width: '92%', overflow: 'hidden' }} onClick={() => {
                        setMegaphone({ ...megaphone, source: item.source, remark: item.remark })
                        setSeleteAudioItem(item.remark)
                      }}>{item.remark}</div>
                      <div style={{ width: '5%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#e1e6ed', color: 'red' }} onClick={() => { deleteWords(item.id) }}>
                        <DeleteOutlined />
                      </div>
                    </div>
                  </Option>
                }
              })}
            </Select>
          </div>
        </div>
      </div>}
      {megaphone.input_type === 1 && <div style={{ padding: '10px 0 0 0' }}>
        <TextArea rows={3} placeholder="输入文本" onChange={(e) => {
          setMegaphone({ ...megaphone, source: e.target.value, remark: e.target.value })
        }} value={megaphone.source} />
      </div>}
      {megaphone.input_type === 2 && <div>
        {recordingDuration !== 0 && <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', padding: '10px 0 0 0' }}>
          <div style={{ width: '20%', }}>
            音频时长：
          </div>
          <div style={{ width: '80%', }}>
            {recordingDuration}秒
          </div>
        </div>}
        {showPreview && <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', padding: '10px 0 0 0' }}>
          <div style={{ width: '20%', }}>
            音频预览：
          </div>
          <div style={{ width: '80%', }}>
            <audio ref={audioPreviewRef} src={audioPreviewUrl} controls style={{ width: '100%' }}></audio>
          </div>
        </div>}
        {showPreview && <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', padding: '10px 0 0 0' }}>
          <div style={{ width: '20%', }}>
            音频标记：
          </div>
          <div style={{ width: '80%', }}>
            <Input placeholder="输入标记" value={megaphone.remark} onChange={(e) => {
              setMegaphone({ ...megaphone, remark: e.target.value })
            }} />
          </div>
        </div>}
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', padding: '10px 0 0 0' }}>
          <div style={{ width: '20%', }}>
          </div>
          <div style={{ width: '80%', display: 'flex', justifyContent: 'flex-end', alignItems: 'center', }}>
            {showPreview && <Button style={{}} onClick={() => { resetRecording() }}>
              {'取消重录'}
            </Button>}
            {!showPreview && <Button style={{ marginLeft: 20 }} type="primary" onClick={() => { record ? startRecording(record) : stopRecording(record) }}>
              {record ? '开始录音' : '结束录音'}
            </Button>}
          </div>
        </div>
      </div>}

      <div style={{ padding: '10px 0 0 0', display: 'flex', justifyContent: 'space-between', alignItems: 'center', }}>
        <div style={{}} >
          <Button style={{}} onClick={() => { cancelShout() }}>
            取消喊话
          </Button>
          {megaphone.input_type === 1 && <Button style={{ marginLeft: 20 }} type="primary" onClick={() => { addWords() }}>
            {'添加常用词'}
          </Button>}
          {megaphone.input_type === 2 && showPreview && <Button style={{ marginLeft: 20 }} type="primary" onClick={() => { addWords() }}>
            {'添加音频'}
          </Button>}
        </div>
        <div style={{}} >
          <Button onClick={() => {
            setMegaphone({
              sn: device.SN,
              input_type: 1,//嘁话输入类型
              source: "",// 文件地址或文本内容
              remark: '',
              play_mode: 0,//{"0":"单次播放","1":"循环播放(单曲)"}
              play_volume: 50,//音量，默认50
              play_go_on: -1,//持续时间循环播放默认为-1,一直播放，可传持续时间<秒>
            })
            setOpen(false)
            setSeleteTextItem(null)
            setSeleteAudioItem(null)
          }}>
            取消
          </Button>
          {<Button style={{ marginLeft: 20 }} type="primary" onClick={() => { Shout() }}>
            喊话
          </Button>}
        </div>
      </div>
    </div>
  );
};

export default HanHuaPage;
