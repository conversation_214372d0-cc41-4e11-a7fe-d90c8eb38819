import React, { Fragment, useState, useEffect, useRef } from "react";

import {
  Breadcrumb,
  Card,
  Input,
  Select,
  Tag,
  DatePicker,
  Descriptions,
  Row,
  Col,
  Button,
  Form,
  message,
  Table,
  Modal,
} from "antd";

import { downloadFile2, getBodyH, getDeviceName, isEmpty } from "@/utils/utils";

import { Get2, Post2 } from "@/services/general";

import FlightFileAddForm from "./form_add";
import FlightFileEditForm from "./form_edit";

import TableCols from "./table";

import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import ComStyles from "@/pages/common.less";
import AddButton from "@/components/AddButton";
import { HGet2 } from "@/utils/request";
import { handleBatchDownload2 } from "@/utils/filezip";
import dayjs from "dayjs";
import useConfigStore from "@/stores/configStore";


const CronListPage = (props) => {
  const { tableCurrent, setTableCurrent } = useConfigStore();
  const [cronList, setCronList] = useState([]);
  const { setModal, setOpen, setPage, lastPage } = useModel("pageModel");
  const [sJC, setSjc] = useState("");
  const [xList, setXList] = useState([]);
  //const [downLoading,setDownloading]=useState(false);
  const downLoading = useRef(false);

  const ifPush = (e) => {
    // if (!isEmpty(sJC)) {
    //   if (e.SN != sJC) {
    //     return false;
    //   }
    // }
    return true;
  };

  useEffect(() => {
    const xx = () => {
      const xL = [];
      //
      cronList.forEach((e) => {
        if (ifPush(e)) {
          xL.push(e);
        }
      });
      setXList([...xL]);
    };
    xx();
  }, [sJC]);

  useEffect(() => {
    const getCronData = async () => {
      let pst = await Get2("/api/v1/FlightTaskFile/GetAllList", {});
      if (isEmpty(pst)) pst = [];
      const pst3 = pst.filter((p) => p.FType == "内飞");
      const pst2 = pst3.sort(
        (a, b) => dayjs(b.FTime).valueOf() - dayjs(a.FTime).valueOf()
      );

      setCronList(pst2);
      setXList(pst2);
    };
    getCronData();
  }, []);

  const downloadTaskFile = async (record) => {
    if (downLoading.current) {
      message.info("正在执行下载任务，请稍后！");
      return;
    }
    message.info("开始下载");
    const list = [];
    downLoading.current = true;

    const sL = record.TaskID.split(",");
    for (let i = 0; i < sL.length; i++) {
      if (sL[i].length > 5) {
        const mList = await HGet2(`/api/v1/Media/GetListByTaskId?id=${sL[i]}`);
        if (!isEmpty(mList)) {
          list.push(...mList);
        }
      }
    }
    const qianz = record.FDepartment + "-" + record.FName + "-" + record.FTime;
    await handleBatchDownload2(list, qianz);
    downLoading.current = false;
    // return list;
  };

  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/FlightTaskFile/GetAllList", {});
    const pst3 = pst.filter((p) => p.FType == "内飞");
    const pst2 = pst3.sort(
      (a, b) => dayjs(b.FTime).valueOf() - dayjs(a.FTime).valueOf()
    );

    //  if(isEmpty(pst)) return;
    setCronList(pst2);
    setXList(pst2);
  };

  const downLoadData = () => {
    downloadFile2("/api/v2/ExcelOut/FlightFile?p1=内飞");
  };
  const getExr = () => {
    // return  <AddButton   type="primary" onClick={()=>{refrush()}}>刷新</AddButton>
    //return  <AddButton   type="primary" onClick={()=>{setPage(<FlightFileAddForm refrush={refrush}></FlightFileAddForm>)}}>添加任务记录</AddButton>
    return (
      <div>
        <span>
          <AddButton
            type="primary"
            onClick={() => {
              setPage(
                <FlightFileAddForm refrush={refrush}></FlightFileAddForm>
              );
            }}
          >
            添加任务记录
          </AddButton>
        </span>

        <span style={{ marginLeft: 24.0 }}>
          <AddButton type="primary" onClick={() => downLoadData()}>
            数据导出
          </AddButton>
        </span>
      </div>
    );
  };

  const editForm = (data) => {
    setPage(
      <FlightFileEditForm refrush={refrush} data0={data}></FlightFileEditForm>
    );
  };

  //   const exr= <div><Button type="primary" onClick={()=>setCanSee(true)}>航线上传</Button> <Modal  title={null} footer={null} onOk={null} visible={canSee}  onCancel={()=>setCanSee(false)}>
  //             <WayLineAddForm/>
  // </Modal></div>

  {
    /* <CronAddForm  data={refrush}/> */
  }
  // if(isEmpty(cronList))return <div></div>
  return (
    <div style={{ margin: 0 }}>
      {/* <div><BreadTitle /></div> */}

      <Card
        title={<LastPageButton title="任务记录" />}
        bordered={false}
        extra={getExr()}
      >
        <div>
          {isEmpty(cronList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                current: tableCurrent,
                onChange: (e) => {
                  setTableCurrent(e);
                },
              }}
              bordered
              dataSource={xList}
              columns={TableCols(refrush, editForm, downloadTaskFile)}
              size="small"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default CronListPage;
