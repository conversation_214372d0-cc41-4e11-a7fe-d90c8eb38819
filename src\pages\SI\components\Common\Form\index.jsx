import React from 'react';
import { Form, Input, Select, Button, Row, Col, Space, DatePicker } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;

const DynamicSearchForm = ({ 
  formConfig = [], 
  onSearch, 
  onReset, 
  loading = false,
  cols = 4, // 默认每行4列
  formStyle = {},
  // 新增按钮配置
  actionButtons = null, // 自定义按钮配置数组
  showDefaultButtons = true, // 是否显示默认按钮
  buttonAlign = 'left', // 按钮对齐方式: 'left' | 'center' | 'right'
  actionAreaStyle = {} // 操作区域样式
}) => {
  const [form] = Form.useForm();

  // 处理查询
  const handleSearch = () => {
    form.validateFields().then(values => {
      onSearch?.(values);
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  // 渲染表单控件
  const renderFormItem = (config) => {
    const { type, placeholder, options = [], ...otherProps } = config;

    switch (type) {
      case 'input':
        return (
          <Input
            placeholder={placeholder}
            {...otherProps}
          />
        );

      case 'select':
        return (
          <Select
            placeholder={placeholder}
            allowClear
            {...otherProps}
          >
            {options.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'multiSelect':
        return (
          <Select
            mode="multiple"
            placeholder={placeholder}
            allowClear
            {...otherProps}
          >
            {options.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'dateRange':
        return (
          <RangePicker
            placeholder={placeholder || ['开始日期', '结束日期']}
            {...otherProps}
          />
        );

      case 'date':
        return (
          <DatePicker
            placeholder={placeholder}
            {...otherProps}
          />
        );

      default:
        return (
          <Input
            placeholder={placeholder}
            {...otherProps}
          />
        );
    }
  };

  // 渲染操作按钮
  const renderActionButtons = () => {
    // 如果有自定义按钮配置，使用自定义配置
    if (actionButtons && Array.isArray(actionButtons)) {
      return actionButtons.map((button, index) => {
        const { 
          key, 
          text, 
          type = 'default', 
          icon, 
          onClick, 
          loading: buttonLoading = false,
          size = 'large',
          ...otherProps 
        } = button;
        
        return (
          <Button
            key={key || index}
            type={type}
            icon={icon}
            onClick={onClick}
            loading={buttonLoading}
            size={size}
            {...otherProps}
          >
            {text}
          </Button>
        );
      });
    }

    // 如果显示默认按钮
    if (showDefaultButtons) {
      return [
        <Button
          key="search"
          type="primary"
          // icon={<SearchOutlined />}
          onClick={handleSearch}
          loading={loading}
          size="large"
        >
          查询
        </Button>,
        <Button
          key="reset"
          // icon={<ReloadOutlined />}
          onClick={handleReset}
          size="large"
        >
          重置
        </Button>
      ];
    }

    return null;
  };

  // 计算每列的span
  const colSpan = 24 / cols;

  // 默认表单容器样式（只保留布局相关样式）
  const defaultFormStyle = {
    padding: '20px',
    background: 'rgba(18, 40, 50, 0.8)',
    border: '1px solid #1abc9c',
    borderRadius: '4px',
    marginBottom: '16px',
    ...formStyle
  };

  // 操作区域样式
  const defaultActionAreaStyle = {
    marginBottom: 0,
    marginTop: '16px',
    textAlign: buttonAlign,
    ...actionAreaStyle
  };

  const buttons = renderActionButtons();

  return (
    <div style={defaultFormStyle} className="dynamic-search-form">
      <Form
        form={form}
        layout="horizontal"
      >
        {/* 表单项区域 */}
        <Row gutter={[16, 16]}>
          {formConfig.map((item, index) => (
            <Col span={colSpan} key={item.name || index}>
              <Form.Item
                name={item.name}
                label={item.label}
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                rules={item.rules}
                initialValue={item.initialValue}
              >
                {renderFormItem(item)}
              </Form.Item>
            </Col>
          ))}
        </Row>

        {/* 操作按钮区域 - 独占一行且可配置 */}
        {buttons && (
          <Row>
            <Col span={24}>
              <Form.Item 
                wrapperCol={{ span: 24 }}
                style={defaultActionAreaStyle}
              >
                <Space size="middle">
                  {buttons}
                </Space>
              </Form.Item>
            </Col>
          </Row>
        )}
      </Form>
    </div>
  );
};

export default DynamicSearchForm; 