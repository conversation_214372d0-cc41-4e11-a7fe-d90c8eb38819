.ButtonBox{
    position: fixed;
    z-index: 9999;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap:20%;
}

.Navbtn .items {
    filter: drop-shadow(#091445 0 5px 10px);
    width: 185px;
    height: 42px;
    border: 0;
    background: url('@/assets/img/btnNav.png') center center no-repeat;
    background-size: 100% 100%;
 

}

.Navbtn .items>span {
    color: #fff;
 
    text-shadow: #000e4c 2px 5px 8px;
}

.Navbtn .items:hover {
    filter: brightness(1.5);
    transform: translateY(-3px);
}

.Navbtn+.Navbtn {
    margin-left: 10px
}
.scroll_container {
    max-height: 300px;
    overflow: auto;
    -ms-overflow-style: none;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.scroll_container::-webkit-scrollbar {
    width: 5px;
}

.scroll_container::-webkit-scrollbar-thumb {
    border-radius: 100%;
    background-color: rgba(118, 220, 251, 0.886);
    background: linear-gradient(to bottom, rgba(118, 220, 251, 0.301), rgb(118, 220, 251));
}