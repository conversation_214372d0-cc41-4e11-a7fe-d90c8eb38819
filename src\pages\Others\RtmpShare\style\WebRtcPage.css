/* .video-buffering::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid #ccc;
    border-top-color: #000;
    animation: spin 1s infinite linear;
  }
 
  @keyframes spin {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
  #myRtmpShareVideo::internal-loading-indicator{
    display: none!important;
  }
  #myRtmpShareVideo::-webkit-media-controls-timeline {
      display: none;
  } */