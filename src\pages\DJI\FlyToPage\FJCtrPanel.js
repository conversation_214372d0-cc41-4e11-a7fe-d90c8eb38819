import { isEmpty } from "@/utils/utils";
import { Button} from 'antd';
import { useModel } from "umi";
import CameraCtrPanel from "./CameraCtrPanel";
import { useState } from "react";
import { CameraDrag,CameraJT,CameraPai,CameraZoom,ToHome ,CameraRefrush} from "./helper";

const ModeCodeJson = { "0": "空闲中", "1": "现场调试", "2": "远程调试", "3": "固件升级中", "4": "作业中" }
const CoverStateJson = { "0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常" }


const FJCtrPanel = (sn) => {

  const { fj } = useModel('droneModel')
  const [ifWhite,setIfWhite]=useState(true)
  const [ifHome,setIfHome]=useState(false)

  const {DoCMD}=useModel('cmdModel')
  const device= JSON.parse( localStorage.getItem('device'));

  const SendCMD=(m1,data)=>{
    if(isEmpty(m1)) return;
      console.log('CameraCtrPanel',m1,data);
     DoCMD(device.SN,m1,data);
  }


const IfReturnHome=()=>{
  console.log('IfReturnHome',fj.data["home_distance"],ifHome)
    if(ifHome) return;
    // if(fj.data["home_distance"]>1500 ||fj.data.battery["capacity_percent"]<40){
    //      HGet2("/api/v1/WayLine/ToHome?sn="+sn)
    //      setIfHome(true)
    // }
}

const getItem = (label, child) => {
    return <div  style={{float:'left',lineHeight: '24px',marginLeft:24.0}}><span onClick={()=>setIfWhite(!ifWhite)}  style={{float:'left',lineHeight: '24px',marginLeft:8.0}}>{label+":"}</span><span style={{ float:'left',lineHeight: '24px',marginLeft:4.0}}>{child}</span></div>
}



  const getPanel = (list) => {
    return <div style={{flex:1, fontSize: 12.0, color: ifWhite ?'white': 'black' ,fontColor:'black'}} >
      {list}
    </div>
  }

  const getButton=(x,onClick)=>{
      return <Button size="small" onClick={onClick} style={{width:28,height:20,fontSize:12.0,fontWeight:'bold'}}>{x}</Button>
  }

  const tzsjP=()=>{
    return <div>
      <span> {getButton('W',()=> CameraDrag(SendCMD, 5, 0))} </span>
      <span> {getButton('A',()=> CameraDrag(SendCMD, 0, -5))} </span>
      <span> {getButton('S',()=> CameraDrag(SendCMD, -5, 0))} </span>
      <span> {getButton('D',()=> CameraDrag(SendCMD, 0, 5))} </span>
    </div>
  }

  const qhjtP=()=>{
    return <div>
      <span> {getButton('F',()=>CameraJT(SendCMD,"zoom"))} </span>
      <span> {getButton('G',()=>CameraJT(SendCMD,"wide"))} </span>
      <span> {getButton('H',()=>CameraJT(SendCMD,"ir"))} </span>
    </div>
  }

  const bjP=()=>{
    return <div>
      <span> {getButton('Q',()=>CameraZoom(SendCMD,fj.data.cameras[0].zoom_factor*2))} </span>
      <span> {getButton('E',()=>CameraZoom(SendCMD,fj.data.cameras[0].zoom_factor*0.5))} </span>
    </div>
  }

  const ytczP=()=>{
    return <div>
      <span> {getButton('C',()=>CameraRefrush(SendCMD, 0))} </span>
      <span> {getButton('V',()=>CameraRefrush(SendCMD, 1))} </span>     
    </div>
  }


  const panel2 = () => {
    const list = [];
    list.push(getItem("调整视角",tzsjP(),4));
    list.push(getItem("切换镜头",qhjtP(),3));
    list.push(getItem("镜头变焦",bjP(),2));
    list.push(getItem("云台重置",ytczP(),4));
    list.push(getItem("拍照",getButton('R',()=>CameraPai(SendCMD,fj.data)),1));
    
    list.push(getItem("一键返航",getButton('T',()=>ToHome(sn)),1));
    // list.push(getItem("上升10m",getButton('I',null),1));
    // list.push(getItem("下降5m",getButton('K',null),1));
    return getPanel(list);
  }

  if (isEmpty(fj)) return <div></div>
  const data = fj.data
  if(data.mode_code==0) return <div />
 
  IfReturnHome();

  return  <div style={{height:24.0}}>
    {panel2()}
    <CameraCtrPanel sn={sn} fj={fj.data} />

  </div>
}

export default FJCtrPanel;
