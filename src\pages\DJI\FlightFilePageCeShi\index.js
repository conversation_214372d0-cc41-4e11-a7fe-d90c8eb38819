import  {  useState, useEffect } from 'react';

import {  Card,  Table } from 'antd';

import { getBodyH,isEmpty } from '@/utils/utils';

import { Get2 } from '@/services/general';



import EditForm from './form_edit';
import DetailForm from './detail';

import TableCols from './table';

import { useModel } from 'umi';
import LastPageButton from '@/components/LastPageButton';
import AddButton from '@/components/AddButton';


const ListPage = (props) => {

  const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
  const [xList, setXList] = useState([]);

  const ifPush = (e) => {
    // if (!isEmpty(sJC)) {
    //   if (e.SN != sJC) {
    //     return false;
    //   }
    // }
    return true;
  }

  const getData = async () => {
    setOpen(false);
    let pst = await Get2('/api/v1/FlightTaskFile/GetAllList', {});
    if(isEmpty(pst)) pst=[];
    const xL = [];
    pst.forEach(e => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    setXList([...xL]);
  };


  useEffect(() => {
    getData();
  }, []);



  const getExr = () => {
    return <div>
      
      <span><AddButton type="primary" onClick={() => { setPage(<EditForm refrush={getData} data0={{}}></EditForm>) }}>新建</AddButton></span> 
      <span style={{marginLeft:12.0}}> <AddButton   type="primary" onClick={()=>{getData()}}>刷新</AddButton></span>
    
     </div> 
  }


  const editForm = (data) => { setPage(<EditForm refrush={getData} data0={data}></EditForm>) }
  const detailForm = (data) => { setPage(<DetailForm  record={data}></DetailForm>) }

  return (

    <div style={{ margin: 0, height: getBodyH(56), background: '#F5F5FF' }}>
      <Card title={<LastPageButton title="数据列表" />} bordered={false} extra={getExr()} >
        <div>
          {isEmpty(xList) ? <div /> : <Table pagination={true}
            bordered dataSource={xList} columns={TableCols(getData, editForm,detailForm)} size='small' />}
        </div>
      </Card>
    </div>
  )
};

export default ListPage;
