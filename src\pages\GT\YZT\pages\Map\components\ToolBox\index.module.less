// 工具箱组件样式
.toolboxContainer {
  position: relative;
  z-index: 1000;

  .toolboxButton {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #333;

    &:hover {
      border-color: #40a9ff;
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    &.active {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      
      &:hover {
        border-color: #40a9ff;
        background: rgba(24, 144, 255, 0.15);
      }
    }

    :global(.anticon) {
      font-size: 16px;
    }
  }
}

.toolboxMenuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  width: 100%;
  min-height: 40px;

  .menuItemText {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .menuItemStatus {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    font-weight: 400;
  }

  &.active {
    .menuItemText {
      color: #1890ff;
      font-weight: 600;
    }

    .menuItemStatus {
      background: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
  }

  &.disabled {
    .menuItemText {
      color: #bfbfbf;
    }

    .menuItemStatus {
      background: rgba(0, 0, 0, 0.04);
      color: #bfbfbf;
    }
  }
}

// 下拉菜单样式
:global(.toolboxDropdown) {
  :global(.ant-dropdown-menu) {
    padding: 8px 0;
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    border: 1px solid #f0f0f0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    min-width: 160px;

    :global(.ant-dropdown-menu-item) {
      padding: 0;
      margin: 2px 8px;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(24, 144, 255, 0.08);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .toolboxContainer {
    .toolboxButton {
      padding: 6px 10px;
      height: 32px;
      font-size: 13px;
    }
  }

  .toolboxMenuItem {
    padding: 8px 10px;
    min-height: 36px;

    .menuItemText {
      font-size: 13px;
    }

    .menuItemStatus {
      font-size: 11px;
    }
  }

  :global(.toolboxDropdown) {
    :global(.ant-dropdown-menu) {
      min-width: 140px;
    }
  }
}
