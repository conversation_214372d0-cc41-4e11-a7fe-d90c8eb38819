import { useState, useEffect } from "react";
import MyHead from "../components/MyHead";
import commonStyle from "../style/common.less";
import "@/pages/GT/style/antd-common.less";
import { useModel } from "umi";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import { queryPage } from "@/utils/MyRoute";
import { BranchesOutlined } from '@ant-design/icons';
import MyMenu from '@/pages/GT/GDBH/Pages/MyMenu';

import RecordsPage from "@/pages/GT/components/RecordsPage";
import VideoManagement from './Pages/VideoManagement';
import WayLineListPage from '@/pages/DJI/WayLine/index';
import NfzPage from "@/pages/DJI/NfzPage";
import AIAlgorithm from "@/pages/DJI/AIPage/ModelListPage3";
import TargetRecognition from '@/pages/GT/ZZCH/Pages/TaskManagement/Pages/TargetRecognition'
import FlightTask from '@/pages/SI/components/flightTask'
import TaskListPage from "@/pages/DJI/TaskListPage/index2";
import Zheng<PERSON>heMap from "@/pages/Maps/MapPanel/ZhengSheYingXiang";
import JuanLianMap from "@/pages/Maps/MapPanel/JuanLian";
import Model3DMap from "@/pages/Maps/MapPanel/Model3DMap";
import SanlianJuanLian from "@/pages/Maps/MapPanel/SanlianJuanLian";
import MapOperatePage from "@/pages/Maps/MapPanel/MapOperatePage/index";
import ChangeDetection from '@/pages/GT/GDBH/Pages/ChangeDetection';

import HeadTabs from "@/pages/GT/components/HeadTabs";
import SITheme from '@/pages/SI/style/theme';

//智慧耕地 耕地保护
function App() {
  const [collapsed, setCollapsed] = useState(false);
  
  const { page, setPage, lastPage, currentPage } = useModel("pageModel");


  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };

  function getItem(label, key, icon, children, type) {
    return {
      label,
      key,
      icon,
      children,
      type,
    };
  }

  // 子页面映射表
  const subPageMap = {
    IntelligentInspection: <RecordsPage title='巡检计划' type='GDBH' />,
    VideoManagement: <VideoManagement/>,
    waylineManage: <WayLineListPage doNotShowLastButton={true} />,
    NfzPage: <NfzPage doNotShowLastButton={true}/>,
    AIAlgorithm: <AIAlgorithm isSipage={true}/>,
    TargetRecognition: <TargetRecognition doNotShowLastButton={true} taskType='目标识别'/>,
    flightTask: <FlightTask  doNotShowLastButton={true}/>,
    TaskListPage: <TaskListPage doNotShowLastButton={true}/>,
    ZhengSheMap: <ZhengSheMap />,
    JuanLianMap: <JuanLianMap />,
    Model3DMap: <Model3DMap doNotShowLastButton={true}/>,
    SanlianJuanLian: <SanlianJuanLian doNotShowLastButton={true}/>,
    MapOperatePage: <MapOperatePage doNotShowLastButton={true}/>,
    ChangeDetection: <TargetRecognition doNotShowLastButton={true} taskType='变化检测'/>,
    SemanticSegmentation: <TargetRecognition doNotShowLastButton={true} taskType='语义分割'/>,
  };

  // 配置顶部项
  const headList = [
    { label: "数据管理", key: "GDBH数据管理" },
    { label: "智能巡飞", key: "巡飞任务" },
    // { label: "变化检测", key: "变化检测" },
    { label: "AI算法仓", key: "AI算法仓" },
  ];

  // 配置侧边菜单项
  const menuItems = () => {
    switch (currentPage) {
      case "GDBH数据管理":
        return [
          getItem('飞行记录', 'TaskListPage', <BranchesOutlined />),
          getItem('正射影像', 'ZhengSheMap', <BranchesOutlined />),
          getItem('正射对比', 'JuanLianMap', <BranchesOutlined />),
          getItem('三维模型', 'Model3DMap', <BranchesOutlined />),
          getItem('三维对比', 'SanlianJuanLian', <BranchesOutlined />),
          getItem('地图管理', 'MapOperatePage', <BranchesOutlined />),


        ];
      case "巡飞任务":
        return [
          getItem('巡飞任务','flightTask',<BranchesOutlined />),
          getItem('巡检计划', 'IntelligentInspection', <BranchesOutlined />),
          getItem('影像管理', 'VideoManagement',  <BranchesOutlined />),
          getItem('航线管理', 'waylineManage', <BranchesOutlined />),
          getItem('电子围栏', 'NfzPage',  <BranchesOutlined />)
        ];
      case "监测处置":
        return [];
      case "AI算法仓":
        return [
          getItem('算法模型', 'AIAlgorithm', <BranchesOutlined />),
          getItem('目标识别', 'TargetRecognition', <BranchesOutlined />), 
          getItem('变化检测', 'ChangeDetection', <BranchesOutlined />),
          getItem('语义分割', 'SemanticSegmentation', <BranchesOutlined />),
        ];
      default:
        return [];
    }
  };

  // 处理菜单点击
  const handleMenuSelect = key => {
    // 设置当前页面
    setPage(subPageMap[key]);
  };

  return (
    <div className="gt-page">
      <div
        className={commonStyle.gt_back_black}
        style={{ position: "relative", overflow: "hidden", height: "100vh" }}
      >
        <MyHead headList={headList} handlePageChange={handlePageChange} />

        <div>
          <MyMenu
            className=''
            handlePageChange={handleMenuSelect}
            setCollapsed={setCollapsed}
            collapsed={collapsed}
            menuItems={menuItems()}
          />
          <div
            className="blackBackground"
            style={{
              marginLeft: collapsed ? 75 : 75,
              transition: 'margin-left 0.2s',
              height: '100%',
              overflow: 'hidden',
            }}
          >
            <ConfigProvider locale={locale} theme={SITheme}>
              {/* <HeadTabs></HeadTabs> */}
              {page}
            </ConfigProvider>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
