import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';
import icon4 from '@/assets/icons/xunluodui.png';
import { BorderBox7 } from '@jiaminghi/data-view-react';

const ZhiBanPanel = () => {
  const data=[
    ['1#', '张正午', '158xxxxxx'],
    ['2#', '张正午', '158xxxxxx'],
    ['3#', '张正午', '158xxxxxx'],
    ['4#', '张正午', '158xxxxxx'],
    ['5#', '张正午', '158xxxxxx'],
  ]

  const getItem=(title,count,bCol)=>{
    return <div style={{background:bCol,marginTop:8.0, display: 'flex', height:36.0 ,fontSize:14.0}}>
       <div style={{width:50,marginLeft:8.0}}><img src={icon4} height={36.0} width={36.0} /></div>
     <div style={{flex:'auto'}}><p style={{lineHeight:'36px',color:'white'}}>{title}</p></div>
     <div style={{width:180,marginRight:16.0}}><p style={{lineHeight:'36px',color:'white',textAlign:'right'}}>{count}</p></div>
   </div>
   }

 const getL=()=>{
  const list=[]
  let i=0;
  data.forEach(e => {
    i++;

    console.log(e);
    if(i%2==0){
      list.push( getItem(e[1],e[2],''))

    }else{
      
      list.push( getItem(e[1],e[2],`rgba(0,45,139,0.6)`))
    }
  })
  return list;
 }
  return <div style={{ height: '45%', width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.6)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="值班人员情况" />
       <div style={{marginTop:24.0}}>
        {getL()}
       </div>
    </BorderBox7>
  </div>
}

export default ZhiBanPanel;
