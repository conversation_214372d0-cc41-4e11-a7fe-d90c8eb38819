import React, { useEffect, useState } from "react";
import { Divider, Radio, Table, ConfigProvider, Select,Input,CloseCircleOutlined } from "antd";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import img from "@/assets/17bg.jpg";
import TableCols from "./table";
import { Get2, Post2 } from "@/services/general";
import { SearchOutlined } from "@ant-design/icons";
import UpdateTable from "./update_table";
import EditTable from "./edit_table";
import DetialTable from "./detial_table";
import { timeFormat,getGuid } from "@/utils/helper";
import useConfigStore from "@/stores/configStore";
import L from "leaflet";
const { Search } = Input;

const suffix = (
  <SearchOutlined
    style={{
      fontSize: 16,
      color: "#1abc9c",
    }}
  />
);
const App = () => {
  const { MapSelf } = useConfigStore();
  const [list, setList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const pageSize = 20; // 固定每页20条

  // 搜索框方法
  const onSearch = (value) => {
    if (value) {
      GetAllList(1, `"室内编号"='${value}'`);
    } else {
      // 清空查询条件
      GetAllList(1);
    }
  };
  const GetAllList = async (page = 1, filter = '') => {
    try {
      // 对中文字段名进行编码处理
      const encodedFilter = filter 
      ? encodeURIComponent(filter.replace(/(\w+)=/g, '"$1"')) 
      : '';
      // 添加过滤参数
      const filterParam = encodedFilter ? `&CQL_FILTER=${encodedFilter}` : '';
      // 请求当前页数据
      const startIndex = (page - 1) * pageSize;
      // 请求URL保持不变
      const dataUrl = `http://47.108.71.9:38081/geoserver/bdzl/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=bdzl:dzzhd&outputFormat=application/json&startIndex=${startIndex}&maxFeatures=${pageSize}${filterParam}`;
      
      const dataResponse = await fetch(dataUrl);
      if (!dataResponse.ok) throw new Error('数据请求失败');
      const data = await dataResponse.json();
      // 转换数据格式
      const dataSource = data.features.map(item => ({
        ...item.properties,
        geometry: item.geometry.coordinates
      }));
      setList(dataSource);
      // setTotal(415)
      // 这里踩了个坑 总数据获取到是有26539条 但是在GeoServer设置了最大返回数量的限制 只能返回到415条
      // 获取总数数据
      const countUrl = `http://47.108.71.9:38081/geoserver/bdzl/ows?service=WFS&version=1.1.0&request=GetFeature&typeName=bdzl:dzzhd&resultType=hits${filterParam}`;
      const countResponse = await fetch(countUrl);
      const countText = await countResponse.text();
      
      // 解析XML获取总数
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(countText, "text/xml");
      const totalFeatures = xmlDoc.getElementsByTagName("wfs:FeatureCollection")[0]
        .getAttribute("numberOfFeatures");
      
      setTotal(parseInt(totalFeatures, 10));
    } catch (error) {
      console.error('请求错误:', error);
    }
  };

  // 分页变化处理
  const handleTableChange = (pagination) => {
    setCurrentPage(pagination.current);
    GetAllList(pagination.current);
  };

  useEffect(() => {
    GetAllList(1);
  }, []);
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.name === 'Disabled User',
      // Column configuration not to be checked
      name: record.name,
    }),
  };
  return (
    <div>
      <ConfigProvider
        theme={{
          components: {
            Input: {
              colorBgContainer: "rgba(21, 33, 33,0.5)",
              colorText: "#fff",
              colorTextPlaceholder: "rgba(255,255,255,0.5)",
            },
            Button: {
              defaultBg: "none",
              defaultActiveBg: "none",
              defaultHoverBg: "none",
            },
            Select: {
              colorBgContainer: "rgba(21, 33, 33,0.5)",
            },
          },
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            margin: "30px 0",
          }}
        >
          <div style={{whiteSpace:'nowrap'}}>
          <Input
            style={{ width: 180 }}
            suffix={suffix}
            placeholder="请输入室内编号"
            allowClear
            onChange={(e) => {
              // 当清空输入时自动刷新列表
              if (!e.target.value) {
                onSearch('')
              }
            }}
            onPressEnter={(e) => onSearch(e.target.value)}
          />
          </div>
          <div style={{display:'flex',gap:10,alignItems:'center'}}>
          </div>
        </div>
      </ConfigProvider>
      <Table 
        
        rowSelection={{
          type: "checkbox",
          ...rowSelection,
        }}
        pagination={{
          pageSize: pageSize,
          current: currentPage,
          total: total,
          showSizeChanger: false,
        }}
        onChange={handleTableChange}
        columns={TableCols(GetAllList)}
        dataSource={list}
        bordered
        size="small"
        scroll={{
                 scrollToFirstRowOnChange:true,
                 y:600,
        }}
        onRow={(record) => {
          return {
            onClick: (event) => {
              // wfs服务获取到的geometry数据是[经度，纬度] 跟我们地图坐标是反的 要转换成[纬度，经度]
              const template = [record.geometry[1], record.geometry[0]]

              // 地图中心移动到当前点 缩放值为15
              MapSelf.setView(template, 15);

              // 添加静态变量存储圆圈标记引用
              if (!window.circleRef) window.circleRef = null;

              // 移除之前的标记
              if (window.circleRef) {
                MapSelf.removeLayer(window.circleRef);
                window.circleRef = null;
              }
              // 创建高亮圆圈
                window.circleRef = L.circleMarker(template, {
                  color: 'yellow',
                  fillColor: 'transparent',
                  weight: 4,
                  opacity: 1,
                  radius: 15
                }).addTo(MapSelf);

              // 弹出详情弹框
              // useConfigStore.getState().handleMapClick(template,MapSelf);
              
              // 清理函数 移除点击事件 避免内存泄漏
              return () => {
                if (mapRef.current) {
                  mapRef.current.off("click"); // 移除点击事件
                  mapRef.current.remove(); // 销毁地图
                }
              };
            }
          }
        }
        }
      />
    </div>
  );
};
export default App;