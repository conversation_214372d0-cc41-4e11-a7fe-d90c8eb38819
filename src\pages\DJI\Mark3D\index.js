import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { getBodyH, isEmpty } from '@/utils/utils';
import useMark3DHooks from '@/hooks/Mark3DHooks'

const Mark3DPage = () => {
    let { Mark3D, getViewer } = useMark3DHooks({ idName: "cesisss" });

    return (
        <div style={{ height: getBodyH(56), background: '#F5F5FF' }}>
            <div style={{ height: getBodyH(108), width: '100%', padding: 12.0, position: 'relative' }} id="cesisss">
                <Mark3D />
            </div>
        </div>
    )
};

export default Mark3DPage;
