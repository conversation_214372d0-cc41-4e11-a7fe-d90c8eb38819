import * as PANOLENS from "panolens";
import { useEffect, useRef } from "react";
import { getImgUrl } from "@/utils/utils";
const Panorama = ({ picture }) => {
  //全景图组件
  const panolensRef = useRef(null);
  const viewerRef = useRef(null);

  useEffect(() => {
    if (panolensRef.current && picture) {
      console.log(picture);

      const panorama = new PANOLENS.ImagePanorama(getImgUrl(picture));
      viewerRef.current = new PANOLENS.Viewer({
        container: panolensRef.current,
        autoRotate: true, //自动播放
        autoRotateActivationDuration: 2000, //时长
        autoRotateSpeed: 0.3, //速度
        enableKeyboardControl: true, //键盘控制
        enableTouchControl: true, //触摸控制
      });

      viewerRef.current.add(panorama);
    }

    return () => {
      if (viewerRef.current) {
        viewerRef.current.dispose();
        viewerRef.current = null;
      }
    };
  }, []);

  return (
    <div
      ref={panolensRef}
      style={{ position: "relative", width: "700px", height: "400px" }}
    ></div>
  );
};

export default Panorama;
