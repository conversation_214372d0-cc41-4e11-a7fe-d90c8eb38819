const canvasHelper = {
    // 画线段
    drawLine(ctx, x1, y1, x2, y2, color, width) {
      ctx.beginPath();
      ctx.lineWidth = width;
      ctx.strokeStyle = color;
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.stroke();
    },

    // 画数组线段
    drawLine2(ctx,pointList,lineWidth,strokeStyle){
      if(pointList.length < 2) return; //至少需要两点才能连线
      ctx.beginPath();
      ctx.lineWidth = lineWidth;  // 线条宽度
      ctx.strokeStyle = strokeStyle; // 线条颜色

      ctx.moveTo(pointList[0].x, pointList[0].y); // 起点
      for(let i = 1; i < pointList.length; i++){
        ctx.lineTo(pointList[i].x, pointList[i].y); // 路径
      }
      ctx.stroke(); // 画线
    },

    // 画带标注的点
    drawPointWithRemarks(ctx, x, y,color,text){
      this.drawPoint(ctx, x, y,color);
      this.drawRemarks(ctx,x,y,color,text);
    },

    // 画实心圆 // 画点
    drawPoint(ctx, x, y,color,radius) {
      // ctx.strokeStyle = color;                      //设置描边画笔颜色
      ctx.fillStyle = color;                        //设置填充画笔颜色
      ctx.beginPath();                              //开始绘制
      ctx.arc(x,y,radius,0,2*Math.PI,false);              //绘制圆弧
      ctx.closePath();                              //结束绘制
      // ctx.stroke();                                 //描边
      ctx.fill();                                   //填充
    },

    // 画标注
    drawRemarks(ctx,x,y,color,text){
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.lineWidth = 3;
      ctx.strokeStyle = color;
      ctx.moveTo(x,y);
      ctx.lineTo(x+20,y-40)
      ctx.lineTo(x+80,y-40)
      ctx.stroke();
      ctx.strokeRect(x+80,y-55,200,100);                         // 画边框
      ctx.clearRect(x+80,y-55,200,100);                          // 清除内里的线条 
      this.drawText(ctx,x+180,y-5,"red",text,20)
    },

    // 画文字
    drawText(ctx, x, y, color, text, fontSize) {
      ctx.font = `${fontSize}px Arial`;
      ctx.fillStyle = color;
      ctx.textBaseline = 'middle'; // 垂直居中
      ctx.textAlign = "center";    // 水平居中
      ctx.fillText(text,x,y);
    },

    // 画多边形
    drawPolygon(ctx,pointList,strokeStyle,lineWidth){
      if(pointList.length < 2) return; //至少需要两点才能连线
      ctx.beginPath();
      ctx.lineWidth = lineWidth;  // 线条宽度
      ctx.strokeStyle = strokeStyle; // 线条颜色

      ctx.moveTo(pointList[0].x, pointList[0].y); // 起点
      for(let i = 1; i < pointList.length; i++){
        ctx.lineTo(pointList[i].x, pointList[i].y); // 路径
      }
      ctx.closePath(); // 闭合路径
      ctx.stroke(); // 画线
    },

    // GSD算法 像素/m
    calculatePixelPerMeter(fj){
      // height: 相机飞行高度（m）
      // sensor_size: 传感器物理尺寸（mm）
      // focal_length: 相机焦距（mm）
      // image_size: 图像分辨率（像素数）
      // GSD 值（m/像素）
      // GSD = (height * sensor_size) / (focal_length * image_size)
      const GSD =  (24 * 3956) / (fj.data.elevation * 18)
      return GSD
    },

    // 返回缩放后的坐标数组
    scaleCoordinates(points, canvas) {

      const cameraWidth = 5280;
      const cameraHeight = 3956;

      const canvasAspect = canvas.width / canvas.height;
      const originalAspect = cameraWidth / cameraHeight;
      let scale, offsetX = 0, offsetY = 0;
    
      // 判断是否宽度方向需要裁剪
      if (originalAspect > canvasAspect) {
        // 高度撑满，宽度方向有裁剪
        scale = canvas.height / cameraHeight;
        const scaledWidth = cameraWidth * scale;
        offsetX = (canvas.width - scaledWidth) / 2;
      } else {
        // 宽度撑满，高度方向有裁剪
        scale = canvas.width / cameraWidth;
        const scaledHeight = cameraHeight * scale;
        offsetY = (canvas.height - scaledHeight) / 2;
      }
    
      return points.map(point => ({
        x: point[0] * scale + offsetX,
        y: point[1] * scale + offsetY
      }));
    },

    drawPointList(pointList,canvas){
      const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布
        // 画点
        pointList.Point.forEach(point => {
          // 缩放坐标
          const scaled = this.scaleCoordinates([point.pixels_point], canvas);
          this.drawPoint(ctx, scaled[0].x, scaled[0].y, point.fillStyle, point.radius);
        });

        // 画线
        pointList.LineString.forEach(line => {
          // 缩放坐标
          const scaledPoints = this.scaleCoordinates(line.pixels_point,canvas)
          this.drawLine2(ctx,scaledPoints,line.lineWidth,line.strokeStyle)
        });

        // 画多边形
        pointList.Polygon.forEach(polygon => {
          // 缩放坐标
          const scaledPoints = this.scaleCoordinates(polygon.pixels_point,canvas)
          this.drawPolygon(ctx,scaledPoints,polygon.strokeStyle,polygon.lineWidth)
        });
    },

    // 画图函数 传入渲染数据
    draw(pointList,canvas,fj){
      if(pointList === null){
        return;
      }else{
        // 初始绘制
        this.drawPointList(pointList,canvas);
        // 预测绘制
        const fjCamara = fj.data.cameras[0].payload_index;
        const gimbal_yaw = fj.data[fjCamara].gimbal_yaw; // 无人机摄像头云台偏航角
        const drone_yaw = fj.data.attitude_head; //无人机偏航角
        const relative_yaw = drone_yaw - gimbal_yaw; //相对偏航角
        const horizontal_speed = fj.data.horizontal_speed; // 无人机水平速度
        const radian = (90 - relative_yaw) * Math.PI / 180;
        const dx = horizontal_speed * Math.cos(radian); // X轴位移速度  m/s
        const dy = horizontal_speed * Math.sin(radian); // Y轴位移速度  m/s
/*         const dx = 0; // X轴位移速度  m/s
        const dy = horizontal_speed; // Y轴位移速度  m/s */
        const pixelMovement = this.calculatePixelPerMeter(fj);
        const speed_pixelX = dx * pixelMovement; // X轴像素位移速度 像素/s
        const speed_pixelY = dy * pixelMovement; // Y轴像素位移速度 像素/s
 
        const duration = 2000; // 预测时长 2000ms
        const interval = 1000 / 60 * 3; // 帧率 60fps

        // 深拷贝原始数据
        const originalData = JSON.parse(JSON.stringify(pointList));
        // 当前的预测数据
        let currentData = JSON.parse(JSON.stringify(originalData));
        
        // 定时器 每5帧预测一次位移
        // 在定时器内创建数据副本并预测修改坐标
        const ctx = canvas.getContext('2d');
        
        if(fj.data.horizontal_speed === 0){
          return;
        }
        
        var timer = setInterval(() => {
          
          const newList = JSON.parse(JSON.stringify(currentData)); 
          
          // 遍历所有点类型
          newList.Point.forEach(point => {
            point.pixels_point[0] += speed_pixelX * (interval/1000); // X坐标增加
            point.pixels_point[1] += speed_pixelY * (interval/1000); // Y坐标增加
          });

          // 遍历所有线类型
          newList.LineString.forEach(line => {
            line.pixels_point.forEach(pt => {
              pt[0] += speed_pixelX * (interval/1000);
              pt[1] += speed_pixelY * (interval/1000);
            });
          });

          // 遍历所有多边形类型
          newList.Polygon.forEach(polygon => {
            polygon.pixels_point.forEach(pt => {
              pt[0] += speed_pixelX * (interval/1000);
              pt[1] += speed_pixelY * (interval/1000);
            });
          });

          currentData = newList;
          this.drawPointList(newList, canvas);
        }, interval);


        // 2000ms后清除定时器
        setTimeout(function(){
          clearInterval(timer);
        },duration)

        // 将定时器的引用返回出去
        return timer;

      }

      
    }
  };
 
  export default canvasHelper;
  