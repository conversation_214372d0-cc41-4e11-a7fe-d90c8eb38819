.tooltip2 {

    color: red;

}

:global(.leaflet-tooltip-bottom:before) {
    display: none;
}

// :global(.leaflet-tooltip) {
//     position: absolute;
//     padding: 6px!important;
//   background-color:#fff0!important ;
//     border:none!important;
//     border-radius:0!important;
//     color: #222!important;
//     white-space: nowrap!important;
//     user-select: none!important;
//     pointer-events: none!important;
//     box-shadow: 0 1px 3px rgb(0 0 0 / 0%)!important;
//     background: url('../../assets/img/bgmap.png') center center no-repeat;
//     background-size: auto  100%;
//     height: 46px;
//    // width: 120px;
//     min-width: 120px;
    
//     text-align: center;
//     color: #fff!important;
//     margin-top: -2px!important;
//     padding-top: 6px!important;
//     filter: drop-shadow(#000 0 3px 3px);
// }

:global(.leaflet-marker-pane) {
    z-index: 651!important;
}

:global(.leaflet-div-icon) {
    background-color: rgba(255, 255,255, 0)!important;
    border-color:  rgba(255, 255,255, 0)!important;
}
.deviceTooltip{
    //无人机设备样式
    position: absolute;
    padding: 6px!important;
  background-color:#fff0!important ;
    border:none!important;
    border-radius:0!important;
    color: #222!important;
    white-space: nowrap!important;
    user-select: none!important;
    pointer-events: none!important;
    box-shadow: 0 1px 3px rgb(0 0 0 / 0%)!important;
    background: url('../../assets/img/bgmap.png') center center no-repeat;
    background-size: auto  100%;
    height: 46px;
   // width: 120px;
    min-width: 120px;
    
    text-align: center;
    color: #fff!important;
    margin-top: -2px!important;
    padding-top: 6px!important;
    filter: drop-shadow(#000 0 3px 3px);
}
.locaSearchTooltip{
    background-color: rgba(#1484ed, 0.7)!important;
    padding: 5px;
    color: #ffffff!important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.9)!important;
}