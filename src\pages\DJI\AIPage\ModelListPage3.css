.modle-list-body {
    .placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(12, 19, 56, 0.4);
        border-radius: 5px;
        border: 1px dashed rgba(255, 255, 255, 0.15);
    }


    .list-item-container {
        height: 300px;
        display: flex;
        flex-direction: column;
        border-radius: 5px;
        overflow: hidden;
        /* border: 1px solid rgba(255, 255, 255, 0.1); */
        background: url('@/assets/矩形.png') no-repeat center/100% 100%;
    }

    .list-item-container:hover {
        transform: translateY(-2px);
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .list-item-content {
        flex: 1;
        position: relative;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .list-item-content-head {
        display: block;
        padding: 0 20px;
    }

    .list-item-content-tag {
        max-height: 80px;
        overflow-y: auto;
        padding-right: 8px;
    }

    .list-item-content-desc{
        display: block;
        padding: 0 20px 20px 20px;
    }


    .file-name {
        padding-left: 8px;
        background: transparent;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-rest {
        padding-left: 8px;
        background: transparent;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(255, 255, 255, 0.5) !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    }
}