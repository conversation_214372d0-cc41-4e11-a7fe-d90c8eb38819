const BD_FACTOR = (3.14159265358979324 * 3000.0) / 180.0; // 百度坐标系的修正因子
const PI = 3.1415926535897932384626; // 圆周率
const RADIUS = 6378245.0; // 地球半径
const EE = 0.00669342162296594323; // 椭球的偏心率平方

class CoordTransform {
  /**
   * BD-09(百度坐标系) To GCJ-02(火星坐标系) 的转换方法
   * @param lng 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static BD09ToGCJ02(lng, lat) {
    let x = +lng - 0.0065; // 经度修正
    let y = +lat - 0.006; // 纬度修正
    let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * BD_FACTOR); // z 值修正
    let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * BD_FACTOR); // theta 值修正
    let gg_lng = z * Math.cos(theta); // 修正后的经度
    let gg_lat = z * Math.sin(theta); // 修正后的纬度
    return [gg_lng, gg_lat];
  }

  /**
   * GCJ-02(火星坐标系) To BD-09(百度坐标系) 的转换方法
   * @param lng 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static GCJ02ToBD09(lng, lat) {
    lat = +lat;
    lng = +lng;
    let z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * BD_FACTOR); // z 值修正
    let theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * BD_FACTOR); // theta 值修正
    let bd_lng = z * Math.cos(theta) + 0.0065; // 修正后的经度
    let bd_lat = z * Math.sin(theta) + 0.006; // 修正后的纬度
    return [bd_lng, bd_lat];
  }

  /**
   * WGS-84(世界大地坐标系) To GCJ-02(火星坐标系) 的转换方法
   * @param lng 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static WGS84ToGCJ02(lng, lat) {
    lat = +lat;
    lng = +lng;
    if (this.out_of_china(lng, lat)) { // 如果在中国境内
      return [lng, lat];
    } else {
      let d = this.delta(lng, lat); // 计算经纬度偏移值
      return [lng + d[0], lat + d[1]]; // 返回修正后的经纬度
    }
  }

  /**
   * GCJ-02(火星坐标系) To WGS-84(世界大地坐标系) 的转换方法
   * @param lng 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度数组
   */
  static GCJ02ToWGS84(lng, lat) {
    lat = +lat;
    lng = +lng;
    if (this.out_of_china(lng, lat)) { // 如果在中国境内
      return [lng, lat];
    } else {
      let d = this.delta(lng, lat); // 计算经纬度偏移值
      let mgLng = lng + d[0];
      let mgLat = lat + d[1];
      return [lng * 2 - mgLng, lat * 2 - mgLat]; // 返回修正后的经纬度
    }
  }

  /**
   * 计算经纬度偏移值的方法
   * @param lng 经度
   * @param lat 纬度
   * @returns {number[]} 返回经纬度偏移值数组
   */
  static delta(lng, lat) {
    let dLng = this.transformLng(lng - 105, lat - 35); // 经度偏移值
    let dLat = this.transformLat(lng - 105, lat - 35); // 纬度偏移值
    const radLat = (lat / 180) * PI;
    let magic = Math.sin(radLat);
    magic = 1 - EE * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLng = (dLng * 180) / ((RADIUS / sqrtMagic) * Math.cos(radLat) * PI);
    dLat = (dLat * 180) / (((RADIUS * (1 - EE)) / (magic * sqrtMagic)) * PI);
    return [dLng, dLat];
  }
    static transformLng(lng, lat) {
    lat = +lat;
    lng = +lng;
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
    return ret;
  }

  /**
   *
   * @param lng
   * @param lat
   * @returns {number}
   */
  static transformLat(lng, lat) {
    lat = +lat;
    lng = +lng;
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
    return ret;
  }

  /**
   * 判断是否在国内。不在国内不做偏移
   * @param lng
   * @param lat
   * @returns {boolean}
   */
  static out_of_china(lng,lat) {
    lat = +lat;
    lng = +lng;
    return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
  }
}
export default CoordTransform;
