import axios from 'axios';
import { getRtmpAIUrl,getRtmpAddr } from '@/utils/config';
export async function BeginAIVideo(sn, model, cls, conf) {
   // await StopAllAIVideo();
   const addr=getRtmpAddr();
   const aiUrl=getRtmpAIUrl();

    const myHeaders = {
        "X-API-Token": "5e8899fe-dc74-4280-8169-2f4d185f3afa",
        "Content-Type": "application/json"
    };

    const raw = {
        "source_url": "rtmp://"+addr+":1935/live/" + sn,
        "push_url": "rtmp://"+addr+":1935/live/" + sn + "ai",
        "model_path": model,
        "detect_classes": cls,
        "confidence": conf
    };

   return axios.post(aiUrl+"/ai/stream/detect", raw, { headers: myHeaders });
}

export function StopAIVideo(taskID) {
    const aiUrl=getRtmpAIUrl();
    const myHeaders = {
        "X-API-Token": "5e8899fe-dc74-4280-8169-2f4d185f3afa"
    };

    axios.post(aiUrl+"/ai/stream/" + taskID, {}, { headers: myHeaders })
        .then((response) => console.log(response.data))
        .catch((error) => console.error(error));
}

export function StopAllAIVideo() {
    const aiUrl=getRtmpAIUrl();
    const myHeaders = {
        "X-API-Token": "5e8899fe-dc74-4280-8169-2f4d185f3afa"
    };

    axios.post(aiUrl+"/ai/stream/stopTasks" , {}, { headers: myHeaders })
        .then((response) => console.log(response.data))
        .catch((error) => console.error(error));
}