import { useEffect, useRef, useState } from 'react';
import {useMapEvents,useMapEvent,Mark<PERSON>,Popup,Polygon,Polyline} from 'react-leaflet';
import {Button,Dropdown,Space,Modal, message} from 'antd';
import { redIcon } from './dt_market';
import { HGet2, HPost2 } from '@/utils/request';
import useWebSocket from 'react-use-websocket';
import { GetWebSocketUrl2 } from '@/utils/websocket';
import { IsAQQ,aqqPoints } from './helper';
import { isEmpty } from '@/utils/utils';
import { useModel } from 'umi';
import { Gcj02ToWgs84, Wgs84ToGcj02 } from './gps_helper';
import { GetDiTuGps } from './ditu';


const LocationMarker=(props)=> {

 

 
  const {gps,sn,baseMap}=props;
  const {position,setPosition,ifP,setIfP}=useModel('mapModel');
 // const pList=useRef([])
  
 const getP = (p1) => {
  const pb1 = GetDiTuGps(baseMap);
  if (pb1) {
    return Gcj02ToWgs84(p1[0], p1[1]);
  }
  return p1;
}

const getP2 = (p1) => {
  const pb1 = GetDiTuGps(baseMap);
  if (pb1) {
    return Wgs84ToGcj02(p1[0], p1[1]);
  }
  return p1;
}


  //console.log('LocationMarker',props)
  const map = useMapEvent('click', (e) => {
    // console.log('LocationMarker',e.latlng)
     if(!ifP){
        const p2=getP([e.latlng.lat,e.latlng.lng])
        localStorage.setItem('MapClickGPS',JSON.stringify(p2))
        setPosition(p2)
        setIfP(true)
     }
  })

  const FlyToPoint = (sn, lat,lng,h1) => {
    // if(isEmpty(h1)){
    //     message.info('请先起飞机场');
    //     return;
    // } 
    
    const xx=IsAQQ(lat,lng)
    if(!xx){
      message.info("该点不在安全区，禁止手动飞行！");
      return;
    }
   // HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1)
   HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1)
}

const LookToPoint = (sn, lat,lng) => {

  const data = {
      "locked": true,
      "payload_index": "80-0-0",
      "latitude": lat,
      "longitude": lng,
      "height": 530
  }
  HPost2("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_look_at", data)
}


const getPanel=()=>{
  const list=[]
  const p2=getP2(position)

  list.push(<Marker icon={redIcon} position={{lat:p2[0],lng:p2[1]}} >
        <Popup style={{width:360}}>
          <Button type='primary' style={{margin:4.0}} onClick={(e)=>{FlyToPoint(sn,position[0],position[1],gps[2]);
            e.stopPropagation();
          }}>飞向该点</Button>
           <Button  style={{margin:4.0}} onClick={(e)=>{LookToPoint(sn,position[0],position[1]);
            e.stopPropagation();
          }}>朝向该点</Button>
          <Button style={{margin:4.0}} onClick={(e)=>{
            localStorage.removeItem('MapClickGPS');
            setIfP(false);
            e.stopPropagation();
          }}>重新选点</Button>
        </Popup>
      </Marker>);
   list.push(<Polyline weight={1} color={'green'} positions={ aqqPoints} />)
   return list;
}
//if(gps[2]<860) return <div/>;

    return !ifP ? <div/> : getPanel();

  }

  export default LocationMarker;