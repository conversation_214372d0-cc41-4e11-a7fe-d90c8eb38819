
import React, { useState } from 'react';
import useBeforeUnload from './useBeforeUnloadHooks';
import { message } from 'antd';
 
function LevelPanel() {
  const [isLeavingPage, setIsLeavingPage] = useState(false);
 
  useBeforeUnload(() => {
    if (!isLeavingPage) {
      setIsLeavingPage(true);
      
      // 如果不希望显示默认提示信息，则返回空字符串
      return '';
    } else {
      // 清除状态
      setIsLeavingPage(false);
    }
  });
 
  return <div></div>;
}
 
export default LevelPanel;