import { <PERSON>, Row, Col, Button, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import droneImage from '@/pages/SI/assets/image/drone.webp';
import './index.less';

// 备用图片 URL - 当本地图片不可用时使用
const fallbackImage =
  'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22320%22%20height%3D%22180%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20320%20180%22%20preserveAspectRatio%3D%22none%22%3E%0A%20%20%3Cdefs%3E%0A%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%0A%20%20%20%20%20%20%23holder%20text%20%7B%0A%20%20%20%20%20%20%20%20fill%3A%20%23999%3B%0A%20%20%20%20%20%20%20%20font-family%3A%20sans-serif%3B%0A%20%20%20%20%20%20%20%20font-size%3A%2016px%3B%0A%20%20%20%20%20%20%20%20font-weight%3A%20400%3B%0A%20%20%20%20%20%20%7D%0A%20%20%20%20%3C%2Fstyle%3E%0A%20%20%3C%2Fdefs%3E%0A%20%20%3Cg%20id%3D%22holder%22%3E%0A%20%20%20%20%3Crect%20width%3D%22100%25%22%20height%3D%22100%25%22%20fill%3D%22%23373940%22%3E%3C%2Frect%3E%0A%20%20%20%20%3Cg%3E%0A%20%20%20%20%20%20%3Ctext%20text-anchor%3D%22middle%22%20x%3D%22160%22%20y%3D%2295%22%3E%E6%97%A0%E4%BA%BA%E6%9C%BA%E5%9B%BE%E7%89%87%3C%2Ftext%3E%0A%20%20%20%20%3C%2Fg%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E';

const AlgorithmModel = () => {
  // 算法数据
  const algorithms = [
    {
      id: 1,
      name: '超范围识别',
      tag: 'hot',
      accuracy: '95%',
      description: '边界永久识别与多源数据处理技术',
      routeCount: 13,
      planCount: 15,
      taskCount: 3,
      updateTime: '2025-04-18',
      image: droneImage,
    },
    {
      id: 2,
      name: '安全生产监测',
      tag: 'new',
      accuracy: '95%',
      description: '多方位安全风险实时检测技术',
      routeCount: 13,
      planCount: 15,
      taskCount: 3,
      updateTime: '2025-04-18',
      image: droneImage,
    },
    {
      id: 3,
      name: '多时相遥感图对比',
      tag: 'hot',
      accuracy: '95%',
      description: '多时相图像变化检测分析技术',
      routeCount: 13,
      planCount: 15,
      taskCount: 3,
      updateTime: '2025-04-18',
      image: droneImage,
    },
    {
      id: 4,
      name: '复期地块多光谱时序分析',
      tag: '',
      accuracy: '95%',
      description: '高精度时序数据分析技术',
      routeCount: 13,
      planCount: 15,
      taskCount: 3,
      updateTime: '2025-04-18',
      image: droneImage,
    },
  ];

  // 处理图片加载错误
  const handleImageError = e => {
    e.target.src = fallbackImage;
  };

  // 处理卡片点击
  const handleCardClick = algorithm => {
    console.log('点击算法:', algorithm.name);
    // 这里可以添加进入算法详情页面的逻辑
  };

  // 处理新建按钮点击
  const handleNewAlgorithm = () => {
    console.log('新建算法');
    // 这里可以添加打开新建算法模态框的逻辑
  };

  return (
    <div className="ai-algorithm-container">
      <div className="page-header">
        <h1 className="page-title">AI算法仓</h1>
        <Button type="primary" className="new-button" icon={<PlusOutlined />} onClick={handleNewAlgorithm}>
          新建
        </Button>
      </div>

      <Row gutter={[24, 24]} className="algorithm-list">
        {algorithms.map(algorithm => (
          <Col key={algorithm.id} xs={24} sm={12} lg={8} xl={6} className="algorithm-card-col">
            <Card
              hoverable
              className="algorithm-card"
              onClick={() => handleCardClick(algorithm)}
              cover={
                <div className="image-container">
                  <img alt={algorithm.name} src={algorithm.image} onError={handleImageError} />
                </div>
              }
            >
              <div className="algorithm-name">
                <Tooltip title={algorithm.description}>{algorithm.name}</Tooltip>
                <div className="accuracy-container">
                  <span>准确度</span>
                  <div className="accuracy-badge">{algorithm.accuracy}</div>
                </div>
              </div>
              <div className="algorithm-description">{algorithm.description}</div>
              <div className="algorithm-stats">
                <div className="stats-item">绑定航线 {algorithm.routeCount}条</div>
                <div className="stats-item">
                  绑定巡检计划 {algorithm.planCount}个，任务{algorithm.taskCount}个
                </div>
              </div>
              <div className="update-time">更新时间: {algorithm.updateTime}</div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default AlgorithmModel;
