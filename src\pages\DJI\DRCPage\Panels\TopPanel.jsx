import { Divide<PERSON>, <PERSON>u } from 'antd';

//import RightContent from '@/components/GlobalHeader/RightContent';
import React, { useEffect, useState } from 'react';
import { Flex, Progress,Row, Col, Button, Dropdown } from 'antd';
import LastPageButton from '@/components/LastPageButton';
import { LeftOutlined } from '@ant-design/icons';
import hImg from "@/assets/drcImgs/h2.png";
import  BatteryButton from './BatteryButton';
import  BatteryButton2 from './BatteryButton2';

import wxImg from "@/assets/drcImgs/weixing.png";
import { useModel } from 'umi';
import { ChangeFullScreen, getBodyW, getBodyW2, isEmpty } from '@/utils/utils';
import { greyCol } from './helper';
import { history } from 'umi';

const ModeCodeJson={"0":"待机","1":"起飞准备","2":"起飞准备完毕","3":"手动飞行","4":"自动起飞","5":"航线飞行","6":"全景拍照","7":"智能跟随","8":"ADS-B 躲避","9":"自动返航","10":"自动降落","11":"强制降落","12":"三桨叶降落","13":"升级中","14":"未连接","15":"APAS","16":"虚拟摇杆状态","17":"指令飞行"}


const TopPanel = () => {
  
  const {fj}=useModel('droneModel');
  const {sdrData}=useModel('dockModel');
  const [gjTitle,setGJTitle]=useState('正常飞行');
  const w2=getBodyW2(0);
  const {joyReason}=useModel('eventModel');


  useEffect(()=>{
    setGJTitle(joyReason.current);
  },[joyReason.current])

  const JDT=()=>{
  
    const panel1=(v,v2)=>{
      const list=[]
     // 
      const xx=<div style={{zIndex:1003, height:24.0,width:120,background:greyCol,color:'white',top:-12.0}}  height={18} width={18}>{'24:00'}</div>
      if(v<15){
        list.push( <div style={{width:v/100*w2,height:2.0,background:'red'}} />);
        return list;
      }

      if(v<v2||v2==0){
        list.push( <div style={{width:'15%',height:2.0,background:'red'}} />);
        list.push(  <div style={{width:(v-15)/100*w2,height:2.0,background:'orange'}} > </div>);
        return list;
      }

      list.push( <div style={{width:'15%',height:2.0,background:'red'}} />);
      list.push(  <div style={{width:(v2-15)/100*w2,height:2.0,background:'orange'}} > </div>);
      list.push( <div style={{position:'relative',zIndex:1000, width:(v-v2)/100*w2,height:2.0,background:'green'}} >
        
        <img style={{userSelect:'none' ,position:'absolute',top:-9.0,left:-9.0}} src={hImg} height={18} width={18}/> 
        
        
        </div>);
      return list;
    }
    
   return  <div style={{zIndex:1000, width:'100%',height:2.0,display:'flex'}}>
     {panel1(fj.data?.battery?.["capacity_percent"],fj.data?.battery?.["return_home_power"])}
   </div>
 
   
  }

  

  // if(isEmpty(fj)||isEmpty(sdrData)) return <div></div>

  const getBatteryLevel=(v)=>{
    //
    if(v>90) return 5;
    if(v>75) return 4;
    if(v>50) return 3;
    if(v>20) return 2;
    return 1;
  }
  const panel1=(data)=>{
   return <div style={{width:'100%',display:'flex'}}>
    <div style={{fontFamily:'MiSan',fontSize:14.0, color:'white',paddingTop:20.0}}>RTK <img style={{marginLeft:4.0, marginBottom:2.0}} src={wxImg} height={18} width={18}/> {data?.position_state?.rtk_number}</div>
    <div style={{fontFamily:'MiSan',  marginLeft:24.0,fontSize:14.0, color:'white',paddingTop:20.0}}>GPS <img style={{marginLeft:4.0, marginBottom:2.0}} src={wxImg} height={18} width={18}/> {data?.position_state?.gps_number}</div>
  
    <div style={{fontFamily:'MiSan',marginLeft:24.0, fontSize:14.0, color:'white',paddingTop:20.0}}>4G</div>
    <div style={{paddingTop:16.0,marginLeft:8.0}}><BatteryButton value={sdrData?.wireless_link?.['4g_uav_quality']}/></div>

    <div style={{fontFamily:'MiSan',marginLeft:24.0, fontSize:14.0, color:'white',paddingTop:20.0}}>SDR</div>
    <div style={{paddingTop:16.0,marginLeft:8.0}}><BatteryButton value={sdrData?.wireless_link?.sdr_quality}/></div>
    {/* <div style={{fontFamily:'MiSan',marginLeft:24.0, fontSize:14.0, color:'white',paddingTop:20.0}}>延时  44ms</div> */}
    <div style={{paddingTop:16.0,marginLeft:24.0,}}><BatteryButton2 value={getBatteryLevel(data?.battery?.["capacity_percent"])}/></div>
    <div style={{fontFamily:'MiSan',marginLeft:4.0, fontSize:14.0, color:'white',paddingTop:20.0}}>{data?.battery?.["capacity_percent"]} %</div>
</div>
  }

  const panelXH=()=>{
    const data=fj.data;
    if(data && data.battery?.["remain_flight_time"]==0){
      data.battery["remain_flight_time"]=861
    }
    if(data && data.battery?.["capacity_percent"]<96){
     return   <div style={{opacity:0.6, height:24,zIndex:1010, position:'absolute',top:48,left:w2*fj.data?.battery?.["capacity_percent"]/100}}> <div style={{fontFamily:'MiSan',background:'white', marginLeft:0.0, fontSize:14.0, color:'green',paddingTop:2.0}}>{getMinstr(data.battery?.["remain_flight_time"])}</div></div>
    }
   
  }

  const getMinstr=(v)=>{
      let v1=Math.floor( v/60);
      let v2= v%60;
      if(v2<10){
        v2="0"+v2
      }
      return v1+":"+v2
  }
  
  return (
      
      <div style={{ background: 'black',position:'relative', height: 58.0, width: '100%', margin: 0 }}>
        <Row style={{height:56}}>
          <Col span={8}> 
            <div style={{width:'100%',display:'flex'}}>
                <LeftOutlined onClick={()=>history.back()} style={{ color:'white',marginLeft:12.0, fontSize:24.0,height:56.0}}/>
                <div  style={{ width:2.0,marginLeft:20.0, marginTop:16.0,height:24.0,borderLeft:'1px solid #9D9D9D'}}></div>
                <div  style={{ flexGrow:1,margin:10.0,borderRadius:5.0, marginLeft:20.0,background:'rgba(65, 65, 65, 0.8)'}}><div style={{fontFamily:'MiSan', color:'grey',paddingTop:10.0, fontSize:16.0, marginLeft:24.0}}>{gjTitle}</div></div>
            </div>
          
          
          </Col>
         
          <Col span={8}> <div onClick={() => { ChangeFullScreen()}} style={{width:'100%',userSelect:'none', textAlign:'center',fontFamily:'MiSan', fontSize:24.0, color:'white',height:56.0,padding:14.0}}><p>{ModeCodeJson[fj.data?.mode_code]} </p> </div></Col>
          <Col span={8} >
             {panel1(fj.data)}
          </Col>

        </Row>

        {JDT()}
        {panelXH()}
      </div>
  );

}


export default TopPanel;
