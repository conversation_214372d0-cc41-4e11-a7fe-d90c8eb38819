import { HGet2, HPost2 } from '@/utils/request';
import { isEmpty } from 'lodash';

export async function PropertyGet(sn,pname) {
    const xx=await HGet2('/api/v1/Property/Get?sn='+sn+"&p1="+pname)
    if(isEmpty(xx)) return '';
    return xx.PValue;
}


export async function PropertySet(sn,pname,pval) {
    const xx=await HPost2('/api/v1/Property/Add',{SN:sn,PName:pname,PValue:pval});
    if(isEmpty(xx.err)) return true;
    return false;
}