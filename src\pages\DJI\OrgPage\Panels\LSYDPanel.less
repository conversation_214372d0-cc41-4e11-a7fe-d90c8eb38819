/* 滚动条整体样式 */
.wrapper::-webkit-scrollbar {
  width: 5px;
}

/* 滚动条thumb(滑块)样式 */
.wrapper::-webkit-scrollbar-thumb {
  border-radius: 50%; /* 圆角 */
  background-color: rgba(118, 220, 251, 0.886);
  background: linear-gradient(to bottom, rgba(118, 220, 251, 0.301), #0ac8b0);
}

/* 滚动条hover状态下thumb(滑块)样式 */
.wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(118, 220, 251, 0.553);
}

/* 滚动条上下箭头样式 */
.wrapper::-webkit-scrollbar-button {
  background-color: #ccc;
  display: none;
}

/* 滚动条左右箭头样式 */
.wrapper::-webkit-scrollbar-button:start:decrement,
.wrapper::-webkit-scrollbar-add-button {
  display: none;
}

.wrapper::-webkit-scrollbar-button:end:increment,
.wrapper::-webkit-scrollbar-sub-button {
  display: none;
}

.section {
  width: 100%;
  margin-bottom: 12px;
}

.sectionTitle {
  color: white;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  padding: 5px 10px 10px;
  margin: 0 10px 10px 10px;
  // background-color: rgb(90 132 147 / 80%);
  // border-radius: 4px 4px 0 0;
  // border: 1px solid #0b8f75;
  // 背景图
  background-image: url('../../../SI/assets/image/title_bg.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 0 10px;
}

.sectionContent {
  padding: 0 10px;
}

.LSYDSection {
  height: 100%;
  // overflow-y: auto;
}

.LSYDList {
  // display: flex;
  // flex-direction: column;
  // gap: 8px;
  max-height: calc(100% - 40px);
  overflow-y: auto;
}

.LSYDContainer {
  // background: rgba(0, 66, 128, 0.4);
  border-radius: 4px;
  width: 100%;
  transition: all 0.3s ease;
  overflow: hidden;
  margin: 4px 0;
}

.airportContainer.expanded {
  // background: rgba(0, 66, 128, 0.6);
}

.LSYDItem {
  display: grid;
  grid-template-columns: 40px 130px 65px 50px 40px;
  column-gap: 8px;
  align-items: center;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  position: relative;
  height: 50px;
  background-image: url('../../../SI/assets/image/airportItem_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // background-position: -22px 0;
  
  /* 离线状态样式 */
  &.offline {
    background-image: url('../../../SI/assets/image/airportItem_bg_offline.png');
    // cursor: not-allowed;
    opacity: 0.7;
  }
}

.LSYDIcon {
  // width: 24px;
  // height: 13px;
  // justify-self: start;
  // background-image: url('../../../SI/assets/image/airportItem.svg');
  // background-size: 100% auto;
  // background-repeat: no-repeat;
  // background-position: 0 100%;
  display: inline-block;
  width: 100%;
  height: 100%;
  grid-column: 1;
}

.xmmc {
  width: 100%;
  font-size: 14px;
  font-weight: bold;
  justify-self: start;
  grid-column: 2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  padding-right: 5px;
  box-sizing: border-box;
}

.xmmc:hover {
  cursor: pointer;
  color: #4dabd9;
}

/* 离线状态下禁用名称点击 */
// .airportItem.offline .airportName:hover {
//   cursor: not-allowed;
//   color: white;
// }

.LSYDisOverTime {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  justify-self: center;
  grid-column: 3;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  // 超时状态
  &.OverTime {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.2);
  }
  // 即将超时状态
  &.SoonOvertime{
    color: orange;
    background: rgba(255, 77, 79, 0.2);
  }
}

/* 离线状态的颜色 */
// .offline {
//   color: #ff4d4f !important;
//   background: rgba(255, 77, 79, 0.2) !important;
// }

.airportOnline:empty {
  display: none;
}

.flyToBtn {
  color: white;
  background: rgba(24, 144, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  justify-self: center;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  grid-column: 4;
}

.sectionFooter {
  color: white;
  background: rgba(24, 144, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  justify-self: center;
  grid-column: 5;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  // width: 100%;
}
