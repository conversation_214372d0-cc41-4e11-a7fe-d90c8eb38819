import React from 'react';
import { useEffect } from 'react';
import { useMapScale } from '../hooks/useMapScale';
import styles from '../index.module.css';

const LeafletScale = ({ map }) => {
  const { distanceLabel, barPixelWidth, updateScale } = useMapScale(map);

  useEffect(() => {
    if (!map) return;

    updateScale();
    map.on('zoom move', updateScale);

    return () => {
      map.off('zoom move', updateScale);
    };
  }, [map, updateScale]);

  if (!distanceLabel || barPixelWidth <= 0) {
    return null;
  }

  return (
    <div className={styles['map-scale-container']}>
      <div className={styles['map-scale-label']}>{distanceLabel}</div>
      <div className={styles['map-scale-bar-wrapper']}>
        <div
          className={styles['map-scale-bar']}
          style={{ width: `${barPixelWidth}px` }}
        />
      </div>
    </div>
  );
};

export default LeafletScale;