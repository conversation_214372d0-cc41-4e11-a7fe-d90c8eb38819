# 地名POI搜索功能 - 开发任务列表

## 📋 项目概述
基于独立组件架构，开发可复用的地名POI搜索功能，参考NFZButton的设计模式，支持多种地图引擎。

## 🎯 总体目标
创建 `PlaceSearchButton` 独立组件，实现：
- 自包含的API调用和状态管理
- 兼容Leaflet和Cesium地图引擎
- 可在任意地图页面复用
- 在YZT地图页面的ToolBox中集成

---

## 📝 开发任务清单

### Task 1: 项目结构搭建
**优先级**: High  
**预估时间**: 30分钟  
**状态**: Pending

#### 子任务
- [ ] 创建组件目录结构 `src/components/PlaceSearchButton/`
- [ ] 创建主组件文件 `index.js`
- [ ] 创建子组件目录 `components/`
- [ ] 创建工具函数目录 `utils/`
- [ ] 创建样式文件 `index.module.less`
- [ ] 创建README文档

#### 验收标准
- [ ] 目录结构符合设计规范
- [ ] 文件命名规范正确
- [ ] 基础文件创建完成

---

### Task 2: 复用现有工具函数
**优先级**: High  
**预估时间**: 30分钟  
**状态**: Pending  
**依赖**: Task 1

#### 子任务
- [ ] 导入现有的地图引擎检测工具 `@/utils/mapEngineDetector`
- [ ] 导入现有的坐标转换工具 `@/pages/Maps/gps_helper`
- [ ] 导入现有的地图判断工具 `@/pages/Maps/ditu`
- [ ] 创建 `utils/markerManager.js` - 标记管理器（基于现有工具）
- [ ] 实现Leaflet标记添加/清除方法
- [ ] 实现Cesium实体添加/清除方法

#### 验收标准
- [ ] 正确使用现有的 `detectMapEngine` 函数
- [ ] 正确使用现有的 `gcj02Towgs84` 坐标转换
- [ ] 正确使用现有的 `GetDiTuGps` 判断函数
- [ ] 标记添加/清除功能正常

---

### Task 3: 搜索API集成
**优先级**: High  
**预估时间**: 1小时  
**状态**: Pending  
**依赖**: Task 1

#### 子任务
- [ ] 创建 `hooks/usePlaceSearch.js` - 搜索逻辑Hook
- [ ] 复用现有的 `HGet2` API调用函数
- [ ] 使用现有的 `/api/v2/Map/GetPlace2` 接口
- [ ] 参考 `MeasurePanel.js` 中的搜索实现
- [ ] 实现搜索防抖处理（500ms）
- [ ] 实现搜索结果格式化
- [ ] 实现错误处理机制

#### 验收标准
- [ ] 正确使用现有的 `HGet2` 函数
- [ ] API调用正常，返回正确数据格式
- [ ] 防抖功能生效，避免频繁请求
- [ ] 最多返回5个搜索结果
- [ ] 错误处理完善，用户体验良好
- [ ] 空状态显示"没有查询到相关地点"

---

### Task 4: 搜索历史管理
**优先级**: Medium  
**预估时间**: 45分钟  
**状态**: Pending  
**依赖**: Task 3

#### 子任务
- [ ] 实现搜索历史保存功能（localStorage）
- [ ] 实现搜索历史加载功能
- [ ] 实现搜索历史清除功能
- [ ] 限制历史记录数量（最多10条）
- [ ] 实现历史记录去重逻辑

#### 验收标准
- [ ] 搜索历史正确保存到localStorage
- [ ] 历史记录按时间倒序排列
- [ ] 最多保留10条历史记录
- [ ] 重复搜索不会产生重复记录
- [ ] 清除功能正常工作

---

### Task 5: UI组件开发
**优先级**: High  
**预估时间**: 2小时  
**状态**: Pending  
**依赖**: Task 2, Task 3

#### 子任务
- [ ] 从专业UI/UX角度设计搜索按钮UI
- [ ] 创建 `components/SearchPanel.js` - 搜索面板
- [ ] 创建 `components/ResultsList.js` - 结果列表
- [ ] 创建 `components/SearchHistory.js` - 搜索历史
- [ ] 实现loading状态显示
- [ ] 实现空状态显示
- [ ] 实现响应式设计

#### 验收标准
- [ ] 搜索按钮样式美观，符合设计规范
- [ ] 搜索面板交互流畅
- [ ] 结果列表显示完整信息
- [ ] Loading和空状态显示正确
- [ ] 移动端适配良好

---

### Task 6: 主组件集成
**优先级**: High  
**预估时间**: 1.5小时  
**状态**: Pending  
**依赖**: Task 2, Task 3, Task 4, Task 5

#### 子任务
- [ ] 实现主组件 `PlaceSearchButton/index.js`
- [ ] 集成地图引擎适配器
- [ ] 集成搜索API和历史管理
- [ ] 集成UI组件
- [ ] 实现props配置系统
- [ ] 实现回调函数机制

#### 验收标准
- [ ] 组件可独立运行
- [ ] 所有props配置生效
- [ ] 回调函数正确触发
- [ ] 地图引擎自动检测和适配
- [ ] 搜索功能完整可用

---

### Task 7: 样式和主题
**优先级**: Medium  
**预估时间**: 1小时  
**状态**: Pending  
**依赖**: Task 5, Task 6

#### 子任务
- [ ] 完善 `index.module.less` 样式文件
- [ ] 实现按钮位置配置（4个方位）
- [ ] 实现自定义样式支持
- [ ] 实现主题适配（light/dark）
- [ ] 优化动画效果
- [ ] 确保样式隔离

#### 验收标准
- [ ] 样式美观，符合项目设计规范
- [ ] 位置配置正确生效
- [ ] 自定义样式可正常覆盖
- [ ] 动画效果流畅自然
- [ ] 不影响其他组件样式

---

### Task 8: YZT地图页面集成
**优先级**: High  
**预估时间**: 45分钟  
**状态**: Pending  
**依赖**: Task 6, Task 7

#### 子任务
- [ ] 在YZT地图页面导入PlaceSearchButton组件
- [ ] 在ToolBox中添加地点搜索菜单项
- [ ] 实现ToolBox与PlaceSearchButton的联动
- [ ] 配置合适的位置和样式
- [ ] 测试与现有功能的兼容性

#### 验收标准
- [ ] ToolBox菜单项显示正确
- [ ] 点击菜单项可正确切换搜索组件
- [ ] 搜索组件位置合理，不遮挡其他功能
- [ ] 与卷帘、多时相对比等功能无冲突
- [ ] 状态显示正确（已启用/未启用）

---

### Task 9: 测试和优化
**优先级**: Medium  
**预估时间**: 1小时  
**状态**: Pending  
**依赖**: Task 8

#### 子任务
- [ ] 编写组件单元测试
- [ ] 编写集成测试
- [ ] 性能优化（防抖、内存泄漏检查）
- [ ] 用户体验优化
- [ ] 错误边界处理
- [ ] 浏览器兼容性测试

#### 验收标准
- [ ] 单元测试覆盖率 > 85%
- [ ] 集成测试通过
- [ ] 无内存泄漏问题
- [ ] 响应速度 < 500ms
- [ ] 主流浏览器兼容

---

### Task 10: 文档和部署
**优先级**: Low  
**预估时间**: 30分钟  
**状态**: Pending  
**依赖**: Task 9

#### 子任务
- [ ] 完善组件README文档
- [ ] 编写使用示例
- [ ] 创建API文档
- [ ] 更新项目文档
- [ ] 创建操作日志

#### 验收标准
- [ ] 文档完整清晰
- [ ] 使用示例可运行
- [ ] API文档准确
- [ ] 操作日志记录完整

---

## 📊 进度跟踪

| 任务 | 状态 | 进度 | 负责人 | 预计完成时间 |
|------|------|------|--------|------------|
| Task 1 | Pending | 0% | - | - |
| Task 2 | Pending | 0% | - | - |
| Task 3 | Pending | 0% | - | - |
| Task 4 | Pending | 0% | - | - |
| Task 5 | Pending | 0% | - | - |
| Task 6 | Pending | 0% | - | - |
| Task 7 | Pending | 0% | - | - |
| Task 8 | Pending | 0% | - | - |
| Task 9 | Pending | 0% | - | - |
| Task 10 | Pending | 0% | - | - |

**总预估时间**: 8小时  
**当前进度**: 0%  
**预计完成日期**: TBD

---

## 🔄 更新记录
- 2024-12-19 - 创建任务列表文档
- 2024-12-19 - 定义10个主要开发任务