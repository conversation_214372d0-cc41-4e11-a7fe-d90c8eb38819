import React, { useEffect, useState } from "react";
import { useModel } from "umi";

const KeyboardListener = ({ joyHandle, CameraDrag }) => {
  const [pressedKeys, setPressedKeys] = useState(new Set());
  const device = JSON.parse(localStorage.getItem("device"));
  const { DoCMD2 } = useModel("cmdModel");

  const onKeyDown = (event) => {
    setPressedKeys((prevKeys) => {
      const newKeys = new Set(prevKeys);
      newKeys.add(event.key);
      return newKeys;
    });
  };

  const onKeyUp = (event) => {
    setPressedKeys((prevKeys) => {
      const newKeys = new Set(prevKeys);
      newKeys.delete(event.key);
      return newKeys;
    });
  };

  useEffect(() => {
    const sendCMD = () => {
      let w = 1024;
      let w2 = 1024;
      let w3 = 1024;
      let w4 = 1024;

      if (pressedKeys.has("w") && !pressedKeys.has("s")) {
        w = 1684;
      }
      if (pressedKeys.has("s") && !pressedKeys.has("w")) {
        w = 364;
      }
      if (pressedKeys.has("a") && !pressedKeys.has("d")) {
        w2 = 364;
      }
      if (pressedKeys.has("d") && !pressedKeys.has("a")) {
        w2 = 1684;
      }
      if (pressedKeys.has("q") && !pressedKeys.has("e")) {
        w3 = 364;
      }
      if (pressedKeys.has("e") && !pressedKeys.has("q")) {
        w3 = 1684;
      }
      if (pressedKeys.has("z") && !pressedKeys.has("c")) {
        w4 = 1684;
      }
      if (pressedKeys.has("c") && !pressedKeys.has("z")) {
        w4 = 1684;
      }

      joyHandle(w, w2, w3, w4);

      if (pressedKeys.has("ArrowUp")) CameraDrag(6, 0);
      if (pressedKeys.has("ArrowDown")) CameraDrag(-6, 0);
      if (pressedKeys.has("ArrowLeft")) CameraDrag(0, -6);
      if (pressedKeys.has("ArrowRight")) CameraDrag(0, 6);

      if (pressedKeys.has(" ")) {
        const gateway_sn = device.SN;
        const topic = `thing/product/${gateway_sn}/drc/down`;
        const data = {
          data: {},
          method: "drone_emergency_stop",
        };
        DoCMD2(topic, data);
      }
    };

    sendCMD();
  }, [pressedKeys, device, CameraDrag, DoCMD2, joyHandle]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!event.repeat) {
        onKeyDown(event);
      }
    };

    const handleKeyUp = (event) => {
      onKeyUp(event);
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  return <div />;
};

export default KeyboardListener;
