import { message, Card, List, Table, Space, Tag, Button } from "antd";
  import { timeFormat, getGuid } from "@/utils/helper";
    function flyToPoint(element,refrush) {
    // 飞行到指定位置
    if (element.source === "test") {
      GotoAndHolding(element,refrush);
    }
  }
  async function GotoAndHolding(element,refrush) {
    //森林防火-无人机飞行到指定位置
    let res = await axiosApi("/api/open/ForestFire/GotoAndHolding", "GET", {
      lat: element.latitude,
      lng: element.longitude,
      //  helight: element.altitude,
    });
    console.log(res, "GotoAndHolding");
    if (res.code === 1) {
      message.success("正在执行");
      updateStatus(element,refrush);
    } else {
      message.warning(res.msg);
    }
  }
  async function updateStatus(element,refrush) {
    // 更新状态
    let res = await axiosApi("/api/v1/ThirdPartyObject/UpdateStatus", "POST", {
      id: element.id,
      status: element.status,
    });
    console.log(res, "UpdateStatus");
    if (res.code === 1) {
      refrush();
    } else {
      message.error(res.msg);
    }
  }
  
  const TableCols = (refrush) => {
    return [
      {
        title: "序列",
        key: "id",
        align: "center",
        render: (record) => <Space size="middle">{record.id+1}</Space>,
      },
      {
        title: "事件",
        dataIndex: "event",
        key: "event",
        align: "center",
      },
      {
        title: "来源",
        dataIndex: "source",
        key: "source",
        align: "center",
      },
      {
        title: "创建时间",
        key: "created_at",
        align: "center",
        render: (record) => <Space size="middle">{timeFormat(record.created_at)}</Space>,
      },
      {
        title: "地图操作",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <Tag>
              <a
                onClick={() => {
                  flyToPoint(record,refrush);
                }}
              >
                飞到此处
              </a>
            </Tag>
          </Space>
        ),
      },
    ];
  };
  export default TableCols;