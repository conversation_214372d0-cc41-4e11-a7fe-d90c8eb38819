import { useState, useEffect, useRef, } from 'react';
import * as turf from '@turf/turf'
import JSZ<PERSON> from "jszip";
import axios from "axios";

function useWaylineFileHooks(waylineChange, droneSubEnumValueOptionsChange) {
    let waylineFileInfo = useRef({
        author: '',//作者
        createTime: 1747644587302,//创建时间
        updateTime: 1747646834326,//更新时间
        missionConfig: {
            flyToWaylineMode: 'safely',//飞向首航点模式
            finishAction: 'goHome',//航线结束动作
            exitOnRCLost: 'goContinue',//失控是否继续执行航线
            executeRCLostAction: 'goBack',//失控动作类型
            takeOffSecurityHeight: 30,//安全起飞高度
            takeOffRefPoint: {//参考起飞点
                longitude: 0,
                latitude: 0,
                height: 0
            },
            takeOffRefPointAGLHeight: 10,//参考起飞点海拔高度
            globalTransitionalSpeed: 15,//全局航线过渡速度
            globalRTHHeight: 100,//全局返航高度
            droneInfo: {//飞行器机型信息
                droneEnumValue: 99,//飞行器机型主类型
                droneSubEnumValue: 0//飞行器机型子类型
            },
            waylineAvoidLimitAreaMode: 0,
            payloadInfo: {//负载机型信息
                payloadEnumValue: 88,//负载机型主类型
                payloadSubEnumValue: 0,
                payloadPositionIndex: 0,//负载挂载位置
            },
        },
        Folder: {
            templateType: 'mappingPrism',//预定义模板类型 * 注：模板为用户提供了快速生成航线的方案。用户填充模板元素，再导入大疆支持客户端（如DJI Pilot），即可快速生成可执行的测绘/ 巡检航线
            templateId: 0,
            executeHeightMode: 'WGS84',//执行高度模式 * 注：该元素仅在waylines.wpml中使用。
            waylineId: 0,
            distance: 48.4277114868164,
            duration: 25.5970311164856,
            autoFlightSpeed: 2,//全局航线飞行速度
            startActionGroup: {//航线初始动作 *注：该元素用于规划一系列初始动作，在航线开始前执行。航线中断恢复时，先执行初始动作，再执行航点动作
                actionList: [
                    {
                        actionId: 0,
                        actionActuatorFunc: 'gimbalRotate',
                        actionActuatorFuncParam: {
                            gimbalHeadingYawBase: 'aircraft',
                            gimbalRotateMode: 'absoluteAngle',
                            gimbalPitchRotateEnable: 1,
                            gimbalPitchRotateAngle: 0,
                            gimbalRollRotateEnable: 0,
                            gimbalRollRotateAngle: 0,
                            gimbalYawRotateEnable: 0,
                            gimbalYawRotateAngle: 0,
                            gimbalRotateTimeEnable: 0,
                            gimbalRotateTime: 0,
                            payloadPositionIndex: 0,
                        }
                    },
                    {
                        actionId: 1,
                        actionActuatorFunc: 'hover',
                        actionActuatorFuncParam: {
                            hoverTime: 0.5,
                        }
                    },
                    {
                        actionId: 2,
                        actionActuatorFunc: 'setFocusType',
                        actionActuatorFuncParam: {
                            cameraFocusType: 'manual',
                            payloadPositionIndex: 0,
                        }
                    },
                    {
                        actionId: 3,
                        actionActuatorFunc: 'focus',
                        actionActuatorFuncParam: {
                            focusX: 0,
                            focusY: 0,
                            focusRegionWidth: 0,
                            focusRegionHeight: 0,
                            isPointFocus: 0,
                            isInfiniteFocus: 1,
                            payloadPositionIndex: 0,
                            isCalibrationFocus: 0
                        }
                    },
                    {
                        actionId: 4,
                        actionActuatorFunc: 'hover',
                        actionActuatorFuncParam: {
                            hoverTime: 1,
                        }
                    },
                ]
            },
            waylineCoordinateSysParam: {//坐标系参数
                coordinateMode: 'WGS84',//经纬度坐标系
                heightMode: 'WGS84',//航点高程参考平面
                globalShootHeight: 20
            },
            Placemark: {
                shootTopFaceEnable: 0,
                shootType: 'time',
                trajectoryType: 'horiz',
                startPositionType: 'bottom',
                overlap: {
                    orthoCameraOverlapH: 80,
                    orthoCameraOverlapW: 70
                },
                Polygon: {
                    outerBoundaryIs: {
                        LinearRing: {
                            coordinates: []
                        }
                    }
                },
                height: 0,
                topFaceFlightSpeed: 1.51590955257416,
                scanExtent: 100,
                scanOffset: 0
            },
            PlacemarkList: [],
            payloadParam: {//负载设置
                payloadPositionIndex: 0,//负载设置
                focusMode: 'firstPoint',//负载对焦模式
                meteringMode: 'average',//负载测光模式
                returnMode: 'singleReturnFirst',//激光雷达回波模式
                samplingRate: 240000,//负载采样率
                scanningMode: 'repetitive',//负载扫描模式
                imageFormat: ['visable'],//图片格式列表
            },
        }
    })
    function PolygonChange(val) {
        waylineFileInfo.current.Folder.Placemark.Polygon.outerBoundaryIs.LinearRing.coordinates = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 添加航点
    function addPlacemarkList(piontList, bearingList) {
        waylineFileInfo.current.Folder.PlacemarkList = []
        for (let i = 0; i < piontList.length; i++) {
            let Placemark = {
                Point: {
                    coordinates: { longitude: piontList[i][0], latitude: piontList[i][1] }
                },
                index: i,
                executeHeight: piontList[i][2], //* 注：该元素仅在waylines.wpml中使用。具体高程参考平面
                waypointSpeed: waylineFileInfo.current.Folder.autoFlightSpeed,//航点飞行速度，当前航点飞向下一个航点的速度
                waypointHeadingParam: {
                    waypointHeadingMode: 'smoothTransition',
                    waypointHeadingAngle: bearingList.filter(item => (item.point[0] === piontList[i][0]) && (item.point[1] === piontList[i][1]))[0].angle,
                    waypointPoiPoint: '0.000000,0.000000,0.000000',
                    waypointHeadingAngleEnable: 1,
                    waypointHeadingPathMode: 'followBadArc',
                    waypointHeadingPoiIndex: 0,
                },
                waypointTurnParam: {
                    waypointTurnMode: 'toPointAndStopWithDiscontinuityCurvature',
                    waypointTurnDampingDist: 0,
                },
                useStraightLine: 0,
                waypointGimbalHeadingParam: {
                    waypointGimbalPitchAngle: 0,
                    waypointGimbalYawAngle: 0,
                },
                isRisky: 0,
                waypointWorkType: 0,
            }

            if (i === 0) {
                Placemark.actionGroup = {
                    actionGroupId: i,
                    actionGroupStartIndex: 0,
                    actionGroupEndIndex: piontList.length - 1,
                    actionGroupMode: 'sequence',
                    actionTrigger: {
                        actionTriggerType: 'betweenAdjacentPoints'
                    },
                    actionList: [{
                        actionId: 0,
                        actionActuatorFunc: 'gimbalRotate',
                        actionActuatorFuncParam: {
                            gimbalHeadingYawBase: 'aircraft',
                            gimbalRotateMode: 'absoluteAngle',
                            gimbalPitchRotateEnable: 1,
                            gimbalPitchRotateAngle: 0,
                            gimbalRollRotateEnable: 0,
                            gimbalRollRotateAngle: 0,
                            gimbalYawRotateEnable: 0,
                            gimbalYawRotateAngle: 0,
                            gimbalRotateTimeEnable: 0,
                            gimbalRotateTime: 0,
                            payloadPositionIndex: 0,
                        }
                    },
                    {
                        actionId: 1,
                        actionActuatorFunc: 'startTimeLapse',
                        actionActuatorFuncParam: {
                            payloadPositionIndex: 0,
                            useGlobalPayloadLensIndex: 0,
                            minShootInterval: 0.708706259727478,
                        }
                    }]
                }
            } else if (i === piontList.length - 1) {
                Placemark.actionGroup = {
                    actionGroupId: i,
                    actionGroupStartIndex: i,
                    actionGroupEndIndex: i,
                    actionGroupMode: 'sequence',
                    actionTrigger: {
                        actionTriggerType: 'reachPoint'
                    },
                    actionList: [{
                        actionId: 0,
                        actionActuatorFunc: 'stopTimeLapse',
                        actionActuatorFuncParam: {
                            payloadPositionIndex: 0,
                        }
                    }]
                }
            }
            waylineFileInfo.current.Folder.PlacemarkList.push(Placemark)
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    // 飞机主型号改变
    function droneEnumValueChange(val) {
        waylineFileInfo.current.missionConfig.droneInfo.droneEnumValue = val
        // waylineFileInfo.current.missionConfig.droneInfo.droneSubEnumValue = 0
        waylineChange({ ...waylineFileInfo.current })
        droneTodroneSub(val)
    }
    // 飞机子型号改变
    function droneSubEnumValueChange(val) {
        waylineFileInfo.current.missionConfig.droneInfo.droneSubEnumValue = val
        waylineChange({ ...waylineFileInfo.current })
        creatPayloadInfo(waylineFileInfo.current.missionConfig.droneInfo)
    }
    // 根据飞机主型号生成子型号
    function droneTodroneSub(val) {
        let droneSubEnumValueOptions = [
            {
                value: 0,
                label: 'M4E',
            },
            {
                value: 1,
                label: 'M4T',
            }
        ]
        if (val === 99) {
            droneSubEnumValueOptions[0].label = 'M4E'
            droneSubEnumValueOptions[1].label = 'M4T'
        } else if (val === 77) {
            droneSubEnumValueOptions[0].label = 'M3E'
            droneSubEnumValueOptions[1].label = 'M3T'
        } else if (val === 91) {
            droneSubEnumValueOptions[0].label = 'M3D'
            droneSubEnumValueOptions[1].label = 'M3TD'
        } else if (val === 100) {
            droneSubEnumValueOptions[0].label = 'M4D'
            droneSubEnumValueOptions[1].label = 'M4TD'
        }
        droneSubEnumValueOptionsChange(droneSubEnumValueOptions)
        creatPayloadInfo(waylineFileInfo.current.missionConfig.droneInfo)
    }
    // 根据飞机主型号和子型号生成负载信息
    function creatPayloadInfo(droneInfo) {
        if (droneInfo.droneEnumValue === 77) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 66
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 67
            }
        } else if (droneInfo.droneEnumValue === 91) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 80
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 81
            }
        } else if (droneInfo.droneEnumValue === 99) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 88
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 89
            }
        } else if (droneInfo.droneEnumValue === 100) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 98
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 99
            }
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    function ChangeGlobalShootHeight(val) {
        waylineFileInfo.current.Folder.waylineCoordinateSysParam.globalShootHeight = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 重叠率改变
    function Changeoverlap(orthoCameraOverlapH, orthoCameraOverlapW) {
        waylineFileInfo.current.Folder.Placemark.overlap = {
            orthoCameraOverlapH,
            orthoCameraOverlapW
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    return {
        waylineFileInfo,
        ChangeGlobalShootHeight,
        Changeoverlap,
        droneEnumValueChange,
        droneSubEnumValueChange,
        addPlacemarkList,
        PolygonChange
    }
}
export default useWaylineFileHooks;