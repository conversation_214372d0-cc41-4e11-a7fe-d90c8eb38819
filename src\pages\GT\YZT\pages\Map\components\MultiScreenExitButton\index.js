import React, { memo } from 'react'
import { Button, Space } from 'antd'
import { CloseOutlined, SettingOutlined } from '@ant-design/icons'
import styles from './index.module.less'

/**
 * 多屏对比模式控制按钮组件
 * 在多屏对比模式下显示，提供退出和设置功能
 */
const MultiScreenExitButton = memo(({
  onExit,
  onShowConfig,
  className = ''
}) => {
  const handleExit = () => {
    try {
      if (onExit) {
        onExit()
      }
    } catch (error) {
      console.error('退出多屏模式失败:', error)
    }
  }

  const handleShowConfig = () => {
    try {
      if (onShowConfig) {
        onShowConfig()
      }
    } catch (error) {
      console.error('显示配置面板失败:', error)
    }
  }

  return (
    <div className={`${styles.multiScreenExitContainer} ${className}`} role="toolbar" aria-label="多屏对比控制工具栏">
      <Space>
        <Button
          type="default"
          icon={<SettingOutlined />}
          onClick={handleShowConfig}
          className={styles.configButton}
          size="large"
          aria-label="显示多屏配置面板"
          title="显示多屏配置面板"
        >
          设置
        </Button>
        <Button
          type="primary"
          danger
          icon={<CloseOutlined />}
          onClick={handleExit}
          className={styles.exitButton}
          size="large"
          aria-label="退出多屏对比模式"
          title="退出多屏对比模式"
        >
          退出多屏对比
        </Button>
      </Space>
    </div>
  )
})

export default MultiScreenExitButton
