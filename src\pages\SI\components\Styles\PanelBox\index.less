.panelBox {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-image: url('../../../assets/image/panel_box_bg.png');
	background-size: 100% 100%;
	// background-position: center;
	background-repeat: no-repeat;

	// 背景层
	.panelBackground {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image: url('../../../assets/image/panel_box_bg.png');
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		z-index: 1;
	}

	// 左下角装饰图标
	.panelCornerLeft {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 8px;
		height: 8px;
		background-image: url('../../../assets/image/panel_box_L.png');
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		z-index: 3;
	}

	// 右下角装饰图标
	.panelCornerRight {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 8px;
		height: 8px;
		background-image: url('../../../assets/image/panel_box_L.png');
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		z-index: 3;
		// 旋转45度
		transform: rotate(270deg);
	}

	// 内容区域
	.panelContent {
		position: relative;
		width: 100%;
		height: 100%;
		// padding: 16px;
		box-sizing: border-box;
		z-index: 2;

		// 确保内容不被角落图标遮挡
		// padding-bottom: 32px;
	}
}

// 响应式调整
@media (max-width: 768px) {
	.panelBox {

		.panelCornerLeft,
		.panelCornerRight {
			width: 18px;
			height: 18px;
		}

		.panelContent {
			padding: 12px;
			padding-bottom: 24px;
		}
	}
}

// 支持自定义尺寸
.panelBox.small {

	.panelCornerLeft,
	.panelCornerRight {
		width: 16px;
		height: 16px;
	}

	.panelContent {
		padding: 8px;
		padding-bottom: 20px;
	}
}

.panelBox.large {

	.panelCornerLeft,
	.panelCornerRight {
		width: 32px;
		height: 32px;
	}

	.panelContent {
		padding: 24px;
		padding-bottom: 40px;
	}
}