
import React, { useState } from 'react';
import { Row, Col, Button, Dropdown } from 'antd';
import styles from './topmenu.less';
import { UserOutlined } from '@ant-design/icons';

import { useModel,history } from 'umi';

import { queryPage2 } from '@/utils/MyRoute';
import { ChangeFullScreen } from '@/utils/helper';

const TopMenu = () => {
  const [current, setCurrent] = useState('')
  const { pageTitle,setPage } = useModel('pageModel')

  const handleClick = e => {
    setCurrent(e.target.innerText);
    console.log(current)
    setPage(queryPage2(e.target.innerText))
  };

  const items = [

    {
      key: '1',
      label: (<a onClick={() =>{
        localStorage.clear()
        history.push('/login');
      } }>退出登录</a>),
    },
  ]

  const UserButton = <Dropdown
    menu={{
      items
    }}
    placement="bottomRight"
    arrow
    //  theme="dark"
    className={styles.btn}
  >
    <Button type="text" icon={<UserOutlined />}>  管理员</Button>
  </Dropdown>
  const getBtn=(title)=>{
   return <Button type="text" onClick={handleClick} className={styles.btn} key={title}>{title}</Button>
  }

  return (
    <div style={{background:'white'}}>
    <div style={{background:'rgba(0,0,139,0.6)', height: 56.0, width: '100%', margin: 0 }}>

      <Row >
       


        <Col span={24} style={{ textAlign: 'center' }}>
          {/* <div onClick={()=>{ChangeFullScreen()}}> <h2 className={styles.vintage}>紫坪铺水库无人机巡检平台</h2></div> */}
            <div onClick={()=>{ChangeFullScreen()}}> <h2 className={styles.vintage}>{pageTitle}</h2></div>
        </Col>
      
        {/* <Col span={6}>
          <div>  {UserButton}</div>
        </Col> */}
      </Row>

    </div></div>
  );

}


export default TopMenu;
