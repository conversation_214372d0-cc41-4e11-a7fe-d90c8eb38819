
// 全局自定义Button样式
// .si-custom-button {

  // Primary按钮 - "查询"按钮样式
  &.ant-btn-primary {
    background: url('../assets/image/btn_bg.png') no-repeat center center;
    background-size: 100% 100%;
    border: 1px solid rgba(8, 231, 203, 0.6);
    color: rgba(8, 231, 203, 1);
    font-weight: 500;
    text-shadow: 0 0 8px rgba(8, 231, 203, 0.5);
    // box-shadow: 0 0 12px rgba(8, 231, 203, 0.3);

    &:hover {
      background: url('../assets/image/btn_bg.png') no-repeat center center;
      background-size: 100% 100%;
      border-color: rgba(8, 231, 203, 0.8);
      // box-shadow: 0 0 16px rgba(8, 231, 203, 0.5);
      text-shadow: 0 0 12px rgba(8, 231, 203, 0.8);
      // transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0px);
      box-shadow: 0 0 8px rgba(8, 231, 203, 0.6);
    }
  }

  // Default按钮 - "重置"按钮样式  
  // &.ant-btn-default {
  //   background: rgba(21, 53, 53, 1);
  //   background-size: 100% 100%;
  //   border: 1px solid rgba(255, 255, 255, 0.2);
  //   color: rgba(255, 255, 255, 1);
  //   font-weight: 500;

  //   &:hover {
  //     background: rgba(21, 53, 53, 0.9);
  //     background-size: 100% 100%;
  //     border-color: rgba(255, 255, 255, 0.4);
  //     box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
  //     // transform: translateY(-1px);
  //   }

  //   &:active {
  //     transform: translateY(0px);
  //   }
  // }

  // 禁用状态
  // &.ant-btn:disabled {
  //   background: url('../assets/image/btn_bg.png') no-repeat center center;
  //   background-size: 100% 100%;
  //   opacity: 0.4;
  //   border-color: rgba(255, 255, 255, 0.1);
  //   color: rgba(255, 255, 255, 0.3);
  //   box-shadow: none;
  //   text-shadow: none;
  // }
// }

// 全局自定义Select样式
// .si-custom-select {

  // 选择器样式
  .ant-select-selector {
    background: #0A2425;
    // border: 1px solid;
    // border-image: linear-gradient(180deg, rgba(19, 245, 231, 0.2), rgba(8, 231, 203, 0.4)) 1 1;
  }

  // 隐藏默认箭头
  .ant-select-arrow .anticon,
  .ant-select-arrow>svg {
    display: none;
  }

  // 自定义箭头图标
  .ant-select-arrow {
    // position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 0px;
      transform: translateY(-50%);
      display: inline-block;
      width: 10px;
      height: 6px;
      background: url('../assets/image/selectIcon.png') no-repeat center center;
      background-size: 10px 6px;
      pointer-events: none;
    }
  }

  // 打开状态箭头旋转
  // &.ant-select-open .ant-select-arrow::after {
  //   transform: translateY(-50%) rotate(180deg);
  // }

  // 悬停状态
  // &:hover .ant-select-selector {
  //   border-image: linear-gradient(180deg, rgba(19, 245, 231, 0.4), rgba(8, 231, 203, 0.6)) 1 1;
  // }

  // 聚焦状态
  // &.ant-select-focused .ant-select-selector {
  //   border-image: linear-gradient(180deg, rgba(19, 245, 231, 0.6), rgba(8, 231, 203, 0.8)) 1 1;
  //   box-shadow: 0 0 8px rgba(26, 188, 156, 0.3);
  // }
// }