import { isEmpty } from "@/utils/utils";
import { Card, Descriptions, Space } from 'antd';
import { useModel } from "umi";
import styles from './FJInfoPanel.less';
import FJButton from "./FJButton";
import {getGuid } from "@/utils/helper";
import BatteryButton from "@/pages/DJI/DRCPage/Panels/BatteryButton";
import PanelBox from '@/pages/SI/components/Styles/PanelBox';

const ModeCodeJson = { "0": "待机", "1": "起飞准备", "2": "起飞准备完毕", "3": "手动飞行", "4": "自动起飞", "5": "航线飞行", "6": "全景拍照", "7": "智能跟随", "8": "ADS-B 躲避", "9": "自动返航", "10": "自动降落", "11": "强制降落", "12": "三桨叶降落", "13": "升级中", "14": "未连接", "15": "APAS", "16": "虚拟摇杆状态", "17": "指令飞行" }
const CoverStateJson = { "0": "关闭", "1": "打开", "2": "半开", "3": "舱盖状态异常" }


const FJInfoPanel = ({ sn, setIfY }) => {
  
  const {fjData } = useModel('droneModel')
  const { sdrData } = useModel("dockModel");
  const getItem = (label, val, dw, color = '#08e7cb') => {
    return <Descriptions.Item key={label} labelStyle={{ fontSize: 12.0, color: 'white' }} contentStyle={{ fontSize: 12.0, color: color }} label={label}>{ val }{ dw }</Descriptions.Item>
  }

  const getPanel = (list) => {
    return <Descriptions style={{ fontSize: 12.0, color: 'white' }} column={2}>
      {list}
    </Descriptions>
  }
  // 获取信号图标
  const signal4G = () => {
    const val = sdrData.wireless_link?.["4g_uav_quality"] || 1
    return <BatteryButton style={{ width: '20px', height: '14px' }} value={val} color='#08e7cb' />
  }
  const signalSDR = () => {
    const val = sdrData.wireless_link?.["sdr_quality"] || 1
    return <BatteryButton style={{ width: '20px', height: '14px' }} value={val} color='#08e7cb' />
  }
  

  const panel2 = (data) => {
    const list = [];
    list.push(getItem("综合电量", data.battery["capacity_percent"], "%", '#FFE16F'));
    list.push(getItem("距离机场", data["home_distance"].toFixed(0), "m"));
    list.push(getItem("Gps星数", data.position_state["gps_number"], "颗"));
    list.push(getItem("RTK星数", data.position_state["rtk_number"], "颗"));
    list.push(getItem("相对高度", data["elevation"].toFixed(1), "m"));
    list.push(getItem("绝对高度", data["height"].toFixed(1), "m"));
    // list.push(getItem("经度:",data["longitude"].toFixed(3),"°"));
    // list.push(getItem("纬度:",data["latitude"].toFixed(3),"°"));
    list.push(getItem("偏航轴", data["attitude_head"].toFixed(1), "°"));
    list.push(getItem("横滚轴", data["attitude_roll"].toFixed(1), "°"));
    list.push(getItem("俯仰轴", data["attitude_pitch"].toFixed(1), "°"));
    list.push(getItem("风速", (data["wind_speed"] / 10).toFixed(1), "m/s"));

    list.push(getItem("水平速度", data["horizontal_speed"].toFixed(1), "m/s"));
    list.push(getItem("垂直速度", data.vertical_speed.toFixed(1), "m/s"));
    list.push(getItem("4G", signal4G(), ""));
    list.push(getItem("SDR", signalSDR(), ""));
    list.push(getItem("无人机状态", ModeCodeJson[data.mode_code], ""));

    return getPanel(list);
  }
  if (isEmpty(fjData.current)) return <div />

  const data = fjData.current

  const panel1 = (data) => <Space direction="vertical" style={{ fontSize: 12.0, color: 'white', marginLeft: 6.0 }} size={-5}>

    <p> <span >综合电量: {data.battery["capacity_percent"]}%</span>    <span style={{ marginLeft: 24.0 }}>距离机场: {data["home_distance"].toFixed(1)}m</span> </p>
    <p> <span >Gps星数: {data.position_state["gps_number"]}颗</span>    <span style={{ marginLeft: 24.0 }}>RTK星数: {data.position_state["rtk_number"]}颗</span> </p>
    <p> <span >相对高度: {data["elevation"].toFixed(1)}m</span>    <span style={{ marginLeft: 24.0 }}>绝对高度: {data.height.toFixed(1)}m</span> </p>
    <p> <span >水平速度: {data["horizontal_speed"].toFixed(1)}m/s</span>    <span style={{ marginLeft: 24.0 }}>垂直速度: {data.vertical_speed.toFixed(1)}m/s</span> </p>

    <p> <span>经度: {data["longitude"].toFixed(3)}</span>    <span style={{ marginLeft: 24.0 }}>纬度: {data.latitude.toFixed(3)}</span> </p>
    <p> <span >偏航轴: {data["attitude_head"].toFixed(1)}°</span>    <span style={{ marginLeft: 24.0 }}>横滚轴: {data.attitude_roll.toFixed(1)}°</span> </p>
    <p> <span >偏航轴: {data["attitude_head"].toFixed(1)}</span>    <span style={{ marginLeft: 24.0 }}>横滚轴: {data.attitude_roll.toFixed(1)}</span> </p>

  </Space>

  const titleDiv = <div key={sn} style={{ userSelect: 'none', cursor: 'pointer' }} draggable={false} onClick={() => setIfY(false)}>飞行信息</div>

  return <div className={styles.cardCon}> <Card bordered={false} size="small" title={titleDiv} extra={FJButton(sn)} headStyle={{ color: 'white' }} >
    <PanelBox>
      {panel2(data)}
    </PanelBox>
  </Card> </div>
}

export default FJInfoPanel;
