import { useState, useEffect, useRef, } from 'react';
import * as turf from '@turf/turf'
import { CreatWaylineFile, analysisWaylineFile } from '../CreatWaylineFile.js';
import JSZip from "jszip";
import axios from "axios";

function useWaylineFileHooks(waylineChange, droneSubEnumValueOptionsChange, item_height_change, item_position_change, SwitchWaypointAction, routeFeedbackFu, heightMode, wayLineData) {
    let waylineFileInfo = useRef({
        author: '',//作者
        createTime: 1747644587302,//创建时间
        updateTime: 1747646834326,//更新时间
        missionConfig: {
            flyToWaylineMode: 'safely',//飞向首航点模式
            finishAction: 'goHome',//航线结束动作
            exitOnRCLost: 'goContinue',//失控是否继续执行航线
            executeRCLostAction: 'goBack',//失控动作类型
            takeOffSecurityHeight: 30,//安全起飞高度
            takeOffRefPoint: {//参考起飞点
                longitude: 0,
                latitude: 0,
                height: 0
            },
            takeOffRefPointAGLHeight: 10,//参考起飞点海拔高度
            globalTransitionalSpeed: 15,//全局航线过渡速度
            globalRTHHeight: 100,//全局返航高度
            droneInfo: {//飞行器机型信息
                droneEnumValue: 99,//飞行器机型主类型
                droneSubEnumValue: 0//飞行器机型子类型
            },
            waylineAvoidLimitAreaMode: 0,
            payloadInfo: {//负载机型信息
                payloadEnumValue: 88,//负载机型主类型
                payloadSubEnumValue: 0,
                payloadPositionIndex: 0,//负载挂载位置
            },
        },
        Folder: {
            templateType: 'waypoint',//预定义模板类型 * 注：模板为用户提供了快速生成航线的方案。用户填充模板元素，再导入大疆支持客户端（如DJI Pilot），即可快速生成可执行的测绘/ 巡检航线
            templateId: 0,
            executeHeightMode: 'aboveGroundLevel',//执行高度模式 * 注：该元素仅在waylines.wpml中使用。
            waylineId: 0,
            distance: 48.4277114868164,
            duration: 25.5970311164856,
            autoFlightSpeed: 10,//全局航线飞行速度
            // startActionGroup: {//航线初始动作 *注：该元素用于规划一系列初始动作，在航线开始前执行。航线中断恢复时，先执行初始动作，再执行航点动作
            //     actionList: [
            //         {
            //             actionId: 0,
            //             actionActuatorFunc: 'gimbalRotate',
            //             actionActuatorFuncParam: {
            //                 gimbalHeadingYawBase: 'aircraft',
            //                 gimbalRotateMode: 'absoluteAngle',
            //                 gimbalPitchRotateEnable: 1,
            //                 gimbalPitchRotateAngle: -70,
            //                 gimbalRollRotateEnable: 0,
            //                 gimbalRollRotateAngle: 0,
            //                 gimbalYawRotateEnable: 0,
            //                 gimbalYawRotateAngle: 0,
            //                 gimbalRotateTimeEnable: 0,
            //                 gimbalRotateTime: 0,
            //                 payloadPositionIndex: 0,
            //             }
            //         },
            //         {
            //             actionId: 1,
            //             actionActuatorFunc: 'hover',
            //             actionActuatorFuncParam: {
            //                 hoverTime: 0.5,
            //             }
            //         },
            //         {
            //             actionId: 2,
            //             actionActuatorFunc: 'setFocusType',
            //             actionActuatorFuncParam: {
            //                 cameraFocusType: 'manual',
            //                 hoverTime: 0,
            //             }
            //         },
            //         {
            //             actionId: 3,
            //             actionActuatorFunc: 'focus',
            //             actionActuatorFuncParam: {
            //                 focusX: 0,
            //                 focusY: 0,
            //                 focusRegionWidth: 0,
            //                 focusRegionHeight: 0,
            //                 isPointFocus: 0,
            //                 isInfiniteFocus: 1,
            //                 payloadPositionIndex: 0,
            //             }
            //         },
            //         {
            //             actionId: 4,
            //             actionActuatorFunc: 'hover',
            //             actionActuatorFuncParam: {
            //                 hoverTime: 1,
            //             }
            //         },
            //     ]
            // },
            waylineCoordinateSysParam: {//坐标系参数
                coordinateMode: 'WGS84',//经纬度坐标系
                heightMode: 'aboveGroundLevel',//航点高程参考平面
            },
            globalHeight: 200,//全局航线高度（相对起飞点高度）
            caliFlightEnable: 0,//是否开启标定飞行 *注：航电类型不可用。建图航拍时仅适用于M300 RTK与M350 RTK机型
            gimbalPitchMode: 'manual',//云台俯仰角控制模式
            globalWaypointHeadingParam: {//全局偏航角模式参数
                waypointHeadingMode: 'followWayline',//飞行器偏航角模式
                waypointHeadingAngle: 0,//飞行器偏航角度
                waypointPoiPoint: '0.000000,0.000000,0.000000',//兴趣点
                waypointHeadingPathMode: 'followBadArc',//飞行器偏航角转动方向
                waypointHeadingPoiIndex: 0,
            },
            globalWaypointTurnMode: 'toPointAndStopWithDiscontinuityCurvature',//全局航点类型（全局航点转弯模式
            globalUseStraightLine: 1,//全局航段轨迹是否尽量贴合直线
            PlacemarkList: [
                // {
                //     Point: {
                //         coordinates: { longitude: 103.991724708, latitude: 30.763083891 }
                //     },
                //     index: 0,
                //     executeHeight: 30, //* 注：该元素仅在waylines.wpml中使用。具体高程参考平面
                //     ellipsoidHeight: 30,
                //     height: 30,
                //     waypointSpeed: 10,//航点飞行速度，当前航点飞向下一个航点的速度
                //     waypointHeadingParam: {
                //         waypointHeadingMode: 'smoothTransition',
                //         waypointHeadingAngle: -180,
                //         waypointPoiPoint: '0.000000,0.000000,0.000000',
                //         waypointHeadingAngleEnable: 0,
                //         waypointHeadingPathMode: 'followBadArc',
                //         waypointHeadingPoiIndex: 0,
                //     },
                //     waypointTurnParam: {
                //         waypointTurnMode: 'toPointAndStopWithDiscontinuityCurvature',
                //         waypointTurnDampingDist: 0,
                //     },
                //     useGlobalHeight: 0,
                //     useGlobalHeadingParam: 1,
                //     useGlobalTurnParam: 1,
                //     useStraightLine: 1,
                //     useGlobalSpeed: 1,
                //     actionGroup: {
                //         actionGroupId: 0,
                //         actionGroupStartIndex: 0,
                //         actionGroupEndIndex: 0,
                //         actionGroupMode: 'sequence',
                //         actionTrigger: {
                //             actionTriggerType: 'reachPoint'
                //         },
                //         actionList: [
                //             {
                //                 actionId: 0,
                //                 actionActuatorFunc: 'gimbalRotate',
                //                 actionActuatorFuncParam: {
                //                     gimbalHeadingYawBase: 'aircraft',
                //                     gimbalRotateMode: 'absoluteAngle',
                //                     gimbalPitchRotateEnable: 1,
                //                     gimbalPitchRotateAngle: -70,
                //                     gimbalRollRotateEnable: 0,
                //                     gimbalRollRotateAngle: 0,
                //                     gimbalYawRotateEnable: 0,
                //                     gimbalYawRotateAngle: 0,
                //                     gimbalRotateTimeEnable: 0,
                //                     gimbalRotateTime: 0,
                //                     payloadPositionIndex: 0,
                //                 }
                //             },
                //             {
                //                 actionId: 1,
                //                 actionActuatorFunc: 'startRecord',
                //                 actionActuatorFuncParam: {
                //                     payloadPositionIndex: 0,
                //                     useGlobalPayloadLensIndex: 1,
                //                     payloadLensIndex: ['visable']//拍摄照片存储类型
                //                 }
                //             },
                //         ]
                //     },
                //     waypointGimbalHeadingParam: {
                //         waypointGimbalPitchAngle: 0,
                //         waypointGimbalYawAngle: 0,
                //     },
                //     isRisky: 0,
                //     waypointWorkType: 0,
                // }
            ],
            payloadParam: {//负载设置
                payloadPositionIndex: 0,//负载设置
                focusMode: 'firstPoint',//负载对焦模式
                meteringMode: 'average',//负载测光模式
                returnMode: 'singleReturnFirst',//激光雷达回波模式
                samplingRate: 240000,//负载采样率
                scanningMode: 'repetitive',//负载扫描模式
                imageFormat: ['visable'],//图片格式列表
            }
        }
    })
    const [wpmlFileText, setWpmlFileText] = useState(null);
    const [KmlFileText, setKmlFileText] = useState(null);
    let over=useRef(false)
    // 页面载入
    useEffect(() => {
        if (wayLineData) {
            const URL = `http://**************:9000/300bdf2b-a150-406e-be63-d28bd29b409f/${wayLineData.ObjectName}`
            axios.get(URL, { responseType: 'blob' }).then(async response => {
                // 实例化zip压缩包
                const zip = new JSZip();
                // 加载zip压缩包的内容
                await zip.loadAsync(response.data);
                // 遍历zip压缩包中的内容
                zip.forEach((relativePath, file) => {
                    if (file.dir) {
                        return
                    }
                    // 读取文件为 blob 格式（通用处理方式，支持文本和二进制文件）
                    file.async('blob').then((content) => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            if (file.name === 'wpmz/waylines.wpml') {
                                setWpmlFileText(e.target.result)
                            } else if (file.name === 'wpmz/template.kml') {
                                setKmlFileText(e.target.result)
                            }
                        };
                        reader.readAsText(content); // 读取文本内容
                    });

                });
            })
        }

    }, []);
    // 同时监听两个状态的变化
    useEffect(() => {
        if (wpmlFileText !== null && KmlFileText !== null ) {
            // 在这里执行当两个状态都变化时需要的操作
            let res = analysisWaylineFile(wpmlFileText, KmlFileText)
            waylineFileInfo.current = { ...res }
            waylineChange({ ...waylineFileInfo.current })
            droneEnumValueChange(waylineFileInfo.current.missionConfig.droneInfo.droneEnumValue)
            droneSubEnumValueChange(waylineFileInfo.current.missionConfig.droneInfo.droneSubEnumValue)
            setTimeout(() => {
                routeFeedbackFu(waylineFileInfo.current)
            }, 3000)
        }
    }, [wpmlFileText, KmlFileText]);
    // 添加航点
    function addPlacemark(index, longitude, latitude, height) {
        if (index === 0) {
            waylineFileInfo.current.missionConfig.takeOffRefPoint = {
                longitude: longitude,
                latitude: latitude,
                height: height
            }
        }
        let Placemark = {
            Point: {
                coordinates: { longitude: longitude, latitude: latitude }
            },
            index: index,
            executeHeight: waylineFileInfo.current.Folder.globalHeight, //* 注：该元素仅在waylines.wpml中使用。具体高程参考平面
            ellipsoidHeight: waylineFileInfo.current.Folder.globalHeight,
            height: waylineFileInfo.current.Folder.globalHeight,
            waypointSpeed: waylineFileInfo.current.Folder.autoFlightSpeed,//航点飞行速度，当前航点飞向下一个航点的速度
            waypointHeadingParam: {
                waypointHeadingMode: 'followWayline',
                waypointHeadingAngle: computerWaypointHeadingAngle(index, longitude, latitude),
                waypointPoiPoint: '0.000000,0.000000,0.000000',
                waypointHeadingAngleEnable: 0,
                waypointHeadingPathMode: 'followBadArc',
                waypointHeadingPoiIndex: 0,
            },
            // actionGroup: {
            //     actionGroupId: 0,
            //     actionGroupStartIndex: 0,
            //     actionGroupEndIndex: 0,
            //     actionGroupMode: 'sequence',
            //     actionTrigger: {
            //         actionTriggerType: 'reachPoint'
            //     },
            //     actionList: []
            // },
            waypointTurnParam: {
                waypointTurnMode: 'toPointAndStopWithDiscontinuityCurvature',
                waypointTurnDampingDist: 0,
            },
            useGlobalHeight: 1,
            useGlobalHeadingParam: 1,
            useGlobalTurnParam: 1,
            useStraightLine: 1,
            useGlobalSpeed: 1,
            waypointGimbalHeadingParam: {
                waypointGimbalPitchAngle: 0,
                waypointGimbalYawAngle: 0,
            },
            isRisky: 0,
            waypointWorkType: 0,
        }
        waylineFileInfo.current.Folder.PlacemarkList.splice(index + 1, 0, Placemark)
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            item.index = index
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    // 删除航点
    function deletePlacemark(index) {
        waylineFileInfo.current.Folder.PlacemarkList.splice(index, 1)
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            item.index = index
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    // 作者改变
    function authorChange(val) {
        waylineFileInfo.current.author = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 飞机主型号改变
    function droneEnumValueChange(val) {        
        waylineFileInfo.current.missionConfig.droneInfo.droneEnumValue = val
        // waylineFileInfo.current.missionConfig.droneInfo.droneSubEnumValue = 0
        waylineChange({ ...waylineFileInfo.current })
        droneTodroneSub(val)
    }
    // 飞机子型号改变
    function droneSubEnumValueChange(val) {
        waylineFileInfo.current.missionConfig.droneInfo.droneSubEnumValue = val
        waylineChange({ ...waylineFileInfo.current })
        creatPayloadInfo(waylineFileInfo.current.missionConfig.droneInfo)
    }
    // 根据飞机主型号生成子型号
    function droneTodroneSub(val) {
        let droneSubEnumValueOptions = [
            {
                value: 0,
                label: 'M4E',
            },
            {
                value: 1,
                label: 'M4T',
            }
        ]
        if (val === 99) {
            droneSubEnumValueOptions[0].label = 'M4E'
            droneSubEnumValueOptions[1].label = 'M4T'
        } else if (val === 77) {
            droneSubEnumValueOptions[0].label = 'M3E'
            droneSubEnumValueOptions[1].label = 'M3T'
        } else if (val === 91) {
            droneSubEnumValueOptions[0].label = 'M3D'
            droneSubEnumValueOptions[1].label = 'M3TD'
        } else if (val === 100) {
            droneSubEnumValueOptions[0].label = 'M4D'
            droneSubEnumValueOptions[1].label = 'M4TD'
        }
        droneSubEnumValueOptionsChange(droneSubEnumValueOptions)
        creatPayloadInfo(waylineFileInfo.current.missionConfig.droneInfo)
    }
    // 根据飞机主型号和子型号生成负载信息
    function creatPayloadInfo(droneInfo) {
        if (droneInfo.droneEnumValue === 77) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 66
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 67
            }
        } else if (droneInfo.droneEnumValue === 91) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 80
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 81
            }
        } else if (droneInfo.droneEnumValue === 99) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 88
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 89
            }
        } else if (droneInfo.droneEnumValue === 100) {
            if (droneInfo.droneSubEnumValue === 0) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 98
            } else if (droneInfo.droneSubEnumValue === 1) {
                waylineFileInfo.current.missionConfig.payloadInfo.payloadEnumValue = 99
            }
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    // 全局高度模式切换
    function heightModeChange(val) {
        waylineFileInfo.current.Folder.waylineCoordinateSysParam.heightMode = val
        waylineFileInfo.current.Folder.executeHeightMode = val
        waylineChange({ ...waylineFileInfo.current })
        heightMode(val)
    }
    // 飞向首航点模式改变
    function flyToWaylineModeChange(val) {
        waylineFileInfo.current.missionConfig.flyToWaylineMode = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 全局航线速度改变
    function autoFlightSpeedChange(val) {
        waylineFileInfo.current.Folder.autoFlightSpeed = val
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            if (item.useGlobalSpeed === 1) {
                item.waypointSpeed = val
            }
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    //全局航线过渡速度
    function globalTransitionalSpeedChange(val) {
        waylineFileInfo.current.missionConfig.globalTransitionalSpeed = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 航点飞行速度改变
    function waypointSpeedChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointSpeed = val
        waylineFileInfo.current.Folder.PlacemarkList[index].useGlobalSpeed = 0
        waylineChange({ ...waylineFileInfo.current })
    }
    // 全局航点类型改变
    function globalWaypointTurnModeChange(val) {
        waylineFileInfo.current.Folder.globalWaypointTurnMode = val
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            if (item.useGlobalTurnParam === 1) {
                item.waypointTurnParam.waypointTurnMode = val
            }
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    // 航点类型改变
    function waypointTurnModeChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointTurnParam.waypointTurnMode = val
        waylineFileInfo.current.Folder.PlacemarkList[index].useGlobalHeadingParam = 0
        waylineChange({ ...waylineFileInfo.current })
    }
    // 云台俯仰角控制模式改变
    function gimbalPitchModeChange(val) {
        waylineFileInfo.current.Folder.gimbalPitchMode = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 全局偏航角模式参数改变
    function globalWaypointHeadingParamChange(val) {
        waylineFileInfo.current.Folder.globalWaypointHeadingParam.waypointHeadingMode = val
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            if (item.useGlobalHeadingParam === 1) {
                item.waypointHeadingParam.waypointHeadingMode = val
            }
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    // 偏航角模式参数改变
    function waypointHeadingParamChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointHeadingParam.waypointHeadingMode = val
        waylineFileInfo.current.Folder.PlacemarkList[index].useGlobalHeadingParam = 0
        waylineChange({ ...waylineFileInfo.current })
    }
    // 全局航线高度改变
    function globalHeightChange(val) {
        waylineFileInfo.current.Folder.globalHeight = val
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            if ((item.useGlobalHeight === 1)) {
                item.executeHeight = val
                item.ellipsoidHeight = val
                item.height = val
                item_height_change(index, waylineFileInfo.current.missionConfig.takeOffRefPoint.height + waylineFileInfo.current.Folder.globalHeight)
            }
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    // 单个航点高度改变
    function PlacemarkListItemHeightChange(index, height) {
        waylineFileInfo.current.Folder.PlacemarkList[index].useGlobalHeight = 0
        waylineFileInfo.current.Folder.PlacemarkList[index].height = height
        waylineFileInfo.current.Folder.PlacemarkList[index].executeHeight = height
        waylineFileInfo.current.Folder.PlacemarkList[index].ellipsoidHeight = height
        waylineChange({ ...waylineFileInfo.current })
        item_height_change(index, waylineFileInfo.current.missionConfig.takeOffRefPoint.height + height)
    }
    // 单个航点位置改变
    function PlacemarkListItemPositionChange(index, Lng, Lat, globeHeight) {
        waylineFileInfo.current.Folder.PlacemarkList[index].Point.coordinates.longitude = Lng
        waylineFileInfo.current.Folder.PlacemarkList[index].Point.coordinates.latitude = Lat
        if (index === 0) {
            waylineFileInfo.current.missionConfig.takeOffRefPoint = {
                longitude: Lng,
                latitude: Lat,
                height: globeHeight
            }
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    // 图片格式列表改变
    function imageFormatChange(val) {
        if (val.length) {
            waylineFileInfo.current.Folder.payloadParam.imageFormat = val
        }
        waylineFileInfo.current.Folder.PlacemarkList.forEach((item, index) => {
            if (item.actionGroup) {
                item.actionGroup.actionList.forEach((ite, ind) => {
                    if (ite.actionActuatorFuncParam.payloadLensIndex && ite.actionActuatorFuncParam.useGlobalPayloadLensIndex === 1) {
                        ite.actionActuatorFuncParam.payloadLensIndex = waylineFileInfo.current.Folder.payloadParam.imageFormat
                    }
                })
            }
        })
        waylineChange({ ...waylineFileInfo.current })
    }
    //航线结束动作
    function finishActionChange(val) {
        waylineFileInfo.current.missionConfig.finishAction = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 添加航点动作
    function addWaypointAction(index, actionType, angle) {
        if (index === null) {
            return
        }
        let action = {}
        if (!waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup) {
            waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup = {
                actionGroupId: index,
                actionGroupStartIndex: index,
                actionGroupEndIndex: index,
                actionGroupMode: 'sequence',
                actionTrigger: {
                    actionTriggerType: 'reachPoint'
                },
                actionList: []
            }
        }
        if (actionType === 'takePhoto') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'takePhoto',
                actionActuatorFuncParam: {
                    payloadPositionIndex: 0,
                    useGlobalPayloadLensIndex: 1,
                    payloadLensIndex: ['visable']//拍摄照片存储类型
                }
            }
        } else if (actionType === 'startRecord') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'startRecord',
                actionActuatorFuncParam: {
                    payloadPositionIndex: 0,
                    useGlobalPayloadLensIndex: 1,
                    payloadLensIndex: ['visable']//拍摄照片存储类型
                }
            }
        } else if (actionType === 'stopRecord') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'stopRecord',
                actionActuatorFuncParam: {
                    payloadPositionIndex: 0,
                    useGlobalPayloadLensIndex: 1,
                    payloadLensIndex: ['visable']//拍摄照片存储类型
                }
            }
        } else if (actionType === 'zoom') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'zoom',
                actionActuatorFuncParam: {
                    payloadPositionIndex: 0,
                    focalLength: 0,
                }
            }
        } else if (actionType === 'rotateYaw') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'rotateYaw',
                actionActuatorFuncParam: {
                    aircraftHeading: angle,
                    aircraftPathMode: 'counterClockwise',
                }
            }
        } else if (actionType === 'gimbalRotate') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'gimbalRotate',
                actionActuatorFuncParam: {
                    gimbalHeadingYawBase: 'aircraft',
                    gimbalRotateMode: 'absoluteAngle',
                    gimbalPitchRotateEnable: 1,
                    gimbalPitchRotateAngle: 0,
                    gimbalRollRotateEnable: 0,
                    gimbalRollRotateAngle: 0,
                    gimbalYawRotateEnable: 0,
                    gimbalYawRotateAngle: 0,
                    gimbalRotateTimeEnable: 0,
                    gimbalRotateTime: 0,
                    payloadPositionIndex: 0,
                }
            }
        } else if (actionType === 'hover') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'hover',
                actionActuatorFuncParam: {
                    hoverTime: 0,
                }
            }
        } else if (actionType === 'panoShot') {
            action = {
                actionId: waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length,
                actionActuatorFunc: 'panoShot',
                actionActuatorFuncParam: {
                    payloadPositionIndex: 0,
                    useGlobalPayloadLensIndex: 1,
                    payloadLensIndex: ['visable'],//拍摄照片存储类型
                    panoShotSubMode: 'panoShot_360'
                }
            }
        }
        waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.push(action)
        waylineChange({ ...waylineFileInfo.current })
    }
    // 修改航点动作
    function editWaypointAction(index, ind, key, value) {
        waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList[ind].actionActuatorFuncParam[key] = value
        waylineChange({ ...waylineFileInfo.current })
    }
    // 删除航点动作
    function deleteWaypointAction(index, ind) {
        SwitchWaypointAction(index, ind >= 1 ? ind - 1 : null)
        waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.splice(ind, 1)
        waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.forEach((ite, inde) => {
            ite.actionGroupId = inde
            ite.actionGroupStartIndex = inde
            ite.actionGroupEndIndex = inde
        })
        if (waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.length === 0) {
            delete waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup;
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    // 计算航点偏航角
    function computerWaypointHeadingAngle(index, longitude, latitude) {
        let Heading = 0
        if (index !== 0) {
            //通过 turf 计算两个点的方向向量
            Heading = turf.rhumbBearing([waylineFileInfo.current.Folder.PlacemarkList[index - 1].Point.coordinates.longitude, waylineFileInfo.current.Folder.PlacemarkList[index - 1].Point.coordinates.latitude], [longitude, latitude])
        }
        return Heading
    }
    // 航点使用全局配置
    function waypointUseGlobalSetting(WaypointIndex, key, val) {
        waylineFileInfo.current.Folder.PlacemarkList[WaypointIndex][key] = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 使用全局高度改变
    function useGlobalHeightChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].useGlobalHeight = val
        if (val === 1) {
            waylineFileInfo.current.Folder.PlacemarkList[index].executeHeight = waylineFileInfo.current.Folder.globalHeight
            waylineFileInfo.current.Folder.PlacemarkList[index].ellipsoidHeight = waylineFileInfo.current.Folder.globalHeight
            waylineFileInfo.current.Folder.PlacemarkList[index].height = waylineFileInfo.current.Folder.globalHeight
            item_height_change(index, waylineFileInfo.current.missionConfig.takeOffRefPoint.height + waylineFileInfo.current.Folder.globalHeight)
        }
        waylineChange({ ...waylineFileInfo.current })
    }
    // 航点速度改变
    function waypointSpeedChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointSpeed = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 航点偏航角模式改变
    function waypointHeadingModeChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointHeadingParam.waypointHeadingMode = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 航点类型改变
    function waypointTurnModeChange(index, val) {
        waylineFileInfo.current.Folder.PlacemarkList[index].waypointTurnParam.waypointTurnMode = val
        waylineChange({ ...waylineFileInfo.current })
    }
    // 计算某个航点的偏航角
    function computeHeading(index) {
        let heading = 0
        if (waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup) {
            const hasRotateYaw = waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.find(item => item.actionActuatorFunc === 'rotateYaw')
            if (hasRotateYaw !== undefined) {
                waylineFileInfo.current.Folder.PlacemarkList[index].actionGroup.actionList.forEach((item, index) => {
                    if (item.actionActuatorFunc === 'rotateYaw') {
                        heading = item.actionActuatorFuncParam.aircraftHeading
                    }
                })
                return heading
            } else {
                return null
            }
        } else {
            return null
        }
    }
    // 计算某个航点的俯仰角
    function computePitch(index) {
        let pitch = 0
        for (let i = 0; i < index + 1; i++) {
            const item = waylineFileInfo.current.Folder.PlacemarkList[i];
            if (item.actionGroup) {
                for (let j = 0; j < item.actionGroup.actionList.length; j++) {
                    const ite = item.actionGroup.actionList[j];
                    if (ite.actionActuatorFunc === 'gimbalRotate') {
                        pitch = ite.actionActuatorFuncParam.gimbalPitchRotateAngle
                    }
                }
            }
        }
        return pitch
    }
    // 创建航线文件
    function CreatFile() {
        return CreatWaylineFile(waylineFileInfo.current)
    }
    return {
        waylineFileInfo,
        addPlacemark,
        deletePlacemark,
        authorChange,
        droneEnumValueChange,
        droneSubEnumValueChange,
        PlacemarkListItemHeightChange,
        PlacemarkListItemPositionChange,
        CreatFile,
        imageFormatChange,
        heightModeChange,
        flyToWaylineModeChange,
        globalHeightChange,
        autoFlightSpeedChange,
        globalTransitionalSpeedChange,
        globalWaypointTurnModeChange,
        globalWaypointHeadingParamChange,
        gimbalPitchModeChange,
        finishActionChange,
        waypointUseGlobalSetting,
        useGlobalHeightChange,
        waypointSpeedChange,
        waypointHeadingModeChange,
        waypointTurnModeChange,
        addWaypointAction,
        editWaypointAction,
        deleteWaypointAction,
        computeHeading,
        computePitch
    }
}
export default useWaylineFileHooks;