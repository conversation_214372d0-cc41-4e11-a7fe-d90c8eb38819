import J<PERSON>Z<PERSON> from "jszip";
import FileSaver from "file-saver";
import { useState, useRef } from "react";
import { HGet2 } from "@/utils/request";
import { message, Progress, Modal } from "antd";
import { getDeviceName, getImgUrl } from "@/utils/utils";
import { DownLoadFile, axiosApi } from "@/services/general";
const { confirm } = Modal;

const DownLoad = ({ record }) => {
  const [totalSize, setTotalSize] = useState(0);
  let loading = useRef(false);
  let [text, setText] = useState("下载");
  const abortController = new AbortController();
  const signal = abortController.signal;

  // const setLocalStore = (res,record) => {
  //   let localGetFileZip =
  //     JSON.parse(localStorage.getItem("localGetFileZip")) || [];
  //   if (
  //     localGetFileZip.some((item) => item.TaskID.toString() === record.TaskID)
  //   ) {
  //     return false;
  //   }
  //   localGetFileZip.push({
  //     TaskID: record.TaskID,
  //     ZipID: res.data,
  //   });
  //   localStorage.setItem("localGetFileZip", JSON.stringify(localGetFileZip));
  //   return true;
  // };
  const onCancel = () => {
    abortController.abort();
    loading.current = false;
    setText("下载");
    setTotalSize(0);
  };

  const beginDownLoad = (record) => {
    if (loading.current) {
      message.warning("正在努力打包下载文件中，此过程时间较长，请稍待");
      return;
    }
    confirm({
      title: "下载媒体文件",
      content: "确定下载全部媒体文件吗？",
      okText: "下载",
      cancelText: "取消",
      async onOk() {
        downLoadMedia(record);
      },
    });
  };

  const downloadFile = async (url, fileName, setCount, message, onCancel) => {
    try {
      const response = await fetch(url);
      const total = +response.headers.get("Content-Length");
      const reader = response.body.getReader();
      let receivedLength = 0;
      const chunks = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
        receivedLength += value.length;
        const percentage = (receivedLength / total) * 100;
        setCount(percentage.toFixed(0));
      }

      const blob = new Blob(chunks);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
      message.success("下载完成");
      onCancel();
    } catch (error) {
      console.error("下载错误:", error);
      message.error("下载文件时出现错误");
      onCancel();
    }
  };

  const downLoadMedia = async () => {
    let isFetching = false; // 添加状态标记
    const mL = await HGet2(`/api/v1/Media/GetListByTaskId?id=${record.TaskID}`);
    console.log("mL", mL);

    if (mL && mL.length > 0) {
      setText("响应中");
      let intervalId;

      const getFileZip = async () => {
        if (isFetching || loading.current) return;
        isFetching = true;

        try {
          const res = await axiosApi(
            `/api/v1/Media/GetFileZip?id=${record.TaskID}`,
            "GET"
          );
          if (res && res.code === 1) {
            setText(`打包中`);
            if (res.data.includes(".zip")) {

              loading.current = true;
              clearInterval(intervalId);
              setText("下载中");
              const zipUrl = getImgUrl(res.data.substring(1));
              const zipName = `${getDeviceName(record.DeviceSN)}_${record.FlightLineName}_${record.CreateTime.slice(0, record.CreateTime.indexOf("T"))}.zip`;
              await downloadFile(zipUrl, zipName, setTotalSize, message, onCancel);
            } else {
              console.error(res?.msg || "下载失败");
            }
          }
        } catch (error) {
          console.error("请求获取文件链接错误:", error);
          // message.error("请求获取文件链接时出现错误");
          onCancel();
        } finally {
          isFetching = false;
        }
      };

      intervalId = setInterval(getFileZip, 3000);
    } else {
      onCancel();
      message.warning("没有文件可下载");
    }
  };

  const handleBatchDownload = async (selectImgList) => {
    const zip = new JSZip();
    for (let i = 0; i < selectImgList.length; i++) {
      const item = selectImgList[i];
      try {
        if (!loading.current) {
          setTotalSize(100);
          break;
        }

        const response = await DownLoadFile(getImgUrl(item.ObjectName), {
          responseType: "blob",
          signal,
        });
        const blob = new Blob([response]);
        const file_name = `${item.WayLineNM}-航点${item.HangDianIndex}${item.FileName.slice(item.FileName.indexOf("."))}`;
        zip.file(file_name, blob, { binary: true });
        setTotalSize(((i + 1) / selectImgList.length) * 100); // 更新进度
      } catch (error) {
        console.warn(`下载失败: ${item.FileName}`, error);
      }
    }

    let zipName = `${getDeviceName(record.DeviceSN)}_${record.FlightLineName}_${record.CreateTime.slice(0, record.CreateTime.indexOf("T"))}.zip`;
    const content = await zip.generateAsync({ type: "blob" });
    if (content.size > 22) {
      FileSaver.saveAs(content, zipName);
      onCancel();
    }
  };

  const renderProgress = () => {
    if (loading.current) {
      return (
        <Progress
          type="circle"
          percent={totalSize}
          steps={{ count: 9, gap: 4 }}
          strokeColor={"#108ee9"}
          trailColor={"#ccc"}
          strokeWidth={20}
          size={12}
        />
      );
    }
  };

  return (
    <div onClick={beginDownLoad} style={{ cursor: "pointer" }}>
      <span style={{ marginRight: 5 }}>{renderProgress()}</span>
      <span>{text}</span>
    </div>
  );
};

export default DownLoad;
