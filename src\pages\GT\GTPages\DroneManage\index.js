import MyMenu from "../../components/MyMenu";
import pages from "./panels/DroneRouter";
import { useState, useEffect } from "react";
import { PageProvider, useDronePage } from "./DronePageManger";
import "../../style/antd-common.less";
import commonStyle from "../../style/common.less";
function droneManage() {
  return (
    <PageProvider>
      <Page />
    </PageProvider>
  );
}

function Page() {
  const { currentPage, setCurrentPage } = useDronePage();
  const renderPage = () => {
    return pages[currentPage] || pages["态势感知"];
  };
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  const headList = [
    { label: "实时监测", key: "实时监测" },
    { label: "缺陷管理", key: "缺陷管理" },
    {
      label: "运维管理",
      key: "运维管理",
      children: [
        { label: "项目管理", key: "项目管理" },
        { label: "组件管理", key: "组件管理" },
        { label: "子阵管理", key: "子阵管理" },
      ],
    },
  ];
  return (
    <>
      {/* <MyHead headTitle={"光伏无人机智能巡检系统"}
        headList={headList}
        handlePageChange={handlePageChange}></MyHead> */}
      <div
        className={commonStyle.gt_back}
        style={{
          display: "flex",
          justifyContent: "flex-start",
          position: "relative",
        }}
      >
        <div>
          <MyMenu setCurrentPage={setCurrentPage}></MyMenu>
        </div>
        <div style={{ width: "100%", height: "100%" }}>{renderPage()}</div>
      </div>
    </>
  );
}
export default droneManage;
