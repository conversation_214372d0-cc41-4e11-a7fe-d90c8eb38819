import React, { useState, useRef, useEffect } from "react";
import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import { isEmpty } from "@/utils/utils";
import { useModel } from "umi";
import { getGrayStyle } from "./helper";
import { color } from "echarts";
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";
import { Popover, ConfigProvider } from "antd";
import styles from "./CameraPanel2.less";
import whiteHotImg from "@/assets/images/white_hot.png";
import blackHotImg from "@/assets/images/black_hot.png";
import redHotImg from "@/assets/images/red_hot.png";
import iceFire from "@/assets/images/ice_fire.png";
import color1 from "@/assets/images/color_1.png";
import color2 from "@/assets/images/color_2.png";
import greenHot from "@/assets/images/green_hot.png";
import ironBow from "@/assets/images/iron_bow_1.png";
import rainBow from "@/assets/images/rain_bow.png";
import rain from "@/assets/images/rain.png";
const CameraPanel2 = () => {

  const { fj } = useModel("droneModel");
  const { DoCMD, DoCMD2, CmdMqttConn,temperatureMode,setTemperatureMode } =
    useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const { cameraJT, setCameraJT } = useModel("rtmpModel");
  const { fjVideo } = useModel("stateModel");


  const colorParams = [
    { value: 0, name: "白热", img: whiteHotImg },
    { value: 1, name: "黑热", img: blackHotImg },
    { value: 2, name: "描红", img: redHotImg },
    { value: 3, name: "医疗", img: greenHot },
    { value: 5, name: "彩虹1", img: rainBow },
    { value: 6, name: "铁红", img: ironBow },
    { value: 8, name: "北极", img: iceFire },
    { value: 11, name: "熔岩", img: color1 },
    { value: 12, name: "热铁", img: color2 },
    { value: 13, name: "彩虹2", img: rain },
  ];
  const content = (
    <div
      style={{
        width: 130,
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "space-between",
      }}
    >
      {colorParams.map((item) => {
        return (
          <div
            style={{ cursor: "pointer" }}
            onClick={() => {
              CameraIRType(item.value);
            }}
          >
            <img src={item.img} alt="" style={{ width: 60 }} />
            <div style={{ width: 60, textAlign: "center" }}>{item.name}</div>
          </div>
        );
      })}
    </div>
  );

  const handleTemperature = (e, mode) => {
    // 负载控制—红外测温 模式 设置：0是关闭，1是点测温，2是区域测温
    setTemperatureMode(mode);
    let data = {
      payload_index: device.Camera2,
      mode: mode,
    };
    DoCMD(device.SN, "ir_metering_mode_set", data);
  };

  const CameraJT = (z) => {
    const data = {
      video_id: `${device.SN2}/${device.Camera2}/normal-0`,
      video_type: z,
    };
    DoCMD(device.SN, "live_lens_change", data);
    setCameraJT(z);

    if (z == "ir") {
      setTimeout(CameraIRType, 1000);
    }
    // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
  };

  const CameraIRType = (value = 0) => {
    if (fjVideo.video_type != "ir") {
      const data = {
        video_id: `${device.SN2}/${device.Camera2}/normal-0`,
        video_type: "ir",
      };
      DoCMD(device.SN, "live_lens_change", data);
      setCameraJT("ir");
    }
    let data = {};
    data[device.Camera2] = { thermal_current_palette_style: value };
    let s = {};
    s.bid = getGuid();
    s.tid = getGuid();
    // s.gateway = sn
    s.data = data;
    s.timestamp = dayjs().valueOf();
    DoCMD2(`thing/product/${device.SN}/property/set`, s);
  };

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);
  };

  const ifHaveIR = () => {
    if (device.Model2.includes("T")) {
      return true;
    }
    return false;
  };

  if (isEmpty(fj)) return;

  let temperatureList = [
    { value: 0, name: "关闭测温" },
    { value: 1, name: "点测温" },
    { value: 2, name: "区域测温" },
  ];

  return (
    <>
      <div className={styles.camera_panel}>
        {/* 红外镜头下显示红外测温 */}
        {
         fjVideo.video_type == "ir" && <div className={`${styles.camera_panel_btn} ${styles.temperature}`}>
          测温
          <div className={styles.temperature_list}>
            {temperatureList.map((item) => (
              <div onClick={(e) => handleTemperature(e, item.value)} style={{color:temperatureMode==item.value?"rgb(255, 179, 0)":''}}>
                {item.name}
              </div>
            ))}
          </div>
        </div>
        }

        <div
          className={styles.camera_panel_btn}
          onClick={() => CameraJT("wide")}
        >
          广角
        </div>
        <div
          className={styles.camera_panel_btn}
          onClick={() => CameraJT("zoom")}
        >
          变焦
        </div>
        {ifHaveIR() ? (
          <ConfigProvider
            theme={{
              components: {
                Popover: {
                  titleMinWidth: 130,
                },
              },
            }}
          >
            <Popover content={content} title="红外调色盘">
              <div
                className={styles.camera_panel_btn}
                onClick={() => CameraJT("ir")}
              >
                红外
              </div>
            </Popover>
          </ConfigProvider>
        ) : null}
      </div>
    </>
  );
};

export default CameraPanel2;
