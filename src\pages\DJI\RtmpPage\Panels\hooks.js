export const removeVideoSource = (snToRemove) => {
    // 从 localStorage 获取当前的 AddVideoSource
    const storedVideoSources = localStorage.getItem("AddVideoSource");
    let videoSources = JSON.parse(storedVideoSources);
    // 移除 SN 为 snToRemove 的视频源
    const updatedVideoSources = videoSources.filter((source) => source.key !== snToRemove);
    const updatedVideoSourcesJson = JSON.stringify(updatedVideoSources)
    localStorage.setItem("AddVideoSource", updatedVideoSourcesJson);
    return updatedVideoSources; 
  };
  
  