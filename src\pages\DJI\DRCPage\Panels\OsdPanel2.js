import React, { useRef, useEffect } from 'react';
import bpImg from "@/assets/drcImgs/bp3.png";
import tb1Img from "@/assets/drcImgs/tb2.png";
import { getAngle } from './helper';
import { isEmpty } from '@/utils/utils';
import hImg from "@/assets/drcImgs/h2.png";
import { useModel } from 'umi';

const BatteryButton = () => {
  const { drc } = useModel('drcModel');
  const {fj}=useModel('droneModel');

  const w1 = 120
  const h1 = 120
  const c1 = 5

  const device = JSON.parse(localStorage.getItem('device'))

  const getDeg = (lat, lng) => {
    const d1 = getAngle(lat, lng, device.Lat, device.Lng);
    const dx = Math.sin(d1) * 80
    const dy = Math.cos(d1) * 80

    // 
    return d1;
  }


  const getXY = (x0, y0, d1, dis) => {
    d1 = d1 * (Math.PI / 180)
    let dx = Math.sin(d1) * dis
    let dy = Math.cos(d1) * dis

    // 
    if (d1 <= 90) {
      return [x0 + dx, y0 - dy]
    }
    if (d1 <= 180) {
      return [x0 + dx, y0 + dy]
    }
    if (d1 <= 270) {
      return [x0 - dx, y0 + dy]
    }
    return [x0 - dx, y0 - dy]
  }

  const getXY2 = (x0, y0, d1) => {
      d1 = d1 * (Math.PI / 180)
      //
      const x1=x0*Math.cos(d1)-y0*Math.sin(d1);
      const y1=x0*Math.sin(d1)+y0*Math.cos(d1);
      return [x1,y1]
  } 


  //const jd1 = getDeg(device.Lat + 1, device.Lng + 1);
  //const jd1 = 30




  useEffect(() => {
    //const canvas = canvasRef.current;
    const xx = () => {
      const cc = document.getElementById('mycanvas');
      //
      const context = cc.getContext('2d');

      cc.width = w1;
      cc.height = h1;

      // 设置线条的颜色
      context.strokeStyle = 'white';
      // 设置线条的宽度
      context.lineWidth = 3;

      // 绘制直线
      context.beginPath();
      // 起点
      
      const dx= drc.speed_y*4.5
      const dy=-1 * drc.speed_x*4.5

      context.moveTo(60, 60);
      context.lineTo(60+dx, 60+dy);

      // context.moveTo(60, 60);
      // const p2=getXY2(dx, dy,drc.attitude_head)
      // console.log('xy',dx,dy,p2[0],p2[1])
      // context.lineTo(60+p2[0],60+ p2[1]);

      
      
      context.closePath();
      context.stroke();
    }
    xx();
  }, [drc]);

  const jd1 = getDeg(drc.latitude, drc.longitude);
  const getDis2=(v1)=>{
    if(v1>1000) return 40;
    if(v1>500) return 30;
    if(v1>200) return 20;
    if(v1>100) return 10;
    if(v1>20)  return 5;
    return 0;

  }
  const p2 = getXY(60, 60, jd1, getDis2(fj.data["home_distance"]))
  return (


    <div style={{ height: 120, width: 120 }}>
      <img draggable={false} height={120} width={120} src={bpImg}></img>
      <img draggable={false} style={{ transform: `rotate(${jd1}deg)`, position: 'absolute', top: p2[1] - 12, left: p2[0] - 10 }} height={20} width={20} src={hImg}></img>
      <img draggable={false} style={{ transform: `rotate(${drc.attitude_head}deg)`, position: 'absolute', top: 48, left: 50 }} height={20} width={20} src={tb1Img}></img>
      
      <canvas id="mycanvas" style={{ position: 'absolute', top: 0, left: 0, height: 120, width: 120 }}>
        您的浏览器不支持canvas元素，请升级您的浏览器
      </canvas>
    </div>
  );
};

export default BatteryButton;