import { getBodyH } from "@/utils/utils";
import styles from "./indexPage.css";
import { useState, useEffect, useRef,lazy } from "react";
import IfShowPanel from "@/components/IfShowPanel";
import LeaflatMap from "@/pages/GT/DZJC/pages/Maps/LeaflatMap/LeaflatMap";
import { Get2 } from "@/services/general";
import DockStatePanel from "@/pages/DJI/OrgPage/Panels/DockPanel";
import { getGuid } from "@/utils/helper";
import NoticePanel from "@/pages/DJI/OrgPage/Panels/NoticePanel";
import { useModel } from "umi";
import AirportsListPanel from "./Panels/AirportsListPanel";
import VideoPanel from "./Panels/VideoPanel/VideoPanel";
import styles2 from '@/pages/SI/ControlCenter/index.module.less';
const FlyDispatch = lazy(() => import('@/pages/DJI/OrgPage/JiaoTongHome'));

const EquipmentOverview = () => {
  const { fetchBaseMapData } = useModel("mapModel");
  const [dL, setDL] = useState([]);

  const [showVideoPanel, setShowVideoPanel] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);

  useEffect(() => {
    fetchBaseMapData();
  }, [fetchBaseMapData]);

  const handleVideoClick = (device) => {
    setSelectedDevice(device);
    setShowVideoPanel(true);
  };

  // 关闭视频面板
  const handleCloseVideoPanel = () => {
    setShowVideoPanel(false);
  };
  const getPanel = (
    <div style={{ height: "100%" }}>
      {/* <DockStatePanel key={getGuid()} data={dL} route="/gt/DZJC/devicePage" /> */}
      <AirportsListPanel
        key={getGuid()}
        data={dL}
        route="/gt/DZJC/devicePage"
        onVideoClick={handleVideoClick}
      ></AirportsListPanel>
      {/* <LeftSidebar key={getGuid()} collapsed={false}></LeftSidebar> */}
    </div>
  );

  const getPanel2 = (
    <div style={{ height: "100%" }}>
      <NoticePanel></NoticePanel>
    </div>
  );

  useEffect(() => {
    const fetchData = async () => {
      const xx = await Get2("/api/open/Device/GetAllList");
      console.log("设备列表", xx);
      setDL(xx);
    };

    fetchData();

    const interval = setInterval(() => {
      fetchData();
    }, 60000); // 每一分钟更新设备列表

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <div
        className={styles.CradGroup}
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
        }}
      >
        <FlyDispatch className={styles2.mapForSI} />
        {/* <LeaflatMap data={dL} h1={getBodyH(56)}></LeaflatMap> */}
      </div>
      <div></div>
    </div>
  );
};

export default EquipmentOverview;
