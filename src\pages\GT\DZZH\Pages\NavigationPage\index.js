import React from 'react';
import { Card, Flex, Image } from 'antd';
import image0 from './picture/0.png';
import image1 from './picture/1.png';
import image2 from './picture/2.png';
import { queryPage2 } from '@/utils/MyRoute';
import { useModel } from "umi";
import './navigationPage.css';
import handleFullScreen from '@/utils/handleFullScreen';

// 随便做的数据
const thumbnails = [
  { id: 1, imageUrl: image0, title: '无人机耕地违规识别系统' },
  { id: 2, imageUrl: image2, title: '地质灾害点智能监控系统' },
  { id: 3, imageUrl: image1, title: '无人机空中巡查系统' },
];


const NavigationPage = () => {
  const { setPage } = useModel("pageModel");
  // 点击系统列表跳转到系统
  const handClick = (e) => {
    window.location.href = "#/GT/index";
    localStorage.setItem("currentPage", e);
    if(e === "无人机空中巡查系统"){
      localStorage.setItem("currentPage", "态势感知");
      setPage(queryPage2("态势感知"))
    }else {
      localStorage.setItem("currentPage", e);
      setPage(queryPage2(e))
    }
  };

  return (
    <div className="navigation-page-container">
      <div className="top-title-section" onClick={handleFullScreen}>
        云端智行-无人机行业应用平台
      </div>
      <Card className="card-container">
        {/* 系统列表 */}
        <Flex className="thumbnail-list-container">
          {thumbnails.map(thumbnail => (
            <div key={thumbnail.id} className="thumbnail-item">
              <Image
                className="thumbnail-image"
                alt={thumbnail.title}
                preview={{
                  visible: false,
                  mask: (
                    <div className="thumbnail-title">
                      进入系统
                    </div>
                  ),
                }}
                width={500}
                src={thumbnail.imageUrl}
                onClick={(event) => {
                  event.stopPropagation(); // 阻止事件冒泡
                  handClick(thumbnail.title);
                }}
              />
              <h3 className='thumbnail-title'>{thumbnail.title}</h3>
            </div>
          ))}
        </Flex>
      </Card>
    </div>
  );
};

export default NavigationPage;
