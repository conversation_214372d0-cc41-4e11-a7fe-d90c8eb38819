.map-info-panel {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  font-size: 12px;
  line-height: 1.5;
  color: #333;
  display: flex;
  gap: 15px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      margin-right: 3px; /* 调整标签和值之间的间距 */
      color: #666;
      font-weight: 800;
    }

    .value {
      font-family: monospace;
      min-width: 40px;
      color: #333;
      font-weight: 800;
    }
  }
}