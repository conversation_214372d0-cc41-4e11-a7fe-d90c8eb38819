//import {Modal,} from 'antd';
import { Form, Modal, InputNumber, Button } from 'antd';
import { connectAsync } from 'mqtt';
import { useState } from 'react';

const ChangeHeightPanel = ({ h1, flyTo }) => {
  const [height, setHeight] = useState(h1)
  return <div >
            <span>调整高度：</span>
            <span > <InputNumber min={20} max={2000} onChange={(e) => setHeight(e)} defaultValue={height} /></span>
            <span style={{marginLeft:24.0}}><Button type='primary' onClick={()=>flyTo(height)}>确定</Button></span>
     </div>
};




export default ChangeHeightPanel;
