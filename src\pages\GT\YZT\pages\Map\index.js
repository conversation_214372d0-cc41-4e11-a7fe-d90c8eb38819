// 导入React hooks和相关组件
import React, { useRef, useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import OneMap from './components/OneMap';
import ToolBox from './components/ToolBox';
import MultiScreenExitButton from './components/MultiScreenExitButton';
import MultiScreenPanel from './components/MultiScreenPanel';
import TemporalComparison from './components/TemporalComparison';
import api from '@/pages/GT/utils/api';
import styles from './index.module.less';
import { useLocation, Cesium } from 'umi';

/**
 * Map页面父组件
 * 负责状态管理和数据传递给OneMap组件
 */
const MapPage = () => {
  const mapRef = useRef(null);

  // 卷帘功能状态管理
  const [isCurtainMode, setIsCurtainMode] = useState(false);

  // 多屏对比功能状态管理
  const [isMultiScreenMode, setIsMultiScreenMode] = useState(false);
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [multiScreenConfig, setMultiScreenConfig] = useState({
    layout: '1x2', // 默认1x2布局
    syncView: false, // 默认不同步视图
    maps: [
      { id: 'map1', mapType: '3D', title: '地图 1' },
      { id: 'map2', mapType: '3D', title: '地图 2' }
    ]
  });

  // 多时相对比功能状态管理
  const [isTemporalMode, setIsTemporalMode] = useState(false);
  const [temporalData, setTemporalData] = useState([]);
  const [selectedTemporalNode, setSelectedTemporalNode] = useState(null);
  const [temporalLoading, setTemporalLoading] = useState(false);

  // 禁飞区
  const [showNFZButton, setShowNFZButton] = useState(true); // 控制禁飞区按钮显示/隐藏

  // 这里可以添加设备数据获取逻辑
  // const [deviceList, setDeviceList] = useState([]);
  // const [deviceLoading, setDeviceLoading] = useState(false);

  // 自主测绘更新 MapPage 初始化时就进入多时相模式
  const location = useLocation();
  useEffect(() => {
    // 获取当前页面路由
    const currentPath = location.pathname;
    if (currentPath.includes('ZZCH')) {
      handleTemporalToggle();
    }
  }, []);

  // 处理卷帘功能切换
  const handleCurtainToggle = useCallback(() => {
    console.log('MapPage: 卷帘功能切换');

    // 通过ref调用OneMap的卷帘切换方法
    if (mapRef.current && mapRef.current.toggleSplitMode) {
      mapRef.current.toggleSplitMode();

      // 获取OneMap的当前卷帘状态并同步
      const currentSplitMode = mapRef.current.getSplitMode();
      setIsCurtainMode(!currentSplitMode);
    }
  }, []);

  // 处理多屏对比功能
  const handleMultiScreenCompare = useCallback(() => {
    console.log('MapPage: 切换多屏对比模式');
    setIsMultiScreenMode(true);
    setShowConfigPanel(true); // 进入多屏模式时显示配置面板
  }, []);

  // 处理退出多屏对比模式
  const handleExitMultiScreen = useCallback(() => {
    console.log('MapPage: 退出多屏对比模式');
    setIsMultiScreenMode(false);
    setShowConfigPanel(false); // 退出时隐藏配置面板
  }, []);

  // 处理布局变化
  const handleLayoutChange = useCallback((layout) => {
    console.log('MapPage: 布局变化', layout);
    setMultiScreenConfig(prev => ({
      ...prev,
      layout
    }));
  }, []);

  // 处理视图同步变化
  const handleSyncViewChange = useCallback((syncView) => {
    console.log('MapPage: 视图同步变化', syncView);
    setMultiScreenConfig(prev => ({
      ...prev,
      syncView
    }));
  }, []);

  // 处理配置面板关闭
  const handleConfigPanelClose = useCallback(() => {
    setShowConfigPanel(false);
  }, []);

  // 处理显示配置面板
  const handleShowConfigPanel = useCallback(() => {
    setShowConfigPanel(true);
  }, []);

  // 根据布局生成地图配置
  const generateMapConfigs = useCallback((layout) => {
    const layoutConfigs = {
      '1x2': [
        { id: 'map1', mapType: '3D', title: '地图 1' },
        { id: 'map2', mapType: '3D', title: '地图 2' }
      ],
      '2x1': [
        { id: 'map1', mapType: '3D', title: '地图 1' },
        { id: 'map2', mapType: '3D', title: '地图 2' }
      ],
      '2x2': [
        { id: 'map1', mapType: '3D', title: '地图 1' },
        { id: 'map2', mapType: '3D', title: '地图 2' },
        { id: 'map3', mapType: '3D', title: '地图 3' },
        { id: 'map4', mapType: '3D', title: '地图 4' }
      ],
      '1x3': [
        { id: 'map1', mapType: '3D', title: '地图 1' },
        { id: 'map2', mapType: '3D', title: '地图 2' },
        { id: 'map3', mapType: '3D', title: '地图 3' }
      ],
      '3x1': [
        { id: 'map1', mapType: '3D', title: '地图 1' },
        { id: 'map2', mapType: '3D', title: '地图 2' },
        { id: 'map3', mapType: '3D', title: '地图 3' }
      ]
    };
    return layoutConfigs[layout] || layoutConfigs['1x2'];
  }, []);

  // 获取当前布局的CSS Grid样式
  const getGridStyle = useCallback((layout) => {
    const gridStyles = {
      '1x2': { gridTemplateColumns: '1fr 1fr', gridTemplateRows: '1fr' },
      '2x1': { gridTemplateColumns: '1fr', gridTemplateRows: '1fr 1fr' },
      '2x2': { gridTemplateColumns: '1fr 1fr', gridTemplateRows: '1fr 1fr' },
      '1x3': { gridTemplateColumns: '1fr 1fr 1fr', gridTemplateRows: '1fr' },
      '3x1': { gridTemplateColumns: '1fr', gridTemplateRows: '1fr 1fr 1fr' }
    };
    return gridStyles[layout] || gridStyles['1x2'];
  }, []);

  // 处理查询功能（暂未实现）
  const handleQuery = useCallback(() => {
    console.log('MapPage: 查询功能待实现');
  }, []);

  // 处理多时相对比功能
  const handleTemporalToggle = useCallback(async () => {
    console.log('MapPage: 切换多时相对比模式');

    if (!isTemporalMode) {
      // 进入多时相模式，获取多时相数据
      setTemporalLoading(true);
      try {
        const params = { pcatalog: 'multidate' };
        const data = await api.queryDataDirectory(params);
        console.log('多时相数据:', data);
        setTemporalData(data);

        // 默认选中最小年份的第一个可用时间点
        if (data && data.length > 0) {
          // 按年份排序，选择最小年份
          const sortedData = [...data].sort((a, b) => {
            const yearA = parseInt(a.Name.match(/\d+/)?.[0] || '0');
            const yearB = parseInt(b.Name.match(/\d+/)?.[0] || '0');
            return yearA - yearB;
          });

          const firstYearNode = sortedData[0];
          let defaultNode = null;

          // 如果有子节点且子节点有服务路径，选择第一个子节点
          if (firstYearNode.Children && firstYearNode.Children.length > 0) {
            const firstChild = firstYearNode.Children[0];
            if (firstChild.Attribute?.servicePath) {
              defaultNode = firstChild;
            }
          }

          // 如果子节点没有服务路径，检查父节点是否有服务路径
          if (!defaultNode && firstYearNode.Attribute?.servicePath) {
            defaultNode = firstYearNode;
          }

          if (defaultNode) {
            setSelectedTemporalNode(defaultNode);
            // 加载默认时间点的图层
            await handleTemporalNodeSelect(defaultNode);
          }
        }

        setIsTemporalMode(true);
      } catch (error) {
        console.error('获取多时相数据失败:', error);
        message.error('获取多时相数据失败: ' + error.message);
      } finally {
        setTemporalLoading(false);
      }
    } else {
      // 退出多时相模式，只移除多时相相关的图层
      try {
        if (mapRef.current) {
          await mapRef.current.removeLayersByPcatalog('multidate');
          console.log('已移除所有多时相图层');
        }
      } catch (error) {
        console.error('移除多时相图层失败:', error);
      }

      setIsTemporalMode(false);
      setSelectedTemporalNode(null);
      setTemporalData([]);
    }
  }, [isTemporalMode]);

  // 处理时间节点选择
  const handleTemporalNodeSelect = useCallback(async (node) => {
    console.log('选择时间节点:', node);
    setSelectedTemporalNode(node);

    // 通过ref调用OneMap的方法来替换多时相图层
    if (mapRef.current && node.Attribute?.servicePath) {
      try {
        // 只移除pcatalog为'multidate'的图层
        await mapRef.current.removeLayersByPcatalog('multidate');

        // 加载选中时间点的图层
        const serviceConfig = {
          id: node.Rid,
          type: node.Attribute.serviceTypeName,
          url: node.Attribute.servicePath,
          zIndex: 1000,
          accessInfo: node.Attribute.accessInfo,
          expandParam: node.Attribute.expandParam,
          pcatalog: 'multidate', // 标识为多时相图层
          layerName: node.Name, // 添加图层名称
        };

        console.log('加载时间点图层:', serviceConfig);
        const layerObj = mapRef.current.addLayer(serviceConfig);

        if (layerObj) {
          console.log('时间点图层加载成功:', node.Name);
        } else {
          console.warn('时间点图层加载失败:', node.Name);
        }
      } catch (error) {
        console.error('切换时间点图层失败:', error);
        message.error('切换时间点图层失败: ' + error.message);
      }
    }
  }, []);

  return (
    <div className={styles.mapPageContainer}>
      {/* 工具箱组件 - 在多屏模式下隐藏 */}
      {!isMultiScreenMode && (
        <ToolBox
          onCurtainToggle={handleCurtainToggle}
          isCurtainMode={isCurtainMode}
          onMultiScreenCompare={handleMultiScreenCompare}
          onTemporalToggle={handleTemporalToggle}
          isTemporalMode={isTemporalMode}
          onQuery={handleQuery}
          className={styles.mapToolbox}
        />
      )}

      {/* 多屏模式控制按钮 */}
      {isMultiScreenMode && (
        <MultiScreenExitButton
          onExit={handleExitMultiScreen}
          onShowConfig={handleShowConfigPanel}
          className={styles.multiScreenExitButton}
        />
      )}

      {/* 多屏模式配置面板 */}
      {isMultiScreenMode && showConfigPanel && (
        <MultiScreenPanel
          layout={multiScreenConfig.layout}
          syncView={multiScreenConfig.syncView}
          onLayoutChange={handleLayoutChange}
          onSyncViewChange={handleSyncViewChange}
          onClose={handleConfigPanelClose}
          className={styles.multiScreenConfigPanel}
        />
      )}

      {/* 单屏模式 */}
      {!isMultiScreenMode && (
        <>
          <OneMap
            ref={mapRef}
            deviceList={[]} // 传递设备数据
            deviceLoading={false} // 传递加载状态
            showTreePanel={true}
            showZoomControls={true}
            showMapInfoPanel={true}
            showMapScale={true}
            showMeasureBtn={true}
            showMapCompass={true}
            showSplitButton={false} // 隐藏原有的卷帘按钮
            showCoordinateQuery={true} // 控制坐标查询组件显示
            showNFZButton={showNFZButton} // 控制禁飞区按钮显示/隐藏
            isLeftSidebarCollapsed={true}
            isRightSidebarCollapsed={true}
            mapType={'2D'}
            isCurtainMode={isCurtainMode} // 传递卷帘状态
            theme={'light'}
          />
        </>
      )}

      {/* 多屏模式 */}
      {isMultiScreenMode && (
        <div className={styles.multiScreenContainer}>
          {/* 多屏模式下的动态布局地图组件 */}
          <div
            className={styles.multiScreenMaps}
            style={getGridStyle(multiScreenConfig.layout)}
          >
            {generateMapConfigs(multiScreenConfig.layout).map((mapConfig) => (
              <div key={mapConfig.id} className={styles.multiScreenMapItem}>
                <div className={styles.mapHeader}>
                  <h4>{mapConfig.title}</h4>
                </div>
                <OneMap
                  deviceList={[]} // 传递设备数据
                  deviceLoading={false} // 传递加载状态
                  showTreePanel={true} // 显示图层树面板
                  showZoomControls={false} // 隐藏缩放控件
                  showMapInfoPanel={false} // 隐藏地图信息面板
                  showMapScale={false} // 隐藏地图比例尺
                  showMeasureBtn={false} // 隐藏测量按钮
                  showMapCompass={false} // 隐藏指南针
                  showSplitButton={false} // 隐藏卷帘按钮
                  showCoordinateQuery={false} // 多屏模式下隐藏坐标查询
                  isLeftSidebarCollapsed={true}
                  isRightSidebarCollapsed={true}
                  mapType={mapConfig.mapType}
                  isCurtainMode={false} // 多屏模式下禁用卷帘
                  theme={'light'}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 多时相对比组件 */}
      {isTemporalMode && (
        <TemporalComparison
          data={temporalData}
          selectedNode={selectedTemporalNode}
          loading={temporalLoading}
          onNodeSelect={handleTemporalNodeSelect}
          onClose={() => setIsTemporalMode(false)}
          className={styles.mapTemporalComparison}
        />
      )}
    </div>
  );
};

export default MapPage;
