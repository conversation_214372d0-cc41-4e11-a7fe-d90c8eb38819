import React from 'react';
import L from 'leaflet';
import * as Cesium from 'cesium';
import LeafletMeasure from './components/LeafletMeasure';
import CesiumMeasure from './components/CesiumMeasure';

// 主组件
const MapMeasure = ({ viewer }) => {
  if (!viewer) return null;

  if (viewer instanceof L.Map) {
    return <LeafletMeasure map={viewer} />;
  } 
  else if (viewer && viewer.scene && viewer.entities) {
    return <CesiumMeasure viewer={viewer} />;
  }

  return null; // 如果 viewer 类型未知，则不渲染任何内容
};

export default MapMeasure;