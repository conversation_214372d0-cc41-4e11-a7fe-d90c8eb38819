import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Cesium } from 'umi'
import './CesiumSplit.less'

/**
 * Cesium卷帘对比组件
 * 实现左右分屏对比不同图层的功能
 */
const CesiumSplit = ({
  viewer,
  enabled = false,
  onSplitPositionChange,
  leftLayers = [],
  rightLayers = [],
  splitPosition = 0.5
}) => {
  const sliderRef = useRef(null)
  const [isDragging, setIsDragging] = useState(false)
  const [currentPosition, setCurrentPosition] = useState(splitPosition)

  // 初始化卷帘功能
  useEffect(() => {
    if (!viewer || !enabled) return

    // 设置初始分割位置
    viewer.scene.splitPosition = currentPosition

    // 配置左右图层的splitDirection
    updateLayersSplitDirection()

    return () => {
      // 清理：重置所有图层的splitDirection
      if (viewer && viewer.scene) {
        viewer.scene.splitPosition = 0.5
        resetAllLayersSplitDirection()
      }
    }
  }, [viewer, enabled, leftLayers, rightLayers])

  // 更新图层的分割方向
  const updateLayersSplitDirection = useCallback(() => {
    if (!viewer) return
    const updateWithRetry = (layerId, direction, side, retryCount = 0) => {
      const layer = findLayerById(layerId)
      if (layer) {
        layer.splitDirection = direction
        console.log(`设置图层 ${layerId} 为${side}侧显示`)
      } else if (retryCount < 3) {
        // 如果没找到图层，可能是异步加载中，延迟重试
        console.log(`${side}侧图层 ${layerId} 未找到，${500 * (retryCount + 1)}ms后重试 (${retryCount + 1}/3)`)
        setTimeout(() => {
          updateWithRetry(layerId, direction, side, retryCount + 1)
        }, 500 * (retryCount + 1))
      } else {
        console.warn(`${side}侧图层 ${layerId} 重试3次后仍未找到`)
      }
    }

    // 设置左侧图层
    leftLayers.forEach(layerId => {
      updateWithRetry(layerId, Cesium.SplitDirection.LEFT, '左')
    })

    // 设置右侧图层
    rightLayers.forEach(layerId => {
      updateWithRetry(layerId, Cesium.SplitDirection.RIGHT, '右')
    })
  }, [viewer, leftLayers, rightLayers, findLayerById])

  // 根据ID查找图层
  const findLayerById = useCallback((layerId) => {
    if (!viewer) return null

    // 查找ImageryLayer - 使用自定义属性或遍历查找
    for (let i = 0; i < viewer.imageryLayers.length; i++) {
      const layer = viewer.imageryLayers.get(i)
      // 检查图层是否有我们设置的ID标识
      if (layer._layerId === layerId || layer.id === layerId) {
        return layer
      }
    }

    // 查找3DTileset
    console.log('viewer.scene.primitives:', viewer.scene.primitives)
    console.log('primitives.length:', viewer.scene.primitives.length)

    for (let i = 0; i < viewer.scene.primitives.length; i++) {
      const primitive = viewer.scene.primitives.get(i)
      if (primitive instanceof Cesium.Cesium3DTileset &&
          (primitive._layerId === layerId || primitive.id === layerId)) {
        return primitive
      }
    }

    // 如果在primitives中没找到，检查_primitives内部数组（处理异步加载情况）
    if (viewer.scene.primitives._primitives) {
      console.log('检查_primitives内部数组，长度:', viewer.scene.primitives._primitives.length)
      for (let i = 0; i < viewer.scene.primitives._primitives.length; i++) {
        const primitive = viewer.scene.primitives._primitives[i]
        if (primitive instanceof Cesium.Cesium3DTileset &&
            (primitive._layerId === layerId || primitive.id === layerId)) {
          return primitive
        }
      }
    }

    console.warn(`未找到图层: ${layerId}，primitives.length: ${viewer.scene.primitives.length}`)
    return null
  }, [viewer])

  // 重置所有图层的splitDirection
  const resetAllLayersSplitDirection = useCallback(() => {
    if (!viewer) return

    // 重置ImageryLayer
    for (let i = 0; i < viewer.imageryLayers.length; i++) {
      const layer = viewer.imageryLayers.get(i)
      layer.splitDirection = Cesium.SplitDirection.NONE
    }

    // 重置3DTileset - 先检查正常的primitives集合
    for (let i = 0; i < viewer.scene.primitives.length; i++) {
      const primitive = viewer.scene.primitives.get(i)
      if (primitive instanceof Cesium.Cesium3DTileset) {
        primitive.splitDirection = Cesium.SplitDirection.NONE
      }
    }

    // 也检查_primitives内部数组
    if (viewer.scene.primitives._primitives) {
      for (let i = 0; i < viewer.scene.primitives._primitives.length; i++) {
        const primitive = viewer.scene.primitives._primitives[i]
        if (primitive instanceof Cesium.Cesium3DTileset) {
          primitive.splitDirection = Cesium.SplitDirection.NONE
        }
      }
    }
  }, [viewer])

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((e) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((e) => {
    if (!isDragging || !sliderRef.current) return

    const rect = sliderRef.current.parentElement.getBoundingClientRect()
    const newPosition = Math.max(0.1, Math.min(0.9, (e.clientX - rect.left) / rect.width))

    setCurrentPosition(newPosition)

    if (viewer) {
      viewer.scene.splitPosition = newPosition
    }

    if (onSplitPositionChange) {
      onSplitPositionChange(newPosition)
    }
  }, [isDragging, viewer, onSplitPositionChange])

  // 处理鼠标释放事件
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 添加全局事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // 更新分割位置
  useEffect(() => {
    setCurrentPosition(splitPosition)
    if (viewer) {
      viewer.scene.splitPosition = splitPosition
    }
  }, [splitPosition, viewer])

  if (!enabled) return null

  return (
    <div className="cesium-split-container">
      <div
        className="cesium-split-slider"
        ref={sliderRef}
        style={{ left: `${currentPosition * 100}%` }}
        onMouseDown={handleMouseDown}
      >
        <div className="cesium-split-handle">
          <div className="cesium-split-line" />
          <div className="cesium-split-grip">
            <span>⋮⋮</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CesiumSplit