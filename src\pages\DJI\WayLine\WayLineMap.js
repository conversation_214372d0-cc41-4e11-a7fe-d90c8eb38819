import React, { useRef, useEffect, useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ay<PERSON>,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { isEmpty, getBodyH, getDevice } from "@/utils/utils";
import { Location_Market5, WaylinePointMarker } from "@/pages/Maps/dt_market";
import { getLayers } from "@/pages/Maps/DiTuJson/ditu.js";
const WeiXingDT="https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
const WayLineMap = ({ h1, data }) => {

  let mapRef = useRef();
  let [map, setMap] = useState();

  useEffect(() => {
    setMap(mapRef.current);
  }, [mapRef, map]);

  if (isEmpty(data)) return <div> 12</div>;
  const device = getDevice(data.SN);
  if (isEmpty(device)) return <div></div>;
  const center = [device.Lat, device.Lng, device.Height];
  if (h1 === 0) {
    h1 = getBodyH(56);
  }

  const getPList = (pL) => {
    const list = [];
    const xL = pL.split(";");
    xL.forEach((p) => {
      const p1 = p.split(",");
      if (p1.length > 1) {
        list.push([Number(p1[1]), Number(p1[0])]);
      }
    });
    return list;
  };

  const getPList2 = (pL) => {
    const list = [];
    let i = 1;
    pL.forEach((p) => {
      list.push(WaylinePointMarker(p, "点位" + i));
      i++;
    });

    return list;
  };

  const xL = getPList(data.PointList);
  const dt_bz2 =
  "http://t0.tianditu.gov.cn/cia_w/wmts?" +
  "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
  "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
  "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";
  
  return (
    <div>
      <MapContainer
        layersOptions={null}
        attributionControl={null}
        zoomControl={null}
        preferCanvas={true}
        center={center}
        zoom={16}
        style={{ width: "100%", height: h1 }}
      >
        <TileLayer
          attribution={null}
          //tms={true}
          url="http://***************:9000/6251daf8-4127-40e0-980d-c86f8a765b20/map/mianzhu/{z}/{x}/{y}.png"
          // url="/api/v2/Map/GetTile?p1=tiles&x={x}&y={y}&z={z}"
        />
        <TileLayer
          attribution={null}
          //tms={true}
          url={WeiXingDT}
        />
        {Location_Market5({
          Lat: center[0],
          Lng: center[1],
          DeviceName: device.DName,
        })}
        {getLayers()}
        <Polyline weight={2} color={"yellow"} positions={xL}>
          <Tooltip sticky>{"航线"}</Tooltip>
        </Polyline>
        {getPList2(xL)}
      </MapContainer>
      {/* <MeasurePanel map={map} left={null} right={24} /> */}
    </div>
  );
};

export default WayLineMap;
