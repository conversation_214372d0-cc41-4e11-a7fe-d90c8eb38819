import { useEffect, useState } from "react";
import <PERSON><PERSON>iewer from "react-file-viewer";

export default function pdf(props) {
  const [fileUrl, setFileUrl] = useState(props?.jobUrl);
  const [fileTitle, setFileTitle] = useState("文件名称");
  const [fileType, setFileType] = useState("pdf");
  useEffect(() => {
    initFileViewer();
  }, [fileUrl]);
  const initFileViewer = () => {
    if (!fileUrl) return;
    setFileTitle(fileUrl.slice(fileUrl.indexOf("_") + 1, fileUrl.length));
    setFileType(fileUrl.slice(fileUrl.lastIndexOf(".") + 1, fileUrl.length));
  };
  return (
    <>
      <div style={{ width: "100%" }}>
        <FileViewer
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
          fileType={fileType}
          filePath={fileUrl}
          onError={(e) => console.log(e)}
        />
      </div>
    </>
  );
}
