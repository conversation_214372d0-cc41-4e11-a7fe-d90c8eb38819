import { getBodyH } from "@/utils/utils";
import { useState, useEffect } from "react";
import IfShowPanel from "@/components/IfShowPanel";
import DangerCountPie from './Panels/DangerCountPie2.js';
import AMap from './Panels/AMapContainer';
import { Get2 } from "@/services/general";
import JianKongPanel from './Panels/JianKongPanel';
import ZhiBanPanel from './Panels/ZhiBanPanel';
import BDZL from '@/assets/images/BDZL.png';
import DockStatePanel from './Panels/DockPanel';
import MainDataPanel from './Panels/MainDataPanel';



const OrgPage = () => {
    const [ifX, setIfX] = useState(false)
    const [dL, setDL] = useState([])
    const div2 = <div style={{ height: '100%', width: '100%', background: `rgba(0,0,255,0.2)` }}></div>

    const getPanel = <div style={{ height: '100%' }}>
            <DockStatePanel />
        <ZhiBanPanel />
    </div>

    const getPanel2 = <div style={{ height: '100%' }}>
       
        <JianKongPanel />
   
        <DangerCountPie />
    </div>


    useEffect(() => {
        const getDList = async () => {
            const xx = await Get2('/api/v1/Device/GetAllList')
            setDL(xx);
        }
        getDList();
    }, []);

    return <div  style={{ height: getBodyH(56) }}>
        <div
    
            style={{
                position: 'relative',
                width: '100%',
                height: '100%'
            }}
        >
            {IfShowPanel(getBodyH(80), 320, 8, 8, null, null, getPanel, true)}
            {IfShowPanel(getBodyH(80), 320, 8, null, 8, null, getPanel2, true)}
            {IfShowPanel(28, 200, getBodyH(96), null, 8, 0, <div style={{ height: 36, width: 200 }}><img style={{ height: 36 }} height={36} width={150} src={BDZL}></img></div>, true)}
            {IfShowPanel(120, 800, 40, 420, null, null, <MainDataPanel></MainDataPanel>, true)}
            <AMap data={dL} h1={getBodyH(56)}/>
            {/* <DJBaseMap data={dL} h1={getBodyH(56)}></DJBaseMap> */}
        </div>
    </div>
}

export default OrgPage;