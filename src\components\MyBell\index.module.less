
.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // background: rgb(121 229 235 / 70%);
  background: rgba(41, 81, 77, 0.7);
  color: #943002;
  margin: 10px 0;
  border-radius: 4px;
  padding: 5px;
  box-shadow: 0 0 5px rgb(121 229 235 / 70%);
}
.myBellList{
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  cursor: pointer;
}
.itemText {
  white-space: normal;
  width: 100%;
  color: rgb(255, 255, 255);
  // font-size: 12px;
  // text-indent: 1em;
}

.btn {
  display: flex;
  justify-content: flex-end;
}
.btnText{
  color: #efefef;
  cursor: pointer;
  background: #754141;
  padding: 0 5px;
  font-size: 12px;
  border-radius: 2px;
}

.btnText:hover {
  background: rgb(251, 127, 127);
  color: rgb(9, 9, 9);
}