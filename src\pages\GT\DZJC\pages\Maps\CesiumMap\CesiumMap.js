/**
 * SanlianJuanLian 组件，用于实现三维地图的卷帘对比功能
 */
import React, { useEffect, useState, useRef } from "react";
// 引入工具函数，用于判断空值和获取页面高度
import { isEmpty, getBodyH } from "@/utils/utils";
// 引入 Ant Design 组件，用于页面布局和交互
import { Row, Col, Button, Radio, List, message, Card } from "antd";
// 引入通用服务，用于发起 API 请求
import { Get2 } from "@/services/general";
// 引入虚拟列表组件，用于优化列表渲染性能
import VirtualList from "rc-virtual-list";
import { GetCesiumViewer } from "@/utils/cesium_help";
import { Cesium } from "umi";
import MapControl from "@/hooks/mapControl";
import LastPageButton from "@/components/LastPageButton";
import useMark3DHooks from '@/hooks/Mark3DHooks'

/**
 * SanweiJuanLian 组件定义
 * @returns {JSX.Element} 渲染的 JSX 元素
 */
const SanweiJuanLian = () => {
  // 使用 useState 来管理左侧或右侧地图的选择状态
  const [isLeft, setIsLeft] = useState("left");
  // 使用 useState 来管理左侧地图的图层信息
  const [layer1, setLayer1] = useState({});
  // 使用 useState 来管理右侧地图的图层信息
  const [layer2, setLayer2] = useState({});
  // 使用 useState 来管理地图列表数据
  const [mapList, setMapList] = useState([]);
  // 使用 useState 来管理是否正在加载地图列表的状态
  const [ifLoad, setIfLoad] = useState(true);
  // 计算地图列表容器的高度
  const ContainerHeight = getBodyH(166);
  // 大地图viewer
  const viewer = useRef(null);
  let [viewerData, setviewerData] = useState(null);
  const left = useRef(null);
  const right = useRef(null);
  const handler = useRef(null);
  const slider = useRef(null);
  let moveActive = false;
    // 标注功能
  let { Mark3D, getViewer } = useMark3DHooks({ idName: "cesiumContainer" });
  useEffect(() => {
    if(!viewer.current) return;
    viewer.current = getViewer()
    setviewerData(viewer.current)
    return () => {
      allClear(viewer.current);
    };
  }, []);
  const getMList = async () => {
    try {
      // 发起 API 请求获取地图列表数据
      let pst = await Get2("/api/v1/MapData/GetAllList");
      console.log(pst);

      if (pst && pst.length > 0) {
        // 数据获取成功，更新加载状态
        setIfLoad(false);
        // 更新地图列表状态
        setMapList(pst);
      } else {
        console.error("获取的地图列表数据为空:", pst);
        message.error("获取的地图列表数据为空，请检查 API 接口。");
      }
    } catch (error) {
      // 打印错误信息
      console.error("获取地图列表失败:", error);
      // 显示错误消息
      message.error("获取地图列表失败，请稍后重试");
    }
  };

  /**
   * 组件挂载时调用的副作用钩子
   * 调用 getMList 函数获取地图列表数据
   */
  useEffect(() => {
    getMList();
    viewer.current = GetCesiumViewer("cesiumContainer");
    setviewerData(viewer.current);
    // Sync the position of the slider with the split position
    slider.current = document.getElementById("slider");
    viewer.current.scene.splitPosition =
      slider.current.offsetLeft / slider.current.parentElement.offsetWidth;
    handler.current = new Cesium.ScreenSpaceEventHandler(slider.current);
    handler.current.setInputAction(function () {
      moveActive = true;
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    handler.current.setInputAction(function () {
      moveActive = true;
    }, Cesium.ScreenSpaceEventType.PINCH_START);
    handler.current.setInputAction(
      move,
      Cesium.ScreenSpaceEventType.MOUSE_MOVE
    );
    handler.current.setInputAction(
      move,
      Cesium.ScreenSpaceEventType.PINCH_MOVE
    );
    handler.current.setInputAction(function () {
      moveActive = false;
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
    handler.current.setInputAction(function () {
      moveActive = false;
    }, Cesium.ScreenSpaceEventType.PINCH_END);
    return () => {
      viewer.current.scene.primitives.remove(left.current);
      viewer.current.scene.primitives.remove(right.current);
    };
  }, []);
  function move(movement) {
    if (!moveActive) {
      return;
    }
    const relativeOffset = movement.endPosition.x;
    let splitPosition =
      (slider.current.offsetLeft + relativeOffset) /
      slider.current.parentElement.offsetWidth;
    if (splitPosition <= 0) {
      splitPosition = 0;
      slider.current.style.left = `${100.0 * splitPosition}%`;
    } else if (splitPosition >= 0.995) {
      splitPosition = 0.995;
    }
    slider.current.style.left = `${100.0 * splitPosition}%`;
    viewer.current.scene.splitPosition = splitPosition;
  }

  /**
   * 地图列表项点击事件处理函数
   * 根据选择的地图更新图层信息，并实现三维卷帘对比
   * @param {object} item 点击的地图列表项信息
   */
  const onMapClick = async (item) => {
    if (isLeft === "left") {
      setLayer1(item);
      if (left.current) {
        viewer.current.scene.primitives.remove(left.current);
      }
      Cesium.Cesium3DTileset.fromUrl(item.layer, {
        skipLevelOfDetail: true, // 启用
        baseScreenSpaceError: 100, // 基础屏幕错误阈值
        skipScreenSpaceErrorFactor: 16, // 跳过屏幕错误因子
        skipLevels: 1, // 跳过级别
        immediatelyLoadDesiredLevelOfDetail: false, // 是否立即加载所需细节级别
        loadSiblings: false, // 是否加载兄弟节点
        maximumScreenSpaceError: 2.0,
        optimizeForCesium: true,
      }).then((left1) => {
        left.current = left1;
        console.log(left1);

        viewer.current.scene.primitives.add(left.current);
        left.current.splitDirection = Cesium.SplitDirection.LEFT;
        viewer.current.zoomTo(left.current);
      });
      message.info(`已选择左侧地图`);
    } else {
      setLayer2(item);
      if (right.current) {
        viewer.current.scene.primitives.remove(right.current);
      }
      Cesium.Cesium3DTileset.fromUrl(item.layer, {
        skipLevelOfDetail: true, // 启用
        baseScreenSpaceError: 100, // 基础屏幕错误阈值
        skipScreenSpaceErrorFactor: 16, // 跳过屏幕错误因子
        skipLevels: 1, // 跳过级别
        immediatelyLoadDesiredLevelOfDetail: false, // 是否立即加载所需细节级别
        loadSiblings: false, // 是否加载兄弟节点
        maximumScreenSpaceError: 2.0,
        optimizeForCesium: true,
      }).then((right1) => {
        right.current = right1;
        viewer.current.scene.primitives.add(right.current);
        right.current.splitDirection = Cesium.SplitDirection.RIGHT;
        viewer.current.zoomTo(right.current);
      });
      message.info(`已选择右侧地图`);
    }
  };

  /**
   * 获取按钮类型的函数
   * 根据当前选择的地图和列表项名称，返回按钮的类型
   * @param {string} x1 列表项的名称
   * @returns {string} 按钮的类型，如 "primary" 或 "text"
   */
  const getType = (x1) => {
    if (isLeft === "left" && layer1.name === x1) {
      return "primary";
    } else if (isLeft === "right" && layer2 && layer2.name === x1) {
      return "primary";
    }
    return "text";
  };

  /**
   * 获取地图列表的函数
   * 根据地图列表数据，生成虚拟列表组件
   * @returns {JSX.Element} 渲染的地图列表组件
   */
  const getList = () => {
    const data = [];
    // 过滤地图列表数据，只保留 MapType 为 11 的项
    mapList?.forEach((e) => {
      if (e.MapType === 11) {
        data.push({
          name: e.MapName,
          layer: e.Url,
          b3dm: true,
        });
      }
    });

    return (
      <List>
        <VirtualList
          data={data}
          height={ContainerHeight}
          itemHeight={47}
          itemKey="i"
        >
          {/* 渲染列表项 */}
          {(item) => (
            <List.Item key={item.name}>
              <Button
                size="small"
                type={getType(item.name)}
                style={{
                  width: "100%",
                  borderColor: "#ccc",
                  display: "flex",
                  justifyContent: "flex-start",
                  fontSize: 12,
                  color:"#000"
                }}
                onClick={() => onMapClick(item)}
              >
                {item.name}
              </Button>
            </List.Item>
          )}
        </VirtualList>
      </List>
    );
  };

  return (
    <div>
      <Row>
        <Col span={3}>
          <Card title={"三维对比"}>
            <Radio.Group
              value={isLeft}
              buttonStyle="solid"
              onChange={(e) => setIsLeft(e.target.value)}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",    
                marginBottom: 20,
              }}
            >
              <Radio.Button value="left">左</Radio.Button>
              <Radio.Button value="right">右</Radio.Button>
            </Radio.Group>
            {getList()}
          </Card>
        </Col>
        <Col span={21}>
          <div
            id="cesiumContainer"
            style={{
              width: "100%",
              height: "calc(100vh - 47px)",
              position: "relative",
            }}
          >
            <div
              id="slider"
              style={{
                cursor: "ew-resize",
                position: "absolute",
                top: 0,
                left: "50%",
                width: 5,
                height: "100%",
                backgroundColor: " #d3d3d3",
                zIndex: 1000,
              }}
            ></div>
             <div style={{ position: 'absolute', right: 30, top: 20, zIndex: 1 }}>
              <Mark3D />
            </div>
            <div
              style={{ position: "absolute", right: 30, bottom: 50, zIndex: 1 }}
            >
              <MapControl viewerData={viewerData} />
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

// 导出组件
export default SanweiJuanLian;
