import { Space, Tag, message, Modal, Switch } from 'antd';
import {  isEmpty } from '@/utils/utils';
// import './table.css';

import { Post2 } from '@/services/general';


const getTableTitle = (title) => { return <div style={{ fontWeight: 'bold', textAlign: 'center' }}>  {title}</div> }

const { confirm } = Modal;


const deteleData = async (record, refrush) => {

  const xx = await Post2("/api/v1/{dName}/Delete", record);

  if (!isEmpty(xx.err)) {
    message.info("错误：" + xx.err)
  } else {
    message.info("删除成功！")
    refrush();
  }
}


const showDeleteConfirm = (record, refrush) => {
  confirm({
    title: '删除记录',
    //icon: <ExclamationCircleFilled />,
    content: '确定删除该记录吗？',
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deteleData(record, refrush);
    },
    onCancel() {
      // console.log('Cancel');
    },
  });
};

const getDataItem=(title,data)=>{
 return {
    title: getTableTitle(title),
    dataIndex: data,
    key: data,
    align: 'center',
  }
}

const TableCols = (refrush, editForm,detailForm) => {
  const list=[]
  //list.push(getColData("单位","FDepartment"));
    {colStr}


   list.push( {
      title: getTableTitle('操作'),
      align: 'center',
      render: (record) => (
        <Space size="middle">
          <Tag><a onClick={()=>detailForm(record)}>详情</a></Tag>
          <Tag><a onClick={() => editForm(record)}>编辑</a></Tag>
          <Tag><a onClick={() => showDeleteConfirm(record, refrush)}>删除</a></Tag>
        </Space>)
    });
  return list;
}



export default TableCols;
